
import React, { useState } from 'react';
import { Button } from './ui/Button';
import { CloseIcon, UsersIcon } from './Icons';
import { TeamMemberRole } from '../types';

interface InviteMemberModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInvite: (email: string, role: TeamMemberRole) => string | null;
}

const InviteMemberModal: React.FC<InviteMemberModalProps> = ({ isOpen, onClose, onInvite }) => {
  const [email, setEmail] = useState('');
  const [role, setRole] = useState<TeamMemberRole>('Member');
  const [error, setError] = useState<string | null>(null);
  
  if (!isOpen) {return null;}

  const handleInviteClick = () => {
    setError(null);
    if (!email.trim()) {
        setError("Email address is required.");
        return;
    }
    const result = onInvite(email, role);
    if (result) {
        setError(result);
    } else {
        onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-md">
        <header className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold text-zinc-800 flex items-center">
            <UsersIcon className="w-5 h-5 mr-2 text-brand-700" />
            Invite Team Member
          </h2>
          <button onClick={onClose} className="p-2 text-zinc-500 hover:bg-zinc-100 rounded-lg">
            <CloseIcon className="w-6 h-6" />
          </button>
        </header>
        <main className="p-6 space-y-4">
          <p className="text-sm text-zinc-600">Enter the email address of the person you want to invite and choose their role.</p>
          <div>
            <label htmlFor="inviteEmail" className="block text-sm font-medium text-zinc-700 mb-1">
              Email Address
            </label>
            <input
              type="email"
              id="inviteEmail"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="e.g., <EMAIL>"
              className="block w-full rounded-md border-zinc-300 shadow-sm p-2"
              autoFocus
            />
          </div>
           <div>
            <label htmlFor="inviteRole" className="block text-sm font-medium text-zinc-700 mb-1">
              Role
            </label>
            <select
              id="inviteRole"
              value={role}
              onChange={(e) => setRole(e.target.value as TeamMemberRole)}
              className="block w-full rounded-md border-zinc-300 shadow-sm p-2"
            >
                <option>Member</option>
                <option>Admin</option>
            </select>
          </div>
          {error && <p className="text-sm text-red-500">{error}</p>}
        </main>
        <footer className="p-4 bg-zinc-50 flex justify-end gap-3 rounded-b-2xl border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleInviteClick} disabled={!email.trim()}>
            Send Invitation
          </Button>
        </footer>
      </div>
    </div>
  );
};

export default InviteMemberModal;