import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient } from '../supabaseClient';

const router = Router();

const insertSchema = z.object({ name: z.string().min(1), content: z.string().min(1) });
const updateSchema = insertSchema.partial();

router.get('/', requireAuth, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('custom_templates').select('*').order('created_at', { ascending: false });
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ templates: data });
});

router.post('/', requireAuth, async (req: AuthRequest, res) => {
  const parsed = insertSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data: profile } = await supa.auth.getUser();
  const userId = profile.user?.id;
  const { data, error } = await supa
    .from('custom_templates')
    .insert({ user_id: userId, name: parsed.data.name, content: parsed.data.content })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ template: data });
});

router.put('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = updateSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{ name: string; content: string }> = { name: parsed.data.name, content: parsed.data.content };
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('custom_templates').update(patch).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ template: data });
});

router.delete('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('custom_templates').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

export default router;

