import { Router } from 'express';
import { z } from 'zod';
import <PERSON><PERSON> from 'stripe';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { supabaseAdmin, getUserClient } from '../supabaseClient';
import { cacheKeys, deleteFromCache } from '../lib/redis';
import {
  createBillingSession,
  getBillingSessionForUser,
  setBillingSessionStatus,
  deleteBillingSession,
} from '../lib/billingSessionStore';
import { logger } from '../lib/logger';
import { getStripeClient } from '../lib/stripe';

const CLIENT_ORIGIN = process.env.CLIENT_ORIGIN || 'http://localhost:5173';

const CHECKOUT_SUCCESS_PATH = process.env.STRIPE_CHECKOUT_SUCCESS_PATH || '/billing/success';
const CHECKOUT_CANCEL_PATH = process.env.STRIPE_CHECKOUT_CANCEL_PATH || '/billing/cancel';

function sanitizeBaseUrl(url: string): string {
  return url.endsWith('/') ? url.slice(0, -1) : url;
}

function buildSuccessUrl(baseUrl: string): string {
  const sanitized = sanitizeBaseUrl(baseUrl);
  const path = CHECKOUT_SUCCESS_PATH.startsWith('/') ? CHECKOUT_SUCCESS_PATH : `/${CHECKOUT_SUCCESS_PATH}`;
  return `${sanitized}${path}?session_id={CHECKOUT_SESSION_ID}`;
}

function buildCancelUrl(baseUrl: string): string {
  const sanitized = sanitizeBaseUrl(baseUrl);
  const path = CHECKOUT_CANCEL_PATH.startsWith('/') ? CHECKOUT_CANCEL_PATH : `/${CHECKOUT_CANCEL_PATH}`;
  return `${sanitized}${path}`;
}

function parseUnitAmount(price: string): number | null {
  const normalized = price.replace(/[^0-9.,]/g, '').replace(/,(?=\d{3}(\D|$))/g, '');
  if (!normalized) {
    return null;
  }
  const parsed = Number.parseFloat(normalized.replace(',', '.'));
  if (Number.isNaN(parsed)) {
    return null;
  }
  const cents = Math.round(parsed * 100);
  return cents > 0 ? cents : null;
}

function parseRecurringInterval(
  priceDetail?: string | null
): Stripe.Checkout.SessionCreateParams.LineItem.PriceData.Recurring.Interval | undefined {
  if (!priceDetail) {
    return undefined;
  }
  const detail = priceDetail.toLowerCase();
  if (detail.includes('year')) {
    return 'year';
  }
  if (detail.includes('month') || detail.includes('mo')) {
    return 'month';
  }
  if (detail.includes('week')) {
    return 'week';
  }
  if (detail.includes('day')) {
    return 'day';
  }
  return undefined;
}

const router = Router();

const checkoutSchema = z.object({
  planName: z.string().min(1, 'Plan name is required'),
});

const sessionSchema = z.object({
  sessionId: z.string().min(1, 'Checkout session is required'),
});

router.post('/checkout', requireAuth, async (req: AuthRequest, res) => {
  const parsed = checkoutSchema.safeParse(req.body);
  if (!parsed.success) {
    return res.status(400).json({ error: parsed.error.message });
  }

  const userId = req.user?.id;
  if (!userId) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const { data: planRow, error } = await supabaseAdmin
    .from('pricing_plans')
    .select('name, price, price_detail')
    .eq('name', parsed.data.planName)
    .single();

  const plan = planRow as { name: string; price: string; price_detail: string | null } | null;

  if (error || !plan) {
    logger.warn({ userId, planName: parsed.data.planName, error }, 'Invalid plan requested for checkout');
    return res.status(400).json({ error: 'Selected plan is not available.' });
  }

  const stripeClient = await getStripeClient();
  if (!stripeClient) {
    logger.error('Stripe secret key not configured; cannot create checkout session');
    return res.status(500).json({ error: 'Payment provider is not configured. Please contact support.' });
  }
  const { stripe } = stripeClient;

  const unitAmount = parseUnitAmount(plan.price);
  if (!unitAmount) {
    logger.error({ userId, planName: plan.name, price: plan.price }, 'Invalid plan price configured for checkout');
    return res.status(500).json({ error: 'Selected plan is misconfigured. Please try again later.' });
  }

  const recurringInterval = parseRecurringInterval(plan.price_detail);
  const baseUrl = typeof req.headers.origin === 'string' ? req.headers.origin : CLIENT_ORIGIN;

  let checkoutSession: Stripe.Checkout.Session;
  try {
    checkoutSession = await stripe.checkout.sessions.create({
      mode: recurringInterval ? 'subscription' : 'payment',
      line_items: [
        {
          quantity: 1,
          price_data: {
            currency: 'usd',
            unit_amount: unitAmount,
            product_data: { name: plan.name },
            ...(recurringInterval ? { recurring: { interval: recurringInterval } } : {}),
          },
        },
      ],
      success_url: buildSuccessUrl(baseUrl),
      cancel_url: buildCancelUrl(baseUrl),
      client_reference_id: userId,
      customer_email: req.user?.email ?? undefined,
      metadata: {
        userId,
        planName: plan.name,
      },
      allow_promotion_codes: true,
    });
  } catch (err) {
    logger.error({ err, userId, planName: plan.name }, 'Failed to create Stripe checkout session');
    return res.status(502).json({ error: 'Unable to start checkout session. Please try again later.' });
  }

  const session = createBillingSession(userId, plan.name, {
    id: checkoutSession.id,
    providerSessionId: checkoutSession.id,
  });

  return res.json({
    sessionId: session.id,
    status: session.status,
    checkoutUrl: checkoutSession.url ?? null,
    plan: {
      name: plan.name,
      price: plan.price,
      priceDetail: plan.price_detail,
    },
  });
});

router.post('/confirm', requireAuth, async (req: AuthRequest, res) => {
  const parsed = sessionSchema.safeParse(req.body);
  if (!parsed.success) {
    return res.status(400).json({ error: parsed.error.message });
  }

  const userId = req.user?.id;
  if (!userId) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const billingSession = getBillingSessionForUser(parsed.data.sessionId, userId);
  if (!billingSession) {
    return res.status(404).json({ error: 'Checkout session not found or has expired.' });
  }
  if (billingSession.status === 'canceled') {
    return res.status(400).json({ error: 'Checkout session has already been canceled.' });
  }
  if (billingSession.status === 'confirmed') {
    return res.status(409).json({ error: 'Checkout session was already confirmed.' });
  }

  const stripeClient = await getStripeClient();
  if (!stripeClient) {
    logger.error({ userId, sessionId: billingSession.id }, 'Stripe secret key not configured; cannot confirm session');
    return res.status(500).json({ error: 'Payment provider is not configured. Please contact support.' });
  }

  const providerSessionId = billingSession.providerSessionId ?? billingSession.id;
  let stripeSession: Stripe.Checkout.Session;
  try {
    stripeSession = await stripeClient.stripe.checkout.sessions.retrieve(providerSessionId, {
      expand: ['payment_intent', 'subscription'],
    });
  } catch (err) {
    logger.error({ err, sessionId: billingSession.id, userId }, 'Failed to retrieve Stripe checkout session');
    return res.status(502).json({ error: 'Unable to verify payment with provider. Please try again shortly.' });
  }

  const metadataUserId =
    typeof stripeSession.metadata?.userId === 'string' ? stripeSession.metadata.userId : stripeSession.client_reference_id;
  if (metadataUserId && metadataUserId !== userId) {
    logger.warn({ sessionId: billingSession.id, userId }, 'User attempted to confirm checkout session owned by another user');
    return res.status(403).json({ error: 'You are not authorized to confirm this checkout session.' });
  }

  const subscriptionStatus =
    stripeSession.subscription && typeof stripeSession.subscription !== 'string'
      ? stripeSession.subscription.status
      : null;
  const paymentComplete =
    stripeSession.payment_status === 'paid' ||
    stripeSession.status === 'complete' ||
    Boolean(subscriptionStatus && ['active', 'trialing'].includes(subscriptionStatus));
  if (!paymentComplete) {
    return res.status(409).json({ error: 'Checkout session has not been paid yet.' });
  }

  const planNameFromMetadata =
    typeof stripeSession.metadata?.planName === 'string' ? stripeSession.metadata.planName : billingSession.planName;

  const { data: planRow, error: planError } = await supabaseAdmin
    .from('pricing_plans')
    .select('name')
    .eq('name', planNameFromMetadata)
    .single();

  if (planError || !planRow) {
    logger.error({ planName: planNameFromMetadata, userId }, 'Confirmed checkout references unknown plan');
    return res.status(500).json({ error: 'Paid plan is not available at this time. Please contact support.' });
  }

  try {
    const supa = getUserClient(getAccessToken(req));
    const { error } = await supa
      .from('profiles')
      .upsert({ id: userId, plan_name: planRow.name, subscription_status: 'active' }, { onConflict: 'id' });

    if (error) {
      throw new Error(error.message);
    }

    await deleteFromCache(cacheKeys.userProfile(userId));
  } catch (err) {
    logger.error({ err, sessionId: billingSession.id, userId }, 'Failed to update plan after billing confirmation');
    return res.status(500).json({ error: 'Failed to update your plan. Please try again.' });
  }

  setBillingSessionStatus(billingSession.id, 'confirmed');
  deleteBillingSession(billingSession.id);

  logger.info({ sessionId: billingSession.id, userId, planName: planRow.name }, 'Billing session confirmed');
  return res.json({ success: true, planName: planRow.name });
});

router.post('/cancel', requireAuth, async (req: AuthRequest, res) => {
  const parsed = sessionSchema.safeParse(req.body);
  if (!parsed.success) {
    return res.status(400).json({ error: parsed.error.message });
  }

  const userId = req.user?.id;
  if (!userId) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const session = getBillingSessionForUser(parsed.data.sessionId, userId);
  if (!session) {
    return res.status(404).json({ error: 'Checkout session not found or has expired.' });
  }
  if (session.status === 'confirmed') {
    return res.status(409).json({ error: 'Checkout session was already completed.' });
  }

  const stripeClient = await getStripeClient();
  if (!stripeClient) {
    logger.error({ sessionId: session.id, userId }, 'Stripe secret key not configured; cannot cancel session');
    return res.status(500).json({ error: 'Payment provider is not configured. Please contact support.' });
  }

  const providerSessionId = session.providerSessionId ?? session.id;
  try {
    await stripeClient.stripe.checkout.sessions.expire(providerSessionId);
  } catch (err) {
    const statusCode = typeof (err as { statusCode?: number }).statusCode === 'number' ? (err as { statusCode: number }).statusCode : undefined;
    if (statusCode === 400 || statusCode === 404) {
      logger.warn({ err, sessionId: session.id, userId }, 'Stripe session already closed when attempting to cancel');
    } else {
      logger.error({ err, sessionId: session.id, userId }, 'Failed to cancel Stripe checkout session');
      return res.status(502).json({ error: 'Unable to cancel checkout session with provider. Please try again later.' });
    }
  }

  setBillingSessionStatus(session.id, 'canceled');
  deleteBillingSession(session.id);
  logger.info({ sessionId: session.id, userId }, 'Billing session canceled');

  return res.json({ success: true });
});

export default router;
