import React, { useMemo, useState } from 'react';
import { User, DashboardView, Document as DocType, Signature, DocumentStatus } from '../types';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from './ui/Card';
import { Button } from './ui/Button';
import { LockSolidIcon } from './Icons';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/Table';

import RequestSignatureModal from './RequestSignatureModal';

interface LifecyclePageProps {
  user: User;
  setView: (view: DashboardView) => void;
  onViewDocument: (doc: DocType) => void;
  onUpdateDocumentStatus: (docId: string, newStatus: DocType['status']) => void;
  onRequestSignatures: (docId: string, signers: Omit<Signature, 'id' | 'status' | 'token'>[]) => void;
  allUsers: User[];
}

const StatCard: React.FC<{ title: string; value: number | string; }> = ({ title, value }) => (
    <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{title}</CardTitle>
        </CardHeader>
        <CardContent>
            <div className="text-2xl font-bold">{value}</div>
        </CardContent>
    </Card>
);

const KanbanCard: React.FC<{ doc: DocType, draggable: boolean, onDragStart: (e: React.DragEvent<HTMLDivElement>, doc: DocType) => void, onClick: () => void }> = ({ doc, draggable, onDragStart, onClick }) => (
    <Card 
        className="p-3 hover:shadow-xl transition-shadow cursor-pointer"
        draggable={draggable}
        onDragStart={(e) => onDragStart(e, doc)}
        onClick={onClick}
    >
        <p className="font-medium text-sm text-zinc-800 dark:text-zinc-200 truncate">{doc.name}</p>
        <p className="text-xs text-zinc-500 dark:text-zinc-400 mt-1">Updated: {new Date(doc.updatedAt).toLocaleDateString()}</p>
    </Card>
);

const KanbanColumn: React.FC<{ title: string, children?: React.ReactNode, onDragOver: (e: React.DragEvent<HTMLDivElement>) => void, onDrop: (e: React.DragEvent<HTMLDivElement>) => void }> = ({ title, children, onDragOver, onDrop }) => (
    <div 
        className="bg-zinc-100 dark:bg-zinc-900 rounded-lg p-4 transition-colors"
        onDragOver={onDragOver}
        onDrop={onDrop}
    >
        <h3 className="font-semibold text-zinc-700 dark:text-zinc-300 capitalize mb-4 bg-zinc-200 dark:bg-zinc-800 p-2 rounded-md">
            {title.replace('-', ' ')}
        </h3>
        <div className="space-y-3 min-h-[100px]">
            {children}
        </div>
    </div>
);

const LifecyclePage: React.FC<LifecyclePageProps> = ({ user, setView, onViewDocument, onUpdateDocumentStatus, onRequestSignatures, allUsers }) => {
    const [draggedDoc, setDraggedDoc] = useState<DocType | null>(null);
    const [isSignatureModalOpen, setIsSignatureModalOpen] = useState(false);
    const [docForSignature, setDocForSignature] = useState<DocType | null>(null);

    const isPremium = user.planName === 'Premium' || user.planName === 'Enterprise';

    const documents = user.documents;

    const stats = useMemo(() => {
        const activeContracts = documents.filter(d => d.status === 'completed');
        const pendingSignatures = documents.filter(d => d.status === 'out-for-signature');
        const archivedContracts = documents.filter(d => d.status === 'archived');
        const now = new Date();
        const ninetyDaysFromNow = new Date();
        ninetyDaysFromNow.setDate(now.getDate() + 90);

        const expiringSoon = activeContracts.flatMap(d => d.keyDates || [])
            .filter(kd => {
                const date = new Date(kd.date);
                return date > now && date <= ninetyDaysFromNow;
            }).length;
            
        return {
            active: activeContracts.length,
            pending: pendingSignatures.length,
            expiring: expiringSoon,
            archived: archivedContracts.length,
        };
    }, [documents]);
    
    const upcomingKeyDates = useMemo(() => {
        const now = new Date();
        return documents
            .flatMap(doc => (doc.keyDates || []).map(kd => ({ ...kd, docName: doc.name, docId: doc.id })))
            .filter(kd => new Date(kd.date) >= now)
            .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    }, [documents]);
    
    const daysRemaining = (dateStr: string) => {
        const diff = new Date(dateStr).getTime() - new Date().getTime();
        return Math.ceil(diff / (1000 * 60 * 60 * 24));
    }

    const handleDragStart = (e: React.DragEvent<HTMLDivElement>, doc: DocType) => {
        setDraggedDoc(doc);
    };

    const handleDrop = (e: React.DragEvent<HTMLDivElement>, newStatus: DocumentStatus) => {
        e.preventDefault();
        if (!draggedDoc) {return;}

        const { id, status: oldStatus } = draggedDoc;
        
        if (oldStatus === 'draft' && newStatus === 'out-for-signature') {
            setDocForSignature(draggedDoc);
            setIsSignatureModalOpen(true);
        } else if (oldStatus === 'out-for-signature' && newStatus === 'draft') {
            onUpdateDocumentStatus(id, 'draft');
        } else if (oldStatus === 'completed' && newStatus === 'archived') {
            onUpdateDocumentStatus(id, 'archived');
        }

        setDraggedDoc(null);
    };

    if (!isPremium) {
        return (
            <div className="p-4 sm:p-6 lg:p-8 h-full flex items-center justify-center">
                <div className="text-center">
                    <LockSolidIcon className="w-12 h-12 mx-auto text-zinc-300 mb-4" />
                    <h3 className="text-xl font-semibold text-zinc-800">Unlock Contract Lifecycle Management</h3>
                    <p className="text-zinc-500 mt-2 mb-4 max-w-md">
                        Upgrade to Premium to track contract statuses, visualize your pipeline, and get AI-powered deadline reminders.
                    </p>
                    <Button onClick={() => setView('subscription')}>
                        Upgrade Now
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <>
        <div className="p-4 sm:p-6 lg:p-8 space-y-8">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <StatCard title="Active Contracts" value={stats.active} />
                <StatCard title="Pending Signatures" value={stats.pending} />
                <StatCard title="Archived Contracts" value={stats.archived} />
                <StatCard title="Expiring Soon (90d)" value={stats.expiring} />
            </div>

            <div>
                <h2 className="text-2xl font-bold text-zinc-900 dark:text-white mb-4">Contract Pipeline</h2>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    {(['draft', 'out-for-signature', 'completed', 'archived'] as const).map(status => (
                        <KanbanColumn 
                            key={status} 
                            title={status}
                            onDragOver={(e) => e.preventDefault()}
                            onDrop={(e) => handleDrop(e, status)}
                        >
                            {documents.filter(d => d.status === status).map(doc => (
                                <KanbanCard 
                                    key={doc.id} 
                                    doc={doc} 
                                    draggable={status !== 'completed'} 
                                    onDragStart={(e) => handleDragStart(e, doc)}
                                    onClick={() => onViewDocument(doc)}
                                 />
                            ))}
                        </KanbanColumn>
                    ))}
                </div>
            </div>
            
             <Card>
                <CardHeader>
                    <CardTitle>Upcoming Key Dates</CardTitle>
                    <CardDescription>AI-extracted deadlines from your completed contracts.</CardDescription>
                </CardHeader>
                <CardContent>
                    <div className="overflow-x-auto">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Document</TableHead>
                                    <TableHead>Event</TableHead>
                                    <TableHead>Date</TableHead>
                                    <TableHead className="text-right hidden sm:table-cell">Days Remaining</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {upcomingKeyDates.length > 0 ? (
                                    upcomingKeyDates.map(item => (
                                        <TableRow key={`${item.docId}-${item.event}`}>
                                            <TableCell>
                                                <button onClick={() => {
                                                    const doc = documents.find(d => d.id === item.docId);
                                                    if (doc) { onViewDocument(doc); }
                                                }} className="font-medium text-brand-600 hover:underline">
                                                    {item.docName}
                                                </button>
                                            </TableCell>
                                            <TableCell>{item.event}</TableCell>
                                            <TableCell>{new Date(item.date).toLocaleDateString()}</TableCell>
                                            <TableCell className="text-right hidden sm:table-cell">{daysRemaining(item.date)}</TableCell>
                                        </TableRow>
                                    ))
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={4} className="text-center h-24 text-zinc-500">
                                            No upcoming dates found in your completed contracts.
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </div>
                </CardContent>
            </Card>
        </div>
        {isSignatureModalOpen && docForSignature && (
            <RequestSignatureModal
                isOpen={isSignatureModalOpen}
                onClose={() => setIsSignatureModalOpen(false)}
                document={docForSignature}
                allUsers={allUsers}
                onRequest={(docId, signers) => {
                    onRequestSignatures(docId, signers);
                    setIsSignatureModalOpen(false);
                }}
            />
        )}
        </>
    );
};

export default LifecyclePage;