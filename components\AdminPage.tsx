import React, { useState } from 'react';
import ErrorBoundary from './ui/ErrorBoundary';
import { User, Team } from '../types';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from './ui/Card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger, TabsContent } from './ui/Tabs';
import { CheckCircleIcon, XCircleIcon } from './Icons';

interface AdminPageProps {
  allUsers: User[];
  allTeams: Team[];
}

// Virtualized row components for better performance with large datasets
const TeamRow: React.FC<{ team: Team; getTeamOwner: (team: Team) => User | undefined }> = ({ team, getTeamOwner }) => {
  const owner = getTeamOwner(team);

  return (
    <div className="border-b border-zinc-200 dark:border-zinc-800">
      <div className="flex items-center min-h-[60px] px-4 hover:bg-zinc-50 dark:hover:bg-zinc-900/50">
        <div className="flex-1 font-medium">{team.name}</div>
        <div className="flex-1">{owner?.email || 'N/A'}</div>
        <div className="flex-1">{team.members.length + 1}</div>
        <div className="flex-1">{owner?.planName || 'N/A'}</div>
      </div>
    </div>
  );
};

const UserRow: React.FC<{ user: User; allTeams: Team[] }> = ({ user, allTeams }) => {
  const team = allTeams.find(t => t.id === user.teamId);

  return (
    <div className="border-b border-zinc-200 dark:border-zinc-800">
      <div className="flex items-center min-h-[60px] px-4 hover:bg-zinc-50 dark:hover:bg-zinc-900/50">
        <div className="flex-1">
          <div className="font-medium">{user.name || 'No Name'}</div>
          <div className="text-sm text-zinc-500">{user.email}</div>
        </div>
        <div className="flex-1">{team?.name || 'No Team'}</div>
        <div className="flex-1">{user.planName}</div>
        <div className="flex-1">
          {user.isVerified ? (
            <span className="flex items-center text-green-600"><CheckCircleIcon className="w-4 h-4 mr-1"/> Verified</span>
          ) : (
            <span className="flex items-center text-amber-600"><XCircleIcon className="w-4 h-4 mr-1"/> Unverified</span>
          )}
        </div>
      </div>
    </div>
  );
};

const AdminPage: React.FC<AdminPageProps> = ({ allUsers, allTeams }) => {
  const [activeTab, setActiveTab] = useState('tenants');

  const getTeamOwner = (team: Team): User | undefined => {
    return allUsers.find(u => u.id === team.ownerId);
  };

  return (
    <div className="p-4 sm:p-6 lg:p-8 space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-zinc-900 dark:text-white">Admin Dashboard</h1>
        <p className="text-zinc-600 dark:text-zinc-400 mt-1">Oversee tenants and users across the platform.</p>
      </div>

      <Tabs>
        <TabsList>
          <TabsTrigger onClick={() => setActiveTab('tenants')} data-state={activeTab === 'tenants' ? 'active' : 'inactive'}>Tenant Management</TabsTrigger>
          <TabsTrigger onClick={() => setActiveTab('users')} data-state={activeTab === 'users' ? 'active' : 'inactive'}>User Management</TabsTrigger>
        </TabsList>
        
        {activeTab === 'tenants' && (
          <TabsContent>
            <Card>
              <CardHeader>
                <CardTitle>All Tenants (Teams)</CardTitle>
                <CardDescription>A list of all registered teams on the platform.</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg">
                  {/* Table Header */}
                  <div className="border-b border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900/50">
                    <div className="flex items-center min-h-[50px] px-4 font-medium text-sm">
                      <div className="flex-1">Team Name</div>
                      <div className="flex-1">Owner Email</div>
                      <div className="flex-1">Members</div>
                      <div className="flex-1">Plan</div>
                    </div>
                  </div>
                  {/* Table Body */}
                  <div className="max-h-[400px] overflow-y-auto">
                    <ErrorBoundary fallback={
                      <div className="flex items-center justify-center h-full text-gray-500">
                        Unable to load teams list
                      </div>
                    }>
                      {allTeams?.map((team) => (
                        <TeamRow
                          key={team.id}
                          team={team}
                          getTeamOwner={getTeamOwner}
                        />
                      ))}
                      {(!allTeams || allTeams.length === 0) && (
                        <div className="flex items-center justify-center py-8 text-gray-500">
                          No teams found
                        </div>
                      )}
                    </ErrorBoundary>
                  </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          )}

        {activeTab === 'users' && (
          <TabsContent>
            <Card>
              <CardHeader>
                <CardTitle>All Users</CardTitle>
                <CardDescription>A list of all registered users on the platform.</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg">
                  {/* Table Header */}
                  <div className="border-b border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900/50">
                    <div className="flex items-center min-h-[50px] px-4 font-medium text-sm">
                      <div className="flex-1">User</div>
                      <div className="flex-1">Team</div>
                      <div className="flex-1">Plan</div>
                      <div className="flex-1">Status</div>
                    </div>
                  </div>
                  {/* Table Body */}
                  <div className="max-h-[400px] overflow-y-auto">
                    <ErrorBoundary fallback={
                      <div className="flex items-center justify-center h-full text-gray-500">
                        Unable to load users list
                      </div>
                    }>
                      {allUsers?.map((user) => (
                        <UserRow
                          key={user.id}
                          user={user}
                          allTeams={allTeams || []}
                        />
                      ))}
                      {(!allUsers || allUsers.length === 0) && (
                        <div className="flex items-center justify-center py-8 text-gray-500">
                          No users found
                        </div>
                      )}
                    </ErrorBoundary>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};

export default AdminPage;
