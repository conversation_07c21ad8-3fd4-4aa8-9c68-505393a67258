import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient } from '../supabaseClient';
import { batchFetchComments } from '../lib/batchUtils';

const router = Router();

// Comment Threads
const threadInsert = z.object({
  documentId: z.string().uuid(),
  textSelection: z.string().min(1),
});
const threadUpdate = z.object({
  textSelection: z.string().min(1).optional(),
  isResolved: z.boolean().optional(),
});

const batchCommentsSchema = z.object({
  documentIds: z.array(z.string().uuid()).max(50),
});

router.get('/threads', requireAuth, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('comment_threads').select('*').order('created_at', { ascending: false });
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ threads: data });
});

// Batch fetch comments for multiple documents
router.post('/batch', requireAuth, async (req: AuthRequest, res) => {
  const parse = batchCommentsSchema.safeParse(req.body);
  if (!parse.success) {
    return res.status(400).json({ error: parse.error.message });
  }
  
  const { documentIds } = parse.data;
  
  try {
    const userClient = getUserClient(getAccessToken(req));
    const commentsMap = await batchFetchComments(userClient, documentIds);
    
    // Convert Map to object for JSON response
    const comments = Object.fromEntries(commentsMap);
    
    res.json({ comments });
  } catch (error) {
    res.status(500).json({ error: error instanceof Error ? error.message : 'Failed to fetch comments' });
  }
});

router.post('/threads', requireAuth, async (req: AuthRequest, res) => {
  const parsed = threadInsert.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa
    .from('comment_threads')
    .insert({
      document_id: parsed.data.documentId,
      text_selection: parsed.data.textSelection,
    })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ thread: data });
});

router.put('/threads/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = threadUpdate.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{
    text_selection: string;
    is_resolved: boolean;
  }> = {
    text_selection: parsed.data.textSelection,
    is_resolved: parsed.data.isResolved,
  };
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('comment_threads').update(patch).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ thread: data });
});

router.delete('/threads/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('comment_threads').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

// Comments
const commentInsert = z.object({
  threadId: z.string().uuid(),
  authorEmail: z.string().email(),
  content: z.string().min(1),
});
const commentUpdate = z.object({ content: z.string().min(1) });

router.get('/threads/:id/comments', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('comments').select('*').eq('thread_id', id).order('created_at', { ascending: true });
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ comments: data });
});

router.post('/comments', requireAuth, async (req: AuthRequest, res) => {
  const parsed = commentInsert.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa
    .from('comments')
    .insert({ thread_id: parsed.data.threadId, author_email: parsed.data.authorEmail, content: parsed.data.content })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ comment: data });
});

router.put('/comments/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = commentUpdate.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('comments').update({ content: parsed.data.content }).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ comment: data });
});

router.delete('/comments/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('comments').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

export default router;

