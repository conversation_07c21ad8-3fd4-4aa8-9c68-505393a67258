
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/AnalyzePage.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> AnalyzePage.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">64.42% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>96/149</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">29.41% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>5/17</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">28.57% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>2/7</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">64.42% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>96/149</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import React, { useState, useRef } from 'react';
import { User, DashboardView, ExternalAnalysisResult } from '../types';
import { Button } from './ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { LockSolidIcon, UploadCloudIcon, AlertTriangleIcon } from './Icons';
import { apiFetch } from '../lib/api';
import { cn } from '../lib/utils';
&nbsp;
interface AnalyzePageProps {
  user: User;
  setView: (view: DashboardView) =&gt; void;
}
&nbsp;
export const ResultDisplay: React.FC&lt;{ result: ExternalAnalysisResult }&gt; = ({ result }) =&gt; {
    const riskStyles = {
        High: 'border-red-500 bg-red-50 text-red-900',
        Medium: 'border-amber-500 bg-amber-50 text-amber-900',
        Low: 'border-sky-500 bg-sky-50 text-sky-900',
    };
    return (
        &lt;div className="space-y-6"&gt;
            &lt;Card&gt;
                &lt;CardHeader&gt;
                    &lt;CardTitle&gt;Analysis Summary&lt;/CardTitle&gt;
                &lt;/CardHeader&gt;
                &lt;CardContent className="space-y-2"&gt;
                    &lt;p&gt;&lt;strong className="font-semibold text-zinc-800"&gt;Document Type:&lt;/strong&gt; {result.documentType}&lt;/p&gt;
                    &lt;p className="text-zinc-600"&gt;{result.summary}&lt;/p&gt;
                &lt;/CardContent&gt;
            &lt;/Card&gt;
&nbsp;
            &lt;Card&gt;
                &lt;CardHeader&gt;
                    &lt;CardTitle&gt;Potential Risks&lt;/CardTitle&gt;
                &lt;/CardHeader&gt;
                &lt;CardContent className="space-y-3"&gt;
                    {result.risks?.length &gt; 0 ? result.risks.map((risk, i) =&gt; (
                        &lt;div key={i} className={cn('p-4 rounded-lg border-l-4', riskStyles[risk.severity])}&gt;
                            &lt;p className="font-bold"&gt;{risk.severity} Risk&lt;/p&gt;
                            &lt;p className="mt-1"&gt;{risk.description}&lt;/p&gt;
                        &lt;/div&gt;
                    )<span class="branch-0 cbranch-no" title="branch not covered" >) : &lt;p className="text-zinc-500"&gt;No significant risks were identified.&lt;/p&gt;}</span>
                &lt;/CardContent&gt;
            &lt;/Card&gt;
            
            &lt;Card&gt;
                &lt;CardHeader&gt;
                    &lt;CardTitle&gt;Key Clauses&lt;/CardTitle&gt;
                &lt;/CardHeader&gt;
                &lt;CardContent className="space-y-3"&gt;
                     {result.keyClauses?.length &gt; 0 ? result.keyClauses.map((clause, i) =&gt; (
                        &lt;div key={i} className="p-3 border-b border-zinc-200"&gt;
                            &lt;p className="font-semibold text-zinc-800"&gt;{clause.title}&lt;/p&gt;
                            &lt;p className="text-sm text-zinc-600 mt-1"&gt;{clause.summary}&lt;/p&gt;
                        &lt;/div&gt;
                    )<span class="branch-0 cbranch-no" title="branch not covered" >) : &lt;p className="text-zinc-500"&gt;Could not extract key clauses.&lt;/p&gt;}</span>
                &lt;/CardContent&gt;
            &lt;/Card&gt;
&nbsp;
            &lt;Card&gt;
                &lt;CardHeader&gt;
                    &lt;CardTitle&gt;Suggestions&lt;/CardTitle&gt;
                &lt;/CardHeader&gt;
                &lt;CardContent&gt;
                    &lt;ul className="list-disc pl-5 space-y-2 text-zinc-700"&gt;
                        {result.suggestions?.length &gt; 0 ? result.suggestions.map((s, i) =&gt; &lt;li key={i}&gt;{s}&lt;/li&gt;<span class="branch-0 cbranch-no" title="branch not covered" >) : &lt;p className="text-zinc-500"&gt;No specific suggestions were generated.&lt;/p&gt;}</span>
                    &lt;/ul&gt;
                &lt;/CardContent&gt;
            &lt;/Card&gt;
        &lt;/div&gt;
    );
};
&nbsp;
&nbsp;
const AnalyzePage: React.FC&lt;AnalyzePageProps&gt; = ({ user, setView }) =&gt; {
    const [documentText, setDocumentText] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [result, setResult] = useState&lt;ExternalAnalysisResult | null&gt;(null);
    const [error, setError] = useState&lt;string | null&gt;(null);
    const fileInputRef = useRef&lt;HTMLInputElement&gt;(null);
&nbsp;
    const isPremium = user.planName === 'Premium' || user.planName === 'Enterprise';
&nbsp;
    const handleAnalyze = <span class="fstat-no" title="function not covered" >async () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (!documentText.trim()) {return;}</span>
<span class="cstat-no" title="statement not covered" >        setIsLoading(true);</span>
<span class="cstat-no" title="statement not covered" >        setError(null);</span>
<span class="cstat-no" title="statement not covered" >        setResult(null);</span>
<span class="cstat-no" title="statement not covered" >        try {</span>
<span class="cstat-no" title="statement not covered" >            const resp = await apiFetch&lt;{ result: ExternalAnalysisResult }&gt;(`/api/ai/external-analyze`, { method: 'POST', body: JSON.stringify({ content: documentText }) });</span>
<span class="cstat-no" title="statement not covered" >            setResult(resp.result);</span>
<span class="cstat-no" title="statement not covered" >        } catch (e) {</span>
<span class="cstat-no" title="statement not covered" >            setError(e instanceof Error ? e.message : 'An unknown error occurred.');</span>
<span class="cstat-no" title="statement not covered" >        } finally {</span>
<span class="cstat-no" title="statement not covered" >            setIsLoading(false);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >    };</span>
    
    const handleFileChange = <span class="fstat-no" title="function not covered" >(e: React.ChangeEvent&lt;HTMLInputElement&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const file = e.target.files?.[0];</span>
<span class="cstat-no" title="statement not covered" >        if (file) {</span>
<span class="cstat-no" title="statement not covered" >            const reader = new FileReader();</span>
<span class="cstat-no" title="statement not covered" >            reader.onload = (event) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                setDocumentText(event.target?.result as string);</span>
<span class="cstat-no" title="statement not covered" >            };</span>
<span class="cstat-no" title="statement not covered" >            reader.readAsText(file);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >    };</span>
&nbsp;
    if (!isPremium) {
        return (
            &lt;div className="p-4 sm:p-6 lg:p-8 h-full flex items-center justify-center"&gt;
                &lt;div className="text-center"&gt;
                    &lt;LockSolidIcon className="w-12 h-12 mx-auto text-zinc-300 mb-4" /&gt;
                    &lt;h3 className="text-xl font-semibold text-zinc-800"&gt;Unlock Document Analysis&lt;/h3&gt;
                    &lt;p className="text-zinc-500 mt-2 mb-4 max-w-md"&gt;
                        Upgrade to Premium to get AI-powered insights on any legal document.
                    &lt;/p&gt;
                    &lt;Button onClick={<span class="fstat-no" title="function not covered" >() =&gt; setView('subscription')}&gt;U</span>pgrade Now&lt;/Button&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        );
<span class="branch-0 cbranch-no" title="branch not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="p-4 sm:p-6 lg:p-8 h-full flex flex-col lg:flex-row gap-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex-1 lg:w-1/2 flex flex-col"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;h1 className="text-2xl font-bold text-zinc-900"&gt;Analyze a Document&lt;/h1&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-zinc-600 mt-1 mb-4"&gt;Paste your document text below or upload a file to get an AI-powered analysis.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="flex-1 flex flex-col"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;textarea</span>
<span class="cstat-no" title="statement not covered" >                        value={documentText}</span>
<span class="cstat-no" title="statement not covered" >                        onChange={<span class="fstat-no" title="function not covered" >(e) =&gt; setDocumentText(e.target.value)}</span></span>
<span class="cstat-no" title="statement not covered" >                        placeholder="Paste your legal document text here..."</span>
<span class="cstat-no" title="statement not covered" >                        className="w-full flex-1 p-4 border border-zinc-300 rounded-t-lg focus:ring-brand-500 focus:border-brand-500 resize-none"</span>
<span class="cstat-no" title="statement not covered" >                    /&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="p-3 border border-t-0 border-zinc-300 rounded-b-lg bg-zinc-50 flex justify-between items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;input type="file" ref={fileInputRef} onChange={handleFileChange} accept=".txt,.md,.html" className="hidden"/&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;Button variant="outline" onClick={<span class="fstat-no" title="function not covered" >() =&gt; fileInputRef.current?.click()}&gt;</span></span>
<span class="cstat-no" title="statement not covered" >                            &lt;UploadCloudIcon className="w-5 h-5 mr-2" /&gt;</span>
                            Upload File
<span class="cstat-no" title="statement not covered" >                        &lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;Button onClick={handleAnalyze} disabled={isLoading || !documentText.trim()}&gt;</span>
                            {<span class="branch-0 cbranch-no" title="branch not covered" >isLoading ? <span class="branch-0 cbranch-no" title="branch not covered" >'Analyzing...' : '</span>Analyze Document'}</span>
                        &lt;/Button&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div className="flex-1 lg:w-1/2 overflow-y-auto"&gt;
                {<span class="branch-0 cbranch-no" title="branch not covered" >isLoading &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="h-full flex flex-col items-center justify-center text-center text-zinc-500"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;div className="w-10 h-10 border-4 border-brand-500 border-t-transparent rounded-full animate-spin"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p className="mt-4 font-semibold"&gt;AI is analyzing your document...&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p className="text-sm"&gt;This may take a moment.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                )}
                {<span class="branch-0 cbranch-no" title="branch not covered" >error &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="h-full flex flex-col items-center justify-center text-center text-red-600 bg-red-50 p-6 rounded-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;AlertTriangleIcon className="w-10 h-10 mb-2"/&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p className="font-semibold"&gt;Analysis Failed&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p className="text-sm"&gt;{error}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                )}
                {<span class="branch-0 cbranch-no" title="branch not covered" >result &amp;&amp; &lt;ResultDisplay result={result} /&gt;}</span>
                {!<span class="branch-0 cbranch-no" title="branch not covered" >isLoading &amp;&amp; !<span class="branch-0 cbranch-no" title="branch not covered" >result &amp;&amp; !<span class="branch-0 cbranch-no" title="branch not covered" ></span>error &amp;&amp; (</span></span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="h-full flex flex-col items-center justify-center text-center text-zinc-400 border-2 border-dashed border-zinc-300 rounded-lg p-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p className="font-semibold"&gt;Your analysis results will appear here.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                )}
            &lt;/div&gt;
        &lt;/div&gt;
    );
};
&nbsp;
export default AnalyzePage;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    