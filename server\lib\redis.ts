import type { RedisClientType } from 'redis';
import { logger } from './logger';

type RedisClientWithSendCommand = RedisClientType & {
  sendCommand: (args: string[]) => Promise<unknown>;
  isOpen?: boolean;
  isReady?: boolean;
  disconnect?: () => Promise<void>;
  removeAllListeners?: () => void;
};

type RedisDisconnectReason = 'end' | 'reconnecting' | 'error';

type RedisHealthStatus = {
  enabled: boolean;
  status: 'disabled' | 'ready' | 'connecting' | 'reconnecting' | 'disconnected';
  isReady: boolean;
  isOpen: boolean;
  reconnecting: boolean;
  reconnectAttempts: number;
  nextAttemptInMs: number | null;
  nextAttemptAt: number | null;
  lastError: string | null;
  lastDisconnectReason: RedisDisconnectReason | null;
};

let redisClient: RedisClientWithSendCommand | null = null;

// Redis configuration
const REDIS_URL = process.env.REDIS_URL || 'redis://127.0.0.1:6379';
const REDIS_ENABLED = process.env.REDIS_ENABLED === 'true';
const BASE_RECONNECT_DELAY_MS = 1000;
const MAX_RECONNECT_DELAY_MS = 30000;
const RECONNECT_JITTER_MS = 250;

let connectPromise: Promise<void> | null = null;
let reconnectAttempts = 0;
let reconnectTimeout: ReturnType<typeof setTimeout> | null = null;
let nextReconnectAttemptAt: number | null = null;
let lastDisconnectReason: RedisDisconnectReason | null = null;
let lastConnectionError: string | null = null;
let isShuttingDown = false;

// Cache TTL settings (in seconds)
export const CACHE_TTL = {
  TEMPLATES: 300, // 5 minutes
  PRICING_PLANS: 600, // 10 minutes
  USER_PROFILE: 180, // 3 minutes
  DOCUMENTS_LIST: 60, // 1 minute
  COMMENTS: 120, // 2 minutes
  BATCH_DOCUMENTS: 90, // 1.5 minutes
} as const;

function computeBackoffDelay(attempt: number): number {
  const baseDelay = BASE_RECONNECT_DELAY_MS * Math.pow(2, Math.max(attempt - 1, 0));
  const jitter = Math.floor(Math.random() * RECONNECT_JITTER_MS);
  return Math.min(baseDelay + jitter, MAX_RECONNECT_DELAY_MS);
}

function clearReconnectTimer(): void {
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout);
    reconnectTimeout = null;
  }
  nextReconnectAttemptAt = null;
}

function scheduleReconnect(reason: RedisDisconnectReason, options?: { force?: boolean }): void {
  if (!REDIS_ENABLED || isShuttingDown) {
    return;
  }

  lastDisconnectReason = reason;
  if (!lastConnectionError) {
    lastConnectionError = `Redis connection ${reason}`;
  }

  const shouldForce = Boolean(options?.force);

  if (reconnectTimeout || (connectPromise && !shouldForce)) {
    return;
  }

  reconnectAttempts += 1;
  const delay = computeBackoffDelay(reconnectAttempts);
  nextReconnectAttemptAt = Date.now() + delay;

  logger.warn({ reason, attempt: reconnectAttempts, delay }, 'Redis connection lost; scheduling reconnect');

  reconnectTimeout = setTimeout(() => {
    reconnectTimeout = null;
    nextReconnectAttemptAt = null;
    void initRedis();
  }, delay);
}

function attachLifecycleHandlers(client: RedisClientWithSendCommand): void {
  client.on('error', (err) => {
    lastConnectionError = err instanceof Error ? err.message : String(err);
    logger.error({ err }, 'Redis Client Error');
  });

  client.on('connect', () => {
    logger.info('Redis client connected');
  });

  client.on('ready', () => {
    const attemptsBeforeReady = reconnectAttempts;
    reconnectAttempts = 0;
    clearReconnectTimer();
    lastDisconnectReason = null;
    lastConnectionError = null;

    if (attemptsBeforeReady > 0) {
      logger.info({ attempts: attemptsBeforeReady }, 'Redis connection reestablished');
    } else {
      logger.info('Redis client ready');
    }
  });

  client.on('end', () => {
    logger.warn('Redis connection ended');
    handleClientDisconnect(client, 'end');
  });

  client.on('reconnecting', (details: { delay: number; attempt: number }) => {
    logger.warn({ attempt: details.attempt, delay: details.delay }, 'Redis client reconnecting');
    handleClientDisconnect(client, 'reconnecting');
  });
}

function handleClientDisconnect(client: RedisClientWithSendCommand, reason: RedisDisconnectReason): void {
  if (isShuttingDown || redisClient !== client) {
    return;
  }

  redisClient = null;

  if (client.disconnect) {
    void client.disconnect().catch((err: unknown) => {
      logger.error({ err }, 'Error while disconnecting Redis client');
    });
  }

  client.removeAllListeners?.();

  scheduleReconnect(reason);
}

// Initialize Redis client
export async function initRedis(): Promise<void> {
  if (!REDIS_ENABLED) {
    return;
  }

  if (isShuttingDown) {
    isShuttingDown = false;
  }

  if (redisClient?.isOpen) {
    return;
  }

  if (connectPromise) {
    await connectPromise;
    return;
  }

  const attemptNumber = reconnectAttempts + 1;
  const promise = (async () => {
    try {
      const { createClient } = await import(/* @vite-ignore */ 'redis');
      const client = createClient({
        url: REDIS_URL,
        socket: {
          connectTimeout: 5000,
        },
      }) as RedisClientWithSendCommand;

      attachLifecycleHandlers(client);

      logger.info({ attempt: attemptNumber }, 'Attempting Redis connection');

      await client.connect();

      redisClient = client;
    } catch (error) {
      lastConnectionError = error instanceof Error ? error.message : String(error);
      logger.error({ err: error }, 'Failed to initialize Redis');
      redisClient = null;
      scheduleReconnect('error', { force: true });
    }
  })();

  connectPromise = promise;

  try {
    await promise;
  } finally {
    connectPromise = null;
  }
}

// Get Redis client
export function getRedisClient(): RedisClientWithSendCommand | null {
  return redisClient;
}

export function getRedisHealth(): RedisHealthStatus {
  if (!REDIS_ENABLED) {
    return {
      enabled: false,
      status: 'disabled',
      isReady: false,
      isOpen: false,
      reconnecting: false,
      reconnectAttempts: 0,
      nextAttemptInMs: null,
      nextAttemptAt: null,
      lastError: null,
      lastDisconnectReason: null,
    };
  }

  const client = redisClient;
  const isReady = Boolean(client?.isReady);
  const isOpen = Boolean(client?.isOpen);
  const reconnecting = Boolean(reconnectTimeout);
  let status: RedisHealthStatus['status'];

  if (isReady) {
    status = 'ready';
  } else if (reconnecting) {
    status = 'reconnecting';
  } else if (isOpen) {
    status = 'connecting';
  } else {
    status = 'disconnected';
  }

  const nextAttemptInMs = nextReconnectAttemptAt ? Math.max(nextReconnectAttemptAt - Date.now(), 0) : null;

  return {
    enabled: true,
    status,
    isReady,
    isOpen,
    reconnecting,
    reconnectAttempts,
    nextAttemptInMs,
    nextAttemptAt: nextReconnectAttemptAt,
    lastError: lastConnectionError,
    lastDisconnectReason,
  };
}

// Cache key generators
export const cacheKeys = {
  templates: () => 'templates:all',
  pricingPlans: () => 'pricing:plans:all',
  userProfile: (userId: string) => `profile:${userId}`,
  userDocuments: (userId: string) => `documents:${userId}`,
  batchDocuments: (userId: string, docIds: string) => `batch:documents:${userId}:${docIds}`,
  batchComments: (docIds: string) => `batch:comments:${docIds}`,
} as const;

// Generic cache operations
export async function getFromCache<T>(key: string): Promise<T | null> {
  if (!redisClient) {return null;}

  try {
    const cached = await redisClient.get(key);
    return cached ? JSON.parse(cached as string) : null;
  } catch (error) {
    logger.error({ err: error }, 'Cache get error');
    return null;
  }
}

export async function setCache<T>(key: string, value: T, ttlSeconds: number): Promise<void> {
  if (!redisClient) {return;}

  try {
    await redisClient.setEx(key, ttlSeconds, JSON.stringify(value));
  } catch (error) {
    logger.error({ err: error }, 'Cache set error');
  }
}

export async function deleteFromCache(key: string): Promise<void> {
  if (!redisClient) {return;}

  try {
    await redisClient.del(key);
  } catch (error) {
    logger.error({ err: error }, 'Cache delete error');
  }
}

export async function deleteCachePattern(pattern: string): Promise<void> {
  if (!redisClient) {return;}

  try {
    const keys = await redisClient.keys(pattern);
    if (keys.length > 0) {
      await redisClient.del(keys);
    }
  } catch (error) {
    logger.error({ err: error }, 'Cache pattern delete error');
  }
}

// Graceful shutdown
export async function closeRedis(): Promise<void> {
  isShuttingDown = true;
  clearReconnectTimer();

  if (connectPromise) {
    try {
      await connectPromise;
    } catch (error) {
      logger.error({ err: error }, 'Error waiting for Redis connection to settle');
    }
  }

  if (redisClient) {
    try {
      redisClient.removeAllListeners?.();
      await redisClient.quit();
      // Redis client disconnected
    } catch (error) {
      logger.error({ err: error }, 'Error closing Redis connection');
    } finally {
      redisClient = null;
    }
  }
}
