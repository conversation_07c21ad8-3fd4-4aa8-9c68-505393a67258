import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock environment variables BEFORE any imports
vi.stubEnv('GEMINI_API_KEY', 'test-api-key');
vi.stubEnv('VITE_GEMINI_API_KEY', 'test-api-key');

// Mock the GoogleGenAI module
const mockGenerateContent = vi.fn();
const mockCreate = vi.fn();

vi.mock('@google/genai', () => ({
  GoogleGenAI: vi.fn().mockImplementation(() => ({
    models: {
      generateContent: mockGenerateContent
    },
    chats: {
      create: mockCreate
    }
  })),
  Type: {
    OBJECT: 'object',
    ARRAY: 'array',
    STRING: 'string'
  }
}));

// Mock the geminiService module to override the API key resolution
vi.doMock('../services/geminiService', async () => {
  const actual = await vi.importActual('../services/geminiService');
  
  // Mock the getAI function to use our mocked GoogleGenAI
  const { GoogleGenAI } = await import('@google/genai');
  const mockAI = new GoogleGenAI({ apiKey: 'test-api-key' });
  
  return {
    ...actual,
    getAI: () => mockAI,
    resetAI: vi.fn(),
    createChatSession: () => {
      return mockAI.chats.create({
        model: 'gemini-2.5-flash',
        config: {
          systemInstruction: 'Test system prompt'
        }
      });
    }
  };
});

// Now import the functions to test
const {
  getSuggestedClauses,
  analyzeDocument,
  compareDocumentVersions,
  createChatSession,
  resetAI: _resetAI
} = await import('../services/geminiService');

describe('geminiService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Set up default mock implementations
    mockGenerateContent.mockImplementation(() => {
      return Promise.resolve({
        text: JSON.stringify({
          suggestions: [],
          summary: 'Test summary',
          findings: []
        })
      });
    });
    
    mockCreate.mockReturnValue({
      sendMessage: vi.fn()
    });
  });

  describe('getSuggestedClauses', () => {
    it('should return suggested clauses for a document', async () => {
      // Mock response for getSuggestedClauses
      mockGenerateContent.mockImplementation(() => {
        return Promise.resolve({
          text: JSON.stringify({
            suggestions: [
              {
                title: 'Test Clause 1',
                description: 'Test description 1',
                content: '<p>Test clause content 1</p>'
              },
              {
                title: 'Test Clause 2', 
                description: 'Test description 2',
                content: '<p>Test clause content 2</p>'
              }
            ]
          })
        });
      });
      
      const result = await getSuggestedClauses('Test document content');
      
      expect(mockGenerateContent).toHaveBeenCalledWith({
        model: 'gemini-2.5-flash',
        contents: expect.stringContaining('Test document content'),
        config: {
          responseMimeType: 'application/json',
          responseSchema: expect.any(Object)
        }
      });
      
      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        title: 'Test Clause 1',
        description: 'Test description 1',
        content: '<p>Test clause content 1</p>'
      });
    });

    it('should return empty array on error', async () => {
      mockGenerateContent.mockRejectedValue(new Error('API Error'));
      
      const result = await getSuggestedClauses('Test document content');
      
      expect(result).toEqual([]);
    });
  });

  describe('analyzeDocument', () => {
    it('should analyze a document and return analysis results', async () => {
      // Mock response for analyzeDocument
      mockGenerateContent.mockImplementation(() => {
        return Promise.resolve({
          text: JSON.stringify({
            summary: 'Test analysis summary',
            findings: [
              {
                severity: 'High Priority',
                description: 'Missing liability clause'
              },
              {
                severity: 'Suggestion',
                description: 'Consider adding termination clause'
              }
            ]
          })
        });
      });
      
      const result = await analyzeDocument('Test document content');
      
      expect(mockGenerateContent).toHaveBeenCalledWith({
        model: 'gemini-2.5-flash',
        contents: expect.stringContaining('Test document content'),
        config: {
          responseMimeType: 'application/json',
          responseSchema: expect.any(Object)
        }
      });
      
      expect(result).toEqual({
        summary: 'Test analysis summary',
        findings: [
          {
            severity: 'High Priority',
            description: 'Missing liability clause'
          },
          {
            severity: 'Suggestion',
            description: 'Consider adding termination clause'
          }
        ]
      });
    });

    it('should throw error on API failure', async () => {
      mockGenerateContent.mockRejectedValue(new Error('API Error'));
      
      await expect(analyzeDocument('Test document content')).rejects.toThrow('Failed to analyze document.');
    });
  });

  describe('compareDocumentVersions', () => {
    it('should compare two document versions', async () => {
      // Mock response for compareDocumentVersions
      mockGenerateContent.mockImplementation(() => {
        return Promise.resolve({
          text: '<div class="comparison">Test comparison HTML</div>'
        });
      });
      
      const result = await compareDocumentVersions('Version 1 content', 'Version 2 content');
      
      expect(mockGenerateContent).toHaveBeenCalledWith({
        model: 'gemini-2.5-flash',
        contents: expect.stringContaining('Version 1 content')
      });
      
      expect(typeof result).toBe('string');
      expect(result).toBe('<div class="comparison">Test comparison HTML</div>');
    });

    it('should return error message on failure', async () => {
      mockGenerateContent.mockRejectedValue(new Error('API Error'));
      
      const result = await compareDocumentVersions('Version 1', 'Version 2');
      
      expect(result).toBe('<p>Error: Could not compare document versions.</p>');
    });
  });

  describe('createChatSession', () => {
    it('should create a chat session successfully', () => {
      const mockSession = { sendMessage: vi.fn() };
      mockCreate.mockReturnValue(mockSession);
      
      const session = createChatSession();
      
      expect(session).toBeDefined();
      expect(mockCreate).toHaveBeenCalledWith({
        model: 'gemini-2.5-flash',
        config: {
          systemInstruction: expect.any(String)
        }
      });
      expect(session).toBe(mockSession);
    });
  });
});
