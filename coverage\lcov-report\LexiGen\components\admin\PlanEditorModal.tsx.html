
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/admin/PlanEditorModal.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> / <a href="index.html">LexiGen/components/admin</a> PlanEditorModal.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/82</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/82</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">&nbsp;
<span class="cstat-no" title="statement not covered" >import React, { useState, useEffect } from 'react';</span>
import { PricingPlan } from '../../types';
<span class="cstat-no" title="statement not covered" >import { Button } from '../ui/Button';</span>
<span class="cstat-no" title="statement not covered" >import { CloseIcon, BillingIcon, PlusCircleIcon, TrashIcon } from '../Icons';</span>
&nbsp;
interface PlanEditorModalProps {
  isOpen: boolean;
  onClose: () =&gt; void;
  onSave: (data: Omit&lt;PricingPlan, 'id'&gt; | PricingPlan) =&gt; void;
  existingPlan?: PricingPlan | null;
}
&nbsp;
<span class="cstat-no" title="statement not covered" >const PlanEditorModal: React.FC&lt;PlanEditorModalProps&gt; = ({ isOpen, onClose, onSave, existingPlan }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const [plan, setPlan] = useState&lt;Omit&lt;PricingPlan, 'id'&gt;&gt;({</span>
<span class="cstat-no" title="statement not covered" >    name: '',</span>
<span class="cstat-no" title="statement not covered" >    price: '',</span>
<span class="cstat-no" title="statement not covered" >    priceDetail: '/ month',</span>
<span class="cstat-no" title="statement not covered" >    features: [''],</span>
<span class="cstat-no" title="statement not covered" >    cta: 'Get Started',</span>
<span class="cstat-no" title="statement not covered" >    isFeatured: false,</span>
<span class="cstat-no" title="statement not covered" >  });</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (existingPlan) {</span>
<span class="cstat-no" title="statement not covered" >      setPlan(existingPlan);</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >      setPlan({ name: '', price: '', priceDetail: '/ month', features: [''], cta: 'Get Started', isFeatured: false });</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }, [existingPlan, isOpen]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (!isOpen) {return null;}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleInputChange = (field: keyof Omit&lt;PricingPlan, 'id' | 'features' | 'isFeatured'&gt;, value: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    setPlan(prev =&gt; ({ ...prev, [field]: value }));</span>
<span class="cstat-no" title="statement not covered" >  };</span>
  
<span class="cstat-no" title="statement not covered" >  const handleFeatureChange = (index: number, value: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const newFeatures = [...plan.features];</span>
<span class="cstat-no" title="statement not covered" >    newFeatures[index] = value;</span>
<span class="cstat-no" title="statement not covered" >    setPlan(prev =&gt; ({ ...prev, features: newFeatures }));</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const addFeature = () =&gt; setPlan(prev =&gt; ({ ...prev, features: [...prev.features, ''] }));</span>
<span class="cstat-no" title="statement not covered" >  const removeFeature = (index: number) =&gt; setPlan(prev =&gt; ({ ...prev, features: plan.features.filter((_, i) =&gt; i !== index) }));</span>
  
<span class="cstat-no" title="statement not covered" >  const handleSave = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const finalPlan = { ...plan, features: plan.features.filter(f =&gt; f.trim() !== '') };</span>
<span class="cstat-no" title="statement not covered" >    if (existingPlan) {</span>
<span class="cstat-no" title="statement not covered" >        onSave({ ...finalPlan, id: existingPlan.id });</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >        onSave(finalPlan);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;header className="flex items-center justify-between p-4 border-b dark:border-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200 flex items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;BillingIcon className="w-5 h-5 mr-2 text-brand-700 dark:text-brand-400" /&gt;</span>
<span class="cstat-no" title="statement not covered" >            {existingPlan ? 'Edit Plan' : 'Create New Plan'}</span>
<span class="cstat-no" title="statement not covered" >          &lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;button onClick={onClose} className="p-2 text-zinc-500 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800 rounded-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;CloseIcon className="w-6 h-6" /&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/header&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;main className="p-6 space-y-4 max-h-[60vh] overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="grid grid-cols-1 sm:grid-cols-2 gap-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div&gt;&lt;label className="block text-sm font-medium dark:text-zinc-300"&gt;Plan Name&lt;/label&gt;&lt;input type="text" value={plan.name} onChange={e =&gt; handleInputChange('name', e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" /&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div&gt;&lt;label className="block text-sm font-medium dark:text-zinc-300"&gt;Call to Action&lt;/label&gt;&lt;input type="text" value={plan.cta} onChange={e =&gt; handleInputChange('cta', e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" /&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div&gt;&lt;label className="block text-sm font-medium dark:text-zinc-300"&gt;Price&lt;/label&gt;&lt;input type="text" value={plan.price} onChange={e =&gt; handleInputChange('price', e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" placeholder="e.g., $49 or Custom"/&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div&gt;&lt;label className="block text-sm font-medium dark:text-zinc-300"&gt;Price Detail&lt;/label&gt;&lt;input type="text" value={plan.priceDetail} onChange={e =&gt; handleInputChange('priceDetail', e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" placeholder="e.g., / month"/&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;label className="block text-sm font-medium dark:text-zinc-300"&gt;Features&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="space-y-2 mt-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >              {plan.features.map((feature, index) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                &lt;div key={index} className="flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;input type="text" value={feature} onChange={e =&gt; handleFeatureChange(index, e.target.value)} className="flex-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;Button variant="ghost" size="icon" className="text-red-500 hover:bg-red-50 dark:hover:bg-red-500/10" onClick={() =&gt; removeFeature(index)} disabled={plan.features.length === 1}&gt;&lt;TrashIcon className="w-4 h-4"/&gt;&lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              ))}</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;Button variant="outline" size="sm" onClick={addFeature} className="mt-2"&gt;&lt;PlusCircleIcon className="w-4 h-4 mr-2"/&gt;Add Feature&lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;input type="checkbox" id="isFeatured" checked={plan.isFeatured} onChange={e =&gt; setPlan(p =&gt; ({...p, isFeatured: e.target.checked}))} className="h-4 w-4 rounded text-brand-600 focus:ring-brand-500" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;label htmlFor="isFeatured" className="text-sm font-medium dark:text-zinc-300"&gt;Mark as "Most Popular"&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/main&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t dark:border-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;Button variant="outline" onClick={onClose}&gt;Cancel&lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;Button onClick={handleSave}&gt;Save Plan&lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/footer&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default PlanEditorModal;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    