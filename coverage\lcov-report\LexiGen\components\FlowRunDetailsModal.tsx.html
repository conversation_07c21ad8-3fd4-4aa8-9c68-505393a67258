
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/FlowRunDetailsModal.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> FlowRunDetailsModal.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/80</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/80</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import React from 'react';
<span class="cstat-no" title="statement not covered" >import { Button } from './ui/Button';</span>
<span class="cstat-no" title="statement not covered" >import { CloseIcon, CheckCircleIcon, XCircleIcon } from './Icons';</span>
import { FlowRun, User } from '../types';
<span class="cstat-no" title="statement not covered" >import { ALL_CONNECTORS } from './connectors';</span>
<span class="cstat-no" title="statement not covered" >import { cn } from '../lib/utils';</span>
&nbsp;
interface FlowRunDetailsModalProps {
  isOpen: boolean;
  onClose: () =&gt; void;
  run: FlowRun | null;
  user: User;
}
&nbsp;
<span class="cstat-no" title="statement not covered" >const DataViewer: React.FC&lt;{ title: string; data: unknown }&gt; = ({ title, data }) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;h4 className="font-semibold text-zinc-800 dark:text-zinc-200"&gt;{title}&lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;pre className="mt-1 p-3 bg-zinc-100 dark:bg-zinc-800 rounded-md text-xs text-zinc-700 dark:text-zinc-300 overflow-x-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;code&gt;{JSON.stringify(data, null, 2)}&lt;/code&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/pre&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
);
&nbsp;
&nbsp;
<span class="cstat-no" title="statement not covered" >const FlowRunDetailsModal: React.FC&lt;FlowRunDetailsModalProps&gt; = ({ isOpen, onClose, run, user }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (!isOpen || !run) {return null;}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const flow = user.flows?.find(f =&gt; f.id === run.flowId);</span>
<span class="cstat-no" title="statement not covered" >    if (!flow) {return null;}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const getConnector = (connectionId: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const connection = user.connections?.find(c =&gt; c.id === connectionId);</span>
<span class="cstat-no" title="statement not covered" >        return ALL_CONNECTORS.find(c =&gt; c.id === connection?.connectorId);</span>
<span class="cstat-no" title="statement not covered" >    };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const triggerConnector = getConnector(flow.trigger.connectionId);</span>
<span class="cstat-no" title="statement not covered" >    const actionConnector = getConnector(flow.action.connectionId);</span>
<span class="cstat-no" title="statement not covered" >    const TriggerIcon = triggerConnector?.icon;</span>
<span class="cstat-no" title="statement not covered" >    const ActionIcon = actionConnector?.icon;</span>
    
<span class="cstat-no" title="statement not covered" >    const StatusDisplay = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const isSuccess = run.status === 'success';</span>
<span class="cstat-no" title="statement not covered" >        const isFailed = run.status === 'failed';</span>
<span class="cstat-no" title="statement not covered" >        const Icon = isSuccess ? CheckCircleIcon : isFailed ? XCircleIcon : null;</span>
        
<span class="cstat-no" title="statement not covered" >        const wrapperClasses = isSuccess </span>
<span class="cstat-no" title="statement not covered" >            ? 'bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800' </span>
<span class="cstat-no" title="statement not covered" >            : 'bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800';</span>
        
<span class="cstat-no" title="statement not covered" >        const iconClasses = isSuccess ? 'text-green-500' : 'text-red-500';</span>
<span class="cstat-no" title="statement not covered" >        const titleClasses = isSuccess ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200';</span>
<span class="cstat-no" title="statement not covered" >        const textClasses = isSuccess ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300';</span>
    
<span class="cstat-no" title="statement not covered" >        if (run.status === 'running') {</span>
<span class="cstat-no" title="statement not covered" >            return null;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
    
<span class="cstat-no" title="statement not covered" >        return (</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className={cn('p-4 rounded-lg', wrapperClasses)}&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="flex items-start gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {Icon &amp;&amp; &lt;Icon className={cn('w-6 h-6 flex-shrink-0', iconClasses)} /&gt;}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;h3 className={cn('font-semibold capitalize', titleClasses)}&gt;</span>
<span class="cstat-no" title="statement not covered" >                            Run {run.status}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                        {isFailed &amp;&amp; run.error &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                            &lt;p className={cn('mt-1 text-sm', textClasses)}&gt;{run.error}&lt;/p&gt;</span>
                        )}
<span class="cstat-no" title="statement not covered" >                         {isSuccess &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                            &lt;p className={cn('mt-1 text-sm', textClasses)}&gt;The flow ran successfully.&lt;/p&gt;</span>
                        )}
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
        );
<span class="cstat-no" title="statement not covered" >    };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-2xl h-[80vh] flex flex-col"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;header className="flex items-center justify-between p-4 border-b dark:border-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                         {TriggerIcon &amp;&amp; &lt;TriggerIcon className="w-6 h-6"/&gt;}</span>
<span class="cstat-no" title="statement not covered" >                         &lt;span className="font-bold"&gt;&amp;rarr;&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                         {ActionIcon &amp;&amp; &lt;ActionIcon className="w-6 h-6"/&gt;}</span>
<span class="cstat-no" title="statement not covered" >                         &lt;h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200"&gt;{flow.name}&lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;Button variant="ghost" size="icon" onClick={onClose}&gt;&lt;CloseIcon className="w-5 h-5"/&gt;&lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/header&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;main className="p-6 flex-1 overflow-y-auto space-y-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="text-sm text-zinc-500 dark:text-zinc-400"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p&gt;&lt;strong&gt;Run ID:&lt;/strong&gt; {run.id}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p&gt;&lt;strong&gt;Started:&lt;/strong&gt; {new Date(run.startedAt).toLocaleString()}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                    &lt;StatusDisplay /&gt;</span>
                    
<span class="cstat-no" title="statement not covered" >                    {run.triggerData &amp;&amp; &lt;DataViewer title="Data In (from Trigger)" data={run.triggerData} /&gt;}</span>
<span class="cstat-no" title="statement not covered" >                    {run.actionData &amp;&amp; &lt;DataViewer title="Data Out (to Action)" data={run.actionData} /&gt;}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                &lt;/main&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t dark:border-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;Button variant="outline" onClick={onClose}&gt;Close&lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/footer&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
    );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default FlowRunDetailsModal;</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    