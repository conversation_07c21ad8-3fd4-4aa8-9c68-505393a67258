// @vitest-environment jsdom
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { describe, it, expect } from 'vitest';
import AnalyzePage, { ResultDisplay } from '../components/AnalyzePage';
import React from 'react';

const mockUser = {
  id: '1',
  name: 'Test User',
  email: '<EMAIL>',
  password: '',
  isVerified: true,
  status: 'active' as const,
  documents: [],
  folders: [],
  quotaUsed: 0,
  quotaTotal: 100,
  planName: 'Free',
};

describe('AnalyzePage', () => {
  it('renders without crashing', () => {
    render(<AnalyzePage user={mockUser} setView={() => {}} />);
    expect(screen.getByText('Unlock Document Analysis')).toBeInTheDocument();
  });

  it('renders result display with mock data', () => {
    const mockResult = {
      summary: 'Test summary',
      documentType: 'Contract',
      keyClauses: [
        { title: 'Clause 1', summary: 'Summary 1' },
        { title: 'Clause 2', summary: 'Summary 2' },
      ],
      risks: [
        { severity: 'High' as const, description: 'High risk' },
        { severity: 'Low' as const, description: 'Low risk' },
      ],
      suggestions: ['Suggestion 1', 'Suggestion 2'],
    };
    // Render ResultDisplay directly for coverage
    const { container } = render(
      <ResultDisplay result={mockResult} />
    );
    expect(container).toHaveTextContent('Test summary');
    expect(container).toHaveTextContent('High risk');
    expect(container).toHaveTextContent('Clause 1');
    expect(container).toHaveTextContent('Suggestion 1');
  });
});
