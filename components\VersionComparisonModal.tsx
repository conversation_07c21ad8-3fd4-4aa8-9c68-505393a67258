
import React, { useState, useEffect } from 'react';
import { sanitizeHtml } from '../lib/sanitize';
import { CloseIcon, RepeatIcon } from './Icons';
import { Button } from './ui/Button';
import { DocumentVersion } from '../types';
import { apiFetch } from '../lib/api';

interface VersionComparisonModalProps {
  isOpen: boolean;
  onClose: () => void;
  oldVersion: DocumentVersion;
  currentContent: string;
}

const VersionComparisonModal: React.FC<VersionComparisonModalProps> = ({ isOpen, onClose, oldVersion, currentContent }) => {
  const [comparisonHtml, setComparisonHtml] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (isOpen) {
      setIsLoading(true);
      setComparisonHtml('');
      apiFetch<{ html: string }>(`/api/ai/compare-versions`, { method: 'POST', body: JSON.stringify({ contentA: oldVersion.content, contentB: currentContent }) })
        .then(resp => setComparisonHtml(resp.html || ''))
        .catch(_err => setComparisonHtml('<p>Error loading comparison.</p>'))
        .finally(() => setIsLoading(false));
    }
  }, [isOpen, oldVersion, currentContent]);

  if (!isOpen) {return null;}

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-60 dark:bg-opacity-80 z-50 flex justify-center items-center p-4 transition-opacity"
      onClick={onClose}
    >
      <div 
        className="bg-white dark:bg-zinc-900 rounded-2xl shadow-2xl w-full max-w-4xl h-[90vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <header className="flex items-center justify-between p-4 border-b border-zinc-200 dark:border-zinc-800 flex-shrink-0">
          <h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200 flex items-center gap-2">
            <RepeatIcon className="w-5 h-5 text-brand-600" />
            <span>Comparing Version from {new Date(oldVersion.savedAt).toLocaleString()}</span>
          </h2>
          <Button variant="ghost" size="icon" onClick={onClose} aria-label="Close">
            <CloseIcon className="w-6 h-6" />
          </Button>
        </header>
        <main className="flex-1 overflow-y-auto page-container">
          {isLoading ? (
            <div className="flex items-center justify-center h-full text-zinc-500">
                <div className="w-8 h-8 border-4 border-brand-500 border-t-transparent rounded-full animate-spin"></div>
                <p className="ml-4">Generating comparison...</p>
            </div>
          ) : (
             <div className="a4-page version-comparison-content" dangerouslySetInnerHTML={{ __html: sanitizeHtml(comparisonHtml) }} />
          )}
        </main>
      </div>
    </div>
  );
};

export default VersionComparisonModal;
