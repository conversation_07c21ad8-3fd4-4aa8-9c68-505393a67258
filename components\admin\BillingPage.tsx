import React, { useState, useMemo, useEffect } from 'react';
import { User, Team, PricingPlan } from '../../types';

// Modal state interfaces
interface CancelSubModalData {
  id: string;
  email: string;
}

interface IssueRefundModalData {
  email: string;
}

interface UserActionModalData {
  id: string;
  email: string;
  status: string;
}

type ModalState = 
  | { type: 'cancel-sub'; data: CancelSubModalData }
  | { type: 'issue-refund'; data: IssueRefundModalData }
  | { type: 'toggle-user-status'; data: UserActionModalData }
  | { type: 'verify-user'; data: UserActionModalData }
  | { type: 'reset-password'; data: UserActionModalData }
  | null;
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../ui/Card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/Table';
import { MoreVerticalIcon, SearchIcon, CheckCircleIcon, XCircleIcon, BanIcon, KeyIcon } from '../Icons';
import Dropdown from '../ui/Dropdown';
import { Button } from '../ui/Button';
import ConfirmationModal from './ConfirmationModal';
import { cn } from '../../lib/utils';
import { apiFetch } from '../../lib/api';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '../ui/Tabs';

interface BillingPageProps {
  allUsers: User[];
  allTeams: Team[];
  onCancelSubscription: (userId: string) => void;
  pricingPlans: PricingPlan[];
  onToggleUserStatus: (userId: string) => void;
  onManualVerifyUser: (userId: string) => void;
  onTriggerPasswordReset: (email: string) => void;
  initialSearchTerm: string | null;
  onClearSearchFilter: () => void;
}

const formatDate = (dateString?: string) => {
    if (!dateString) {return 'N/A';}
    return new Date(dateString).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
};

const BillingPage: React.FC<BillingPageProps> = (props) => {
  const { allUsers, allTeams, onCancelSubscription, pricingPlans, onToggleUserStatus, onManualVerifyUser, onTriggerPasswordReset, initialSearchTerm, onClearSearchFilter } = props;
  
  const [activeTab, setActiveTab] = useState('subscribers');
  const [searchTerm, setSearchTerm] = useState('');
  const [modalState, setModalState] = useState<ModalState>(null);

  useEffect(() => {
    if (initialSearchTerm) {
      setActiveTab('users');
      setSearchTerm(initialSearchTerm);
      onClearSearchFilter();
    }
  }, [initialSearchTerm, onClearSearchFilter]);

  // Subscribers represent tenants: derive from teams with their owner profile
  const subscriberRows = useMemo(() => {
    const rows: { team: Team; owner: User }[] = [];
    // Teams with owners
    allTeams.forEach((team) => {
      const owner = allUsers.find(u => u.id === team.ownerId);
      if (owner && !owner.isSuperAdmin) {
        rows.push({ team, owner });
      }
    });
    // Individual users without a team still count as tenants of their personal workspace
    allUsers.forEach((u) => {
      const isOwnerOfAnyTeam = allTeams.some(t => t.ownerId === u.id);
      if (!u.isSuperAdmin && !u.teamId && !isOwnerOfAnyTeam) {
        const pseudoTeam: Team = { id: `personal-${u.id}`, name: 'Personal', ownerId: u.id, members: [], status: 'active' } as Team;
        rows.push({ team: pseudoTeam, owner: u });
      }
    });
    return rows;
  }, [allTeams, allUsers]);

  const subscribersFiltered = useMemo(() => {
    if (!searchTerm) {return subscriberRows;}
    const q = searchTerm.toLowerCase();
    return subscriberRows.filter(({ team, owner }) =>
      owner.name?.toLowerCase().includes(q) ||
      owner.email.toLowerCase().includes(q) ||
      team.name.toLowerCase().includes(q)
    );
  }, [subscriberRows, searchTerm]);

  const allNonAdminUsers = useMemo(() => allUsers.filter(u => !u.isSuperAdmin), [allUsers]);

  const usersFiltered = useMemo(() => {
    if (!searchTerm) {return allNonAdminUsers;}
    const q = searchTerm.toLowerCase();
    return allNonAdminUsers.filter(u =>
      u.name?.toLowerCase().includes(q) || u.email.toLowerCase().includes(q)
    );
  }, [allNonAdminUsers, searchTerm]);

  const getTeam = (teamId?: string | null) => allTeams.find(t => t.id === teamId);
  const getPlanPrice = (planName: string) => {
    const plan = pricingPlans.find(p => p.name === planName);
    if (plan?.price.startsWith('$')) {
        return parseInt(plan.price.replace('$', ''));
    }
    return 0;
  };
  
  const handleAction = (
    type: 'cancel-sub' | 'issue-refund' | 'toggle-user-status' | 'verify-user' | 'reset-password',
    data: CancelSubModalData | IssueRefundModalData | UserActionModalData
  ): void => {
    setModalState({ type, data } as ModalState);
  };

  const renderModals = () => {
    if (!modalState) {return null;}
    const { type, data } = modalState;
    switch(type) {
        case 'cancel-sub':
             return <ConfirmationModal isOpen={true} onClose={() => setModalState(null)} onConfirm={() => { onCancelSubscription(data.id); setModalState(null); }} title="Cancel Subscription" message={<>Are you sure you want to cancel the subscription for <strong>{data.email}</strong>? Their plan will revert to 'Registered User'.</>} confirmText="Cancel Subscription" />
        case 'issue-refund':
             return <ConfirmationModal isOpen={true} onClose={() => setModalState(null)} onConfirm={async () => {
                 try {
                   const result = await apiFetch<{ success: boolean; refundId?: string; error?: string }>(
                     '/api/refunds',
                     { method: 'POST', body: JSON.stringify({ email: data.email }) }
                   );
                   if (!result.success) {
                     throw new Error(result.error || 'Refund failed');
                   }
                   // Refund successful
                   alert('Refund issued successfully.');
                  } catch (err) {
                    // Refund failed
                    alert(`Refund failed: ${err instanceof Error ? err.message : String(err)}`);
                  } finally {
                    setModalState(null);
                 }
             }} title="Issue Refund" message={<>This will trigger a refund via the payment provider for <strong>{data.email}</strong>.</>} confirmText="Issue Refund" variant="default" />
        case 'toggle-user-status':
             return <ConfirmationModal isOpen={true} onClose={() => setModalState(null)} onConfirm={() => { onToggleUserStatus(data.id); setModalState(null); }} title={`${data.status === 'active' ? 'Suspend' : 'Reactivate'} User`} message={<>Are you sure you want to {data.status === 'active' ? 'suspend' : 'reactivate'} <strong>{data.email}</strong>?</>} confirmText={data.status === 'active' ? 'Suspend' : 'Reactivate'}/>
        case 'verify-user':
            return <ConfirmationModal isOpen={true} onClose={() => setModalState(null)} onConfirm={() => { onManualVerifyUser(data.id); setModalState(null); }} title="Manually Verify User" message={<>Manually verify the email for <strong>{data.email}</strong>?</>} confirmText="Verify" variant="default"/>
        case 'reset-password':
            return <ConfirmationModal isOpen={true} onClose={() => setModalState(null)} onConfirm={() => { onTriggerPasswordReset(data.email); setModalState(null); }} title="Send Password Reset" message={<>Send a password reset link to <strong>{data.email}</strong>?</>} confirmText="Send Link" variant="default"/>
        default: return null;
    }
  };


  return (
    <>
      <div className="p-4 sm:p-6 lg:p-8 space-y-6">
        <Tabs>
            <TabsList>
                <TabsTrigger onClick={() => { setActiveTab('subscribers'); setSearchTerm(''); }} data-state={activeTab === 'subscribers' ? 'active' : 'inactive'}>Subscribers</TabsTrigger>
                <TabsTrigger onClick={() => { setActiveTab('users'); setSearchTerm(''); }} data-state={activeTab === 'users' ? 'active' : 'inactive'}>All Users</TabsTrigger>
            </TabsList>

            <TabsContent className={activeTab === 'subscribers' ? 'block' : 'hidden'}>
                <Card>
                    <CardHeader>
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                            <div><CardTitle>Active Subscriptions</CardTitle><CardDescription>Manage all active customer subscriptions.</CardDescription></div>
                            <div className="relative w-full sm:max-w-xs"><div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"><SearchIcon className="h-5 w-5 text-zinc-400" /></div><input type="text" placeholder="Search subscribers..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 p-2" /></div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <Table className="min-w-[800px]">
                            <TableHeader><TableRow><TableHead>Subscriber</TableHead><TableHead>Team</TableHead><TableHead>Plan</TableHead><TableHead>Status</TableHead><TableHead className="hidden md:table-cell">MRR</TableHead><TableHead className="hidden md:table-cell">Renews On</TableHead><TableHead className="text-right">Actions</TableHead></TableRow></TableHeader>
                            <TableBody>
                                {subscribersFiltered.map(({ team, owner }) => {
                                    return (
                                    <TableRow key={`${team.id}-${owner.id}`}>
                                        <TableCell><div className="font-medium">{owner.name || 'No Name'}</div><div className="text-sm text-zinc-500">{owner.email}</div></TableCell>
                                        <TableCell>{team.name}</TableCell>
                                        <TableCell>{owner.planName || 'Registered User'}</TableCell>
                                        <TableCell><span className={cn('px-2 py-1 text-xs font-semibold rounded-full capitalize', (owner.subscriptionStatus || 'active') === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300' : 'bg-zinc-100 text-zinc-700 dark:bg-zinc-800 dark:text-zinc-300')}>{owner.subscriptionStatus || 'active'}</span></TableCell>
                                        <TableCell className="hidden md:table-cell">${getPlanPrice(owner.planName)}</TableCell>
                                        <TableCell className="hidden md:table-cell">{formatDate(owner.planExpiryDate)}</TableCell>
                                        <TableCell className="text-right">
                                            <Dropdown><Dropdown.Trigger><Button variant="ghost" size="icon" className="h-8 w-8"><MoreVerticalIcon className="w-4 h-4" /></Button></Dropdown.Trigger>
                                            <Dropdown.Content align="right">
                                                <Dropdown.Item onClick={() => handleAction('cancel-sub', owner)} icon={<BanIcon className="w-4 h-4"/>}>Cancel Subscription</Dropdown.Item>
                                                <Dropdown.Item onClick={() => handleAction('issue-refund', owner)} icon={<CheckCircleIcon className="w-4 h-4"/>}>Issue Refund</Dropdown.Item>
                                            </Dropdown.Content>
                                            </Dropdown>
                                        </TableCell>
                                    </TableRow>
                                    );
                                })}
                            </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>
            </TabsContent>
            
            <TabsContent className={activeTab === 'users' ? 'block' : 'hidden'}>
                <Card>
                    <CardHeader>
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                        <div><CardTitle>All Users</CardTitle><CardDescription>Manage all registered users on the platform.</CardDescription></div>
                        <div className="relative w-full sm:max-w-xs"><div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"><SearchIcon className="h-5 w-5 text-zinc-400" /></div><input type="text" placeholder="Search users..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 p-2" /></div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <Table className="min-w-[600px]">
                                <TableHeader><TableRow><TableHead>User</TableHead><TableHead>Team</TableHead><TableHead>Status</TableHead><TableHead>Verified</TableHead><TableHead className="text-right">Actions</TableHead></TableRow></TableHeader>
                                <TableBody>
                                    {usersFiltered.map(user => {
                                    const team = getTeam(user.teamId);
                                    return (
                                        <TableRow key={user.id}>
                                        <TableCell><div className="font-medium">{user.name || 'No Name'}</div><div className="text-sm text-zinc-500">{user.email}</div></TableCell>
                                        <TableCell>{team?.name || 'No Team'}</TableCell>
                                        <TableCell><span className={cn('px-2 py-1 text-xs font-semibold rounded-full capitalize', user.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300' : 'bg-zinc-100 text-zinc-700 dark:bg-zinc-800 dark:text-zinc-300')}>{user.status}</span></TableCell>
                                        <TableCell>
                                            {user.isVerified 
                                            ? <span className="flex items-center text-green-600"><CheckCircleIcon className="w-4 h-4 mr-1"/> Yes</span> 
                                            : <span className="flex items-center text-amber-600"><XCircleIcon className="w-4 h-4 mr-1"/> No</span>}
                                        </TableCell>
                                        <TableCell className="text-right">
                                            <Dropdown><Dropdown.Trigger><Button variant="ghost" size="icon" className="h-8 w-8"><MoreVerticalIcon className="w-4 h-4" /></Button></Dropdown.Trigger>
                                            <Dropdown.Content align="right">
                                                <Dropdown.Item onClick={() => handleAction('toggle-user-status', user)} icon={<BanIcon className="w-4 h-4"/>}>{user.status === 'active' ? 'Suspend User' : 'Reactivate User'}</Dropdown.Item>
                                                {!user.isVerified && <Dropdown.Item onClick={() => handleAction('verify-user', user)} icon={<CheckCircleIcon className="w-4 h-4"/>}>Manually Verify</Dropdown.Item>}
                                                <Dropdown.Item onClick={() => handleAction('reset-password', user)} icon={<KeyIcon className="w-4 h-4"/>}>Send Password Reset</Dropdown.Item>
                                            </Dropdown.Content>
                                            </Dropdown>
                                        </TableCell>
                                        </TableRow>
                                    );
                                    })}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                </Card>
            </TabsContent>
        </Tabs>
      </div>
      {renderModals()}
    </>
  );
};

export default BillingPage;
