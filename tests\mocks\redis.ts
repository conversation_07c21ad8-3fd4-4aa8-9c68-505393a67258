export type RedisClientType = {
  connect: () => Promise<void>;
  on: (event: string, listener: (...args: unknown[]) => void) => void;
  get: (key: string) => Promise<string | null>;
  setEx: (key: string, ttl: number, value: string) => Promise<void>;
  del: (key: string | string[]) => Promise<void>;
  keys: (pattern: string) => Promise<string[]>;
  quit: () => Promise<void>;
};

export function createClient(): RedisClientType {
  return {
    connect: async () => {},
    on: () => {},
    get: async () => null,
    setEx: async () => {},
    del: async () => {},
    keys: async () => [],
    quit: async () => {}
  };
}
