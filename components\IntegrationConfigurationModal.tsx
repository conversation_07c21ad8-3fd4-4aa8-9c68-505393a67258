import React, { useState, useEffect } from 'react';
import { Integration, IntegrationConfig, DocumentStatus } from '../types';
import { Button } from './ui/Button';
import { CloseIcon, SettingsIcon } from './Icons';
import { cn } from '../lib/utils';

interface IntegrationConfigurationModalProps {
  isOpen: boolean;
  onClose: () => void;
  integration: Integration | null;
  onSave: (config: IntegrationConfig) => void;
}

const ToggleSwitch: React.FC<{ checked: boolean; onChange: (checked: boolean) => void; label: string; description: string; }> = ({ checked, onChange, label, description }) => (
    <div className="flex items-center justify-between">
        <div>
            <label className="font-medium text-zinc-800 dark:text-zinc-200">{label}</label>
            <p className="text-sm text-zinc-500 dark:text-zinc-400">{description}</p>
        </div>
        <button
            type="button"
            role="switch"
            aria-checked={checked}
            onClick={() => onChange(!checked)}
            className={cn(
                'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 dark:ring-offset-zinc-900',
                checked ? 'bg-brand-600' : 'bg-zinc-200 dark:bg-zinc-700'
            )}
        >
            <span
                aria-hidden="true"
                className={cn(
                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                    checked ? 'translate-x-5' : 'translate-x-0'
                )}
            />
        </button>
    </div>
);

const allStatuses: DocumentStatus[] = ['completed', 'approved', 'out-for-signature', 'in-review', 'draft'];

const IntegrationConfigurationModal: React.FC<IntegrationConfigurationModalProps> = ({ isOpen, onClose, integration, onSave }) => {
    const [config, setConfig] = useState<IntegrationConfig>({
        autoCreateCustomer: true,
        invoiceTriggerStatus: 'completed',
        defaultInvoiceItem: 'General Services',
    });

    useEffect(() => {
        if (integration?.config) {
            setConfig(integration.config);
        }
    }, [integration]);

    if (!isOpen || !integration) {return null;}

    const handleSave = () => {
        onSave(config);
    };

    return (
        <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-lg">
                <header className="flex items-center justify-between p-4 border-b dark:border-zinc-800">
                    <h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200 flex items-center gap-2">
                        <SettingsIcon className="w-5 h-5 text-brand-600" />
                        Configure {integration.name}
                    </h2>
                    <Button variant="ghost" size="icon" onClick={onClose}><CloseIcon className="w-5 h-5"/></Button>
                </header>
                <main className="p-6 space-y-6">
                    <ToggleSwitch
                        checked={config.autoCreateCustomer}
                        onChange={(value) => setConfig(c => ({ ...c, autoCreateCustomer: value }))}
                        label="Auto-create new customers"
                        description="Automatically create a customer when a new client is added."
                    />
                    <div>
                        <label className="block text-sm font-medium text-zinc-700 dark:text-zinc-300">Invoice Trigger Status</label>
                        <p className="text-sm text-zinc-500 dark:text-zinc-400 mb-2">Create an invoice when a document status changes to:</p>
                        <select
                            value={config.invoiceTriggerStatus}
                            onChange={(e) => setConfig(c => ({ ...c, invoiceTriggerStatus: e.target.value as DocumentStatus }))}
                            className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 shadow-sm p-2"
                        >
                            {allStatuses.map(s => <option key={s} value={s} className="capitalize">{s.replace('-', ' ')}</option>)}
                        </select>
                    </div>
                    <div>
                        <label className="block text-sm font-medium text-zinc-700 dark:text-zinc-300">Default Service/Product Item</label>
                        <p className="text-sm text-zinc-500 dark:text-zinc-400 mb-2">The default item to use when creating invoices.</p>
                        <input
                            type="text"
                            value={config.defaultInvoiceItem}
                            onChange={(e) => setConfig(c => ({ ...c, defaultInvoiceItem: e.target.value }))}
                            className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 shadow-sm p-2"
                        />
                    </div>
                </main>
                <footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t dark:border-zinc-800">
                    <Button variant="outline" onClick={onClose}>Cancel</Button>
                    <Button onClick={handleSave}>Save Configuration</Button>
                </footer>
            </div>
        </div>
    );
};

export default IntegrationConfigurationModal;