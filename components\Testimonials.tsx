

import React from 'react';
import { Testimonial } from '../types';
import { cn } from '../lib/utils';

interface TestimonialsProps {
  testimonials: Testimonial[];
}

const getInitials = (name: string) => {
  const parts = name.trim().split(' ').filter(Boolean);
  if (!parts.length) {return '?';}
  const [first, second] = parts;
  return (first[0] + (second ? second[0] : '')).toUpperCase();
};

const Testimonials: React.FC<TestimonialsProps> = ({ testimonials }) => {
  return (
    <section id="testimonials" className="py-20 bg-white dark:bg-zinc-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-zinc-900 dark:text-white sm:text-4xl">
            Trusted by Professionals Worldwide
          </h2>
          <p className="mt-4 text-lg text-zinc-600 dark:text-zinc-400">
            See what our satisfied customers have to say about our platform.
          </p>
        </div>
        {testimonials.length === 0 ? (
          <div className="mt-12 text-center text-zinc-600 dark:text-zinc-400">
            Customer stories are being refreshed. Check back soon for new testimonials.
          </div>
        ) : (
          <div className="mt-12 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {testimonials.map((testimonial) => {
              const cardClasses = cn(
                'bg-zinc-50 dark:bg-zinc-900 p-8 rounded-2xl shadow-md border border-zinc-200 dark:border-zinc-800 transition-shadow',
                testimonial.highlight && 'border-brand-200 dark:border-brand-700 shadow-lg shadow-brand-100/40 dark:shadow-brand-900/30'
              );

              return (
                <div key={testimonial.id} className={cardClasses}>
                  <blockquote className="text-zinc-700 dark:text-zinc-300">
                    <p>“{testimonial.quote}”</p>
                  </blockquote>
                  <figcaption className="flex items-center mt-6 space-x-4">
                    {testimonial.avatar ? (
                      <img className="w-14 h-14 rounded-full object-cover" src={testimonial.avatar} alt={testimonial.name} />
                    ) : (
                      <div className="w-14 h-14 rounded-full bg-brand-100 text-brand-700 flex items-center justify-center font-semibold">
                        {getInitials(testimonial.name)}
                      </div>
                    )}
                    <div>
                      <div className="font-semibold text-zinc-900 dark:text-white">{testimonial.name}</div>
                      {testimonial.title && (
                        <div className="text-zinc-600 dark:text-zinc-400">{testimonial.title}</div>
                      )}
                    </div>
                  </figcaption>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </section>
  );
};

export default Testimonials;