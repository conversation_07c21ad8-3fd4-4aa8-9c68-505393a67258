import React from 'react';

const TemplateCard: React.FC<{ title: string; description: string }> = ({ title, description }) => (
    <div className="bg-white rounded-lg shadow border border-zinc-200 flex flex-col">
        <div className="p-4 flex-1">
            <h3 className="font-semibold text-sm text-zinc-800">{title}</h3>
            <p className="text-xs text-zinc-500 mt-1">{description}</p>
        </div>
        <div className="p-3 border-t border-zinc-100">
            <div className="bg-indigo-600 text-white text-xs text-center font-semibold py-1.5 rounded-md">
                Use Template
            </div>
        </div>
    </div>
);

const templates = [
    { title: 'Mutual Non-Disclosure Agreement (NDA)', description: 'A standard agreement for two parties sharing confidential information.' },
    { title: 'SaaS Terms of Service', description: 'A non-binding Letter of Intent for a potential M&A.' },
    { title: 'Merger and Acquisition (M&A) LOI', description: 'A non-binding Letter of Intent for a potential M&A.' },
    { title: 'Freelance Graphic Design Contract', description: 'A contract for hiring a freelance graphic designer.' },
    { title: 'Consulting Agreement', description: 'A detailed agreement for providing professional consulting services.' },
    { title: 'Software Development Agreement', description: 'A contract for building a custom software application for a client.' },
];

const TemplatesScreenshot = () => {
    return (
        <div className="w-full h-full bg-zinc-50 p-6 overflow-hidden select-none">
            <h1 className="text-2xl font-bold text-zinc-900">Templates</h1>
            <p className="text-sm text-zinc-600 mt-1 mb-4">Browse our library of professionally crafted legal templates or create your own to get started quickly.</p>

            <div className="bg-white rounded-lg shadow border border-zinc-200 p-4">
                <div className="grid grid-cols-3 gap-4">
                    {templates.map(t => <TemplateCard key={t.title} title={t.title} description={t.description} />)}
                </div>
            </div>
        </div>
    );
};

export default TemplatesScreenshot;
