import '@testing-library/jest-dom';
// @vitest-environment jsdom

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import SaveTemplateModal from '../components/SaveTemplateModal';

describe('SaveTemplateModal', () => {
  const onClose = vi.fn();
  const onSave = vi.fn();

  beforeEach(() => {
    onClose.mockClear();
    onSave.mockClear();
  });

  it('renders nothing when isOpen is false', () => {
    const { container } = render(
      <SaveTemplateModal isOpen={false} onClose={onClose} onSave={onSave} />
    );
    expect(container.firstChild).toBeNull();
  });

  it('renders modal when isOpen is true', () => {
    render(<SaveTemplateModal isOpen={true} onClose={onClose} onSave={onSave} />);
    expect(screen.getByText('Save as Template')).toBeInTheDocument();
    expect(screen.getByLabelText('Template Name')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Save Template')).toBeInTheDocument();
  });

  it('calls onClose when Cancel button is clicked', () => {
    render(<SaveTemplateModal isOpen={true} onClose={onClose} onSave={onSave} />);
    fireEvent.click(screen.getByText('Cancel'));
    expect(onClose).toHaveBeenCalled();
  });

  it('calls onClose when close icon is clicked', () => {
    render(<SaveTemplateModal isOpen={true} onClose={onClose} onSave={onSave} />);
    const closeBtn = screen.getByRole('button', { name: '' });
    fireEvent.click(closeBtn);
    expect(onClose).toHaveBeenCalled();
  });

  it('disables Save Template button when input is empty', () => {
    render(<SaveTemplateModal isOpen={true} onClose={onClose} onSave={onSave} />);
    const saveBtn = screen.getByText('Save Template');
    expect(saveBtn).toBeDisabled();
  });

  it('enables Save Template button when input is not empty', () => {
    render(<SaveTemplateModal isOpen={true} onClose={onClose} onSave={onSave} />);
    const input = screen.getByLabelText('Template Name');
    fireEvent.change(input, { target: { value: 'My NDA' } });
    const saveBtn = screen.getByText('Save Template');
    expect(saveBtn).not.toBeDisabled();
  });

  it('calls onSave with trimmed template name when Save Template is clicked', () => {
    render(<SaveTemplateModal isOpen={true} onClose={onClose} onSave={onSave} />);
    const input = screen.getByLabelText('Template Name');
    fireEvent.change(input, { target: { value: '  NDA Template  ' } });
    const saveBtn = screen.getByText('Save Template');
    fireEvent.click(saveBtn);
    expect(onSave).toHaveBeenCalledWith('NDA Template');
  });
});
