import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup.ts'],
    server: {
      deps: {
        inline: ['@google/genai']
      }
    },
    env: {
      SUPABASE_URL: 'https://test.supabase.co',
      SUPABASE_ANON_KEY: 'test-anon-key',
      SUPABASE_SERVICE_ROLE_KEY: 'test-service-role-key',
      TEST_BYPASS_AUTH: '1',
    },
    coverage: {
      provider: 'v8',
      reporter: ['text', 'lcov'],
      reportsDirectory: './coverage',
      thresholds: {
        lines: 10,
        functions: 10,
        statements: 10,
        branches: 10,
      },
    },
  },
  resolve: {
    alias: {
      redis: path.resolve(__dirname, 'tests/mocks/redis.ts')
    }
  }
});
