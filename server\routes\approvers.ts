import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient } from '../supabaseClient';

const router = Router();

const insertSchema = z.object({
  documentId: z.string().uuid(),
  email: z.string().email(),
  status: z.enum(['pending','approved','changes-requested']).default('pending'),
  comments: z.string().optional(),
});
const updateSchema = z.object({
  status: z.enum(['pending','approved','changes-requested']).optional(),
  comments: z.string().optional(),
  respondedAt: z.string().optional(),
});

router.get('/', requireAuth, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('document_approvers').select('*').order('id');
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ approvers: data });
});

router.post('/', requireAuth, async (req: AuthRequest, res) => {
  const parsed = insertSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa
    .from('document_approvers')
    .insert({
      document_id: parsed.data.documentId,
      email: parsed.data.email,
      status: parsed.data.status,
      comments: parsed.data.comments,
    })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ approver: data });
});

router.put('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = updateSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{ status: string; comments: string; responded_at: string }> = { status: parsed.data.status, comments: parsed.data.comments };
  if (parsed.data.respondedAt) {patch.responded_at = parsed.data.respondedAt;}
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('document_approvers').update(patch).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ approver: data });
});

router.delete('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('document_approvers').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

export default router;

