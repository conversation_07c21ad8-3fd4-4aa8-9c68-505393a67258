
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/DocumentHistoryTable.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> DocumentHistoryTable.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/218</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/218</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import React, { useState, useMemo, useEffect } from 'react';<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
import { Document as DocType, Folder, User, DocumentStatus, DashboardView } from '../types';
<span class="cstat-no" title="statement not covered" >import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/Table';</span>
<span class="cstat-no" title="statement not covered" >import { Button } from './ui/Button';</span>
<span class="cstat-no" title="statement not covered" >import { SearchIcon, MoreVerticalIcon, FolderIcon, PlusCircleIcon, AlertTriangleIcon, EditIcon, TrashIcon, DocumentIcon, ArchiveIcon, FolderMoveIcon } from './Icons';</span>
<span class="cstat-no" title="statement not covered" >import { cn } from '../lib/utils';</span>
<span class="cstat-no" title="statement not covered" >import CreateFolderModal from './CreateFolderModal';</span>
<span class="cstat-no" title="statement not covered" >import MoveDocumentModal from './MoveDocumentModal';</span>
<span class="cstat-no" title="statement not covered" >import Dropdown from './ui/Dropdown';</span>
&nbsp;
interface DocumentHistoryTableProps {
  user: User;
  onView: (doc: DocType) =&gt; void;
  onDeleteDocument: (documentId: string) =&gt; void;
  onCreateFolder: (name: string) =&gt; void;
  onUpdateFolder: (folderId: string, newName: string) =&gt; void;
  onDeleteFolder: (folderId: string) =&gt; void;
  onMoveDocument: (documentId: string, folderId: string | null) =&gt; void;
  onUpdateDocumentStatus: (docId: string, newStatus: DocumentStatus) =&gt; void;
  setView: (view: DashboardView) =&gt; void;
  // New: allow parent to suggest which folder to show
  initialSelectedFolderId?: string | 'all' | 'uncategorized' | 'archived' | null;
  // New: highlight and anchor a specific document
  highlightDocumentId?: string;
}
&nbsp;
<span class="cstat-no" title="statement not covered" >const StatusBadge: React.FC&lt;{ status: DocumentStatus }&gt; = ({ status }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize';</span>
<span class="cstat-no" title="statement not covered" >    const statusClasses = {</span>
<span class="cstat-no" title="statement not covered" >        draft: 'bg-zinc-100 text-zinc-800 dark:bg-zinc-800 dark:text-zinc-300',</span>
<span class="cstat-no" title="statement not covered" >        'in-review': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300',</span>
<span class="cstat-no" title="statement not covered" >        approved: 'bg-sky-100 text-sky-800 dark:bg-sky-900/50 dark:text-sky-300',</span>
<span class="cstat-no" title="statement not covered" >        'out-for-signature': 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300',</span>
<span class="cstat-no" title="statement not covered" >        completed: 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300',</span>
<span class="cstat-no" title="statement not covered" >        archived: 'bg-zinc-100 text-zinc-600 dark:bg-zinc-800 dark:text-zinc-400',</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" >    return &lt;span className={cn(baseClasses, status ? statusClasses[status] : '')}&gt;{status.replace('-', ' ')}&lt;/span&gt;;</span>
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
&nbsp;
<span class="cstat-no" title="statement not covered" >const DocumentHistoryTable: React.FC&lt;DocumentHistoryTableProps&gt; = ({ user, onView, onDeleteDocument, onCreateFolder, onUpdateFolder, onDeleteFolder, onMoveDocument, onUpdateDocumentStatus, setView, initialSelectedFolderId, highlightDocumentId }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const [selectedFolderId, setSelectedFolderId] = useState&lt;string | null&gt;('all');</span>
<span class="cstat-no" title="statement not covered" >  const [justSavedDocId, setJustSavedDocId] = useState&lt;string | null&gt;(null);</span>
&nbsp;
  // Apply externally suggested folder selection
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (typeof initialSelectedFolderId !== 'undefined') {</span>
<span class="cstat-no" title="statement not covered" >      setSelectedFolderId(initialSelectedFolderId === null ? 'uncategorized' : initialSelectedFolderId);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }, [initialSelectedFolderId]);</span>
<span class="cstat-no" title="statement not covered" >  const [searchTerm, setSearchTerm] = useState('');</span>
  
<span class="cstat-no" title="statement not covered" >  const [isCreateFolderModalOpen, setIsCreateFolderModalOpen] = useState(false);</span>
<span class="cstat-no" title="statement not covered" >  const [folderToEdit, setFolderToEdit] = useState&lt;Folder | null&gt;(null);</span>
  
<span class="cstat-no" title="statement not covered" >  const [isMoveModalOpen, setIsMoveModalOpen] = useState(false);</span>
<span class="cstat-no" title="statement not covered" >  const [docToMove, setDocToMove] = useState&lt;DocType | null&gt;(null);</span>
  
<span class="cstat-no" title="statement not covered" >  const [itemToDelete, setItemToDelete] = useState&lt;{ type: 'folder' | 'document', id: string, name: string } | null&gt;(null);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const filteredDocuments = useMemo(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    let docs = user.documents.filter(d =&gt; d.status !== 'archived'); // By default, don't show archived</span>
<span class="cstat-no" title="statement not covered" >    if (selectedFolderId === 'uncategorized') {</span>
<span class="cstat-no" title="statement not covered" >      docs = docs.filter(d =&gt; !d.folderId);</span>
<span class="cstat-no" title="statement not covered" >    } else if (selectedFolderId === 'archived') {</span>
<span class="cstat-no" title="statement not covered" >      docs = user.documents.filter(d =&gt; d.status === 'archived');</span>
<span class="cstat-no" title="statement not covered" >    } else if (selectedFolderId &amp;&amp; selectedFolderId !== 'all') {</span>
<span class="cstat-no" title="statement not covered" >      docs = docs.filter(d =&gt; d.folderId === selectedFolderId);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
<span class="cstat-no" title="statement not covered" >    if (searchTerm) {</span>
<span class="cstat-no" title="statement not covered" >      docs = docs.filter(d =&gt; d.name.toLowerCase().includes(searchTerm.toLowerCase()));</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    return docs.sort((a, b) =&gt; new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());</span>
<span class="cstat-no" title="statement not covered" >  }, [user.documents, selectedFolderId, searchTerm]);</span>
&nbsp;
  // When asked to highlight a document, scroll to it and apply a temporary highlight
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (!highlightDocumentId) {return;}</span>
<span class="cstat-no" title="statement not covered" >    const exists = user.documents.some(d =&gt; d.id === highlightDocumentId);</span>
<span class="cstat-no" title="statement not covered" >    if (!exists) {return;}</span>
<span class="cstat-no" title="statement not covered" >    setJustSavedDocId(highlightDocumentId);</span>
    // Delay to ensure row is rendered
<span class="cstat-no" title="statement not covered" >    setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      const el = document.getElementById(`doc-row-${highlightDocumentId}`);</span>
<span class="cstat-no" title="statement not covered" >      el?.scrollIntoView({ behavior: 'smooth', block: 'center' });</span>
<span class="cstat-no" title="statement not covered" >    }, 50);</span>
<span class="cstat-no" title="statement not covered" >    const t = setTimeout(() =&gt; setJustSavedDocId(null), 4000);</span>
<span class="cstat-no" title="statement not covered" >    return () =&gt; clearTimeout(t);</span>
<span class="cstat-no" title="statement not covered" >  }, [highlightDocumentId, user.documents]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const currentFolder = user.folders.find(f =&gt; f.id === selectedFolderId);</span>
<span class="cstat-no" title="statement not covered" >  const breadcrumb = currentFolder ? `Documents / ${currentFolder.name}` : selectedFolderId === 'uncategorized' ? 'Documents / Uncategorized' : selectedFolderId === 'archived' ? 'Documents / Archived' : 'Documents / All Documents';</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleSaveFolder = (name: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (folderToEdit) {</span>
<span class="cstat-no" title="statement not covered" >      onUpdateFolder(folderToEdit.id, name);</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >      onCreateFolder(name);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    setIsCreateFolderModalOpen(false);</span>
<span class="cstat-no" title="statement not covered" >    setFolderToEdit(null);</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const confirmDelete = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (!itemToDelete) {return;}</span>
<span class="cstat-no" title="statement not covered" >    if (itemToDelete.type === 'document') {</span>
<span class="cstat-no" title="statement not covered" >      onDeleteDocument(itemToDelete.id);</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >      onDeleteFolder(itemToDelete.id);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >    setItemToDelete(null);</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const formatDate = (dateString: string) =&gt; new Date(dateString).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="flex flex-col md:flex-row gap-6 h-full"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;aside className="w-full md:w-64 flex-shrink-0 bg-white dark:bg-zinc-950 p-4 rounded-xl border border-zinc-200 dark:border-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;Button className="w-full mb-4" onClick={() =&gt; setIsCreateFolderModalOpen(true)}&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;PlusCircleIcon className="w-5 h-5 mr-2"/&gt; New Folder</span>
<span class="cstat-no" title="statement not covered" >        &lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;nav className="space-y-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;button onClick={() =&gt; setSelectedFolderId('all')} className={cn('w-full text-left flex items-center p-2 rounded-md text-sm font-medium', selectedFolderId === 'all' ? 'bg-brand-50 text-brand-700 dark:bg-brand-900/40 dark:text-brand-300' : 'hover:bg-zinc-100 dark:hover:bg-zinc-800 text-zinc-700 dark:text-zinc-300')}&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;FolderIcon className="w-5 h-5 mr-3"/&gt; All Documents</span>
<span class="cstat-no" title="statement not covered" >          &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;button onClick={() =&gt; setSelectedFolderId('uncategorized')} className={cn('w-full text-left flex items-center p-2 rounded-md text-sm font-medium', selectedFolderId === 'uncategorized' ? 'bg-brand-50 text-brand-700 dark:bg-brand-900/40 dark:text-brand-300' : 'hover:bg-zinc-100 dark:hover:bg-zinc-800 text-zinc-700 dark:text-zinc-300')}&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;FolderIcon className="w-5 h-5 mr-3"/&gt; Uncategorized</span>
<span class="cstat-no" title="statement not covered" >          &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="border-t my-2 border-zinc-200 dark:border-zinc-800"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          {user.folders.map(folder =&gt; (</span>
<span class="cstat-no" title="statement not covered" >            &lt;div key={folder.id} className="group flex items-center justify-between rounded-md hover:bg-zinc-100 dark:hover:bg-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button onClick={() =&gt; setSelectedFolderId(folder.id)} className={cn('flex-1 text-left flex items-center p-2 text-sm font-medium', selectedFolderId === folder.id ? 'bg-brand-50 text-brand-700 dark:bg-brand-900/40 dark:text-brand-300' : 'text-zinc-700 dark:text-zinc-300')}&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;FolderIcon className="w-5 h-5 mr-3"/&gt; {folder.name}</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="opacity-0 group-hover:opacity-100 transition-opacity pr-1" onClick={e =&gt; e.stopPropagation()}&gt;</span>
<span class="cstat-no" title="statement not covered" >                 &lt;Dropdown&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;Dropdown.Trigger&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;Button variant="ghost" size="icon" className="h-8 w-8 dark:text-zinc-400 dark:hover:bg-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;MoreVerticalIcon className="h-4 w-4" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/Dropdown.Trigger&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;Dropdown.Content&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;Dropdown.Item onClick={() =&gt; { setFolderToEdit(folder); setIsCreateFolderModalOpen(true); }} icon={&lt;EditIcon className="w-4 h-4"/&gt;}&gt;</span>
                             Rename
<span class="cstat-no" title="statement not covered" >                        &lt;/Dropdown.Item&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;Dropdown.Item onClick={() =&gt; setItemToDelete({ type: 'folder', id: folder.id, name: folder.name })} className="text-red-600 dark:text-red-500 hover:bg-red-50 dark:hover:bg-red-500/10" icon={&lt;TrashIcon className="w-4 h-4"/&gt;}&gt;</span>
                            Delete
<span class="cstat-no" title="statement not covered" >                        &lt;/Dropdown.Item&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/Dropdown.Content&gt;</span>
<span class="cstat-no" title="statement not covered" >                 &lt;/Dropdown&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          ))}</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="border-t my-2 border-zinc-200 dark:border-zinc-800"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >           &lt;button onClick={() =&gt; setSelectedFolderId('archived')} className={cn('w-full text-left flex items-center p-2 rounded-md text-sm font-medium', selectedFolderId === 'archived' ? 'bg-brand-50 text-brand-700 dark:bg-brand-900/40 dark:text-brand-300' : 'hover:bg-zinc-100 dark:hover:bg-zinc-800 text-zinc-700 dark:text-zinc-300')}&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;ArchiveIcon className="w-5 h-5 mr-3"/&gt; Archived</span>
<span class="cstat-no" title="statement not covered" >          &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/nav&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/aside&gt;</span>
      
<span class="cstat-no" title="statement not covered" >      &lt;main className="flex-1 bg-white dark:bg-zinc-950 p-6 rounded-xl border border-zinc-200 dark:border-zinc-800 flex flex-col"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;h2 className="text-xl font-bold text-zinc-900 dark:text-white"&gt;Your Documents&lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-sm text-zinc-500 dark:text-zinc-400"&gt;{breadcrumb}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="relative w-full sm:w-64"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;SearchIcon className="h-5 w-5 text-zinc-400" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;input type="text" placeholder="Search documents..." value={searchTerm} onChange={(e) =&gt; setSearchTerm(e.target.value)} className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 focus:border-brand-500 focus:ring-brand-500 sm:text-sm p-2" /&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        {justSavedDocId &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="mb-4 text-sm text-amber-800 bg-amber-50 dark:bg-amber-900/30 dark:text-amber-200 border border-amber-200 dark:border-amber-800 rounded-md px-3 py-2"&gt;</span>
            New document saved.
<span class="cstat-no" title="statement not covered" >            &lt;a</span>
<span class="cstat-no" title="statement not covered" >              href={`#doc-row-${justSavedDocId}`}</span>
<span class="cstat-no" title="statement not covered" >              onClick={(e) =&gt; { e.preventDefault(); document.getElementById(`doc-row-${justSavedDocId}`)?.scrollIntoView({ behavior: 'smooth', block: 'center' }); }}</span>
<span class="cstat-no" title="statement not covered" >              className="ml-2 font-medium underline"</span>
<span class="cstat-no" title="statement not covered" >            &gt;</span>
              Jump to it
<span class="cstat-no" title="statement not covered" >            &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
        )}
<span class="cstat-no" title="statement not covered" >        &lt;div className="border-t border-zinc-200 dark:border-zinc-800 flex-1 overflow-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="overflow-x-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;Table className="min-w-[600px]"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;TableHeader&gt;&lt;TableRow&gt;&lt;TableHead&gt;Name&lt;/TableHead&gt;&lt;TableHead&gt;Status&lt;/TableHead&gt;&lt;TableHead className="hidden sm:table-cell"&gt;Date Updated&lt;/TableHead&gt;&lt;TableHead className="text-right w-[10%]"&gt;Actions&lt;/TableHead&gt;&lt;/TableRow&gt;&lt;/TableHeader&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;TableBody&gt;</span>
<span class="cstat-no" title="statement not covered" >                {filteredDocuments.length &gt; 0 ? (</span>
<span class="cstat-no" title="statement not covered" >                    filteredDocuments.map(doc =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;TableRow key={doc.id} id={`doc-row-${doc.id}`} className={cn(justSavedDocId === doc.id ? 'bg-amber-50 dark:bg-amber-900/20 ring-2 ring-amber-400' : '')}&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;TableCell onClick={() =&gt; onView(doc)} className="font-medium text-zinc-900 dark:text-zinc-200 cursor-pointer"&gt;{doc.name}&lt;/TableCell&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;TableCell onClick={() =&gt; onView(doc)} className="cursor-pointer"&gt;&lt;StatusBadge status={doc.status} /&gt;&lt;/TableCell&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;TableCell onClick={() =&gt; onView(doc)} className="text-zinc-500 dark:text-zinc-400 cursor-pointer hidden sm:table-cell"&gt;{formatDate(doc.updatedAt)}&lt;/TableCell&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;TableCell className="text-right" onClick={e =&gt; e.stopPropagation()}&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;Dropdown&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;Dropdown.Trigger&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;Button variant="ghost" size="icon" className="h-8 w-8 dark:text-zinc-400 dark:hover:bg-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;MoreVerticalIcon className="h-4 w-4" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/Dropdown.Trigger&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;Dropdown.Content&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;Dropdown.Item onClick={() =&gt; onView(doc)} icon={&lt;DocumentIcon className="w-4 h-4"/&gt;}&gt;View&lt;/Dropdown.Item&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;Dropdown.Item onClick={() =&gt; { setDocToMove(doc); setIsMoveModalOpen(true); }} icon={&lt;FolderMoveIcon className="w-4 h-4"/&gt;}&gt;Move to...&lt;/Dropdown.Item&gt;</span>
<span class="cstat-no" title="statement not covered" >                                {doc.status === 'completed' &amp;&amp; &lt;Dropdown.Item onClick={() =&gt; onUpdateDocumentStatus(doc.id, 'archived')} icon={&lt;ArchiveIcon className="w-4 h-4"/&gt;}&gt;Archive&lt;/Dropdown.Item&gt;}</span>
<span class="cstat-no" title="statement not covered" >                                &lt;Dropdown.Item onClick={() =&gt; setItemToDelete({ type: 'document', id: doc.id, name: doc.name })} className="text-red-600 dark:text-red-500 hover:bg-red-50 dark:hover:bg-red-500/10" icon={&lt;TrashIcon className="w-4 h-4"/&gt;}&gt;Delete&lt;/Dropdown.Item&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/Dropdown.Content&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/Dropdown&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/TableCell&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/TableRow&gt;</span>
<span class="cstat-no" title="statement not covered" >                    ))</span>
                ) : (
<span class="cstat-no" title="statement not covered" >                    &lt;TableRow&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;TableCell colSpan={5} className="h-96"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;div className="text-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;DocumentIcon className="mx-auto h-12 w-12 text-zinc-300 dark:text-zinc-700" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;h3 className="mt-2 text-lg font-semibold text-zinc-800 dark:text-zinc-200"&gt;No Documents Found&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;p className="mt-1 text-sm text-zinc-500 dark:text-zinc-400"&gt;There are no documents in this view. Try another folder or create a new one.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;Button className="mt-4" onClick={() =&gt; setView('generate')}&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;PlusCircleIcon className="w-5 h-5 mr-2"/&gt;</span>
                                Create Document
<span class="cstat-no" title="statement not covered" >                            &lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/TableCell&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/TableRow&gt;</span>
                )}
<span class="cstat-no" title="statement not covered" >                &lt;/TableBody&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/Table&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/main&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    &lt;CreateFolderModal isOpen={isCreateFolderModalOpen} onClose={() =&gt; { setIsCreateFolderModalOpen(false); setFolderToEdit(null); }} onSave={handleSaveFolder} existingFolder={folderToEdit} /&gt;</span>
<span class="cstat-no" title="statement not covered" >    {docToMove &amp;&amp; &lt;MoveDocumentModal isOpen={isMoveModalOpen} onClose={() =&gt; setIsMoveModalOpen(false)} onMove={(folderId) =&gt; { onMoveDocument(docToMove.id, folderId); setIsMoveModalOpen(false); }} document={docToMove} folders={user.folders} /&gt;}</span>
<span class="cstat-no" title="statement not covered" >    {itemToDelete &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-md p-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="flex items-start gap-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 sm:mx-0 sm:h-10 sm:w-10"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;AlertTriangleIcon className="h-6 w-6 text-red-600 dark:text-red-400" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="mt-0 text-center sm:text-left"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;h3 className="text-lg leading-6 font-medium text-zinc-900 dark:text-zinc-50"&gt;Delete {itemToDelete.type}&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p className="mt-2 text-sm text-zinc-500 dark:text-zinc-400"&gt;</span>
<span class="cstat-no" title="statement not covered" >                          Are you sure you want to delete "{itemToDelete.name}"? </span>
<span class="cstat-no" title="statement not covered" >                          {itemToDelete.type === 'folder' &amp;&amp; ' All documents inside will become uncategorized.'} This action cannot be undone.</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="mt-5 sm:mt-4 flex flex-col-reverse sm:flex-row-reverse gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                     &lt;Button variant="destructive" onClick={confirmDelete}&gt;Delete&lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                     &lt;Button variant="outline" onClick={() =&gt; setItemToDelete(null)}&gt;Cancel&lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
    )}
<span class="cstat-no" title="statement not covered" >    &lt;/&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default DocumentHistoryTable;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    