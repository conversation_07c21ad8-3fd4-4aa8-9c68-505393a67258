
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/Hero.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> Hero.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/74</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/74</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// FIX: Correctly import useState and useEffect from React to make them available in the component.
<span class="cstat-no" title="statement not covered" >import React, { useState, useEffect } from 'react';</span>
<span class="cstat-no" title="statement not covered" >import { SparklesIcon } from './Icons';</span>
<span class="cstat-no" title="statement not covered" >import DashboardScreenshot from './screenshots/DashboardScreenshot';</span>
<span class="cstat-no" title="statement not covered" >import DocumentsScreenshot from './screenshots/DocumentsScreenshot';</span>
<span class="cstat-no" title="statement not covered" >import LifecycleScreenshot from './screenshots/LifecycleScreenshot';</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const UI_SCREENS = [</span>
<span class="cstat-no" title="statement not covered" >  { name: 'Dashboard', component: &lt;DashboardScreenshot /&gt; },</span>
<span class="cstat-no" title="statement not covered" >  { name: 'Documents', component: &lt;DocumentsScreenshot /&gt; },</span>
<span class="cstat-no" title="statement not covered" >  { name: 'Lifecycle', component: &lt;LifecycleScreenshot /&gt; },</span>
<span class="cstat-no" title="statement not covered" >];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const AnimatedUiPreview = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const [activeIndex, setActiveIndex] = useState(0);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const interval = setInterval(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      setActiveIndex((prevIndex) =&gt; (prevIndex + 1) % UI_SCREENS.length);</span>
<span class="cstat-no" title="statement not covered" >    }, 4000);</span>
<span class="cstat-no" title="statement not covered" >    return () =&gt; clearInterval(interval);</span>
<span class="cstat-no" title="statement not covered" >  }, []);</span>
  
<span class="cstat-no" title="statement not covered" >  const activeScreen = UI_SCREENS[activeIndex];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="relative mt-20 w-full max-w-4xl animate-fade-in-up" style={{ animationDelay: '0.4s' }}&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="absolute -inset-2 bg-gradient-to-r from-brand-500 to-brand-700 rounded-2xl blur-lg opacity-60 animate-float"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="relative bg-zinc-800/60 backdrop-blur-md rounded-2xl shadow-2xl border border-zinc-700 overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="h-10 bg-zinc-900/50 flex items-center px-4 gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="w-3 h-3 bg-red-500 rounded-full"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="w-3 h-3 bg-yellow-500 rounded-full"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="w-3 h-3 bg-green-500 rounded-full"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex-1 text-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="bg-zinc-700 text-sm text-zinc-300 rounded-md px-4 py-1 max-w-xs mx-auto truncate"&gt;</span>
<span class="cstat-no" title="statement not covered" >              lexigen.ai/{activeScreen.name.toLowerCase()}</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="relative h-72 bg-zinc-100 dark:bg-zinc-900 overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >          {UI_SCREENS.map((screen, index) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >             &lt;div</span>
<span class="cstat-no" title="statement not covered" >              key={screen.name}</span>
<span class="cstat-no" title="statement not covered" >              className={`absolute inset-0 w-full h-full transition-opacity duration-1000 ${activeIndex === index ? 'opacity-100' : 'opacity-0'}`}</span>
            &gt;
<span class="cstat-no" title="statement not covered" >              {screen.component}</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          ))}</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
&nbsp;
<span class="cstat-no" title="statement not covered" >const Hero: React.FC = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="relative w-full pt-32 pb-20 lg:pt-40 lg:pb-28 overflow-hidden bg-zinc-950"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="absolute top-0 left-0 w-full h-full bg-grid-zinc-800/[0.2] [mask-image:linear-gradient(to_bottom,white_5%,transparent_50%)]"&gt;&lt;/div&gt;</span>
      
      {/* Background Glows */}
<span class="cstat-no" title="statement not covered" >      &lt;div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="absolute -translate-x-[50%] -translate-y-[50%] w-[800px] h-[800px] bg-brand-900/30 rounded-full blur-3xl"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="absolute translate-x-[50%] translate-y-[20%] w-[600px] h-[600px] bg-brand-600/20 rounded-full blur-3xl"&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      &lt;div className="relative z-10 max-w-7xl mx-auto px-4 flex flex-col items-center text-center"&gt;</span>
        
<span class="cstat-no" title="statement not covered" >        &lt;h1 className="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl lg:text-7xl animate-fade-in-up" style={{ animationDelay: '0.1s' }}&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;span&gt;Draft, Analyze, and Manage&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;span className="block bg-gradient-to-r from-brand-400 to-brand-500 text-transparent bg-clip-text mt-2"&gt;Legal Documents with AI&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/h1&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        &lt;p className="mt-6 max-w-3xl text-lg text-zinc-300/90 md:text-xl animate-fade-in-up" style={{ animationDelay: '0.2s' }}&gt;</span>
          Stop paying exorbitant legal fees. LexiGen is the all-in-one platform to generate, review, and track contracts in minutes, not days.
<span class="cstat-no" title="statement not covered" >        &lt;/p&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        &lt;div className="mt-8 flex flex-wrap justify-center gap-4 animate-fade-in-up" style={{ animationDelay: '0.3s' }}&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;a href="#solutions" className="inline-flex items-center justify-center px-8 py-3.5 text-base font-semibold text-white bg-brand-600 rounded-lg shadow-lg hover:bg-brand-700"&gt;</span>
                See Solutions
<span class="cstat-no" title="statement not covered" >            &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;a href="#pricing" className="inline-flex items-center justify-center px-8 py-3.5 text-base font-semibold text-white bg-zinc-800/50 rounded-lg shadow-lg hover:bg-zinc-800/80 backdrop-blur-sm border border-zinc-700"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;SparklesIcon className="w-5 h-5 mr-2 text-amber-300"/&gt;</span>
                View Pricing
<span class="cstat-no" title="statement not covered" >            &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        &lt;AnimatedUiPreview /&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;style&gt;{`</span>
        .bg-grid-zinc-800\\[\\[0\\.2\\]] {
          background-image: linear-gradient(to right, rgb(63 63 70 / 0.2) 1px, transparent 1px), linear-gradient(to bottom, rgb(63 63 70 / 0.2) 1px, transparent 1px);
        }
<span class="cstat-no" title="statement not covered" >      `}&lt;/style&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default Hero;</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    