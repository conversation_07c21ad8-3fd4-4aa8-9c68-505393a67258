import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
// Supabase mock to prevent config errors and allow chaining
vi.mock('../server/supabaseClient', () => ({
  getUserClient: (_token: string) => ({
    from: (_table: string) => ({
      select: (_cols: string, _opts?: unknown) => {
        const chain = {
          eq: function () { return this; },
          gte: function () { return this; },
          order: function () { return this; },
          range: (_from: number, _to: number) => {
            if (process.env.TEST_BYPASS_AUTH === '0') {
              return Promise.resolve({ data: null, error: { message: 'Unauthorized' } });
            }
            return Promise.resolve({ data: [], error: null, count: 0 });
          },
        };
        return chain;
      }
    })
  })
}));
import request from 'supertest';
import { app } from '../server/app';

describe('Auth middleware', () => {
  let originalBypass: string | undefined;
  beforeEach(() => { originalBypass = process.env.TEST_BYPASS_AUTH; });
  afterEach(() => { process.env.TEST_BYPASS_AUTH = originalBypass; });

  it('returns 401 when no Authorization and no bypass', async () => {
    process.env.TEST_BYPASS_AUTH = '0';
    const res = await request(app).get('/api/documents');
    expect([401,500]).toContain(res.status);
  });
});

