
import React, { useState, useEffect } from 'react';
import { PricingPlan } from '../../types';
import { Button } from '../ui/Button';
import { CloseIcon, BillingIcon, PlusCircleIcon, TrashIcon } from '../Icons';

interface PlanEditorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: Omit<PricingPlan, 'id'> | PricingPlan) => void;
  existingPlan?: PricingPlan | null;
}

const PlanEditorModal: React.FC<PlanEditorModalProps> = ({ isOpen, onClose, onSave, existingPlan }) => {
  const [plan, setPlan] = useState<Omit<PricingPlan, 'id'>>({
    name: '',
    price: '',
    priceDetail: '/ month',
    features: [''],
    cta: 'Get Started',
    isFeatured: false,
  });

  useEffect(() => {
    if (existingPlan) {
      setPlan(existingPlan);
    } else {
      setPlan({ name: '', price: '', priceDetail: '/ month', features: [''], cta: 'Get Started', isFeatured: false });
    }
  }, [existingPlan, isOpen]);

  if (!isOpen) {return null;}

  const handleInputChange = (field: keyof Omit<PricingPlan, 'id' | 'features' | 'isFeatured'>, value: string) => {
    setPlan(prev => ({ ...prev, [field]: value }));
  };
  
  const handleFeatureChange = (index: number, value: string) => {
    const newFeatures = [...plan.features];
    newFeatures[index] = value;
    setPlan(prev => ({ ...prev, features: newFeatures }));
  };

  const addFeature = () => setPlan(prev => ({ ...prev, features: [...prev.features, ''] }));
  const removeFeature = (index: number) => setPlan(prev => ({ ...prev, features: plan.features.filter((_, i) => i !== index) }));
  
  const handleSave = () => {
    const finalPlan = { ...plan, features: plan.features.filter(f => f.trim() !== '') };
    if (existingPlan) {
        onSave({ ...finalPlan, id: existingPlan.id });
    } else {
        onSave(finalPlan);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-lg">
        <header className="flex items-center justify-between p-4 border-b dark:border-zinc-800">
          <h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200 flex items-center">
            <BillingIcon className="w-5 h-5 mr-2 text-brand-700 dark:text-brand-400" />
            {existingPlan ? 'Edit Plan' : 'Create New Plan'}
          </h2>
          <button onClick={onClose} className="p-2 text-zinc-500 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800 rounded-lg">
            <CloseIcon className="w-6 h-6" />
          </button>
        </header>
        <main className="p-6 space-y-4 max-h-[60vh] overflow-y-auto">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div><label className="block text-sm font-medium dark:text-zinc-300">Plan Name</label><input type="text" value={plan.name} onChange={e => handleInputChange('name', e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" /></div>
            <div><label className="block text-sm font-medium dark:text-zinc-300">Call to Action</label><input type="text" value={plan.cta} onChange={e => handleInputChange('cta', e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" /></div>
            <div><label className="block text-sm font-medium dark:text-zinc-300">Price</label><input type="text" value={plan.price} onChange={e => handleInputChange('price', e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" placeholder="e.g., $49 or Custom"/></div>
            <div><label className="block text-sm font-medium dark:text-zinc-300">Price Detail</label><input type="text" value={plan.priceDetail} onChange={e => handleInputChange('priceDetail', e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" placeholder="e.g., / month"/></div>
          </div>
          <div>
            <label className="block text-sm font-medium dark:text-zinc-300">Features</label>
            <div className="space-y-2 mt-1">
              {plan.features.map((feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  <input type="text" value={feature} onChange={e => handleFeatureChange(index, e.target.value)} className="flex-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" />
                  <Button variant="ghost" size="icon" className="text-red-500 hover:bg-red-50 dark:hover:bg-red-500/10" onClick={() => removeFeature(index)} disabled={plan.features.length === 1}><TrashIcon className="w-4 h-4"/></Button>
                </div>
              ))}
            </div>
            <Button variant="outline" size="sm" onClick={addFeature} className="mt-2"><PlusCircleIcon className="w-4 h-4 mr-2"/>Add Feature</Button>
          </div>
          <div className="flex items-center gap-2">
              <input type="checkbox" id="isFeatured" checked={plan.isFeatured} onChange={e => setPlan(p => ({...p, isFeatured: e.target.checked}))} className="h-4 w-4 rounded text-brand-600 focus:ring-brand-500" />
              <label htmlFor="isFeatured" className="text-sm font-medium dark:text-zinc-300">Mark as "Most Popular"</label>
          </div>
        </main>
        <footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t dark:border-zinc-800">
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSave}>Save Plan</Button>
        </footer>
      </div>
    </div>
  );
};

export default PlanEditorModal;
