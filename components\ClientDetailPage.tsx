import React, { useEffect, useMemo, useState } from 'react';
import { User, Client, Document as DocType, ClientAsset } from '../types';
import { Button } from './ui/Button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from './ui/Card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/Table';
import { ArrowLeftIcon, EditIcon, TrashIcon, DocumentIcon, LinkIcon, PlusCircleIcon, PdfIcon, WordIcon } from './Icons';
import PdfThumbnails from './PdfThumbnails';
import ClientEditorModal from './ClientEditorModal';
import ConfirmationModal from './admin/ConfirmationModal';
import { apiFetch } from '../lib/api';
import ClientAssetModal from './ClientAssetModal';
import { supabase } from '../lib/supabaseClient';

interface ClientDetailPageProps {
  user: User;
  client: Client;
  onBack: () => void;
  onUpdateClient: (clientId: string, updates: Partial<Omit<Client, 'id' | 'createdAt'>>) => void;
  onDeleteClient: (clientId: string) => void;
  onViewDocument: (doc: DocType) => void;
}

const ClientDetailPage: React.FC<ClientDetailPageProps> = ({ user, client, onBack, onUpdateClient, onDeleteClient, onViewDocument }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'documents' | 'assets'>('overview');

  const relatedDocuments = useMemo(() => {
    return user.documents.filter(d => d.clientId === client.id);
  }, [user.documents, client.id]);

  // Client assets state
  const [assets, setAssets] = useState<ClientAsset[]>([]);
  const [assetModalOpen, setAssetModalOpen] = useState(false);
  const [assetToEdit, setAssetToEdit] = useState<ClientAsset | null>(null);
  const [editOpen, setEditOpen] = useState(false);
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [viewerOpen, setViewerOpen] = useState(false);
  const [viewerHref, setViewerHref] = useState('');
  const [viewerType, setViewerType] = useState<null | 'image' | 'pdf'>(null);
  const [viewerOriginalUrl, setViewerOriginalUrl] = useState('');
  const [viewerScale, setViewerScale] = useState(1);
  const [viewerRotation, setViewerRotation] = useState(0);
  const [viewerAssetId, setViewerAssetId] = useState<string | null>(null);

  useEffect(() => {
    // Load assets for this client
    (async () => {
      try {
        const resp = await apiFetch<{ assets: { id: string; client_id: string; name: string; url: string; created_at: string }[] }>(`/api/clients/${client.id}/assets`);
        const rows = resp.assets || [];
        setAssets(rows.map(r => ({ id: r.id, clientId: r.client_id, name: r.name, url: r.url, createdAt: r.created_at })));
      } catch {
        // No-op on failure; possibly no assets yet
        setAssets([]);
      }
    })();
  }, [client.id]);

  const isImageName = (s: string) => /\.(png|jpg|jpeg|gif|webp|svg)$/i.test(s);
  const isPdfName = (s: string) => /\.(pdf)$/i.test(s);
  const getServerSignedUrl = async (assetId: string, ttlSeconds: number) => {
    try {
      const resp = await apiFetch<{ url: string }>(`/api/clients/${client.id}/assets/${assetId}/signed-url?ttl=${ttlSeconds}`);
      return resp.url || '';
    } catch { return ''; }
  };

  return (
    <div className="p-4 sm:p-6 lg:p-8 space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeftIcon className="w-4 h-4 mr-2" /> Back
          </Button>
          <h1 className="text-2xl font-bold text-zinc-900 dark:text-white">{client.name}</h1>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={() => setEditOpen(true)}><EditIcon className="w-4 h-4 mr-2" />Edit</Button>
          <Button variant="destructive" onClick={() => setConfirmDeleteOpen(true)}><TrashIcon className="w-4 h-4 mr-2" />Delete</Button>
        </div>
      </div>

      <div className="flex items-center gap-2 border-b border-zinc-200 dark:border-zinc-800">
        {(
          [
            { key: 'overview', label: 'Overview' },
            { key: 'documents', label: `Documents (${relatedDocuments.length})` },
            { key: 'assets', label: 'Assets' },
          ] as const
        ).map(t => (
          <button
            key={t.key}
            onClick={() => setActiveTab(t.key)}
            className={`px-4 py-2 -mb-px border-b-2 ${activeTab === t.key ? 'border-brand-600 text-brand-700 dark:text-brand-300' : 'border-transparent text-zinc-600 dark:text-zinc-400 hover:text-zinc-800 dark:hover:text-zinc-200'}`}
          >
            {t.label}
          </button>
        ))}
      </div>

      {activeTab === 'overview' && (
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Client Information</CardTitle>
              <CardDescription>Basic details for this client.</CardDescription>
            </CardHeader>
            <CardContent>
              <dl className="grid grid-cols-3 gap-3 text-sm">
                <dt className="text-zinc-500 col-span-1">Name</dt>
                <dd className="col-span-2">{client.name}</dd>
                <dt className="text-zinc-500 col-span-1">Type</dt>
                <dd className="col-span-2">{client.type}</dd>
                <dt className="text-zinc-500 col-span-1">Contact Person</dt>
                <dd className="col-span-2">{client.contactPerson || '—'}</dd>
                <dt className="text-zinc-500 col-span-1">Email</dt>
                <dd className="col-span-2">{client.email || '—'}</dd>
                <dt className="text-zinc-500 col-span-1">Phone</dt>
                <dd className="col-span-2">{client.phone || '—'}</dd>
                <dt className="text-zinc-500 col-span-1">Address</dt>
                <dd className="col-span-2">{client.address || '—'}</dd>
                <dt className="text-zinc-500 col-span-1">Created</dt>
                <dd className="col-span-2">{new Date(client.createdAt).toLocaleString()}</dd>
              </dl>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Summary</CardTitle>
              <CardDescription>Quick stats for this client.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 rounded-lg border border-zinc-200 dark:border-zinc-800">
                  <div className="text-sm text-zinc-500">Documents</div>
                  <div className="mt-1 text-2xl font-bold">{relatedDocuments.length}</div>
                </div>
                <div className="p-4 rounded-lg border border-zinc-200 dark:border-zinc-800">
                  <div className="text-sm text-zinc-500">Folders</div>
                  <div className="mt-1 text-2xl font-bold">{new Set(relatedDocuments.map(d => d.folderId || 'uncategorized')).size}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTab === 'documents' && (
        <Card>
          <CardHeader>
            <CardTitle>Related Documents</CardTitle>
            <CardDescription>Documents linked to this client.</CardDescription>
          </CardHeader>
          <CardContent>
            {relatedDocuments.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="hidden sm:table-cell text-right">Updated</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {relatedDocuments.map(doc => (
                    <TableRow key={doc.id} className="cursor-pointer" onClick={() => onViewDocument(doc)}>
                      <TableCell className="font-medium text-zinc-900 dark:text-zinc-100">
                        <div className="flex items-center gap-2"><DocumentIcon className="w-4 h-4" /> {doc.name}</div>
                      </TableCell>
                      <TableCell>{doc.status}</TableCell>
                      <TableCell className="hidden sm:table-cell text-right">{new Date(doc.updatedAt).toLocaleString()}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-center py-10 text-zinc-500">No documents linked to this client.</div>
            )}
          </CardContent>
        </Card>
      )}

      {activeTab === 'assets' && (
        <Card>
          <CardHeader>
            <CardTitle>Assets</CardTitle>
            <CardDescription>Links or files related to this client.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-4 flex justify-end">
              <Button onClick={() => { setAssetToEdit(null); setAssetModalOpen(true); }}>
                <PlusCircleIcon className="w-5 h-5 mr-2" /> Add Asset
              </Button>
            </div>
            {assets.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-24">Preview</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Link</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {assets.map(a => (
                    <TableRow key={a.id}>
                      <TableCell onClick={async () => {
                        const href = await getServerSignedUrl(a.id, 60 * 60);
                        const lower = (() => {
                          try {
                            if (a.url.startsWith('storage://contractgini/')) {
                              return decodeURIComponent(a.url.substring('storage://contractgini/'.length));
                            }
                            const u = new URL(a.url);
                            return decodeURIComponent(u.pathname);
                          } catch {
                            return (a.url || a.name || '');
                          }
                        })().toLowerCase();
                        const t: null | 'image' | 'pdf' = isImageName(lower) ? 'image' : (isPdfName(lower) ? 'pdf' : null);
                        setViewerType(t);
                        setViewerOriginalUrl(a.url);
                        setViewerAssetId(a.id);
                        setViewerScale(1);
                        setViewerRotation(0);
                        if (href && t) { setViewerHref(href); setViewerOpen(true); }
                      }} className="cursor-pointer">
                        <SignedThumb clientId={client.id} assetId={a.id} url={a.url} name={a.name} />
                      </TableCell>
                      <TableCell className="font-medium">{a.name}</TableCell>
                      <TableCell>
                        <SignedLink clientId={client.id} assetId={a.id} url={a.url} />
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="ghost" size="icon" onClick={() => { setAssetToEdit(a); setAssetModalOpen(true); }} className="h-8 w-8"><EditIcon className="w-4 h-4" /></Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={async () => {
                            // Client-side cleanup (best-effort)
                            try {
                              if (a.url?.startsWith('storage://contractgini/')) {
                                const path = decodeURIComponent(a.url.substring('storage://contractgini/'.length));
                                await supabase.storage.from('contractgini').remove([path]);
                              }
                            } catch {
                              // Ignore storage cleanup errors
                            }
                            try { await apiFetch<void>(`/api/clients/${client.id}/assets/${a.id}`, { method: 'DELETE' }); } catch {
                              // Ignore API deletion errors
                            }
                            setAssets(prev => prev.filter(x => x.id !== a.id));
                          }}
                          className="h-8 w-8 text-red-600 hover:text-red-700"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className="text-sm text-zinc-500">No assets yet.</div>
            )}
          </CardContent>
        </Card>
      )}

      <ClientEditorModal
        isOpen={editOpen}
        onClose={() => setEditOpen(false)}
        existingClient={client}
        onSave={(data) => { onUpdateClient(client.id, data); setEditOpen(false); }}
      />

      <ConfirmationModal
        isOpen={confirmDeleteOpen}
        onClose={() => setConfirmDeleteOpen(false)}
        onConfirm={() => { onDeleteClient(client.id); setConfirmDeleteOpen(false); onBack(); }}
        title="Delete Client"
        message={<span>Are you sure you want to delete <strong>{client.name}</strong>? This will unassign them from any linked documents.</span>}
        confirmText="Delete Client"
      />

      <ClientAssetModal
        isOpen={assetModalOpen}
        onClose={() => setAssetModalOpen(false)}
        existingAsset={assetToEdit}
        onSave={async (payload) => {
          const finalPayload = { name: payload.name, url: payload.url } as { name: string; url?: string };
          if (payload.file) {
            // Validate size (<= 10MB) and MIME type (images, docs, pdf)
            const maxBytes = 10 * 1024 * 1024;
            const allowedTypes = new Set([
              'application/pdf',
              'application/msword',
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            ]);
            const isImage = payload.file.type.startsWith('image/');
            const isAllowed = isImage || allowedTypes.has(payload.file.type);
            if (!isAllowed) {
              alert('Unsupported file type. Allowed: images, .doc, .docx, .pdf');
              return;
            }
            if (payload.file.size > maxBytes) {
              alert('File is too large. Maximum size is 10MB.');
              return;
            }
            // Request signed upload URL from server and perform one-time upload
            try {
              const sign = await apiFetch<{ path: string; token: string }>(`/api/clients/${client.id}/assets/upload-url`, { method: 'POST', body: JSON.stringify({ fileName: payload.file.name }) });
              const { error: uploadErr } = await supabase.storage.from('contractgini').uploadToSignedUrl(sign.path, sign.token, payload.file);
              if (uploadErr) {
                alert(`Upload failed: ${uploadErr.message}`);
                return;
              }
              finalPayload.url = `storage://contractgini/${sign.path}`;
            } catch (_e: unknown) {
                alert(`Upload failed: ${_e instanceof Error ? _e.message : String(_e)}`);
              return;
            }
          }
          if (assetToEdit) {
            try {
              const resp = await apiFetch<{ asset: { id: string; client_id: string; name: string; url: string; created_at: string } }>(`/api/clients/${client.id}/assets/${assetToEdit.id}`, { method: 'PUT', body: JSON.stringify(finalPayload) });
              const row = resp.asset;
              const updated = { id: row.id, clientId: row.client_id, name: row.name, url: row.url, createdAt: row.created_at } as ClientAsset;
              setAssets(prev => prev.map(a => a.id === updated.id ? updated : a));
            } catch {
              alert('Failed to update asset.');
            }
          } else {
            try {
              const resp = await apiFetch<{ asset: { id: string; client_id: string; name: string; url: string; created_at: string } }>(`/api/clients/${client.id}/assets`, { method: 'POST', body: JSON.stringify(finalPayload) });
              const row = resp.asset;
              setAssets(prev => [{ id: row.id, clientId: row.client_id, name: row.name, url: row.url, createdAt: row.created_at }, ...prev]);
            } catch (e: unknown) {
              const msg = e instanceof Error ? e.message : 'Create asset failed';
              alert(msg);
            }
          }
          setAssetModalOpen(false);
          setAssetToEdit(null);
        }}
      />

      {viewerOpen && viewerType && (
        <div className="fixed inset-0 z-50 bg-black/70 flex items-center justify-center p-6" onClick={() => setViewerOpen(false)}>
          <div className="relative bg-white dark:bg-zinc-900 rounded-2xl shadow-2xl max-w-6xl w-full h-[85vh] overflow-hidden" onClick={(e) => e.stopPropagation()}>
            <div className="absolute top-2 right-2 flex gap-2">
              <Button variant="outline" size="icon" onClick={() => setViewerScale(s => Math.min(3, s + 0.1))}><span className="font-bold">+</span></Button>
              <Button variant="outline" size="icon" onClick={() => setViewerScale(s => Math.max(0.2, s - 0.1))}><span className="font-bold">-</span></Button>
              <Button variant="outline" size="icon" onClick={() => setViewerRotation(r => (r - 90) % 360)}>↺</Button>
              <Button variant="outline" size="icon" onClick={() => setViewerRotation(r => (r + 90) % 360)}>↻</Button>
              <Button variant="outline" size="icon" onClick={() => { setViewerScale(1); setViewerRotation(0); }}>⤾</Button>
              <Button variant="ghost" size="icon" onClick={() => setViewerOpen(false)}><span className="font-bold">×</span></Button>
            </div>
            <div className="flex h-full">
              {viewerType === 'pdf' && (
                <PdfThumbnails clientId={client.id} assetId={viewerAssetId || ''} url={viewerOriginalUrl} onSelect={async (page) => {
                  // Re-generate signed url and set page anchor
                  const href = viewerAssetId ? await getServerSignedUrl(viewerAssetId, 60 * 60) : '';
                  if (href) {setViewerHref(`${href}#page=${page}`);}
                }} />
              )}
              <div className="flex-1 overflow-auto bg-zinc-50 dark:bg-zinc-900">
                <div className="w-full h-full flex items-center justify-center p-4">
                  <div style={{ transform: `scale(${viewerScale}) rotate(${viewerRotation}deg)`, transformOrigin: 'center center' }} className="transition-transform">
                    {viewerType === 'image' && (
                      <img src={viewerHref} alt="preview" className="max-w-full max-h-[70vh] object-contain" />
                    )}
                    {viewerType === 'pdf' && (
                      <iframe src={viewerHref} title="PDF Preview" className="w-[900px] h-[70vh] rounded-b-2xl bg-white" />
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Helper component to present a signed URL for private storage assets
const SignedLink: React.FC<{ clientId: string; assetId: string; url: string }> = ({ clientId, assetId, url }) => {
  const [href, setHref] = React.useState<string>('');
  // TTL 1 hour; auto-refresh every 55 minutes
  const TTL = 60 * 60;
  React.useEffect(() => {
    let mounted = true;
    const gen = async () => {
      try {
        if (url && url.startsWith('storage://contractgini/')) {
          const resp = await apiFetch<{ url: string }>(`/api/clients/${clientId}/assets/${assetId}/signed-url?ttl=${TTL}`);
          if (mounted) {setHref(resp.url || '');}
        } else {
          if (mounted) {setHref(url);}
        }
      } catch {
        if (mounted) {setHref('');}
      }
    };
    gen();
    // refresh slightly before expiry
    const interval = setInterval(gen, Math.max(1, (TTL - 300)) * 1000);
    return () => { mounted = false; clearInterval(interval); };
  }, [clientId, assetId, url]);
  if (!href) {return <span className="text-zinc-500">(unavailable)</span>;}
  return (
    <a href={href} target="_blank" rel="noreferrer" className="inline-flex items-center text-brand-600 hover:underline">
      <LinkIcon className="w-4 h-4 mr-1" /> Open
    </a>
  );
};

const SignedThumb: React.FC<{ clientId: string; assetId: string; url: string; name?: string }> = ({ clientId, assetId, url, name }) => {
  const [href, setHref] = React.useState<string>('');
  const isImage = (p: string) => /(\.png|\.jpg|\.jpeg|\.gif|\.webp|\.svg)$/i.test(p);
  const isPdf = (p: string) => /(\.pdf)$/i.test(p);
  const isDoc = (p: string) => /(\.doc|\.docx)$/i.test(p);
  React.useEffect(() => {
    let mounted = true;
    (async () => {
      try {
        if (url && url.startsWith('storage://contractgini/')) {
          const resp = await apiFetch<{ url: string }>(`/api/clients/${clientId}/assets/${assetId}/signed-url?ttl=${60 * 15}`);
          if (mounted) {setHref(resp.url || '');}
        } else {
          if (mounted) {setHref(url);}
        }
      } catch { if (mounted) {setHref('');} }
    })();
    return () => { mounted = false; };
  }, [clientId, assetId, url]);
  const text = (() => {
    try {
      if (url.startsWith('storage://contractgini/')) {
        return decodeURIComponent(url.substring('storage://contractgini/'.length)).toLowerCase();
      }
      const u = new URL(url);
      return decodeURIComponent(u.pathname).toLowerCase();
    } catch {
      return (url || name || '').toLowerCase();
    }
  })();
  if (href && isImage(text)) {
    return <img src={href} alt={name || 'asset'} className="h-12 w-20 object-cover rounded border border-zinc-200 dark:border-zinc-800" />;
  }
  if (isPdf(text)) {return <div className="flex items-center justify-center h-12 w-20"><PdfIcon className="w-6 h-6 text-red-600" /></div>;}
  if (isDoc(text)) {return <div className="flex items-center justify-center h-12 w-20"><WordIcon className="w-6 h-6 text-blue-600" /></div>;}
  return <div className="flex items-center justify-center h-12 w-20"><DocumentIcon className="w-6 h-6 text-zinc-500" /></div>;
};

export default ClientDetailPage;
