// @vitest-environment jsdom
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import AdminPage from '../components/AdminPage';

describe('AdminPage', () => {
  it('renders AdminPage component', () => {
    const allUsers = [
      { id: 'u1', email: '<EMAIL>', planName: 'Pro', name: 'Owner', documents: [], folders: [] }
    ];
    const allTeams = [
      { id: 't1', name: 'Team 1', ownerId: 'u1', members: [] }
    ];
    render(<AdminPage allUsers={allUsers as any} allTeams={allTeams as any} />);
    expect(screen.getByText(/Admin Dashboard/i)).toBeInTheDocument();
    expect(screen.getByText(/All Tenants/i)).toBeInTheDocument();
    expect(screen.getByText('Team 1')).toBeInTheDocument();
  });
});
