import React, { useState } from 'react';
import { Button } from './ui/Button';
import { CloseIcon, SaveIcon } from './Icons';

interface SaveTemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (templateName: string) => void;
}

const SaveTemplateModal: React.FC<SaveTemplateModalProps> = ({ isOpen, onClose, onSave }) => {
  const [templateName, setTemplateName] = useState('');

  if (!isOpen) {return null;}

  const handleSaveClick = () => {
    if (templateName.trim()) {
      onSave(templateName.trim());
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-md">
        <header className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold text-slate-800 flex items-center">
            <SaveIcon className="w-5 h-5 mr-2 text-blue-700" />
            Save as Template
          </h2>
          <button onClick={onClose} className="p-2 text-slate-500 hover:bg-slate-100 rounded-lg">
            <CloseIcon className="w-6 h-6" />
          </button>
        </header>
        <main className="p-6">
          <p className="text-sm text-slate-600 mb-4">
            Save the current document as a personal template for easy reuse.
          </p>
          <label htmlFor="templateName" className="block text-sm font-medium text-slate-700 mb-1">
            Template Name
          </label>
          <input
            type="text"
            id="templateName"
            value={templateName}
            onChange={(e) => setTemplateName(e.target.value)}
            placeholder="e.g., My Standard NDA"
            className="block w-full rounded-md border-slate-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm p-2"
            autoFocus
          />
        </main>
        <footer className="p-4 bg-slate-50 flex justify-end gap-3 rounded-b-2xl">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSaveClick} disabled={!templateName.trim()}>
            Save Template
          </Button>
        </footer>
      </div>
    </div>
  );
};

export default SaveTemplateModal;