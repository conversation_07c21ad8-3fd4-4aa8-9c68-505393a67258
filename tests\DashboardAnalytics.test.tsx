// @vitest-environment jsdom
import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import DashboardAnalytics from '../components/DashboardAnalytics';
import React from 'react';

describe('DashboardAnalytics', () => {
  it('renders DashboardAnalytics component with all props', () => {
    const user = {
      id: 'u1',
      email: '<EMAIL>',
      password: '',
      isVerified: true,
      status: 'active',
      documents: [{ id: 'd1', name: 'Doc1', content: '', createdAt: '', updatedAt: '', versions: [], collaborators: [], commentThreads: [], status: 'draft', signatures: [] }],
      folders: [{ id: 'f1', name: 'Folder1' }],
      quotaUsed: 5,
      quotaTotal: 10,
      planName: 'Free',
      clients: [],
      customTemplates: [],
    };
    const setView = vi.fn();
    const publicTemplates = [{ id: 't1', title: 'Template1', description: '', category: '', prompt: '', requiredPlan: 'Registered User' }];
    const { container } = render(
      <DashboardAnalytics user={user as any} setView={setView} publicTemplates={publicTemplates as any} />
    );
    expect(container).toBeInTheDocument();
    expect(screen.getByText(/Total Documents/i)).toBeInTheDocument();
    expect(screen.getByText(/Folders Created/i)).toBeInTheDocument();
    expect(screen.getByText(/Templates Used/i)).toBeInTheDocument();
    expect(screen.getByText(/Monthly Quota/i)).toBeInTheDocument();
  });
});
