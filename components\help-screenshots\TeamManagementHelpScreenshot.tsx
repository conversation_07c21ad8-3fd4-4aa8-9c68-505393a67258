
import React from 'react';
import Indicator from './Indicator';
import { PlusCircleIcon } from '../Icons';

const TeamManagementHelpScreenshot: React.FC = () => {
    return (
        <div className="relative my-6 p-4 border rounded-lg bg-zinc-50 dark:bg-zinc-900 overflow-hidden select-none text-xs">
            <Indicator number={1} position="top-8 right-8" arrow="down" />
            <div className="flex justify-between items-center mb-4">
                <div>
                    <h1 className="text-lg font-bold text-zinc-900 dark:text-zinc-100">Team Management</h1>
                    <p className="text-zinc-600 dark:text-zinc-400 mt-1">Invite and manage members.</p>
                </div>
                <div className="bg-brand-600 text-white font-semibold px-3 py-1.5 rounded-lg shadow flex items-center">
                    <PlusCircleIcon className="w-4 h-4 mr-1"/> Invite Member
                </div>
            </div>

            <div className="bg-white dark:bg-zinc-800 rounded-lg shadow border border-zinc-200 dark:border-zinc-700 p-3">
                 <div className="w-full h-24 border-2 border-dashed border-zinc-200 dark:border-zinc-700 rounded-md"></div>
            </div>
        </div>
    );
};

export default TeamManagementHelpScreenshot;
