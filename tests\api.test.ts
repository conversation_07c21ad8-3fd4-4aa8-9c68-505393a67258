import { describe, expect, it, vi } from 'vitest';
import { apiFetch, setAccessTokenGetter } from '../lib/api';

describe('apiFetch', () => {
  it('calls fetch with correct arguments and returns data', async () => {
    const mockResponse = { foo: 'bar' };
    global.fetch = vi.fn().mockResolvedValue({
      ok: true,
      json: async () => mockResponse,
      text: async () => JSON.stringify(mockResponse),
      headers: new Map([['content-type', 'application/json']]),
    });
    const result = await apiFetch('/test');
    expect(result).toEqual(mockResponse);
    expect(global.fetch).toHaveBeenCalledWith(expect.any(String), expect.objectContaining({ headers: expect.any(Object) }));
  });

    it('throws error if response is not ok', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: async () => ({}),
        text: async () => 'API request failed: 500 Internal Server Error',
      });
      await expect(apiFetch('/fail')).rejects.toThrow('API request failed: 500 Internal Server Error');
    });
});

describe('setAccessTokenGetter', () => {
  it('sets the access token getter function', async () => {
    let called = false;
    // Patch globalThis to ensure __accessTokenGetter is set
    Object.defineProperty(globalThis, '__accessTokenGetter', {
      value: async () => {
        called = true;
        return 'token';
      },
      configurable: true,
      writable: true,
    });
    setAccessTokenGetter(globalThis.__accessTokenGetter);
    expect(typeof globalThis.__accessTokenGetter).toBe('function');
    await globalThis.__accessTokenGetter();
    expect(called).toBe(true);
  });
});
