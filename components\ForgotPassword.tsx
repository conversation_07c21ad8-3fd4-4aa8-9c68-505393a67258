

import React, { useState } from 'react';
import { MailIcon, UserIcon } from './Icons';

interface ForgotPasswordProps {
  onSwitchToLogin: () => void;
  handleForgotPassword: (email: string) => boolean;
  authError: string | null;
}

const ForgotPassword: React.FC<ForgotPasswordProps> = ({ onSwitchToLogin, handleForgotPassword, authError }) => {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (handleForgotPassword(email)) {
        setIsSubmitted(true);
    }
  };

  if (isSubmitted) {
    return (
       <div className="text-center">
            <MailIcon className="mx-auto h-12 w-12 text-brand-500" />
            <h2 className="mt-4 text-2xl font-bold text-zinc-900 dark:text-white">Check your inbox</h2>
            <p className="mt-2 text-zinc-600 dark:text-zinc-400">
                If an account with the email <span className="font-semibold text-zinc-800 dark:text-zinc-200">{email}</span> exists, we've sent a link to reset your password.
            </p>
            <p className="mt-6 text-center text-sm text-zinc-600 dark:text-zinc-400">
                <button onClick={onSwitchToLogin} className="font-medium text-brand-600 hover:text-brand-700 focus:outline-none">
                    Back to Login
                </button>
            </p>
        </div>
    );
  }

  return (
    <div className="w-full max-w-md">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-zinc-900 dark:text-white">Forgot Password</h2>
        <p className="text-zinc-500 dark:text-zinc-400 mt-2">Enter your email and we'll send you a reset link.</p>
      </div>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="email-forgot" className="block text-sm font-medium text-zinc-700 dark:text-zinc-300">Email Address</label>
          <div className="mt-1 relative rounded-md shadow-sm">
             <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <UserIcon className="h-5 w-5 text-zinc-400" />
            </div>
            <input 
              type="email" 
              id="email-forgot" 
              name="email" 
              autoComplete="email"
              required 
              className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 focus:border-brand-500 focus:ring-brand-500 sm:text-sm p-3" 
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>
        </div>
        {authError && <p className="text-sm text-red-600 text-center">{authError}</p>}
        <div>
          <button type="submit" className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-brand-600 hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500">
            Send Reset Link
          </button>
        </div>
      </form>
       <p className="mt-6 text-center text-sm text-zinc-600 dark:text-zinc-400">
        Remember your password?{' '}
        <button onClick={onSwitchToLogin} className="font-medium text-brand-600 hover:text-brand-700 focus:outline-none">
          Log in
        </button>
      </p>
    </div>
  );
};
export default ForgotPassword;