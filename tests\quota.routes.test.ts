import { describe, it, expect, beforeAll, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

const events: { created_at: string }[] = [];

vi.mock('../server/supabaseClient', () => {
  const getUserClient = (_token: string) => ({
    auth: { getUser: async () => ({ data: { user: { id: 'test-user' } }, error: null }) },
    from: (table: string) => ({
      select: (_cols: string, _opts?: unknown) => {
        if (table === 'profiles') {
          return {
            eq: (_c: string, _v: unknown) => ({ single: async () => ({ data: { plan_name: 'Registered User' }, error: null }) }),
          };
        }
        if (table === 'usage_events') {
          return {
            gte: async (_c: string, iso: string) => {
              const count = events.filter(e => new Date(e.created_at) >= new Date(iso)).length;
              return { count, error: null };
            },
          };
        }
        return {};
      },
    }),
  });
  return { getUserClient };
});

beforeAll(() => { process.env.TEST_BYPASS_AUTH = '1'; });
beforeEach(() => { vi.useFakeTimers(); });
afterEach(() => { vi.useRealTimers(); });

describe('Quota route', () => {
  it('counts only current month events', async () => {
    vi.setSystemTime(new Date('2023-05-15T12:00:00Z'));
    events.splice(0, events.length,
      { created_at: '2023-04-30T23:59:59Z' },
      { created_at: '2023-05-01T00:00:00Z' },
      { created_at: '2023-05-15T12:00:00Z' },
    );
    const res = await request(app).get('/api/quota');
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('planName');
    expect(res.body.used).toBe(3);
  });

  it('resets counts on new month boundary', async () => {
    vi.setSystemTime(new Date('2023-06-01T00:00:00Z'));
    events.splice(0, events.length,
      { created_at: '2023-05-31T23:59:59Z' },
      { created_at: '2023-06-01T00:00:00Z' },
    );
    const res = await request(app).get('/api/quota');
    expect(res.status).toBe(200);
    expect(res.body.used).toBe(2);
  });
});
