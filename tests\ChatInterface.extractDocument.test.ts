import { describe, it, expect } from 'vitest';

// Extract the function for testing by creating a test instance
const createTestExtractFunction = () => {
  return (raw: string): { cleaned: string; hadMarkers: boolean; preContractText?: string; hasStageMarker?: boolean; initialReply?: string; result?: string; hasTwoSections?: boolean } => {
    const contractStartMarker = '---CONTRACT START---';
    const contractEndMarker = '---CONTRACT END---';
    const stageMarker = '---STAGE 1 COMPLETE---';
    
    // Check for stage marker first
    if (raw.includes(stageMarker)) {
      const stage1Text = raw.substring(0, raw.indexOf(stageMarker)).trim();
      const remainingText = raw.substring(raw.indexOf(stageMarker) + stageMarker.length).trim();
      
      // Check if remaining text has contract markers
      if (remainingText.includes(contractStartMarker) && remainingText.includes(contractEndMarker)) {
        const contractContent = remainingText.substring(
          remainingText.indexOf(contractStartMarker) + contractStartMarker.length, 
          remainingText.indexOf(contractEndMarker)
        ).trim();
        return { 
          cleaned: contractContent, 
          hadMarkers: true, 
          preContractText: stage1Text || undefined,
          hasStageMarker: true
        };
      }
      
      return {
        cleaned: remainingText || raw.trim(),
        hadMarkers: false,
        preContractText: stage1Text || undefined,
        hasStageMarker: true
      };
    }
    
    // Check for contract markers
    if (raw.includes(contractStartMarker) && raw.includes(contractEndMarker)) {
      const preContractText = raw.substring(0, raw.indexOf(contractStartMarker)).trim();
      let contractContent = raw.substring(raw.indexOf(contractStartMarker) + contractStartMarker.length, raw.indexOf(contractEndMarker)).trim();
      
      // Simply trim the contract content without regex filtering
      contractContent = contractContent.trim();
        
      return { cleaned: contractContent, hadMarkers: true, preContractText: preContractText || undefined };
    }
    
    // Check for two-section structure patterns
    const twoSectionPatterns = [
      /^(.+?)\n\n(?:Result:|Output:|Final Result:|Document:|Generated Content:)\s*\n\n(.+)$/s,
      /^(.+?)\n\n(?:---\s*)?(?:Result|Output|Final Result|Document|Generated Content)(?:\s*---)\s*\n\n(.+)$/s,
      /^(.+?)\n\n(?:\*\*)?(?:Result|Output|Final Result|Document|Generated Content)(?:\*\*)?:?\s*\n\n(.+)$/s
    ];
    
    for (const pattern of twoSectionPatterns) {
      const match = raw.match(pattern);
      if (match && match[1] && match[2]) {
        const initialReply = match[1].trim();
        const result = match[2].trim();
        
        // Ensure both sections have substantial content
        if (initialReply.length > 10 && result.length > 10) {
          return {
            cleaned: raw.trim(),
            hadMarkers: false,
            initialReply,
            result,
            hasTwoSections: true
          };
        }
      }
    }
    
    // Apply disclaimer removal and intro text cleaning
    let cleaned = raw;
    
    // Remove AI introductory phrases
    const introPatterns = [
      /^Okay, here's a simple[^\n]*\n\n/i,
      /^Here is a basic[^\n]*\n\n/i,
      /^I will help you create[^\n]*\n\n/i
    ];
    
    for (const pattern of introPatterns) {
      cleaned = cleaned.replace(pattern, '');
    }
    
    // Remove disclaimer sections (both mid-text and at start)
    const disclaimerPatterns = [
      /\n\nImportant Disclaimer:[^\n]*(?:\n(?!#)[^\n]*)*(?=\n\n|$)/gi,
      /\n\nPlease note:[^\n]*(?:\n(?!#)[^\n]*)*(?=\n\n|$)/gi,
      /\n\n\*\*Disclaimer\*\*:[^\n]*(?:\n(?!#)[^\n]*)*(?=\n\n|$)/gi,
      /^Important Disclaimer:[^\n]*(?:\n(?!#)[^\n]*)*(?=\n\n|$)/gi,
      /^Please note:[^\n]*(?:\n(?!#)[^\n]*)*(?=\n\n|$)/gi,
      /^\*\*Disclaimer\*\*:[^\n]*(?:\n(?!#)[^\n]*)*(?=\n\n|$)/gi
    ];

    for (const pattern of disclaimerPatterns) {
      cleaned = cleaned.replace(pattern, '');
    }
    
    // Fallback to original if cleaning results in empty string
    if (cleaned.trim() === '') {
      cleaned = raw;
    }
    
    return { cleaned: cleaned.trim(), hadMarkers: false };
  };
};

describe('ChatInterface - extractDocumentFromResponse', () => {
  const extractDocumentFromResponse = createTestExtractFunction();

  describe('Contract markers handling', () => {
    it('should extract content between CONTRACT START/END markers', () => {
      const input = `Some intro text
---CONTRACT START---
Actual contract content
More contract content
---CONTRACT END---
Some outro text`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hadMarkers).toBe(true);
      expect(result.cleaned).toBe('Actual contract content\nMore contract content');
    });

    it('should handle empty content between markers', () => {
      const input = `---CONTRACT START---


---CONTRACT END---`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hadMarkers).toBe(true);
      expect(result.cleaned).toBe('');
    });

    it('should extract pre-contract text when markers are present', () => {
      const input = `Here's your NDA with important disclaimers.

Important Note: This is a basic template. Please consult with a legal professional.

---CONTRACT START---
# NON-DISCLOSURE AGREEMENT

This agreement is between...
---CONTRACT END---`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hadMarkers).toBe(true);
      expect(result.cleaned).toBe('# NON-DISCLOSURE AGREEMENT\n\nThis agreement is between...');
      expect(result.preContractText).toBe('Here\'s your NDA with important disclaimers.\n\nImportant Note: This is a basic template. Please consult with a legal professional.');
    });
  });

  describe('Stage marker handling', () => {
    it('should handle stage marker with contract markers', () => {
      const input = `I'll help you create an NDA. Here are some important disclaimers:

- This is a template and should be reviewed by legal counsel
- Customize it for your specific needs

---STAGE 1 COMPLETE---

---CONTRACT START---
# NON-DISCLOSURE AGREEMENT

This agreement is between the parties...
---CONTRACT END---`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hasStageMarker).toBe(true);
      expect(result.hadMarkers).toBe(true);
      expect(result.cleaned).toBe('# NON-DISCLOSURE AGREEMENT\n\nThis agreement is between the parties...');
      expect(result.preContractText).toBe('I\'ll help you create an NDA. Here are some important disclaimers:\n\n- This is a template and should be reviewed by legal counsel\n- Customize it for your specific needs');
    });

    it('should handle stage marker without contract markers', () => {
      const input = `I understand you need help with a contract clause. Here are some considerations:

- Make sure to include termination conditions
- Consider jurisdiction requirements

---STAGE 1 COMPLETE---

## Termination Clause

Either party may terminate this agreement with 30 days written notice.`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hasStageMarker).toBe(true);
      expect(result.hadMarkers).toBe(false);
      expect(result.cleaned).toBe('## Termination Clause\n\nEither party may terminate this agreement with 30 days written notice.');
      expect(result.preContractText).toBe('I understand you need help with a contract clause. Here are some considerations:\n\n- Make sure to include termination conditions\n- Consider jurisdiction requirements');
    });

    it('should handle stage marker with empty stage 2', () => {
      const input = `I'll help you with that request. Please note:

- This is for informational purposes only
- Consult legal counsel for specific advice

---STAGE 1 COMPLETE---

`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hasStageMarker).toBe(true);
      expect(result.hadMarkers).toBe(false);
      expect(result.preContractText).toBe('I\'ll help you with that request. Please note:\n\n- This is for informational purposes only\n- Consult legal counsel for specific advice');
    });
  });

  describe('AI introductory text removal', () => {
    it('should remove "Okay, here\'s a simple" introductory text', () => {
      const input = `Okay, here's a simple, straightforward Non-Disclosure Agreement (NDA).\n\n# NON-DISCLOSURE AGREEMENT\n\nThis agreement...`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.cleaned).toBe('# NON-DISCLOSURE AGREEMENT\n\nThis agreement...');
    });

    it('should remove "Here is a basic" introductory text', () => {
      const input = `Here is a basic contract template for you.\n\n# CONTRACT\n\nThis contract...`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.cleaned).toBe('# CONTRACT\n\nThis contract...');
    });

    it('should remove "I will help you create" introductory text', () => {
       const input = `I will help you create a simple NDA.\n\n# NON-DISCLOSURE AGREEMENT\n\nThis agreement...`;
       const result = extractDocumentFromResponse(input);
       
       expect(result.cleaned).toBe('# NON-DISCLOSURE AGREEMENT\n\nThis agreement...');
     });

     it('should remove the exact user-reported introductory text and disclaimer', () => {
       const input = `Okay, here's a simple, straightforward Non-Disclosure Agreement (NDA).\n\nImportant Disclaimer: This is a basic template and may not be suitable for all situations. It is always recommended to consult with a legal professional to ensure an NDA meets your specific needs and complies with applicable laws in your jurisdiction.\n\n---\n\n# NON-DISCLOSURE AGREEMENT\n\nThis agreement...`;
       const result = extractDocumentFromResponse(input);
       
       expect(result.cleaned).toBe('# NON-DISCLOSURE AGREEMENT\n\nThis agreement...');
     });

    it('should only trim whitespace from content', () => {
      const input = `\n\n  # EMPLOYMENT CONTRACT\n\nEmployee: [Name]...  \n\n`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.cleaned).toBe('# EMPLOYMENT CONTRACT\n\nEmployee: [Name]...');
    });
  });

  describe('Disclaimer removal', () => {
    it('should remove "Important Disclaimer" sections', () => {
      const input = `# CONTRACT\n\nImportant Disclaimer: This is a basic template and may not be suitable for all situations. It is always recommended to consult with a legal professional.\n\n# ACTUAL CONTRACT\n\nContent here...`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.cleaned).toBe('# CONTRACT\n\n# ACTUAL CONTRACT\n\nContent here...');
    });

    it('should remove "Please note" disclaimer sections', () => {
      const input = `# AGREEMENT\n\nPlease note: This template requires customization for specific legal requirements.\n\n# MAIN AGREEMENT\n\nTerms and conditions...`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.cleaned).toBe('# AGREEMENT\n\n# MAIN AGREEMENT\n\nTerms and conditions...');
    });

    it('should remove markdown formatted disclaimers', () => {
      const input = `# CONTRACT\n\n**Disclaimer**: This is a standardized template.\n\n# ACTUAL CONTRACT\n\nContent here...`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.cleaned).toBe('# CONTRACT\n\n# ACTUAL CONTRACT\n\nContent here...');
    });
  });

  describe('HTML and Markdown preservation', () => {
    it('should preserve HTML content as-is', () => {
      const input = `<h1>CONTRACT TITLE</h1>\n<p>Contract content...</p>`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.cleaned).toBe('<h1>CONTRACT TITLE</h1>\n<p>Contract content...</p>');
    });

    it('should preserve markdown dividers', () => {
      const input = `# ACTUAL CONTRACT\n\n---\n\nContract content...`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.cleaned).toBe('# ACTUAL CONTRACT\n\n---\n\nContract content...');
    });
  });

  describe('Simplified behavior scenarios', () => {
    it('should preserve all content as-is (AI generates clean documents)', () => {
      const input = `# NON-DISCLOSURE AGREEMENT\n\nThis agreement...`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.cleaned).toBe('# NON-DISCLOSURE AGREEMENT\n\nThis agreement...');
    });

    it('should only trim whitespace from complex content', () => {
      const input = `\n\n  # PARTNERSHIP AGREEMENT\n\nPartnership details...  \n\n`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.cleaned).toBe('# PARTNERSHIP AGREEMENT\n\nPartnership details...');
    });

    it('should preserve content when no patterns match', () => {
      const input = `# CLEAN CONTRACT\n\nThis contract has no intro or disclaimer patterns.\n\nTerms and conditions apply.`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.cleaned).toBe(input);
    });

    it('should handle empty or whitespace-only input', () => {
      const result1 = extractDocumentFromResponse('');
      const result2 = extractDocumentFromResponse('   \n\n   ');
      
      expect(result1.cleaned).toBe('');
      expect(result2.cleaned).toBe('');
    });

    it('should fallback to original content if cleaning results in empty string', () => {
      const input = `Here's a template:\n\n`; // Only intro, no actual content
      const result = extractDocumentFromResponse(input);
      
      expect(result.cleaned).toBe(input.trim());
    });
  });

  describe('Simplified content preservation', () => {
    it('should preserve content as-is regardless of case (AI generates clean content)', () => {
      const input = `# CONTRACT`;
      const result = extractDocumentFromResponse(input);
      expect(result.cleaned).toBe('# CONTRACT');
    });

    it('should preserve all content including any context (trusting AI)', () => {
      const input = `# CONTRACT\n\nContract content here...`;
      const result = extractDocumentFromResponse(input);
      expect(result.cleaned).toBe('# CONTRACT\n\nContract content here...');
    });

    it('should preserve NDA template content as-is (trusting AI to generate clean content)', () => {
      const input = `Non-Disclosure Agreement (NDA) Template
This document serves as a basic confidentiality agreement template for general use cases. Please note this is a standardized template and may require customization for specific legal requirements. We strongly recommend consulting legal counsel for matters of significant importance.

---

# NON-DISCLOSURE AGREEMENT

This Agreement is entered into on [DATE] between [DISCLOSING PARTY NAME], a [STATE] [ENTITY TYPE] ("Disclosing Party"), and [RECEIVING PARTY NAME], a [STATE] [ENTITY TYPE] ("Receiving Party").

## 1. PURPOSE

The parties wish to explore [BUSINESS PURPOSE] and in connection with such discussions, Disclosing Party may disclose certain confidential information to Receiving Party.

Template Usage Instructions:

1. Complete Required Fields: Insert all relevant information in place of the bracketed \`[ ]\` placeholders.

2. Effective Date: Specify the commencement date when this agreement becomes legally binding.

Legal Disclaimer: This template is provided for informational purposes only and does not constitute legal advice. Consult qualified legal professionals to ensure compliance with applicable laws and to address specific contractual needs.`;
      
      const result = extractDocumentFromResponse(input);
      
      expect(result.cleaned).toContain('# NON-DISCLOSURE AGREEMENT');
      expect(result.cleaned).toContain('## 1. PURPOSE');
      expect(result.cleaned).toContain('Template Usage Instructions');
      expect(result.cleaned).toContain('Legal Disclaimer');
      expect(result.cleaned).toContain('Complete Required Fields');
    });
  });

  describe('Contract marker extraction', () => {
    it('should extract contract content with pre-contract text', () => {
      const input = `I'll create a simple NDA for you.

---CONTRACT START---
# NON-DISCLOSURE AGREEMENT

This agreement is between...
---CONTRACT END---`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hadMarkers).toBe(true);
      expect(result.preContractText).toBe(`I'll create a simple NDA for you.`);
      expect(result.cleaned).toBe(`# NON-DISCLOSURE AGREEMENT

This agreement is between...`);
    });

    it('should handle contract markers without pre-contract text', () => {
      const input = `---CONTRACT START---
# SERVICE AGREEMENT

This contract outlines...
---CONTRACT END---`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hadMarkers).toBe(true);
      expect(result.preContractText).toBeUndefined();
      expect(result.cleaned).toBe(`# SERVICE AGREEMENT

This contract outlines...`);
    });

    it('should preserve all content between contract markers including introductory text', () => {
      const input = `---CONTRACT START---
Okay, here's a draft for a simple Non-Disclosure Agreement (NDA).

Important Note: This is a basic template and may not cover all specific situations or legal requirements in every jurisdiction. It's always recommended to consult with a legal professional to ensure an NDA meets your specific needs and complies with applicable laws.

---

# NON-DISCLOSURE AGREEMENT

This agreement is between...
---CONTRACT END---`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hadMarkers).toBe(true);
      expect(result.cleaned).toBe(`Okay, here's a draft for a simple Non-Disclosure Agreement (NDA).

Important Note: This is a basic template and may not cover all specific situations or legal requirements in every jurisdiction. It's always recommended to consult with a legal professional to ensure an NDA meets your specific needs and complies with applicable laws.

---

# NON-DISCLOSURE AGREEMENT

This agreement is between...`);
    });

    it('should preserve introductory text in contract content', () => {
      const input = `---CONTRACT START---
Here's a basic template for your contract needs.

# SERVICE AGREEMENT

This contract outlines...
---CONTRACT END---`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hadMarkers).toBe(true);
      expect(result.cleaned).toBe(`Here's a basic template for your contract needs.

# SERVICE AGREEMENT

This contract outlines...`);
    });

    it('should preserve disclaimer text in contract content', () => {
      const input = `---CONTRACT START---
This is a basic draft and you should consult with legal professionals.

# EMPLOYMENT CONTRACT

This agreement is made...
---CONTRACT END---`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hadMarkers).toBe(true);
      expect(result.cleaned).toBe(`This is a basic draft and you should consult with legal professionals.

# EMPLOYMENT CONTRACT

This agreement is made...`);
    });

    it('should return original content when no markers found', () => {
      const input = `# EMPLOYMENT CONTRACT

This contract is between...`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hadMarkers).toBe(false);
      expect(result.preContractText).toBeUndefined();
      expect(result.cleaned).toBe(`# EMPLOYMENT CONTRACT

This contract is between...`);
    });
  });

  describe('Two-section structure handling', () => {
    it('should detect and parse basic two-section structure with "Result:"', () => {
      const input = `This is my initial response to your question about contracts.

I'll analyze the requirements and provide a comprehensive solution.

Result:

# EMPLOYMENT CONTRACT

This agreement is made between the Company and Employee...`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hasTwoSections).toBe(true);
      expect(result.hadMarkers).toBe(false);
      expect(result.initialReply).toBe('This is my initial response to your question about contracts.\n\nI\'ll analyze the requirements and provide a comprehensive solution.');
      expect(result.result).toBe('# EMPLOYMENT CONTRACT\n\nThis agreement is made between the Company and Employee...');
    });

    it('should detect and parse two-section structure with "Output:"', () => {
      const input = `I understand you need a legal document. Let me create that for you.

Output:

**TERMS OF SERVICE**

By using this service, you agree to...`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hasTwoSections).toBe(true);
      expect(result.initialReply).toBe('I understand you need a legal document. Let me create that for you.');
      expect(result.result).toBe('**TERMS OF SERVICE**\n\nBy using this service, you agree to...');
    });

    it('should detect and parse two-section structure with "Final Result:"', () => {
      const input = `Here's my analysis of your request for a privacy policy.

Final Result:

PRIVACY POLICY

We collect and process personal data...`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hasTwoSections).toBe(true);
      expect(result.initialReply).toBe('Here\'s my analysis of your request for a privacy policy.');
      expect(result.result).toBe('PRIVACY POLICY\n\nWe collect and process personal data...');
    });

    it('should detect and parse two-section structure with "Document:"', () => {
      const input = `I'll help you create a comprehensive agreement.

Document:

SERVICE AGREEMENT

This service agreement...`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hasTwoSections).toBe(true);
      expect(result.initialReply).toBe('I\'ll help you create a comprehensive agreement.');
      expect(result.result).toBe('SERVICE AGREEMENT\n\nThis service agreement...');
    });

    it('should detect and parse two-section structure with "Generated Content:"', () => {
      const input = `Based on your requirements, I'll generate the requested document.

Generated Content:

NON-DISCLOSURE AGREEMENT

This NDA is entered into...`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hasTwoSections).toBe(true);
      expect(result.initialReply).toBe('Based on your requirements, I\'ll generate the requested document.');
      expect(result.result).toBe('NON-DISCLOSURE AGREEMENT\n\nThis NDA is entered into...');
    });

    it('should detect two-section structure with markdown formatting', () => {
      const input = `I'll create the contract you requested.

**Result**:

# CONSULTING AGREEMENT

This agreement governs...`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hasTwoSections).toBe(true);
      expect(result.initialReply).toBe('I\'ll create the contract you requested.');
      expect(result.result).toBe('# CONSULTING AGREEMENT\n\nThis agreement governs...');
    });

    it('should detect two-section structure with dashes', () => {
      const input = `Let me draft this for you.

--- Result ---

LICENSE AGREEMENT

This software license...`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hasTwoSections).toBe(true);
      expect(result.initialReply).toBe('Let me draft this for you.');
      expect(result.result).toBe('LICENSE AGREEMENT\n\nThis software license...');
    });

    it('should not detect two-section structure if sections are too short', () => {
      const input = `Short.

Result:

Too short.`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hasTwoSections).toBeUndefined();
      expect(result.hadMarkers).toBe(false);
      expect(result.cleaned).toBe(input);
    });

    it('should not detect two-section structure without proper formatting', () => {
      const input = `This is just a regular response without any special structure. It contains the word Result: but not in the right format.`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hasTwoSections).toBeUndefined();
      expect(result.hadMarkers).toBe(false);
      expect(result.cleaned).toBe(input);
    });

    it('should prioritize contract markers over two-section structure', () => {
      const input = `This looks like a two-section response.

Result:

---CONTRACT START---
Actual contract content
---CONTRACT END---`;
      const result = extractDocumentFromResponse(input);
      
      expect(result.hadMarkers).toBe(true);
      expect(result.hasTwoSections).toBeUndefined();
      expect(result.cleaned).toBe('Actual contract content');
    });
  });
});