
import React, { useState } from 'react';
import { Document as DocType, User } from '../types';
import { Button } from './ui/Button';
import { CloseIcon, TrashIcon, PlusCircleIcon, SendPlanIcon } from './Icons';

interface RequestApprovalModalProps {
  isOpen: boolean;
  onClose: () => void;
  document: DocType;
  allUsers: User[];
  onRequest: (docId: string, approverEmails: string[]) => void;
}

const RequestApprovalModal: React.FC<RequestApprovalModalProps> = ({ isOpen, onClose, document, allUsers, onRequest }) => {
  const [approvers, setApprovers] = useState<string[]>(['']);
  const [error, setError] = useState('');

  if (!isOpen) {return null;}

  const handleApproverChange = (index: number, value: string) => {
    const newApprovers = [...approvers];
    newApprovers[index] = value;
    setApprovers(newApprovers);
  };
  
  const addApprover = () => setApprovers([...approvers, '']);
  const removeApprover = (index: number) => setApprovers(approvers.filter((_, i) => i !== index));

  const handleSendRequest = () => {
    setError('');
    const validApprovers = approvers.filter(email => email.trim() !== '');
    if (validApprovers.length === 0) {
        setError('Please add at least one approver.');
        return;
    }
    
    for (const email of validApprovers) {
        if (!allUsers.find(u => u.email === email)) {
            setError(`User with email "${email}" not found in the system.`);
            return;
        }
    }
    
    onRequest(document.id, validApprovers);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-lg">
        <header className="flex items-center justify-between p-4 border-b dark:border-zinc-800">
          <h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200 flex items-center gap-2">
            <SendPlanIcon className="w-5 h-5 text-brand-600"/>
            Request Approval
          </h2>
          <button onClick={onClose} className="p-2 text-zinc-500 hover:bg-zinc-100 rounded-lg">
            <CloseIcon className="w-6 h-6" />
          </button>
        </header>
        <main className="p-6 space-y-4 max-h-[60vh] overflow-y-auto">
            <p className="text-sm text-zinc-600 dark:text-zinc-400">
                Invite people to approve "{document.name}". The document status will be changed to "In Review".
            </p>
            
            <div className="space-y-3">
                {approvers.map((email, index) => (
                    <div key={index} className="flex items-center gap-2">
                        <input
                            type="email"
                            value={email}
                            onChange={(e) => handleApproverChange(index, e.target.value)}
                            placeholder="Approver's Email"
                            className="flex-1 w-full text-sm rounded-md border-zinc-300 dark:border-zinc-700 p-2"
                        />
                        <Button variant="ghost" size="icon" className="text-zinc-500 hover:text-red-600" onClick={() => removeApprover(index)} disabled={approvers.length === 1}>
                            <TrashIcon className="w-4 h-4"/>
                        </Button>
                    </div>
                ))}
            </div>
            
            <Button variant="outline" size="sm" onClick={addApprover}>
                <PlusCircleIcon className="w-4 h-4 mr-2"/> Add Another Approver
            </Button>
             {error && <p className="text-sm text-red-500">{error}</p>}
        </main>
        <footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t dark:border-zinc-800">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSendRequest}>
            Send Request
          </Button>
        </footer>
      </div>
    </div>
  );
};

export default RequestApprovalModal;