
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/GlobalSearchResults.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> GlobalSearchResults.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.81% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>3/44</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.81% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>3/44</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">&nbsp;
import React from 'react';
import { SearchResult } from '../types';
import { DocumentIcon, FolderIcon, LibraryIcon, QuestionMarkCircleIcon } from './Icons';
&nbsp;
interface GlobalSearchResultsProps {
  results: SearchResult[];
  onResultClick: (result: SearchResult) =&gt; void;
  searchTerm: string;
}
&nbsp;
const GlobalSearchResults: React.FC&lt;GlobalSearchResultsProps&gt; = <span class="fstat-no" title="function not covered" >({ results, onResultClick, searchTerm }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const groupedResults = results.reduce((acc, result) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        (acc[result.type] = acc[result.type] || []).push(result);</span>
<span class="cstat-no" title="statement not covered" >        return acc;</span>
<span class="cstat-no" title="statement not covered" >    }, {} as Record&lt;string, SearchResult[]&gt;);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const resultTypes = [</span>
<span class="cstat-no" title="statement not covered" >        { type: 'document', title: 'Documents', icon: DocumentIcon },</span>
<span class="cstat-no" title="statement not covered" >        { type: 'folder', title: 'Folders', icon: FolderIcon },</span>
<span class="cstat-no" title="statement not covered" >        { type: 'clause', title: 'Clauses', icon: LibraryIcon },</span>
<span class="cstat-no" title="statement not covered" >        { type: 'help', title: 'Help Articles', icon: QuestionMarkCircleIcon },</span>
<span class="cstat-no" title="statement not covered" >    ];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="absolute top-full mt-2 w-72 sm:w-96 max-h-96 overflow-y-auto rounded-lg shadow-lg bg-white dark:bg-zinc-800 ring-1 ring-black dark:ring-zinc-700 ring-opacity-5 z-20 -translate-x-1/2 left-1/2 sm:left-auto sm:translate-x-0 sm:right-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >            {results.length &gt; 0 ? (</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="p-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {resultTypes.map(resultType =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                        groupedResults[resultType.type] &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                            &lt;div key={resultType.type}&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;h4 className="px-2 py-1 text-xs font-semibold text-zinc-500 dark:text-zinc-400"&gt;{resultType.title}&lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;ul className="mb-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    {groupedResults[resultType.type].map(result =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                                        &lt;li key={`${result.type}-${result.id}`}&gt;</span>
<span class="cstat-no" title="statement not covered" >                                            &lt;button</span>
<span class="cstat-no" title="statement not covered" >                                                onClick={() =&gt; onResultClick(result)}</span>
<span class="cstat-no" title="statement not covered" >                                                className="w-full text-left flex items-center gap-3 px-2 py-1.5 rounded-md hover:bg-zinc-100 dark:hover:bg-zinc-700"</span>
                                            &gt;
<span class="cstat-no" title="statement not covered" >                                                &lt;resultType.icon className="w-4 h-4 text-zinc-600 dark:text-zinc-300 flex-shrink-0" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                                                &lt;div className="flex-1 truncate"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                                    &lt;p className="text-sm text-zinc-800 dark:text-zinc-100 truncate"&gt;{result.title}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                                                    {result.context &amp;&amp; &lt;p className="text-xs text-zinc-500 dark:text-zinc-400 truncate"&gt;{result.context}&lt;/p&gt;}</span>
<span class="cstat-no" title="statement not covered" >                                                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                                            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                                        &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    ))}</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/ul&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/div&gt;</span>
                        )
<span class="cstat-no" title="statement not covered" >                    ))}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
            ) : (
<span class="cstat-no" title="statement not covered" >                &lt;div className="p-4 text-center text-sm text-zinc-500 dark:text-zinc-400"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    No results found for "{searchTerm}"</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
            )}
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
    );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
export default GlobalSearchResults;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    