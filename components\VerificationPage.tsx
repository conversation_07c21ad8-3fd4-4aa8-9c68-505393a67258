

import React from 'react';
import { CheckCircleIcon, XCircleIcon } from './Icons';

interface VerificationPageProps {
  status: 'verifying' | 'success' | 'failed';
  setView: (view: 'auth') => void;
}

const VerificationPage: React.FC<VerificationPageProps> = ({ status, setView }) => {
  const renderStatus = () => {
    switch (status) {
      case 'verifying':
        return (
          <>
            <div className="w-12 h-12 border-4 border-brand-500 border-t-transparent rounded-full animate-spin"></div>
            <h2 className="text-2xl font-bold text-zinc-900 dark:text-white mt-6">Verifying your email...</h2>
            <p className="text-zinc-600 dark:text-zinc-400 mt-2">Please wait a moment.</p>
          </>
        );
      case 'success':
        return (
          <>
            <CheckCircleIcon className="w-16 h-16 text-green-500" />
            <h2 className="text-2xl font-bold text-zinc-900 dark:text-white mt-6">Email Verified!</h2>
            <p className="text-zinc-600 dark:text-zinc-400 mt-2">Your account has been successfully verified. You can now log in.</p>
            <button
              onClick={() => setView('auth')}
              className="mt-8 w-full max-w-xs flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-brand-600 hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500"
            >
              Go to Login
            </button>
          </>
        );
      case 'failed':
        return (
          <>
            <XCircleIcon className="w-16 h-16 text-red-500" />
            <h2 className="text-2xl font-bold text-zinc-900 dark:text-white mt-6">Verification Failed</h2>
            <p className="text-zinc-600 dark:text-zinc-400 mt-2">The verification link is invalid or has expired. Please try registering again.</p>
            <button
              onClick={() => setView('auth')}
              className="mt-8 w-full max-w-xs flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-brand-600 hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500"
            >
              Back to Sign Up
            </button>
          </>
        );
    }
  };

  return (
    <section className="py-20 bg-zinc-50 dark:bg-zinc-900 min-h-[calc(100vh-144px)] flex items-center justify-center">
      <div className="w-full max-w-md bg-white dark:bg-zinc-950 p-8 md:p-10 rounded-2xl shadow-lg border border-zinc-200 dark:border-zinc-800 flex flex-col items-center text-center">
        {renderStatus()}
      </div>
    </section>
  );
};

export default VerificationPage;