import { describe, it, expect, vi, beforeEach } from 'vitest';
import { exportAsPdf, exportAsDocx } from '../lib/file-export';

// Mock the global functions and objects
const mockSaveAs = vi.fn();
const mockJsPDF = vi.fn().mockImplementation(() => ({
  addImage: vi.fn(),
  addPage: vi.fn(),
  save: vi.fn(),
  internal: {
    pageSize: {
      getWidth: () => 210,
      getHeight: () => 297
    }
  }
}));
const mockHtml2canvas = vi.fn().mockResolvedValue({
  width: 800,
  height: 1200,
  getContext: vi.fn().mockReturnValue({
    getImageData: vi.fn().mockReturnValue({
      data: new Uint8ClampedArray(800 * 400 * 4)
    })
  }),
  toDataURL: vi.fn().mockReturnValue('data:image/png;base64,mock')
});
const mockHtmlDocx = {
  asBlob: vi.fn().mockReturnValue(new Blob(['mock docx'], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' }))
};

// Mock document.createElement to intercept script loading
const originalCreateElement = document.createElement;
vi.spyOn(document, 'createElement').mockImplementation((tagName: string) => {
  if (tagName === 'script') {
    const script = originalCreateElement.call(document, 'script') as HTMLScriptElement;
    // Mock script loading to resolve immediately
    setTimeout(() => {
      if (script.onload) {
        script.onload({} as Event);
      }
    }, 0);
    return script;
  }
  return originalCreateElement.call(document, tagName);
});

// Mock the global window object
Object.defineProperty(window, 'jspdf', {
  value: { jsPDF: mockJsPDF },
  writable: true
});
Object.defineProperty(window, 'html2canvas', {
  value: mockHtml2canvas,
  writable: true
});
Object.defineProperty(window, 'htmlDocx', {
  value: mockHtmlDocx,
  writable: true
});
Object.defineProperty(window, 'saveAs', {
  value: mockSaveAs,
  writable: true
});

describe('file-export', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('exportAsPdf', () => {
    it('should export element as PDF successfully', async () => {
      // Create a mock element
      const mockElement = document.createElement('div');
      mockElement.innerHTML = '<p>Test content</p>';
      mockElement.style.width = '800px';
      mockElement.style.height = '600px';
      document.body.appendChild(mockElement);

      await exportAsPdf(mockElement, 'test-document');

      expect(mockHtml2canvas).toHaveBeenCalledWith(expect.any(Element), {
        scale: 2,
        useCORS: true,
        logging: false,
        scrollY: 0,
        windowWidth: 0,
        windowHeight: 0
      });
      expect(mockJsPDF).toHaveBeenCalledWith('p', 'pt', 'a4');
      
      // Cleanup
      document.body.removeChild(mockElement);
    });

    it('should handle errors gracefully', async () => {
      const mockElement = document.createElement('div');
      mockHtml2canvas.mockRejectedValueOnce(new Error('Canvas error'));
      
      await expect(exportAsPdf(mockElement, 'test')).rejects.toThrow('Canvas error');
    });
  });

  describe('exportAsDocx', () => {
    it('should export HTML as DOCX successfully', async () => {
      const htmlContent = '<div><h1>Test Document</h1><p>Test content</p></div>';
      
      await exportAsDocx(htmlContent, 'test-document');
      
      expect(mockHtmlDocx.asBlob).toHaveBeenCalledWith(expect.stringContaining('Test Document'));
      expect(mockSaveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        'test-document.docx'
      );
    });

    it('should handle errors gracefully', async () => {
      mockHtmlDocx.asBlob.mockImplementationOnce(() => {
        throw new Error('DOCX error');
      });
      
      await expect(exportAsDocx('<div>test</div>', 'test')).rejects.toThrow('DOCX error');
    });

    it('should wrap content in proper HTML structure', async () => {
      const htmlContent = '<p>Simple content</p>';
      
      await exportAsDocx(htmlContent, 'test-document');
      
      const expectedHtml = expect.stringContaining('<!DOCTYPE html>');
      expect(mockHtmlDocx.asBlob).toHaveBeenCalledWith(expectedHtml);
    });
  });
});