
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/EditorToolbar.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> EditorToolbar.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/80</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/80</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">&nbsp;
&nbsp;
<span class="cstat-no" title="statement not covered" >import React, { useState, useRef, useEffect } from 'react';</span>
<span class="cstat-no" title="statement not covered" >import { Button } from './ui/Button';</span>
<span class="cstat-no" title="statement not covered" >import { </span>
    BoldIcon, ItalicIcon, UnderlineIcon, StrikethroughIcon,
    H1Icon, H2Icon, ListIcon,
    UndoIcon, RedoIcon,
    TextColorIcon, HighlightIcon,
    AlignLeftIcon, AlignCenterIcon, AlignRightIcon, AlignJustifyIcon
} from './Icons';
&nbsp;
interface EditorToolbarProps {
  editorRef: React.RefObject&lt;HTMLDivElement&gt;;
}
&nbsp;
<span class="cstat-no" title="statement not covered" >const TEXT_COLORS = ['#000000', '#e03131', '#c2255c', '#9c36b5', '#6741d9', '#3b5bdb', '#1c7ed6', '#0b7285', '#087f5b', '#2f9e44', '#f08c00', '#787c82'];</span>
<span class="cstat-no" title="statement not covered" >const HIGHLIGHT_COLORS = ['#ffff00', '#ffc9c9', '#fcc2d7', '#eebefa', '#d0bfff', '#bac8ff', '#a5d8ff', '#99e9f2', '#63e6be', '#8ce99a', '#ffd43b', '#d0d4d9', 'transparent'];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const ColorPalette: React.FC&lt;{</span>
    colors: string[];
    onSelect: (color: string) =&gt; void;
    onClose: () =&gt; void;
<span class="cstat-no" title="statement not covered" >}&gt; = ({ colors, onSelect, onClose }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const paletteRef = useRef&lt;HTMLDivElement&gt;(null);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const handleClickOutside = (event: MouseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            if (paletteRef.current &amp;&amp; !paletteRef.current.contains(event.target as Node)) {</span>
<span class="cstat-no" title="statement not covered" >                onClose();</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >        };</span>
<span class="cstat-no" title="statement not covered" >        document.addEventListener('mousedown', handleClickOutside);</span>
<span class="cstat-no" title="statement not covered" >        return () =&gt; document.removeEventListener('mousedown', handleClickOutside);</span>
<span class="cstat-no" title="statement not covered" >    }, [onClose]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div ref={paletteRef} className="absolute top-full mt-2 z-20 bg-white dark:bg-zinc-800 rounded-md shadow-lg border border-zinc-200 dark:border-zinc-700 p-2 grid grid-cols-6 gap-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >            {colors.map(color =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                &lt;button</span>
<span class="cstat-no" title="statement not covered" >                    key={color}</span>
<span class="cstat-no" title="statement not covered" >                    onClick={() =&gt; onSelect(color)}</span>
<span class="cstat-no" title="statement not covered" >                    className="w-6 h-6 rounded-sm border border-zinc-200 dark:border-zinc-600"</span>
<span class="cstat-no" title="statement not covered" >                    style={{ backgroundColor: color === 'transparent' ? undefined : color }}</span>
<span class="cstat-no" title="statement not covered" >                    title={color === 'transparent' ? 'No Color' : color}</span>
                &gt;
<span class="cstat-no" title="statement not covered" >                  {color === 'transparent' &amp;&amp; &lt;div className="w-full h-full bg-no-repeat bg-center" style={{backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M-10 10l20-20M80 110l20-20M-10 90l100-100' stroke='%23dc2626' stroke-width='8'/%3E%3C/svg%3E")`}}&gt;&lt;/div&gt;}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            ))}</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
    );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
&nbsp;
<span class="cstat-no" title="statement not covered" >const EditorToolbar: React.FC&lt;EditorToolbarProps&gt; = ({ editorRef }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const [showTextColorPicker, setShowTextColorPicker] = useState(false);</span>
<span class="cstat-no" title="statement not covered" >  const [showHighlightColorPicker, setShowHighlightColorPicker] = useState(false);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const applyFormat = (command: string, value?: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    if (editorRef.current) {</span>
<span class="cstat-no" title="statement not covered" >      editorRef.current.focus();</span>
<span class="cstat-no" title="statement not covered" >      document.execCommand(command, false, value);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const applyColor = (command: 'foreColor' | 'hiliteColor', color: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    applyFormat(command, color);</span>
<span class="cstat-no" title="statement not covered" >    setShowTextColorPicker(false);</span>
<span class="cstat-no" title="statement not covered" >    setShowHighlightColorPicker(false);</span>
<span class="cstat-no" title="statement not covered" >  };</span>
  
<span class="cstat-no" title="statement not covered" >  const ToolbarButton: React.FC&lt;{onClick: () =&gt; void; title: string; children: React.ReactNode}&gt; = ({onClick, title, children}) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >      &lt;Button variant="ghost" size="icon" onClick={onClick} title={title} className="h-9 w-9 dark:text-zinc-300 dark:hover:bg-zinc-700"&gt;</span>
<span class="cstat-no" title="statement not covered" >          {children}</span>
<span class="cstat-no" title="statement not covered" >      &lt;/Button&gt;</span>
  );
&nbsp;
<span class="cstat-no" title="statement not covered" >  const Divider = () =&gt; &lt;div className="w-px h-6 bg-zinc-200 dark:bg-zinc-700 mx-1"&gt;&lt;/div&gt;;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="sticky top-2 z-10 mb-4 bg-white/90 dark:bg-zinc-800/90 backdrop-blur-md p-1 rounded-lg shadow-lg border border-zinc-200 dark:border-zinc-700 flex items-center justify-center flex-wrap gap-0.5 max-w-2xl mx-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;ToolbarButton onClick={() =&gt; applyFormat('undo')} title="Undo"&gt;&lt;UndoIcon className="w-5 h-5" /&gt;&lt;/ToolbarButton&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;ToolbarButton onClick={() =&gt; applyFormat('redo')} title="Redo"&gt;&lt;RedoIcon className="w-5 h-5" /&gt;&lt;/ToolbarButton&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;Divider /&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;ToolbarButton onClick={() =&gt; applyFormat('bold')} title="Bold"&gt;&lt;BoldIcon className="w-5 h-5" /&gt;&lt;/ToolbarButton&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;ToolbarButton onClick={() =&gt; applyFormat('italic')} title="Italic"&gt;&lt;ItalicIcon className="w-5 h-5" /&gt;&lt;/ToolbarButton&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;ToolbarButton onClick={() =&gt; applyFormat('underline')} title="Underline"&gt;&lt;UnderlineIcon className="w-5 h-5" /&gt;&lt;/ToolbarButton&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;ToolbarButton onClick={() =&gt; applyFormat('strikeThrough')} title="Strikethrough"&gt;&lt;StrikethroughIcon className="w-5 h-5" /&gt;&lt;/ToolbarButton&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;Divider /&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="relative"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;ToolbarButton onClick={() =&gt; { setShowHighlightColorPicker(false); setShowTextColorPicker(p =&gt; !p); }} title="Text Color"&gt;&lt;TextColorIcon className="w-5 h-5" /&gt;&lt;/ToolbarButton&gt;</span>
<span class="cstat-no" title="statement not covered" >          {showTextColorPicker &amp;&amp; &lt;ColorPalette colors={TEXT_COLORS} onSelect={(color) =&gt; applyColor('foreColor', color)} onClose={() =&gt; setShowTextColorPicker(false)}/&gt;}</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="relative"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;ToolbarButton onClick={() =&gt; { setShowTextColorPicker(false); setShowHighlightColorPicker(p =&gt; !p); }} title="Highlight Color"&gt;&lt;HighlightIcon className="w-5 h-5" /&gt;&lt;/ToolbarButton&gt;</span>
<span class="cstat-no" title="statement not covered" >          {showHighlightColorPicker &amp;&amp; &lt;ColorPalette colors={HIGHLIGHT_COLORS} onSelect={(color) =&gt; applyColor('hiliteColor', color)} onClose={() =&gt; setShowHighlightColorPicker(false)} /&gt;}</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;Divider /&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;ToolbarButton onClick={() =&gt; applyFormat('justifyLeft')} title="Align Left"&gt;&lt;AlignLeftIcon className="w-5 h-5" /&gt;&lt;/ToolbarButton&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;ToolbarButton onClick={() =&gt; applyFormat('justifyCenter')} title="Align Center"&gt;&lt;AlignCenterIcon className="w-5 h-5" /&gt;&lt;/ToolbarButton&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;ToolbarButton onClick={() =&gt; applyFormat('justifyRight')} title="Align Right"&gt;&lt;AlignRightIcon className="w-5 h-5" /&gt;&lt;/ToolbarButton&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;ToolbarButton onClick={() =&gt; applyFormat('justifyFull')} title="Justify"&gt;&lt;AlignJustifyIcon className="w-5 h-5" /&gt;&lt;/ToolbarButton&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;Divider /&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;ToolbarButton onClick={() =&gt; applyFormat('formatBlock', '&lt;h1&gt;')} title="Heading 1"&gt;&lt;H1Icon className="w-5 h-5" /&gt;&lt;/ToolbarButton&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;ToolbarButton onClick={() =&gt; applyFormat('formatBlock', '&lt;h2&gt;')} title="Heading 2"&gt;&lt;H2Icon className="w-5 h-5" /&gt;&lt;/ToolbarButton&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;ToolbarButton onClick={() =&gt; applyFormat('insertUnorderedList')} title="Bulleted List"&gt;&lt;ListIcon className="w-5 h-5" /&gt;&lt;/ToolbarButton&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default EditorToolbar;</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    