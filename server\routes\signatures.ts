import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient } from '../supabaseClient';

const router = Router();

const insertSchema = z.object({
  documentId: z.string().uuid(),
  email: z.string().email(),
  role: z.string().min(1),
  status: z.enum(['pending','signed']).default('pending'),
  token: z.string().min(1),
});

const updateSchema = z.object({
  status: z.enum(['pending','signed']).optional(),
  signedAt: z.string().optional(),
});

router.get('/', requireAuth, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('signatures').select('*').order('id');
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ signatures: data });
});

router.post('/', requireAuth, async (req: AuthRequest, res) => {
  const parsed = insertSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa
    .from('signatures')
    .insert({
      document_id: parsed.data.documentId,
      email: parsed.data.email,
      role: parsed.data.role,
      status: parsed.data.status,
      token: parsed.data.token,
    })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ signature: data });
});

router.put('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = updateSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{ status: string; signed_at: string }> = { status: parsed.data.status };
  if (parsed.data.signedAt) {patch.signed_at = parsed.data.signedAt;}
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('signatures').update(patch).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ signature: data });
});

router.delete('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('signatures').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

export default router;

