

import React, { useState, useEffect } from 'react';
import { User, DashboardView, Team, WorkflowTemplate, WorkflowInstance } from '../types';
import { Button } from './ui/Button';
import { LockSolidIcon, PlusCircleIcon, GitBranchIcon, TrashIcon, EditIcon } from './Icons';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from './ui/Card';
import WorkflowCanvas from './WorkflowCanvas';
import { PRECONFIGURED_WORKFLOWS } from '../constants';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from './ui/Tabs';
import { cn } from '../lib/utils';

interface WorkflowBuilderPageProps {
  user: User;
  team: Team | undefined;
  setView: (view: DashboardView) => void;
  onCreateTemplate: (templateData: Omit<WorkflowTemplate, 'id' | 'status'>) => void;
  onUpdateTemplate: (template: WorkflowTemplate) => void;
  onDeleteTemplate: (templateId: string) => void;
  workflowInstances: WorkflowInstance[];
}

const ToggleSwitch: React.FC<{ checked: boolean; onChange: (checked: boolean) => void; }> = ({ checked, onChange }) => (
    <button
        type="button"
        role="switch"
        aria-checked={checked}
        onClick={() => onChange(!checked)}
        className={cn(
            'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 dark:ring-offset-zinc-950',
            checked ? 'bg-brand-600' : 'bg-zinc-200 dark:bg-zinc-700'
        )}
    >
        <span
            aria-hidden="true"
            className={cn(
                'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                checked ? 'translate-x-5' : 'translate-x-0'
            )}
        />
    </button>
);


const WorkflowBuilderPage: React.FC<WorkflowBuilderPageProps> = (props) => {
    const { user, team, setView, onCreateTemplate, onUpdateTemplate, onDeleteTemplate } = props;
    const [editorView, setEditorView] = useState<{ isOpen: boolean, template: WorkflowTemplate | null }>({ isOpen: false, template: null });
    const [activeTab, setActiveTab] = useState<'my-workflows' | 'templates'>('my-workflows');
    const [pendingTemplateNameToOpen, setPendingTemplateNameToOpen] = useState<string | null>(null);

    useEffect(() => {
        if (pendingTemplateNameToOpen && team?.workflows) {
            const newTemplate = team.workflows[team.workflows.length - 1];
            if (newTemplate && newTemplate.name === pendingTemplateNameToOpen) {
                setEditorView({ isOpen: true, template: newTemplate });
                setPendingTemplateNameToOpen(null);
            }
        }
    }, [team?.workflows, pendingTemplateNameToOpen]);

    const isPremium = user.planName === 'Premium' || user.planName === 'Enterprise';

    const handleCreateNew = () => {
        const newTemplateData: Omit<WorkflowTemplate, 'id' | 'status'> = {
            name: 'New Approval Workflow',
            nodes: [
                { id: 'trigger-1', type: 'trigger', position: { x: 50, y: 150 }, data: { label: 'On Approval Request', triggerType: 'approval-requested' } }
            ],
            edges: []
        };
        setPendingTemplateNameToOpen(newTemplateData.name);
        onCreateTemplate(newTemplateData);
    };

    const handleUseTemplate = (templateData: Omit<WorkflowTemplate, 'id'>) => {
        const { status: _status, ...templateCoreData } = templateData;
        setPendingTemplateNameToOpen(templateData.name);
        onCreateTemplate(templateCoreData);
    };

    const handleEdit = (template: WorkflowTemplate) => {
        setEditorView({ isOpen: true, template });
    };

    const handleSave = (template: WorkflowTemplate) => {
        onUpdateTemplate(template);
        setEditorView({ isOpen: false, template: null });
    }
    
    const handleStatusToggle = (template: WorkflowTemplate, newStatus: boolean) => {
        onUpdateTemplate({ ...template, status: newStatus ? 'active' : 'inactive' });
    };

    if (!isPremium) {
        return (
            <div className="p-4 sm:p-6 lg:p-8 h-full flex items-center justify-center">
                <div className="text-center">
                    <LockSolidIcon className="w-12 h-12 mx-auto text-zinc-300 dark:text-zinc-700 mb-4" />
                    <h3 className="text-xl font-semibold text-zinc-800 dark:text-zinc-200">Unlock Workflow Builder</h3>
                    <p className="text-zinc-500 dark:text-zinc-400 mt-2 mb-4 max-w-md">
                        Upgrade to Premium to design custom, multi-step approval workflows for your contracts.
                    </p>
                    <Button onClick={() => setView('subscription')}>
                        Upgrade Now
                    </Button>
                </div>
            </div>
        );
    }
    
    if (editorView.isOpen && editorView.template) {
        return <WorkflowCanvas 
                    user={user}
                    template={editorView.template} 
                    onSave={handleSave} 
                    onBack={() => setEditorView({ isOpen: false, template: null })} 
                />
    }

    return (
        <div className="p-4 sm:p-6 lg:p-8 space-y-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 className="text-3xl font-bold text-zinc-900 dark:text-white">Workflow Builder</h1>
                    <p className="text-zinc-600 dark:text-zinc-400 mt-1">Design automated approval processes for your documents.</p>
                </div>
                <Button onClick={handleCreateNew}>
                    <PlusCircleIcon className="w-5 h-5 mr-2" />
                    New Workflow
                </Button>
            </div>
            
            <Tabs>
                <TabsList>
                    <TabsTrigger onClick={() => setActiveTab('my-workflows')} data-state={activeTab === 'my-workflows' ? 'active' : 'inactive'}>My Workflows</TabsTrigger>
                    <TabsTrigger onClick={() => setActiveTab('templates')} data-state={activeTab === 'templates' ? 'active' : 'inactive'}>Templates</TabsTrigger>
                </TabsList>

                <TabsContent className={activeTab === 'my-workflows' ? 'block' : 'hidden'}>
                    <Card className="mt-4">
                        <CardHeader>
                            <CardTitle>Your Workflow Templates</CardTitle>
                            <CardDescription>Manage your saved workflow templates. Only active workflows will be enforced.</CardDescription>
                        </CardHeader>
                        <CardContent>
                           {(team?.workflows || []).length > 0 ? (
                                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                                    {(team?.workflows || []).map(template => (
                                        <Card key={template.id} className="p-4 flex flex-col justify-between">
                                            <div>
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center gap-3">
                                                        <GitBranchIcon className="w-6 h-6 text-brand-600"/>
                                                        <span className="font-medium text-zinc-800 dark:text-zinc-200">{template.name}</span>
                                                    </div>
                                                    <ToggleSwitch checked={template.status === 'active'} onChange={(isChecked) => handleStatusToggle(template, isChecked)} />
                                                </div>
                                                <p className="text-xs text-zinc-500 dark:text-zinc-400 mt-2">{template.nodes.length} step(s) &bull; Status: <span className={template.status === 'active' ? 'text-green-600' : 'text-zinc-500'}>{template.status}</span></p>
                                            </div>
                                            <div className="flex items-center gap-1 mt-3 justify-end">
                                                <Button variant="ghost" size="sm" onClick={() => handleEdit(template)}><EditIcon className="w-4 h-4 mr-2"/>Edit</Button>
                                                <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-600" onClick={() => onDeleteTemplate(template.id)}><TrashIcon className="w-4 h-4 mr-2"/>Delete</Button>
                                            </div>
                                        </Card>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-16">
                                     <GitBranchIcon className="mx-auto h-12 w-12 text-zinc-300 dark:text-zinc-700" />
                                     <h3 className="mt-2 text-lg font-semibold text-zinc-800 dark:text-zinc-200">No Workflows Yet</h3>
                                     <p className="mt-1 text-sm text-zinc-500 dark:text-zinc-400">Create a new workflow or start from a template.</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent className={activeTab === 'templates' ? 'block' : 'hidden'}>
                     <Card className="mt-4">
                        <CardHeader>
                            <CardTitle>Pre-configured Templates</CardTitle>
                            <CardDescription>Choose a template to copy to "My Workflows" and customize it to fit your needs.</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                                {PRECONFIGURED_WORKFLOWS.map((template, index) => (
                                    <Card key={index} className="text-left p-4 hover:shadow-lg transition-shadow bg-zinc-50 dark:bg-zinc-900">
                                        <CardTitle className="text-base flex items-center gap-2"><GitBranchIcon className="w-5 h-5 text-brand-500"/>{template.name}</CardTitle>
                                        <ul className="text-xs text-zinc-500 dark:text-zinc-400 mt-2 pl-4 list-disc space-y-1">
                                            {template.nodes.slice(1, 4).map(n => <li key={n.id} className="capitalize">{n.type.replace('-', ' ')} Step</li>)}
                                        </ul>
                                        <Button size="sm" className="w-full mt-4" onClick={() => handleUseTemplate(template)}>Use Template</Button>
                                    </Card>
                                ))}
                            </div>
                        </CardContent>
                     </Card>
                </TabsContent>
            </Tabs>
        </div>
    )
};

export default WorkflowBuilderPage;