

import React, { useState, useEffect } from 'react';
import { Button } from './ui/Button';
import { CloseIcon, FolderIcon } from './Icons';

interface CreateFolderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (name: string) => void;
  existingFolder?: { id: string; name: string } | null;
}

const CreateFolderModal: React.FC<CreateFolderModalProps> = ({ isOpen, onClose, onSave, existingFolder }) => {
  const [name, setName] = useState('');
  const modalTitle = existingFolder ? 'Rename Folder' : 'Create New Folder';
  const buttonText = existingFolder ? 'Save Changes' : 'Create Folder';

  useEffect(() => {
    if (isOpen && existingFolder) {
      setName(existingFolder.name);
    } else if (!isOpen) {
      setName('');
    }
  }, [isOpen, existingFolder]);

  if (!isOpen) {return null;}

  const handleSaveClick = () => {
    if (name.trim()) {
      onSave(name.trim());
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-md">
        <header className="flex items-center justify-between p-4 border-b dark:border-zinc-800">
          <h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200 flex items-center">
            <FolderIcon className="w-5 h-5 mr-2 text-brand-700 dark:text-brand-400" />
            {modalTitle}
          </h2>
          <button onClick={onClose} className="p-2 text-zinc-500 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800 rounded-lg">
            <CloseIcon className="w-6 h-6" />
          </button>
        </header>
        <main className="p-6">
          <label htmlFor="folderName" className="block text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-1">
            Folder Name
          </label>
          <input
            type="text"
            id="folderName"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="e.g., Client Contracts"
            className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm p-2"
            autoFocus
          />
        </main>
        <footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t dark:border-zinc-800">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSaveClick} disabled={!name.trim()}>
            {buttonText}
          </Button>
        </footer>
      </div>
    </div>
  );
};

export default CreateFolderModal;