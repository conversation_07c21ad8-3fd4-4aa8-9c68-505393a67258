import * as Sentry from '@sentry/node';

function parseSampleRate(value: string | undefined, defaultRate: number) {
  if (!value) {
    return defaultRate;
  }
  const parsed = Number(value);
  if (Number.isNaN(parsed)) {
    return defaultRate;
  }
  return Math.min(Math.max(parsed, 0), 1);
}

function resolveRelease() {
  return process.env.SENTRY_RELEASE
    ?? process.env.VERCEL_GIT_COMMIT_SHA
    ?? process.env.HEROKU_SLUG_COMMIT
    ?? process.env.GITHUB_SHA
    ?? undefined;
}

export function initMonitoring() {
  const isProduction = process.env.NODE_ENV === 'production';

  if (!isProduction || !process.env.SENTRY_DSN) {
    return;
  }

  Sentry.init({
    dsn: process.env.SENTRY_DSN,
    environment: process.env.SENTRY_ENVIRONMENT ?? process.env.NODE_ENV,
    release: resolveRelease(),
    tracesSampleRate: parseSampleRate(process.env.SENTRY_TRACES_SAMPLE_RATE, 0.1),
  });
}

export { Sentry };
