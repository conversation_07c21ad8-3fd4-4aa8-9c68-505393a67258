import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient } from '../supabaseClient';

const router = Router();

const insertSchema = z.object({ name: z.string().min(1) });
const updateSchema = z.object({ name: z.string().min(1) });

router.get('/', requireAuth, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('folders').select('id, name, created_at').order('created_at', { ascending: false });
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ folders: data });
});

router.post('/', requireAuth, async (req: AuthRequest, res) => {
  const parsed = insertSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data: profile } = await supa.auth.getUser();
  const userId = profile.user?.id;
  const { data, error } = await supa
    .from('folders')
    .insert({ user_id: userId, name: parsed.data.name })
    .select('id, name, created_at')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ folder: data });
});

router.put('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = updateSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('folders').update({ name: parsed.data.name }).eq('id', id).select('id, name, created_at').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ folder: data });
});

router.delete('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('folders').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

export default router;

