
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/ClientsPage.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> ClientsPage.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">66.36% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>73/110</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">33.33% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>3/9</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">9.09% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/11</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">66.36% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>73/110</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import React, { useState, useMemo } from 'react';
import { User, Client } from '../types';
import { Button } from './ui/Button';
import { Card, CardContent, CardHeader } from './ui/Card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/Table';
import { PlusCircleIcon, SearchIcon, EditIcon, TrashIcon, UsersIcon } from './Icons';
import ClientEditorModal from './ClientEditorModal';
import ConfirmationModal from './admin/ConfirmationModal';
&nbsp;
interface ClientsPageProps {
  user: User;
  onCreateClient: (clientData: Omit&lt;Client, 'id' | 'createdAt'&gt;) =&gt; void;
  onUpdateClient: (clientId: string, updates: Partial&lt;Omit&lt;Client, 'id' | 'createdAt'&gt;&gt;) =&gt; void;
  onDeleteClient: (clientId: string) =&gt; void;
  onViewClient?: (client: Client) =&gt; void;
}
&nbsp;
const ClientsPage: React.FC&lt;ClientsPageProps&gt; = ({ user, onCreateClient, onUpdateClient, onDeleteClient, onViewClient }) =&gt; {
    const [isEditorOpen, setIsEditorOpen] = useState(false);
    const [clientToEdit, setClientToEdit] = useState&lt;Client | null&gt;(null);
    const [clientToDelete, setClientToDelete] = useState&lt;Client | null&gt;(null);
    const [searchTerm, setSearchTerm] = useState('');
&nbsp;
    const filteredClients = useMemo(() =&gt; {
        const clients = user.<span class="branch-0 cbranch-no" title="branch not covered" >clients || [];</span>
        if (!searchTerm) {return clients;<span class="branch-0 cbranch-no" title="branch not covered" >}</span>
<span class="cstat-no" title="statement not covered" >        return clients.filter(c =&gt;</span>
<span class="cstat-no" title="statement not covered" >            c.name.toLowerCase().includes(searchTerm.toLowerCase()) ||</span>
<span class="cstat-no" title="statement not covered" >            c.email?.toLowerCase().includes(searchTerm.toLowerCase())</span>
<span class="cstat-no" title="statement not covered" >        );</span>
    }, [user.clients, searchTerm]);
&nbsp;
    const handleEdit = <span class="fstat-no" title="function not covered" >(client: Client) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        setClientToEdit(client);</span>
<span class="cstat-no" title="statement not covered" >        setIsEditorOpen(true);</span>
<span class="cstat-no" title="statement not covered" >    };</span>
&nbsp;
    const handleCreate = <span class="fstat-no" title="function not covered" >() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        setClientToEdit(null);</span>
<span class="cstat-no" title="statement not covered" >        setIsEditorOpen(true);</span>
<span class="cstat-no" title="statement not covered" >    };</span>
    
    const handleSave = <span class="fstat-no" title="function not covered" >(data: Omit&lt;Client, 'id' | 'createdAt'&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (clientToEdit) {</span>
<span class="cstat-no" title="statement not covered" >            onUpdateClient(clientToEdit.id, data);</span>
<span class="cstat-no" title="statement not covered" >        } else {</span>
<span class="cstat-no" title="statement not covered" >            onCreateClient(data);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        setIsEditorOpen(false);</span>
<span class="cstat-no" title="statement not covered" >    };</span>
&nbsp;
    const handleDeleteConfirm = <span class="fstat-no" title="function not covered" >() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (clientToDelete) {</span>
<span class="cstat-no" title="statement not covered" >            onDeleteClient(clientToDelete.id);</span>
<span class="cstat-no" title="statement not covered" >            setClientToDelete(null);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >    };</span>
&nbsp;
    return (
        &lt;&gt;
            &lt;div className="p-4 sm:p-6 lg:p-8 space-y-6"&gt;
                &lt;div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"&gt;
                    &lt;div&gt;
                        &lt;h1 className="text-3xl font-bold text-zinc-900 dark:text-white"&gt;Clients&lt;/h1&gt;
                        &lt;p className="text-zinc-600 dark:text-zinc-400 mt-1"&gt;Manage your clients and their information.&lt;/p&gt;
                    &lt;/div&gt;
                    &lt;Button onClick={handleCreate}&gt;
                        &lt;PlusCircleIcon className="w-5 h-5 mr-2" /&gt; New Client
                    &lt;/Button&gt;
                &lt;/div&gt;
&nbsp;
                &lt;Card&gt;
                    &lt;CardHeader&gt;
                        &lt;div className="relative w-full sm:max-w-xs"&gt;
                            &lt;div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"&gt;&lt;SearchIcon className="h-5 w-5 text-zinc-400" /&gt;&lt;/div&gt;
                            &lt;input type="text" placeholder="Search clients..." value={searchTerm} onChange={<span class="fstat-no" title="function not covered" >e =&gt; setSearchTerm(e.target.value)} c</span>lassName="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 p-2" /&gt;
                        &lt;/div&gt;
                    &lt;/CardHeader&gt;
                    &lt;CardContent&gt;
                        &lt;Table&gt;
                            &lt;TableHeader&gt;
                                &lt;TableRow&gt;
                                    &lt;TableHead&gt;Name&lt;/TableHead&gt;
                                    &lt;TableHead&gt;Type&lt;/TableHead&gt;
                                    &lt;TableHead&gt;Contact Person&lt;/TableHead&gt;
                                    &lt;TableHead&gt;Email&lt;/TableHead&gt;
                                    &lt;TableHead className="text-right"&gt;Actions&lt;/TableHead&gt;
                                &lt;/TableRow&gt;
                            &lt;/TableHeader&gt;
                            &lt;TableBody&gt;
                                {filteredClients.length &gt; 0 ? filteredClients.map(client =&gt; (
                                    &lt;TableRow key={client.id} className={<span class="branch-0 cbranch-no" title="branch not covered" >onViewClient ? 'cursor-pointer' : '</span>'} onClick={<span class="fstat-no" title="function not covered" >() =&gt; onViewClient &amp;&amp; onViewClient(client)}&gt;</span>
                                        &lt;TableCell className="font-medium"&gt;{client.name}&lt;/TableCell&gt;
                                        &lt;TableCell&gt;{client.type}&lt;/TableCell&gt;
                                        &lt;TableCell&gt;{client.contactPerson || 'N/A'}&lt;/TableCell&gt;
                                        &lt;TableCell&gt;{client.<span class="branch-0 cbranch-no" title="branch not covered" >email || 'N/A'}&lt;</span>/TableCell&gt;
                                        &lt;TableCell className="text-right"&gt;
                                            &lt;Button variant="ghost" size="icon" onClick={<span class="fstat-no" title="function not covered" >(e) =&gt; { e.stopPropagation(); handleEdit(client); }} c</span>lassName="h-8 w-8"&gt;&lt;EditIcon className="w-4 h-4" /&gt;&lt;/Button&gt;
                                            &lt;Button variant="ghost" size="icon" onClick={<span class="fstat-no" title="function not covered" >(e) =&gt; { e.stopPropagation(); setClientToDelete(client); }} c</span>lassName="h-8 w-8 text-red-600 hover:text-red-700"&gt;&lt;TrashIcon className="w-4 h-4" /&gt;&lt;/Button&gt;
                                        &lt;/TableCell&gt;
                                    &lt;/TableRow&gt;
                                )<span class="branch-0 cbranch-no" title="branch not covered" >) : (</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;TableRow&gt;</span>
<span class="cstat-no" title="statement not covered" >                                        &lt;TableCell colSpan={5} className="h-48 text-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                            &lt;UsersIcon className="mx-auto h-12 w-12 text-zinc-300 dark:text-zinc-700" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                                            &lt;h3 className="mt-2 font-semibold"&gt;No clients found&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                                            &lt;p className="text-sm text-zinc-500"&gt;Get started by creating a new client.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                                        &lt;/TableCell&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;/TableRow&gt;</span>
                                )}
                            &lt;/TableBody&gt;
                        &lt;/Table&gt;
                    &lt;/CardContent&gt;
                &lt;/Card&gt;
            &lt;/div&gt;
            &lt;ClientEditorModal isOpen={isEditorOpen} onClose={<span class="fstat-no" title="function not covered" >() =&gt; setIsEditorOpen(false)} o</span>nSave={handleSave} existingClient={clientToEdit} /&gt;
            {<span class="branch-0 cbranch-no" title="branch not covered" >clientToDelete &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                &lt;ConfirmationModal</span>
<span class="cstat-no" title="statement not covered" >                    isOpen={true}</span>
<span class="cstat-no" title="statement not covered" >                    onClose={<span class="fstat-no" title="function not covered" >() =&gt; setClientToDelete(null)}</span></span>
<span class="cstat-no" title="statement not covered" >                    onConfirm={handleDeleteConfirm}</span>
<span class="cstat-no" title="statement not covered" >                    title="Delete Client"</span>
<span class="cstat-no" title="statement not covered" >                    message={&lt;&gt;Are you sure you want to delete &lt;strong&gt;{clientToDelete.name}&lt;/strong&gt;? This will also unassign them from any documents. This action cannot be undone.&lt;/&gt;}</span>
<span class="cstat-no" title="statement not covered" >                    confirmText="Delete Client"</span>
<span class="cstat-no" title="statement not covered" >                /&gt;</span>
            )}
        &lt;/&gt;
    );
};
&nbsp;
export default ClientsPage;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    