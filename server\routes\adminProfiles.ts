import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest } from '../middleware/auth';
import { requireAdmin } from '../middleware/admin';
import { supabaseAdmin } from '../supabaseClient';

const router = Router();

// Admin-only: update protected profile fields
const adminProfileSchema = z.object({
  status: z.enum(['active','suspended']).optional(),
  planName: z.string().optional(),
  planExpiryDate: z.string().optional(),
  quotaUsed: z.number().int().nullable().optional(),
  quotaTotal: z.number().int().nullable().optional(),
  teamId: z.string().uuid().nullable().optional(),
  subscriptionId: z.string().nullable().optional(),
  customerId: z.string().nullable().optional(),
  subscriptionStatus: z.string().nullable().optional(),
});

router.put('/:id', requireAuth, requireAdmin, async (req: AuthRequest<{ id: string }>, res) => {
  const { id } = req.params;
  const parsed = adminProfileSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{ status: string; plan_name: string; plan_expiry_date: string; quota_used: number; quota_total: number; team_id: string; subscription_id: string; customer_id: string; subscription_status: string }> = {
    status: parsed.data.status,
    plan_name: parsed.data.planName,
    plan_expiry_date: parsed.data.planExpiryDate,
    quota_used: parsed.data.quotaUsed,
    quota_total: parsed.data.quotaTotal,
    team_id: parsed.data.teamId,
    subscription_id: parsed.data.subscriptionId,
    customer_id: parsed.data.customerId,
    subscription_status: parsed.data.subscriptionStatus,
  };
  Object.keys(patch).forEach((k) => patch[k] === undefined && delete patch[k]);
  const { data, error } = await supabaseAdmin
    .from('profiles')
    .update(patch)
    .eq('id', id)
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  return res.json({ profile: data });
});

export default router;

