// @vitest-environment jsdom
import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import SplashScreen from '../components/SplashScreen';
import React from 'react';

describe('SplashScreen', () => {
  it('renders SplashScreen component', () => {
    const { container } = render(<SplashScreen />);
    expect(container).toBeInTheDocument();
  });
});
