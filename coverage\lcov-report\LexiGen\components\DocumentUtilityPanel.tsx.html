
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/DocumentUtilityPanel.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> DocumentUtilityPanel.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/159</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/159</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import React from 'react';
import { Document as DocType, User, DashboardView, ActivityLog, Obligation } from '../types';
<span class="cstat-no" title="statement not covered" >import { Button } from './ui/Button';</span>
<span class="cstat-no" title="statement not covered" >import { CloseIcon, WandIcon, ClockIcon, MessageSquarePlusIcon, ShieldCheckIcon, SignatureIcon, ActivityIcon, UsersIcon, LockSolidIcon, CheckCircleIcon } from './Icons';</span>
<span class="cstat-no" title="statement not covered" >import ClauseSuggestionsPanel from './ClauseSuggestionsPanel';</span>
<span class="cstat-no" title="statement not covered" >import VersionHistoryPanel from './VersionHistoryPanel';</span>
<span class="cstat-no" title="statement not covered" >import CommentsPanel from './CommentsPanel';</span>
<span class="cstat-no" title="statement not covered" >import AnalysisPanel from './AnalysisPanel';</span>
<span class="cstat-no" title="statement not covered" >import SignaturePanel from './SignaturePanel';</span>
<span class="cstat-no" title="statement not covered" >import { cn } from '../lib/utils';</span>
<span class="cstat-no" title="statement not covered" >import ApprovalsPanel from './ApprovalsPanel';</span>
<span class="cstat-no" title="statement not covered" >import ObligationsPanel from './ObligationsPanel';</span>
&nbsp;
type UtilityTab = 'suggestions' | 'history' | 'comments' | 'analysis' | 'signatures' | 'activity' | 'approvals' | 'obligations';
&nbsp;
interface DocumentUtilityPanelProps {
  document: DocType;
  isEditing: boolean;
  onInsertClause: (clauseContent: string) =&gt; void;
  onRevert: (documentId: string, versionId: string) =&gt; void;
  user: User;
  allUsers: User[];
  setView: (view: DashboardView) =&gt; void;
  documentContent: string;
  activeTool: UtilityTab | null;
  setActiveTool: (tab: UtilityTab | null) =&gt; void;
  onAddReply: (documentId: string, threadId: string, content: string) =&gt; void;
  onResolveThread: (documentId: string, threadId: string) =&gt; void;
  onDeleteComment: (documentId: string, threadId: string, commentId: string) =&gt; void;
  activityLogs: ActivityLog[];
  isOpen: boolean;
  onClose: () =&gt; void;
  onUpdateObligationStatus: (docId: string, obligationId: string, status: Obligation['status']) =&gt; void;
}
&nbsp;
<span class="cstat-no" title="statement not covered" >const tabConfig: { id: UtilityTab; icon: React.FC&lt;{className?: string}&gt;; label: string; premium?: boolean; }[] = [</span>
<span class="cstat-no" title="statement not covered" >    { id: 'activity', icon: ActivityIcon, label: 'Activity' },</span>
<span class="cstat-no" title="statement not covered" >    { id: 'comments', icon: MessageSquarePlusIcon, label: 'Comments' },</span>
<span class="cstat-no" title="statement not covered" >    { id: 'history', icon: ClockIcon, label: 'Versions' },</span>
<span class="cstat-no" title="statement not covered" >    { id: 'signatures', icon: SignatureIcon, label: 'Signatures' },</span>
<span class="cstat-no" title="statement not covered" >    { id: 'approvals', icon: UsersIcon, label: 'Approvals', premium: true },</span>
<span class="cstat-no" title="statement not covered" >    { id: 'obligations', icon: CheckCircleIcon, label: 'Obligations', premium: true },</span>
<span class="cstat-no" title="statement not covered" >    { id: 'suggestions', icon: WandIcon, label: 'AI Suggestions', premium: true },</span>
<span class="cstat-no" title="statement not covered" >    { id: 'analysis', icon: ShieldCheckIcon, label: 'AI Analysis', premium: true },</span>
<span class="cstat-no" title="statement not covered" >];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const ActivityPanel: React.FC&lt;{ logs: ActivityLog[], allUsers: User[] }&gt; = ({ logs, allUsers }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const getUser = (email: string) =&gt; allUsers.find(u =&gt; u.email === email);</span>
    
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="flex-1 overflow-y-auto p-4 space-y-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >            {logs.length &gt; 0 ? logs.map(log =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                const user = getUser(log.userEmail);</span>
<span class="cstat-no" title="statement not covered" >                return (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div key={log.id} className="flex items-start gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;img src={user?.avatarUrl} alt={user?.name || log.userEmail} className="w-8 h-8 rounded-full mt-1"/&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;p className="text-sm text-zinc-800 dark:text-zinc-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;span className="font-semibold"&gt;{user?.name || log.userEmail}&lt;/span&gt; {log.details}</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;p className="text-xs text-zinc-500 dark:text-zinc-400"&gt;{new Date(log.timestamp).toLocaleString()}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                );
<span class="cstat-no" title="statement not covered" >            }) : (</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-center text-sm text-zinc-500 dark:text-zinc-400 pt-8"&gt;No activity recorded yet.&lt;/p&gt;</span>
            )}
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
    );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const TooltipIconButton: React.FC&lt;{</span>
  title: string;
  isActive: boolean;
  isDisabled: boolean;
  onClick: () =&gt; void;
  children: React.ReactNode;
<span class="cstat-no" title="statement not covered" >}&gt; = ({ title, isActive, isDisabled, onClick, children }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="relative group flex justify-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;button</span>
<span class="cstat-no" title="statement not covered" >        onClick={onClick}</span>
<span class="cstat-no" title="statement not covered" >        disabled={isDisabled}</span>
<span class="cstat-no" title="statement not covered" >        className={cn(</span>
<span class="cstat-no" title="statement not covered" >          'w-12 h-12 rounded-lg flex items-center justify-center relative transition-colors',</span>
<span class="cstat-no" title="statement not covered" >          isActive</span>
<span class="cstat-no" title="statement not covered" >            ? 'bg-brand-50 text-brand-600 dark:bg-zinc-700 dark:text-brand-400'</span>
<span class="cstat-no" title="statement not covered" >            : 'text-zinc-500 hover:bg-zinc-100 hover:text-zinc-700 dark:hover:bg-zinc-800 dark:text-zinc-400 dark:hover:text-zinc-100',</span>
<span class="cstat-no" title="statement not covered" >          isDisabled &amp;&amp; 'text-zinc-400 dark:text-zinc-600 cursor-not-allowed hover:bg-transparent dark:hover:bg-transparent'</span>
<span class="cstat-no" title="statement not covered" >        )}</span>
<span class="cstat-no" title="statement not covered" >        aria-label={title}</span>
      &gt;
<span class="cstat-no" title="statement not covered" >        {children}</span>
<span class="cstat-no" title="statement not covered" >        {isActive &amp;&amp; &lt;div className="absolute left-0 top-1/4 h-1/2 w-1 bg-brand-600 dark:bg-brand-400 rounded-r-full"&gt;&lt;/div&gt;}</span>
<span class="cstat-no" title="statement not covered" >        {isDisabled &amp;&amp; &lt;LockSolidIcon className="w-3 h-3 absolute bottom-1 right-1 text-amber-500" /&gt;}</span>
<span class="cstat-no" title="statement not covered" >      &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="absolute right-full top-1/2 -translate-y-1/2 mr-4 w-max px-2 py-1 bg-zinc-800 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-20"&gt;</span>
<span class="cstat-no" title="statement not covered" >        {isDisabled ? 'Upgrade to Premium' : title}</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
&nbsp;
<span class="cstat-no" title="statement not covered" >const DocumentUtilityPanel: React.FC&lt;DocumentUtilityPanelProps&gt; = ({</span>
<span class="cstat-no" title="statement not covered" >  document,</span>
<span class="cstat-no" title="statement not covered" >  _isEditing,</span>
<span class="cstat-no" title="statement not covered" >  onInsertClause,</span>
<span class="cstat-no" title="statement not covered" >  onRevert,</span>
<span class="cstat-no" title="statement not covered" >  user,</span>
<span class="cstat-no" title="statement not covered" >  allUsers,</span>
<span class="cstat-no" title="statement not covered" >  setView,</span>
<span class="cstat-no" title="statement not covered" >  documentContent,</span>
<span class="cstat-no" title="statement not covered" >  activeTool,</span>
<span class="cstat-no" title="statement not covered" >  setActiveTool,</span>
<span class="cstat-no" title="statement not covered" >  onAddReply,</span>
<span class="cstat-no" title="statement not covered" >  onResolveThread,</span>
<span class="cstat-no" title="statement not covered" >  onDeleteComment,</span>
<span class="cstat-no" title="statement not covered" >  activityLogs,</span>
<span class="cstat-no" title="statement not covered" >  isOpen,</span>
<span class="cstat-no" title="statement not covered" >  onClose,</span>
<span class="cstat-no" title="statement not covered" >  onUpdateObligationStatus,</span>
<span class="cstat-no" title="statement not covered" >}) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const isPremium = user.planName === 'Premium' || user.planName === 'Enterprise';</span>
    
<span class="cstat-no" title="statement not covered" >    const renderTabContent = (tool: UtilityTab) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        switch (tool) {</span>
<span class="cstat-no" title="statement not covered" >            case 'activity':</span>
<span class="cstat-no" title="statement not covered" >                return &lt;ActivityPanel logs={activityLogs} allUsers={allUsers} /&gt;;</span>
<span class="cstat-no" title="statement not covered" >            case 'suggestions':</span>
<span class="cstat-no" title="statement not covered" >                return &lt;ClauseSuggestionsPanel documentContent={documentContent} onInsertClause={onInsertClause} user={user} setView={setView} /&gt;;</span>
<span class="cstat-no" title="statement not covered" >            case 'history':</span>
<span class="cstat-no" title="statement not covered" >                return &lt;VersionHistoryPanel document={document} onRevert={onRevert} /&gt;;</span>
<span class="cstat-no" title="statement not covered" >            case 'comments':</span>
<span class="cstat-no" title="statement not covered" >                return &lt;CommentsPanel document={document} currentUser={user} allUsers={allUsers} onAddReply={onAddReply} onResolveThread={onResolveThread} onDeleteComment={onDeleteComment} /&gt;;</span>
<span class="cstat-no" title="statement not covered" >            case 'analysis':</span>
<span class="cstat-no" title="statement not covered" >                return &lt;AnalysisPanel documentContent={documentContent} /&gt;;</span>
<span class="cstat-no" title="statement not covered" >            case 'signatures':</span>
<span class="cstat-no" title="statement not covered" >                return &lt;SignaturePanel document={document} allUsers={allUsers} /&gt;;</span>
<span class="cstat-no" title="statement not covered" >            case 'approvals':</span>
<span class="cstat-no" title="statement not covered" >                return &lt;ApprovalsPanel document={document} allUsers={allUsers} /&gt;;</span>
<span class="cstat-no" title="statement not covered" >            case 'obligations':</span>
<span class="cstat-no" title="statement not covered" >                return &lt;ObligationsPanel document={document} onUpdateStatus={onUpdateObligationStatus} /&gt;;</span>
<span class="cstat-no" title="statement not covered" >            default:</span>
<span class="cstat-no" title="statement not covered" >                return null;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >    };</span>
    
<span class="cstat-no" title="statement not covered" >    const activeTabConfig = tabConfig.find(t =&gt; t.id === activeTool);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >        &lt;&gt;</span>
<span class="cstat-no" title="statement not covered" >            {isOpen &amp;&amp; &lt;div onClick={onClose} className="fixed inset-0 bg-black/50 z-20 md:hidden" /&gt;}</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className={cn(</span>
<span class="cstat-no" title="statement not covered" >                "w-[400px] max-w-[90vw] flex-shrink-0 border-l border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900 flex h-full",</span>
<span class="cstat-no" title="statement not covered" >                "fixed md:relative inset-y-0 right-0 z-30 transition-transform transform",</span>
<span class="cstat-no" title="statement not covered" >                isOpen ? "translate-x-0" : "translate-x-full",</span>
<span class="cstat-no" title="statement not covered" >                "md:translate-x-0 md:w-[320px] xl:w-[400px]"</span>
<span class="cstat-no" title="statement not covered" >            )}&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="flex-1 flex flex-col bg-white dark:bg-zinc-950 overflow-hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {activeTool ? (</span>
<span class="cstat-no" title="statement not covered" >                        &lt;div className="flex-1 flex flex-col h-full"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;header className="p-4 border-b border-zinc-200 dark:border-zinc-800 flex items-center justify-between flex-shrink-0"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;h3 className="font-semibold text-zinc-800 dark:text-zinc-200 flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    {activeTabConfig &amp;&amp; &lt;activeTabConfig.icon className="w-5 h-5 text-zinc-500"/&gt;}</span>
<span class="cstat-no" title="statement not covered" >                                    {activeTabConfig?.label}</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;Button variant="ghost" size="icon" onClick={() =&gt; setActiveTool(null)} className="h-8 w-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;CloseIcon className="w-5 h-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/header&gt;</span>
<span class="cstat-no" title="statement not covered" >                            {renderTabContent(activeTool)}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
                    ) : (
<span class="cstat-no" title="statement not covered" >                        &lt;div className="flex-1 flex flex-col items-center justify-center text-center p-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;div className="w-16 h-16 bg-zinc-100 dark:bg-zinc-800 rounded-full flex items-center justify-center mb-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;WandIcon className="w-8 h-8 text-zinc-400 dark:text-zinc-600"/&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;h3 className="font-semibold text-zinc-800 dark:text-zinc-200"&gt;Document Tools&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;p className="text-sm text-zinc-500 dark:text-zinc-400 mt-1"&gt;Select a tool from the sidebar to get started.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
                    )}
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >                &lt;div className="w-16 flex-shrink-0 border-l border-zinc-200 dark:border-zinc-800 flex flex-col items-center py-2 space-y-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {tabConfig.map(tab =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                        const isDisabled = !!tab.premium &amp;&amp; !isPremium;</span>
<span class="cstat-no" title="statement not covered" >                        return (</span>
<span class="cstat-no" title="statement not covered" >                            &lt;TooltipIconButton</span>
<span class="cstat-no" title="statement not covered" >                                key={tab.id}</span>
<span class="cstat-no" title="statement not covered" >                                title={tab.label}</span>
<span class="cstat-no" title="statement not covered" >                                isActive={activeTool === tab.id}</span>
<span class="cstat-no" title="statement not covered" >                                isDisabled={isDisabled}</span>
<span class="cstat-no" title="statement not covered" >                                onClick={() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                                    if(isDisabled) {return;}</span>
<span class="cstat-no" title="statement not covered" >                                    setActiveTool(activeTool === tab.id ? null : tab.id);</span>
<span class="cstat-no" title="statement not covered" >                                }}</span>
                            &gt;
<span class="cstat-no" title="statement not covered" >                                &lt;tab.icon className="w-6 h-6"/&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/TooltipIconButton&gt;</span>
                        );
<span class="cstat-no" title="statement not covered" >                    })}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/&gt;</span>
    );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default DocumentUtilityPanel;</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    