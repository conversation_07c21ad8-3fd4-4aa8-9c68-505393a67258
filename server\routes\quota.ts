import { Router } from 'express';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient } from '../supabaseClient';

const router = Router();

function planLimit(planName?: string | null): number | null {
  if (!planName || planName === 'Registered User') {return 5;}
  return null; // null => unlimited on client
}

router.get('/', requireAuth, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data: auth } = await supa.auth.getUser();
  const userId = auth.user?.id;
  if (!userId) {return res.status(401).json({ error: 'Unauthorized' });}

  // Get plan name
  const { data: profile } = await supa.from('profiles').select('plan_name').eq('id', userId).single();
  const planName: string = (profile?.plan_name as string) || 'Registered User';
  const limit = planLimit(planName);

  // Month-to-date usage
  const now = new Date();
  const periodStart = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();
  const { count, error } = await supa
    .from('usage_events')
    .select('id', { count: 'exact', head: true })
    .gte('created_at', periodStart);
  if (error) {return res.status(500).json({ error: error.message });}
  const usedCount = count ?? 0;
  const nextMonth = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1);
  const periodEnd = nextMonth.toISOString();

  res.json({ planName, limit, used: usedCount, remaining: limit === null ? null : Math.max(0, limit - usedCount), period: { start: periodStart, end: periodEnd } });
});

export default router;
