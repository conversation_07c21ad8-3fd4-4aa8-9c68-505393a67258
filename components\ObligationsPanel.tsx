import React from 'react';
import { Document as DocType, Obligation } from '../types';
import { Button } from './ui/Button';
import { ClockIcon, CheckCircleIcon } from './Icons';

interface ObligationsPanelProps {
  document: DocType;
  onUpdateStatus: (docId: string, obligationId: string, status: Obligation['status']) => void;
}

const ObligationsPanel: React.FC<ObligationsPanelProps> = ({ document, onUpdateStatus }) => {
    const obligations = document.obligations || [];

    if (document.status !== 'completed' || obligations.length === 0) {
        return (
            <div className="flex-1 flex items-center justify-center p-6 text-center">
                <p className="text-sm text-zinc-500 dark:text-zinc-400">
                    No obligations found. AI will extract obligations once the document is completed.
                </p>
            </div>
        );
    }
    
    return (
        <div className="flex-1 overflow-y-auto p-4 space-y-3">
            <h4 className="font-semibold text-zinc-800 dark:text-zinc-200 px-2">Key Obligations</h4>
            {obligations.map(ob => {
                const isPending = ob.status === 'pending';
                const Icon = isPending ? ClockIcon : CheckCircleIcon;
                return (
                    <div key={ob.id} className="p-3 rounded-lg border bg-zinc-50 dark:bg-zinc-800/50">
                        <p className="text-sm text-zinc-700 dark:text-zinc-300">{ob.description}</p>
                        <div className="flex items-center justify-between mt-2">
                            <div className="flex items-center gap-2 text-xs text-zinc-500 dark:text-zinc-400">
                                <Icon className="w-4 h-4" />
                                <span>Due: {new Date(ob.dueDate).toLocaleDateString()}</span>
                            </div>
                            {isPending && (
                                <Button size="sm" variant="outline" onClick={() => onUpdateStatus(document.id, ob.id, 'completed')}>
                                    Mark Complete
                                </Button>
                            )}
                        </div>
                    </div>
                );
            })}
        </div>
    );
};

export default ObligationsPanel;
