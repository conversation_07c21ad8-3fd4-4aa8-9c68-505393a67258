import React, { useState, useEffect } from 'react';
import { Button } from './ui/Button';
import { CloseIcon, ArrowRightIcon } from './Icons';
import { User, Flow, Connector } from '../types';
import { ALL_CONNECTORS } from './connectors';
import { cn } from '../lib/utils';

interface FlowEditorModalProps {
  isOpen: boolean;
  onClose: () => void;
  user: User;
  onSave: (flowData: Omit<Flow, 'id' | 'userId' | 'createdAt'>, flowId?: string) => void;
  initialFlow?: Flow | null;
}

type FlowData = {
    name: string;
    triggerConnectionId: string | null;
    triggerKey: string | null;
    actionConnectionId: string | null;
    actionKey: string | null;
    fieldMapping: Record<string, string>;
};

const FlowEditorModal: React.FC<FlowEditorModalProps> = ({ isOpen, onClose, user, onSave, initialFlow }) => {
    const [step, setStep] = useState(1);
    const [flowData, setFlowData] = useState<FlowData>({
        name: '',
        triggerConnectionId: null,
        triggerKey: null,
        actionConnectionId: null,
        actionKey: null,
        fieldMapping: {},
    });

    useEffect(() => {
        if (initialFlow) {
            setFlowData({
                name: initialFlow.name,
                triggerConnectionId: initialFlow.trigger.connectionId,
                triggerKey: initialFlow.trigger.triggerKey,
                actionConnectionId: initialFlow.action.connectionId,
                actionKey: initialFlow.action.actionKey,
                fieldMapping: initialFlow.fieldMapping,
            });
            setStep(1);
        } else {
             setFlowData({ name: '', triggerConnectionId: null, triggerKey: null, actionConnectionId: null, actionKey: null, fieldMapping: {} });
             setStep(1);
        }
    }, [initialFlow, isOpen]);

    if (!isOpen) {return null;}

    const userConnections = user.connections || [];
    
    const getConnectorById = (id: string) => ALL_CONNECTORS.find(c => c.id === id);

    const getConnectorForConnection = (connectionId: string): Connector | undefined => {
        const conn = userConnections.find(c => c.id === connectionId);
        return conn ? getConnectorById(conn.connectorId) : undefined;
    };

    const selectedTriggerConnector = flowData.triggerConnectionId ? getConnectorForConnection(flowData.triggerConnectionId) : null;
    const selectedActionConnector = flowData.actionConnectionId ? getConnectorForConnection(flowData.actionConnectionId) : null;
    
    const selectedTrigger = selectedTriggerConnector?.triggers?.find(t => t.key === flowData.triggerKey);
    const selectedAction = selectedActionConnector?.actions?.find(a => a.key === flowData.actionKey);

    const handleSave = () => {
        if (!flowData.name || !flowData.triggerConnectionId || !flowData.triggerKey || !flowData.actionConnectionId || !flowData.actionKey) {
            return;
        }
        onSave({
            name: flowData.name,
            trigger: { connectionId: flowData.triggerConnectionId, triggerKey: flowData.triggerKey },
            action: { connectionId: flowData.actionConnectionId, actionKey: flowData.actionKey },
            fieldMapping: flowData.fieldMapping,
            status: initialFlow?.status || 'inactive'
        }, initialFlow?.id);
    };

    const steps = [
        { name: 'Trigger', isComplete: !!(flowData.triggerConnectionId && flowData.triggerKey) },
        { name: 'Action', isComplete: !!(flowData.actionConnectionId && flowData.actionKey) },
        { name: 'Map Fields', isComplete: true },
        { name: 'Review', isComplete: !!flowData.name },
    ];

    const renderStepContent = () => {
        switch (step) {
            case 1: // Select Trigger
                return (
                    <div>
                        <h3 className="font-semibold text-lg mb-2">1. Choose a Trigger</h3>
                        <p className="text-sm text-zinc-500 mb-4">Select the event that starts your flow.</p>
                        <div className="grid grid-cols-2 gap-4">
                             <div>
                                <label className="font-medium text-sm">App</label>
                                <div className="space-y-2 mt-2">
                                    {userConnections.map(conn => {
                                        const connector = getConnectorById(conn.connectorId);
                                        if (!connector || !connector.triggers || connector.triggers.length === 0) {return null;}
                                        return (
                                            <button key={conn.id} onClick={() => setFlowData({...flowData, triggerConnectionId: conn.id, triggerKey: null })} className={cn("w-full flex items-center gap-3 p-3 border rounded-lg text-left", flowData.triggerConnectionId === conn.id ? 'border-brand-500 ring-2 ring-brand-200' : 'border-zinc-300')}>
                                                <connector.icon className="w-6 h-6"/> {connector.name}
                                            </button>
                                        )
                                    })}
                                </div>
                            </div>
                            {selectedTriggerConnector && (
                                <div>
                                     <label className="font-medium text-sm">Event</label>
                                     <div className="space-y-2 mt-2">
                                        {selectedTriggerConnector.triggers?.map(trigger => (
                                            <button key={trigger.key} onClick={() => setFlowData({...flowData, triggerKey: trigger.key})} className={cn("w-full p-3 border rounded-lg text-left", flowData.triggerKey === trigger.key ? 'border-brand-500 ring-2 ring-brand-200' : 'border-zinc-300')}>
                                                <p className="font-semibold">{trigger.name}</p>
                                                <p className="text-xs text-zinc-500">{trigger.description}</p>
                                            </button>
                                        ))}
                                     </div>
                                </div>
                            )}
                        </div>
                    </div>
                );
            case 2: // Select Action
                return (
                     <div>
                        <h3 className="font-semibold text-lg mb-2">2. Choose an Action</h3>
                        <p className="text-sm text-zinc-500 mb-4">Select the event that your flow will perform.</p>
                        <div className="grid grid-cols-2 gap-4">
                             <div>
                                <label className="font-medium text-sm">App</label>
                                <div className="space-y-2 mt-2">
                                    {userConnections.map(conn => {
                                        const connector = getConnectorById(conn.connectorId);
                                        if (!connector || !connector.actions || connector.actions.length === 0) {return null;}
                                        return (
                                            <button key={conn.id} onClick={() => setFlowData({...flowData, actionConnectionId: conn.id, actionKey: null })} className={cn("w-full flex items-center gap-3 p-3 border rounded-lg text-left", flowData.actionConnectionId === conn.id ? 'border-brand-500 ring-2 ring-brand-200' : 'border-zinc-300')}>
                                                <connector.icon className="w-6 h-6"/> {connector.name}
                                            </button>
                                        )
                                    })}
                                </div>
                            </div>
                            {selectedActionConnector && (
                                <div>
                                     <label className="font-medium text-sm">Event</label>
                                     <div className="space-y-2 mt-2">
                                        {selectedActionConnector.actions?.map(action => (
                                            <button key={action.key} onClick={() => setFlowData({...flowData, actionKey: action.key})} className={cn("w-full p-3 border rounded-lg text-left", flowData.actionKey === action.key ? 'border-brand-500 ring-2 ring-brand-200' : 'border-zinc-300')}>
                                                <p className="font-semibold">{action.name}</p>
                                                <p className="text-xs text-zinc-500">{action.description}</p>
                                            </button>
                                        ))}
                                     </div>
                                </div>
                            )}
                        </div>
                    </div>
                );
            case 3: // Map fields
                 return (
                     <div>
                        <h3 className="font-semibold text-lg mb-2">3. Map Data Fields</h3>
                        <p className="text-sm text-zinc-500 mb-4">Connect the data from your trigger to your action.</p>
                        {(selectedAction?.operation.inputFields || []).map(inputField => (
                            <div key={inputField.key} className="grid grid-cols-2 items-center gap-4 mb-3 p-3 bg-zinc-50 rounded-lg">
                                <div className="text-right">
                                    <p className="font-medium">{inputField.label}</p>
                                    <p className="text-xs text-zinc-500">{selectedActionConnector?.name}</p>
                                </div>
                                <div>
                                    <select 
                                        value={flowData.fieldMapping[inputField.key] || ''} 
                                        onChange={(e) => setFlowData(prev => ({...prev, fieldMapping: {...prev.fieldMapping, [inputField.key]: e.target.value}}))}
                                        className="w-full p-2 border border-zinc-300 rounded-md"
                                    >
                                        <option value="">Select a field...</option>
                                        {(selectedTrigger?.operation.outputFields || []).map(outputField => (
                                            <option key={outputField.key} value={outputField.key}>{outputField.label}</option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                        ))}
                    </div>
                );
            case 4: // Review
                return (
                     <div>
                        <h3 className="font-semibold text-lg mb-2">4. Review and Save</h3>
                        <p className="text-sm text-zinc-500 mb-4">Give your flow a name and save it.</p>
                        <div className="p-4 bg-zinc-50 rounded-lg mb-4">
                            <p><strong>Trigger:</strong> {selectedTrigger?.name} in {selectedTriggerConnector?.name}</p>
                            <p><strong>Action:</strong> {selectedAction?.name} in {selectedActionConnector?.name}</p>
                        </div>
                         <div>
                            <label htmlFor="flowName" className="font-medium text-sm">Flow Name</label>
                            <input
                                id="flowName"
                                type="text"
                                value={flowData.name}
                                onChange={e => setFlowData({...flowData, name: e.target.value})}
                                placeholder="e.g., Create Invoice from Completed Contract"
                                className="w-full mt-1 p-2 border border-zinc-300 rounded-md"
                            />
                        </div>
                    </div>
                );
            default: return null;
        }
    }

    return (
        <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-4xl h-[80vh] flex flex-col">
                <header className="flex items-center justify-between p-4 border-b dark:border-zinc-800">
                    <h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200">{initialFlow ? 'Edit Flow' : 'Create New Flow'}</h2>
                    <Button variant="ghost" size="icon" onClick={onClose}><CloseIcon className="w-5 h-5"/></Button>
                </header>
                <div className="p-6 border-b dark:border-zinc-800">
                    <ol className="flex items-center w-full">
                        {steps.map((s, index) => (
                            <li key={s.name} className={cn("flex w-full items-center", index < steps.length - 1 && "after:content-[''] after:w-full after:h-1 after:border-b after:border-zinc-300 dark:after:border-zinc-700 after:border-1 after:inline-block", index < step - 1 && 'after:border-brand-500')}>
                                <span className={cn("flex items-center justify-center w-10 h-10 rounded-full shrink-0", index < step ? "bg-brand-600 text-white" : "bg-zinc-200 dark:bg-zinc-700 dark:text-zinc-200")}>
                                    {index + 1}
                                </span>
                            </li>
                        ))}
                    </ol>
                </div>
                <main className="p-6 flex-1 overflow-y-auto">
                    {renderStepContent()}
                </main>
                 <footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-between items-center rounded-b-2xl border-t dark:border-zinc-800">
                    <Button variant="outline" onClick={() => setStep(s => Math.max(1, s - 1))} disabled={step === 1}>Back</Button>
                    <div>
                    {step < 4 ? (
                        <Button onClick={() => setStep(s => Math.min(4, s + 1))} disabled={!steps[step-1].isComplete}>
                            Continue <ArrowRightIcon className="w-4 h-4 ml-2"/>
                        </Button>
                    ) : (
                        <Button onClick={handleSave} disabled={!steps[step-1].isComplete}>
                            {initialFlow ? 'Save Flow' : 'Save & Activate'}
                        </Button>
                    )}
                    </div>
                </footer>
            </div>
        </div>
    );
};

export default FlowEditorModal;
