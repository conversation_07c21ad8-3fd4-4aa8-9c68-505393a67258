import Stripe from 'stripe';
import { supabaseAdmin } from '../supabaseClient';
import { logger } from './logger';

const API_VERSION: Stripe.LatestApiVersion = '2024-06-20';
const CACHE_TTL_MS = 5 * 60 * 1000;

type StripeMode = 'live' | 'test';

interface StripeCredentials {
  secretKey: string;
  mode: StripeMode;
}

let cachedCredentials: { creds: StripeCredentials; fetchedAt: number } | null = null;
let cachedClient: { secretKey: string; stripe: Stripe } | null = null;

async function fetchCredentials(): Promise<StripeCredentials | null> {
  const envSecret = process.env.STRIPE_SECRET_KEY;
  if (envSecret) {
    const modeEnv = (process.env.STRIPE_MODE || process.env.STRIPE_ENV || '').toLowerCase();
    const mode: StripeMode = modeEnv === 'live' ? 'live' : 'test';
    return { secretKey: envSecret, mode };
  }

  try {
    const { data, error } = await supabaseAdmin
      .from('stripe_config')
      .select('is_live_mode, live_secret_key, test_secret_key')
      .eq('id', 1)
      .single();

    if (error) {
      logger.error({ err: error }, 'Failed to load Stripe configuration from database');
      return null;
    }

    if (!data) {
      logger.error('Stripe configuration row not found in database');
      return null;
    }

    const secretKey = data.is_live_mode ? data.live_secret_key : data.test_secret_key;
    if (!secretKey) {
      logger.error('Stripe secret key missing from configuration');
      return null;
    }

    return { secretKey, mode: data.is_live_mode ? 'live' : 'test' };
  } catch (err) {
    logger.error({ err }, 'Unexpected error retrieving Stripe credentials');
    return null;
  }
}

export async function getStripeClient(): Promise<{ stripe: Stripe; mode: StripeMode } | null> {
  const now = Date.now();
  if (cachedCredentials && now - cachedCredentials.fetchedAt < CACHE_TTL_MS) {
    const { creds } = cachedCredentials;
    if (!cachedClient || cachedClient.secretKey !== creds.secretKey) {
      cachedClient = {
        secretKey: creds.secretKey,
        stripe: new Stripe(creds.secretKey, { apiVersion: API_VERSION }),
      };
    }
    return { stripe: cachedClient.stripe, mode: creds.mode };
  }

  const creds = await fetchCredentials();
  if (!creds) {
    return null;
  }

  cachedCredentials = { creds, fetchedAt: now };
  if (!cachedClient || cachedClient.secretKey !== creds.secretKey) {
    cachedClient = {
      secretKey: creds.secretKey,
      stripe: new Stripe(creds.secretKey, { apiVersion: API_VERSION }),
    };
  }

  return { stripe: cachedClient.stripe, mode: creds.mode };
}

export function resetStripeCache(): void {
  cachedCredentials = null;
  cachedClient = null;
}
