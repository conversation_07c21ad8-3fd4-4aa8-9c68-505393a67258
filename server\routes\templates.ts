import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient, supabaseAdmin } from '../supabaseClient';
import { requireAdmin } from '../middleware/admin';
import { getFromCache, setCache, deleteFromCache, cacheKeys, CACHE_TTL } from '../lib/redis';

const router = Router();

// Public templates are readable by anyone; still require auth to simplify client logic
router.get('/', requireAuth, async (req: AuthRequest, res) => {
  const cacheKey = cacheKeys.templates();
  
  // Try to get from cache first
  const cachedTemplates = await getFromCache(cacheKey);
  if (cachedTemplates) {
    return res.json({ templates: cachedTemplates });
  }

  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('templates').select('id, title, description, category, required_plan').order('id');
  if (error) {return res.status(500).json({ error: error.message });}
  
  // Cache the result
  await setCache(cacheKey, data, CACHE_TTL.TEMPLATES);
  
  res.json({ templates: data });
});

// Admin-only modifications to public templates
const tplSchema = z.object({ title: z.string().min(1), description: z.string().min(1), category: z.string().min(1), prompt: z.string().min(1), requiredPlan: z.enum(['Registered User','Premium']) });
router.post('/', requireAuth, requireAdmin, async (_req: AuthRequest, res) => {
  const parsed = tplSchema.safeParse(_req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const { data, error } = await supabaseAdmin.from('templates').insert({
    title: parsed.data.title,
    description: parsed.data.description,
    category: parsed.data.category,
    prompt: parsed.data.prompt,
    required_plan: parsed.data.requiredPlan,
  }).select('id, title, description, category, required_plan').single();
  if (error) {return res.status(500).json({ error: error.message });}
  
  // Invalidate templates cache
  await deleteFromCache(cacheKeys.templates());
  
  res.status(201).json({ template: data });
});

router.put('/:id', requireAuth, requireAdmin, async (_req: AuthRequest<{ id: string }>, res) => {
  const parsed = tplSchema.partial().safeParse(_req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{
    title: string;
    description: string;
    category: string;
    prompt: string;
    required_plan: string;
  }> = {
    title: parsed.data.title,
    description: parsed.data.description,
    category: parsed.data.category,
    prompt: parsed.data.prompt,
    required_plan: parsed.data.requiredPlan,
  };
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const { data, error } = await supabaseAdmin.from('templates').update(patch).eq('id', _req.params.id).select('id, title, description, category, required_plan').single();
  if (error) {return res.status(500).json({ error: error.message });}
  
  // Invalidate templates cache
  await deleteFromCache(cacheKeys.templates());
  
  res.json({ template: data });
});

router.delete('/:id', requireAuth, requireAdmin, async (_req: AuthRequest<{ id: string }>, res) => {
  const { error } = await supabaseAdmin.from('templates').delete().eq('id', _req.params.id);
  if (error) {return res.status(500).json({ error: error.message });}
  
  // Invalidate templates cache
  await deleteFromCache(cacheKeys.templates());
  
  res.status(204).end();
});

export default router;
