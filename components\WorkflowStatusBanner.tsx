import React from 'react';
import { WorkflowInstance, WorkflowTemplate } from '../types';
import { GitBranchIcon } from './Icons';
import { cn } from '../lib/utils';

interface WorkflowStatusBannerProps {
  instance: WorkflowInstance;
  template: WorkflowTemplate;
}

const WorkflowStatusBanner: React.FC<WorkflowStatusBannerProps> = ({ instance, template }) => {
    const currentNode = template.nodes.find(n => n.id === instance.currentNodeId);
    if (!currentNode) {return null;}

    return (
        <div className={cn("p-3 border-b text-center text-sm flex items-center justify-center gap-2", "bg-blue-100 border-blue-200 text-blue-800 dark:bg-blue-900/30 dark:border-blue-800 dark:text-blue-200")}>
            <GitBranchIcon className="w-5 h-5" />
            <span className="font-semibold">Workflow Active:</span>
            <span>{template.name} - </span>
            <span className="italic">{currentNode.data.label}</span>
        </div>
    );
};

export default WorkflowStatusBanner;
