-- Add performance indexes for better query performance
-- This migration adds missing indexes on frequently queried columns

-- Teams table indexes
create index if not exists teams_owner_id_idx on public.teams(owner_id);
create index if not exists teams_status_idx on public.teams(status);
create index if not exists teams_created_at_idx on public.teams(created_at desc);

-- Comments table indexes
create index if not exists comments_thread_id_idx on public.comments(thread_id);
create index if not exists comments_author_email_idx on public.comments(author_email);
create index if not exists comments_created_at_idx on public.comments(created_at desc);

-- Comment threads table indexes
create index if not exists comment_threads_document_id_idx on public.comment_threads(document_id);
create index if not exists comment_threads_is_resolved_idx on public.comment_threads(is_resolved);
create index if not exists comment_threads_created_at_idx on public.comment_threads(created_at desc);

-- Document collaborators indexes
create index if not exists document_collaborators_document_id_idx on public.document_collaborators(document_id);
create index if not exists document_collaborators_email_idx on public.document_collaborators(email);
create index if not exists document_collaborators_permission_idx on public.document_collaborators(permission);

-- Notifications indexes
create index if not exists notifications_user_id_idx on public.notifications(user_id);
create index if not exists notifications_type_idx on public.notifications(type);
create index if not exists notifications_is_read_idx on public.notifications(is_read);
create index if not exists notifications_created_at_idx on public.notifications(created_at desc);
create index if not exists notifications_document_id_idx on public.notifications(document_id);

-- Signatures indexes
create index if not exists signatures_document_id_idx on public.signatures(document_id);
create index if not exists signatures_email_idx on public.signatures(email);
create index if not exists signatures_status_idx on public.signatures(status);
create index if not exists signatures_token_idx on public.signatures(token);

-- Document key dates indexes
create index if not exists document_key_dates_document_id_idx on public.document_key_dates(document_id);
create index if not exists document_key_dates_date_idx on public.document_key_dates(date);

-- Document approvers indexes
create index if not exists document_approvers_document_id_idx on public.document_approvers(document_id);
create index if not exists document_approvers_email_idx on public.document_approvers(email);
create index if not exists document_approvers_status_idx on public.document_approvers(status);

-- Obligations indexes
create index if not exists obligations_document_id_idx on public.obligations(document_id);
create index if not exists obligations_due_date_idx on public.obligations(due_date);
create index if not exists obligations_status_idx on public.obligations(status);
create index if not exists obligations_owner_email_idx on public.obligations(owner_email);

-- Activity logs indexes
create index if not exists activity_logs_document_id_idx on public.activity_logs(document_id);
create index if not exists activity_logs_user_email_idx on public.activity_logs(user_email);
create index if not exists activity_logs_type_idx on public.activity_logs(type);
create index if not exists activity_logs_timestamp_idx on public.activity_logs(timestamp desc);

-- Custom templates indexes
create index if not exists custom_templates_user_id_idx on public.custom_templates(user_id);
create index if not exists custom_templates_created_at_idx on public.custom_templates(created_at desc);

-- Team members indexes
create index if not exists team_members_team_id_idx on public.team_members(team_id);
create index if not exists team_members_user_id_idx on public.team_members(user_id);
create index if not exists team_members_email_idx on public.team_members(email);
create index if not exists team_members_role_idx on public.team_members(role);

-- Team API keys indexes
create index if not exists team_api_keys_team_id_idx on public.team_api_keys(team_id);
create index if not exists team_api_keys_created_at_idx on public.team_api_keys(created_at desc);

-- Profiles indexes
create index if not exists profiles_username_idx on public.profiles(username);
create index if not exists profiles_status_idx on public.profiles(status);
create index if not exists profiles_plan_name_idx on public.profiles(plan_name);
create index if not exists profiles_team_id_idx on public.profiles(team_id);
create index if not exists profiles_subscription_status_idx on public.profiles(subscription_status);

-- Workflow templates indexes
create index if not exists workflow_templates_team_id_idx on public.workflow_templates(team_id);
create index if not exists workflow_templates_status_idx on public.workflow_templates(status);

-- Workflow nodes indexes
create index if not exists workflow_nodes_template_id_idx on public.workflow_nodes(template_id);
create index if not exists workflow_nodes_type_idx on public.workflow_nodes(type);

-- Workflow edges indexes
create index if not exists workflow_edges_template_id_idx on public.workflow_edges(template_id);
create index if not exists workflow_edges_source_idx on public.workflow_edges(source);
create index if not exists workflow_edges_target_idx on public.workflow_edges(target);

-- Workflow instances indexes
create index if not exists workflow_instances_template_id_idx on public.workflow_instances(template_id);
create index if not exists workflow_instances_document_id_idx on public.workflow_instances(document_id);
create index if not exists workflow_instances_user_id_idx on public.workflow_instances(user_id);
create index if not exists workflow_instances_status_idx on public.workflow_instances(status);
create index if not exists workflow_instances_created_at_idx on public.workflow_instances(created_at desc);

-- User connections indexes
create index if not exists user_connections_user_id_idx on public.user_connections(user_id);
create index if not exists user_connections_connector_id_idx on public.user_connections(connector_id);
create index if not exists user_connections_created_at_idx on public.user_connections(created_at desc);

-- Flows indexes
create index if not exists flows_user_id_idx on public.flows(user_id);
create index if not exists flows_trigger_connection_id_idx on public.flows(trigger_connection_id);
create index if not exists flows_action_connection_id_idx on public.flows(action_connection_id);
create index if not exists flows_status_idx on public.flows(status);
create index if not exists flows_created_at_idx on public.flows(created_at desc);

-- Flow runs indexes
create index if not exists flow_runs_flow_id_idx on public.flow_runs(flow_id);
create index if not exists flow_runs_user_id_idx on public.flow_runs(user_id);
create index if not exists flow_runs_status_idx on public.flow_runs(status);
create index if not exists flow_runs_started_at_idx on public.flow_runs(started_at desc);

-- Integrations indexes
create index if not exists integrations_user_id_idx on public.integrations(user_id);
create index if not exists integrations_status_idx on public.integrations(status);
create index if not exists integrations_name_idx on public.integrations(name);

-- Usage events indexes (for analytics and quota tracking)
create index if not exists usage_events_user_id_idx on public.usage_events(user_id);
create index if not exists usage_events_event_idx on public.usage_events(event);
create index if not exists usage_events_created_at_idx on public.usage_events(created_at desc);

-- Composite indexes for common query patterns
-- Documents by user and status
create index if not exists documents_user_status_idx on public.documents(user_id, status);
-- Documents by user and folder
create index if not exists documents_user_folder_idx on public.documents(user_id, folder_id);
-- Documents by user and client
create index if not exists documents_user_client_idx on public.documents(user_id, client_id);
-- Notifications by user and read status
create index if not exists notifications_user_read_idx on public.notifications(user_id, is_read);
-- Activity logs by document and timestamp
create index if not exists activity_logs_document_timestamp_idx on public.activity_logs(document_id, timestamp desc);
-- Team members by team and role
create index if not exists team_members_team_role_idx on public.team_members(team_id, role);
-- Workflow instances by user and status
create index if not exists workflow_instances_user_status_idx on public.workflow_instances(user_id, status);