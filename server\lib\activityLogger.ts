import { SupabaseClient } from '@supabase/supabase-js';

export interface ActivityLogData {
  documentId?: string | null;
  userEmail: string;
  type: 'create' | 'edit' | 'share' | 'comment' | 'view' | 'signature' | 'revert' | 'team' | 'approval';
  details: string;
  timestamp?: string;
}

/**
 * Creates an activity log entry in the database
 * This is a best-effort operation - failures are logged but don't break the main workflow
 */
export async function createActivityLog(
  supabaseClient: SupabaseClient,
  logData: ActivityLogData
): Promise<void> {
  try {
    // Check if supabaseClient is valid and has the from method
    if (!supabaseClient || typeof supabaseClient.from !== 'function') {
      console.warn('Invalid supabase client provided to createActivityLog');
      return;
    }

    const fromResult = supabaseClient.from('activity_logs');
    if (!fromResult || typeof fromResult.insert !== 'function') {
      console.warn('Supabase client from() method returned invalid result');
      return;
    }

    const { error } = await fromResult.insert({
        document_id: logData.documentId ?? null,
        user_email: logData.userEmail,
        type: logData.type,
        details: logData.details,
        timestamp: logData.timestamp ?? new Date().toISOString(),
      });

    if (error) {
      console.error('Failed to create activity log:', error);
    }
  } catch (err) {
    // Log the error but don't throw - activity logging should not break main workflows
    console.error('Activity log creation failed:', err);
  }
}

/**
 * Helper function to get user email from request context
 */
export function getUserEmailFromRequest(req: any): string {
  // Try to get email from user object first
  if (req.user?.email) {
    return req.user.email;
  }

  // Fallback to a default or extract from token if needed
  return '<EMAIL>';
}

/**
 * Activity log creation helpers for common actions
 */
export const ActivityLogHelpers = {
  documentCreated: (userEmail: string, documentId: string, documentName: string): ActivityLogData => ({
    documentId,
    userEmail,
    type: 'create',
    details: `Created document "${documentName}"`,
  }),

  documentEdited: (userEmail: string, documentId: string, documentName: string): ActivityLogData => ({
    documentId,
    userEmail,
    type: 'edit',
    details: `Edited document "${documentName}"`,
  }),

  documentShared: (userEmail: string, documentId: string, documentName: string, sharedWith: string[]): ActivityLogData => ({
    documentId,
    userEmail,
    type: 'share',
    details: `Shared document "${documentName}" with ${sharedWith.join(', ')}`,
  }),

  commentAdded: (userEmail: string, documentId: string, documentName: string): ActivityLogData => ({
    documentId,
    userEmail,
    type: 'comment',
    details: `Added comment to document "${documentName}"`,
  }),

  documentReverted: (userEmail: string, documentId: string, documentName: string, versionId: string): ActivityLogData => ({
    documentId,
    userEmail,
    type: 'revert',
    details: `Reverted document "${documentName}" to version ${versionId}`,
  }),

  documentSigned: (userEmail: string, documentId: string, documentName: string): ActivityLogData => ({
    documentId,
    userEmail,
    type: 'signature',
    details: `Signed document "${documentName}"`,
  }),
};
