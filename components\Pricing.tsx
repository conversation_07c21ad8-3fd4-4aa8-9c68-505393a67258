

import React from 'react';
import { PricingPlan } from '../types';
import { CheckIcon } from './Icons';

interface PricingProps {
  setView: (view: 'home' | 'auth') => void;
  pricingPlans: PricingPlan[];
}

const Pricing: React.FC<PricingProps> = ({ setView, pricingPlans }) => {
  return (
    <section id="pricing" className="py-20 bg-zinc-100 dark:bg-zinc-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-zinc-900 dark:text-white sm:text-4xl">
            Choose a Plan That's Right for You
          </h2>
          <p className="mt-4 text-lg text-zinc-600 dark:text-zinc-400">
            Start for free, and scale up as you grow. No hidden fees.
          </p>
        </div>
        {pricingPlans.length === 0 ? (
          <div className="mt-12 text-center text-zinc-600 dark:text-zinc-400">
            Pricing is being refreshed. Please check back soon or contact our team for a custom quote.
          </div>
        ) : (
          <div className="mt-12 space-y-8 lg:space-y-0 lg:grid lg:grid-cols-3 lg:gap-8">
            {pricingPlans.map((plan) => (
              <div
                key={plan.id || plan.name}
                className={`bg-white dark:bg-zinc-950 rounded-2xl shadow-lg p-8 relative flex flex-col ${plan.isFeatured ? 'border-2 border-brand-600' : 'dark:border dark:border-zinc-800'}`}
              >
                {plan.isFeatured && (
                  <div className="absolute top-0 -translate-y-1/2 w-full flex justify-center">
                      <span className="px-4 py-1 text-sm font-semibold tracking-wider text-white bg-brand-600 rounded-full uppercase">Most Popular</span>
                  </div>
                )}
                <h3 className="text-2xl font-semibold text-zinc-900 dark:text-white">{plan.name}</h3>
                <div className="mt-4 flex items-baseline text-zinc-900 dark:text-white">
                  <span className="text-5xl font-extrabold tracking-tight">{plan.price}</span>
                  {plan.priceDetail && <span className="ml-1 text-xl font-semibold text-zinc-500 dark:text-zinc-400">{plan.priceDetail}</span>}
                </div>
                <ul role="list" className="mt-6 space-y-4 flex-1">
                  {plan.features.map((feature) => (
                    <li key={feature} className="flex space-x-3">
                      <CheckIcon className="flex-shrink-0 h-6 w-6 text-brand-500" />
                      <span className="text-zinc-600 dark:text-zinc-300">{feature}</span>
                    </li>
                  ))}
                </ul>
                <button
                  onClick={() => setView('auth')}
                  className={`w-full text-center mt-8 block px-6 py-3 text-base font-medium rounded-lg shadow-md ${
                    plan.isFeatured
                      ? 'text-white bg-brand-600 hover:bg-brand-700'
                      : 'text-white bg-zinc-800 hover:bg-zinc-900'
                  }`}
                >
                  {plan.cta}
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
};

export default Pricing;