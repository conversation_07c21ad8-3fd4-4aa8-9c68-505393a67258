import { describe, it, expect, beforeAll, vi } from 'vitest';
import request from 'supertest';

describe('AI SSE route', () => {
  beforeAll(async () => {
    process.env.TEST_BYPASS_AUTH = '1';
    process.env.GEMINI_API_KEY = 'test';
  });

  it('streams data and done events', async () => {
    vi.doMock('@google/genai', () => {
      const iter = async function* () {
        yield { text: 'Hello ' } as any;
        yield { text: 'World' } as any;
      };
      return {
        GoogleGenAI: class {
          chats = { create: () => ({ sendMessageStream: async () => iter() }) };
        },
        Type: {},
      };
    });
    const { app } = await import('../server/app');
    const res = await request(app).get('/api/ai/chat/stream?prompt=hi');
    expect(res.status).toBe(200);
    const text = res.text || '';
    expect(text).toContain('data:');
    expect(text).toContain('event: done');
  });
});

