
import React from 'react';
import { MessageSquarePlusIcon, LibraryIcon } from '../Icons';
import Indicator from './Indicator';

const CommentingScreenshot: React.FC = () => {
  return (
    <div className="relative my-6 p-4 border rounded-lg bg-white dark:bg-zinc-800 overflow-hidden select-none font-[Georgia,serif] text-[11pt] leading-[1.7] text-zinc-900 dark:text-zinc-300">
      <Indicator number={1} position="top-10 left-20" arrow="down" />
      <Indicator number={2} position="top-0 left-1/2" arrow="down" />
      
      <div
        className="absolute bg-zinc-800 text-white rounded-lg shadow-xl flex items-center"
        style={{ top: '1.5rem', left: '50%', transform: 'translateX(-50%)' }}
      >
        <div className="p-2 hover:bg-zinc-700 rounded-l-lg"><MessageSquarePlusIcon className="w-5 h-5"/></div>
        <div className="w-px h-5 bg-zinc-600"></div>
        <div className="p-2 hover:bg-zinc-700 rounded-r-lg"><LibraryIcon className="w-5 h-5"/></div>
      </div>
      
      <h1 className="font-bold text-lg dark:text-white">Mutual Non-Disclosure Agreement</h1>
      <p>This Agreement is made between <span className="bg-blue-200 dark:bg-blue-900/50 px-1 rounded">Party A and Party B</span>...</p>
      <p>The term of this Agreement shall commence on the Effective Date and shall continue in full force and effect until December 31, 2025.</p>
    </div>
  );
};

export default CommentingScreenshot;
