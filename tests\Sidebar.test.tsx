// @vitest-environment jsdom
import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import Sidebar from '../components/Sidebar';
import React from 'react';

describe('Sidebar', () => {
  const mockUser = {
    id: 'u1',
    name: 'Test User',
    email: '<EMAIL>',
    documents: [],
    folders: [],
  };
  const setView = vi.fn();
  const setIsSidebarOpen = vi.fn();
  const setIsCollapsed = vi.fn();

  it('renders Sidebar component', () => {
    render(
      <Sidebar
        currentView="dashboard"
        setView={setView}
        isSidebarOpen={true}
        setIsSidebarOpen={setIsSidebarOpen}
        user={mockUser as any}
        isCollapsed={false}
        setIsCollapsed={setIsCollapsed}
      />
    );
    expect(screen.getByText(/Dashboard/i)).toBeInTheDocument();
  });
});
