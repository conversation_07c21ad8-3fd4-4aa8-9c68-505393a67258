import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest } from '../middleware/auth';
import { supabaseAdmin } from '../supabaseClient';
import { requireAdmin } from '../middleware/admin';

const router = Router();

router.get('/', requireAuth, async (_req: AuthRequest, res) => {
  // Read with user client; RLS denies unless public, so fallback to admin but return limited info
  const { data, error } = await supabaseAdmin.from('stripe_config').select('*').eq('id', 1).single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ stripeConfig: data });
});

const cfgSchema = z.object({
  livePublishableKey: z.string().optional(),
  liveSecretKey: z.string().optional(),
  testPublishableKey: z.string().optional(),
  testSecretKey: z.string().optional(),
  webhookSecret: z.string().optional(),
  isLiveMode: z.boolean().optional(),
});

router.put('/', requireAuth, requireAdmin, async (req: AuthRequest, res) => {
  const parsed = cfgSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{
    live_publishable_key: string;
    live_secret_key: string;
    test_publishable_key: string;
    test_secret_key: string;
    webhook_secret: string;
    is_live_mode: boolean;
  }> = {
    live_publishable_key: parsed.data.livePublishableKey,
    live_secret_key: parsed.data.liveSecretKey,
    test_publishable_key: parsed.data.testPublishableKey,
    test_secret_key: parsed.data.testSecretKey,
    webhook_secret: parsed.data.webhookSecret,
    is_live_mode: parsed.data.isLiveMode,
  };
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const { data, error } = await supabaseAdmin.from('stripe_config').upsert({ id: 1, ...patch }, { onConflict: 'id' }).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ stripeConfig: data });
});

export default router;

