

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/Card';
import { PricingPlan, User } from '../types';
import { Button } from './ui/Button';
import { CheckIcon } from './Icons';
import { cn } from '../lib/utils';
import BillingModal from './BillingModal';

interface SubscriptionPageProps {
  user: User;
  onChangePlan: (planName: string) => void;
  pricingPlans: PricingPlan[];
}

const SubscriptionPage: React.FC<SubscriptionPageProps> = ({ user, onChangePlan, pricingPlans }) => {
    const currentUserPlan = user.planName;
    const [isBillingModalOpen, setIsBillingModalOpen] = React.useState(false);
    const [statusMsg, setStatusMsg] = React.useState<string | null>(null);
    const [selectedPlan, setSelectedPlan] = React.useState<string | null>(null);

    const selectedPlanDetails = React.useMemo(() => {
        if (!selectedPlan) {return null;}
        return pricingPlans.find(plan => plan.name === selectedPlan) || null;
    }, [pricingPlans, selectedPlan]);

    const handleUpgradeClick = (planName: string) => {
        // For paid plans, show billing modal then call onChangePlan
        setSelectedPlan(planName);
        setIsBillingModalOpen(true);
    };

    const handlePaymentSuccess = (confirmedPlan: string) => {
        const planName = confirmedPlan || selectedPlan || 'Premium';
        onChangePlan(planName);
        setIsBillingModalOpen(false);
        setStatusMsg(`Subscription updated to ${planName}.`);
        setTimeout(() => setStatusMsg(null), 2500);
        setSelectedPlan(null);
    };

    return (
        <>
        <div className="p-4 sm:p-6 lg:p-8">
            {statusMsg && (
              <div className="mb-4 px-4 py-2 rounded-md bg-green-50 text-green-700 border border-green-200 text-sm">{statusMsg}</div>
            )}
            <Card>
                <CardHeader>
                    <CardTitle>Subscription Management</CardTitle>
                    <CardDescription>View your current plan and explore upgrade options.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-8">
                    <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                      {pricingPlans.map((plan) => {
                          const isCurrentPlan = plan.name === currentUserPlan;
                          return (
                              <div
                                  key={plan.name}
                                  className={cn(
                                      'bg-white dark:bg-zinc-950 rounded-2xl shadow-lg p-6 relative flex flex-col border',
                                      plan.isFeatured ? 'border-brand-600 border-2' : 'border-zinc-200 dark:border-zinc-800',
                                      isCurrentPlan ? 'bg-zinc-50 dark:bg-zinc-900' : ''
                                  )}
                              >
                                  {isCurrentPlan && (
                                      <div className="absolute top-0 -translate-y-1/2 w-full flex justify-center">
                                          <span className="px-3 py-1 text-xs font-semibold tracking-wider text-zinc-900 bg-zinc-200 dark:text-zinc-200 dark:bg-zinc-700 rounded-full uppercase">Current Plan</span>
                                      </div>
                                  )}
                                  {plan.isFeatured && !isCurrentPlan && (
                                      <div className="absolute top-0 -translate-y-1/2 w-full flex justify-center">
                                          <span className="px-4 py-1 text-sm font-semibold tracking-wider text-white bg-brand-600 rounded-full uppercase">Most Popular</span>
                                      </div>
                                  )}

                                  <h3 className="text-xl font-semibold text-zinc-900 dark:text-white">{plan.name}</h3>
                                  <div className="mt-4 flex items-baseline text-zinc-900 dark:text-white">
                                      <span className="text-4xl font-extrabold tracking-tight">{plan.price}</span>
                                      {plan.priceDetail && <span className="ml-1 text-lg font-semibold text-zinc-500 dark:text-zinc-400">{plan.priceDetail}</span>}
                                  </div>
                                  <ul role="list" className="mt-6 space-y-3 flex-1">
                                      {plan.features.map((feature) => (
                                          <li key={feature} className="flex space-x-3">
                                              <CheckIcon className="flex-shrink-0 h-5 w-5 text-brand-500" />
                                              <span className="text-sm text-zinc-600 dark:text-zinc-300">{feature}</span>
                                          </li>
                                      ))}
                                  </ul>
                                  <Button
                                      disabled={isCurrentPlan}
                                      onClick={!isCurrentPlan ? (
                                        plan.name === 'Registered User' ? () => { onChangePlan('Registered User'); setStatusMsg('Subscription downgraded to Registered User.'); setTimeout(()=>setStatusMsg(null), 2500); } : () => handleUpgradeClick(plan.name)
                                      ) : undefined}
                                      className={cn(
                                          'w-full mt-8',
                                          isCurrentPlan ? 'bg-zinc-300 dark:bg-zinc-700' : ''
                                      )}
                                  >
                                      {isCurrentPlan ? 'Your Current Plan' : plan.cta}
                                  </Button>
                              </div>
                          );
                      })}
                    </div>
                </CardContent>
            </Card>
        </div>
        <BillingModal
            isOpen={isBillingModalOpen}
            onClose={() => setIsBillingModalOpen(false)}
            onPaymentSuccess={handlePaymentSuccess}
            planName={selectedPlan || ''}
            planPrice={selectedPlanDetails?.price}
            planPriceDetail={selectedPlanDetails?.priceDetail}
        />
        </>
    );
};

export default SubscriptionPage;
