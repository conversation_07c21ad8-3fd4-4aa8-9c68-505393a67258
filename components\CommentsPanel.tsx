import React, { useState } from 'react';
import { Document as DocType, User } from '../types';
import { Button } from './ui/Button';
import { CheckSquareIcon } from './Icons';

interface CommentsPanelProps {
    document: DocType;
    currentUser: User;
    allUsers: User[];
    onAddReply: (documentId: string, threadId: string, content: string) => void;
    onResolveThread: (documentId: string, threadId: string) => void;
    onDeleteComment: (documentId: string, threadId: string, commentId: string) => void;
}

const CommentsPanel: React.FC<CommentsPanelProps> = ({ document, currentUser, allUsers, onAddReply, onResolveThread, onDeleteComment }) => {
    const [replyingTo, setReplyingTo] = useState<string | null>(null);
    const [replyContent, setReplyContent] = useState('');

    const handleAddReply = (threadId: string) => {
        if (!replyContent.trim()) {return;}
        onAddReply(document.id, threadId, replyContent);
        setReplyContent('');
        setReplyingTo(null);
    };

    const getAuthor = (email: string) => {
        return allUsers.find(u => u.email === email) || { name: 'Unknown User', avatarUrl: '' };
    }

    const activeThreads = document.commentThreads.filter(t => !t.isResolved);
    
    return (
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {activeThreads.length === 0 && (
                <div className="text-center text-sm text-slate-400 pt-8">
                    <p>No active comments.</p>
                    <p className="mt-2">Highlight text in the document to start a conversation.</p>
                </div>
            )}
            {activeThreads.map(thread => (
                <div key={thread.id} id={`comment-thread-${thread.id}`} className="comment-thread bg-slate-50 border rounded-lg p-3">
                    <div className="border-l-2 border-slate-300 pl-2 mb-2">
                        <blockquote className="text-sm italic text-slate-600">"{thread.textSelection}"</blockquote>
                    </div>
                    {thread.comments.map(comment => {
                        const author = getAuthor(comment.authorEmail);
                        return (
                            <div key={comment.id} className="flex items-start gap-2.5 my-3">
                                <img src={author.avatarUrl} alt={author.name} className="w-7 h-7 rounded-full" />
                                <div className="flex-1">
                                    <div className="flex items-center justify-between">
                                        <p className="text-sm font-semibold text-slate-800">{author.name}</p>
                                        <p className="text-xs text-slate-400">{new Date(comment.createdAt).toLocaleTimeString()}</p>
                                    </div>
                                    <p className="text-sm text-slate-700 whitespace-pre-wrap">{comment.content}</p>
                                    {currentUser.email === comment.authorEmail && (
                                        <button onClick={() => onDeleteComment(document.id, thread.id, comment.id)} className="text-xs text-red-500 hover:underline mt-1">Delete</button>
                                    )}
                                </div>
                            </div>
                        )
                    })}
                    <div className="mt-3">
                        {replyingTo === thread.id ? (
                            <div className="flex flex-col gap-2">
                                <textarea
                                    value={replyContent}
                                    onChange={(e) => setReplyContent(e.target.value)}
                                    placeholder="Add your reply..."
                                    className="block w-full text-sm rounded-md border-slate-300 focus:border-blue-500 focus:ring-blue-500 p-2"
                                    rows={2}
                                />
                                <div className="flex justify-end gap-2">
                                    <Button size="sm" variant="outline" onClick={() => setReplyingTo(null)}>Cancel</Button>
                                    <Button size="sm" onClick={() => handleAddReply(thread.id)}>Reply</Button>
                                </div>
                            </div>
                        ) : (
                            <div className="flex items-center gap-2">
                                <Button size="sm" variant="ghost" onClick={() => setReplyingTo(thread.id)}>Reply</Button>
                                <Button size="sm" variant="ghost" onClick={() => onResolveThread(document.id, thread.id)} className="text-green-600 hover:text-green-700 hover:bg-green-50">
                                    <CheckSquareIcon className="w-4 h-4 mr-1.5"/> Resolve
                                </Button>
                            </div>
                        )}
                    </div>
                </div>
            ))}
        </div>
    );
};

export default CommentsPanel;
