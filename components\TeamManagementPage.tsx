import React, { useState, useMemo } from 'react';
import { User, DashboardView, Team, TeamMember, TeamMemberRole } from '../types';
import { Button } from './ui/Button';
import { LockSolidIcon, PlusCircleIcon, SearchIcon, TrashIcon, ChevronDownIcon, CheckCircleIcon } from './Icons';
import { Card, CardContent, CardHeader } from './ui/Card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/Table';
import InviteMemberModal from './InviteMemberModal';
import Input from './ui/Input';
import Dropdown from './ui/Dropdown';

interface TeamManagementPageProps {
  user: User;
  team: Team | undefined;
  allUsers: User[];
  setView: (view: DashboardView) => void;
  onInviteMember: (email: string, role: TeamMemberRole) => string | null;
  onUpdateMemberRole: (userId: string, role: TeamMemberRole) => void;
  onRemoveMember: (userId: string) => void;
}

const TeamManagementPage: React.FC<TeamManagementPageProps> = (props) => {
  const { user, team, allUsers, setView, onInviteMember, onUpdateMemberRole, onRemoveMember } = props;
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [memberToRemove, setMemberToRemove] = useState<TeamMember | null>(null);

  const isPremium = user.planName === 'Premium' || user.planName === 'Enterprise';

  const fullTeamMembers = useMemo(() => {
    if (!team) {return [];}
    
    const ownerInfo = allUsers.find(u => u.id === team.ownerId);
    const owner: TeamMember = { 
        userId: team.ownerId, 
        email: ownerInfo?.email || 'Unknown', 
        role: 'Owner' as TeamMemberRole,
        avatarUrl: ownerInfo?.avatarUrl
    };

    const members = team.members.map(member => {
        const memberInfo = allUsers.find(u => u.id === member.userId);
        return { ...member, avatarUrl: memberInfo?.avatarUrl, name: memberInfo?.name };
    });

    return [owner, ...members];

  }, [team, allUsers]);

  const filteredMembers = useMemo(() => {
    if (!searchTerm) {return fullTeamMembers;}
    return fullTeamMembers.filter(m => 
        (allUsers.find(u => u.id === m.userId)?.name?.toLowerCase().includes(searchTerm.toLowerCase())) ||
        m.email.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [fullTeamMembers, searchTerm, allUsers]);
  
  if (!isPremium) {
    return (
      <div className="p-4 sm:p-6 lg:p-8 h-full flex items-center justify-center">
        <div className="text-center">
          <LockSolidIcon className="w-12 h-12 mx-auto text-zinc-300 dark:text-zinc-700 mb-4" />
          <h3 className="text-xl font-semibold text-zinc-800 dark:text-zinc-200">Unlock Team Management</h3>
          <p className="text-zinc-500 dark:text-zinc-400 mt-2 mb-4 max-w-md">
            Upgrade to Premium to invite and manage your team members, enabling collaborative workflows.
          </p>
          <Button onClick={() => setView('subscription')}>
            Upgrade Now
          </Button>
        </div>
      </div>
    );
  }
  
  const handleRemoveConfirm = () => {
    if (memberToRemove) {
      onRemoveMember(memberToRemove.userId);
      setMemberToRemove(null);
    }
  };

  return (
    <>
      <div className="p-4 sm:p-6 lg:p-8 space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-zinc-900 dark:text-white">Team Management</h1>
            <p className="text-zinc-600 dark:text-zinc-400 mt-1">Invite and manage members of your team.</p>
          </div>
          <Button onClick={() => setIsInviteModalOpen(true)}>
            <PlusCircleIcon className="w-5 h-5 mr-2" />
            Invite Member
          </Button>
        </div>

        <Card>
          <CardHeader>
             <div className="relative w-full sm:max-w-xs">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <SearchIcon className="h-5 w-5 text-zinc-400" />
                </div>
                <Input
                  placeholder="Search members..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  leftIcon={<SearchIcon className="h-5 w-5" />}
                  className="bg-white dark:bg-zinc-800 border-zinc-300 dark:border-zinc-700"
                />
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMembers.map(member => {
                    const memberInfo = allUsers.find(u => u.id === member.userId);
                    const isOwner = member.role === ('Owner' as TeamMemberRole);
                    return (
                      <TableRow key={member.userId}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <img src={member.avatarUrl} alt={memberInfo?.name} className="w-10 h-10 rounded-full" />
                            <div>
                              <p className="font-medium text-zinc-800 dark:text-zinc-200">{memberInfo?.name || 'Unknown'}</p>
                              <p className="text-sm text-zinc-500 dark:text-zinc-400">{member.email}</p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {isOwner ? (
                            <span className="text-sm font-medium text-zinc-600 dark:text-zinc-400">Owner</span>
                          ) : (
                            <Dropdown>
                                <Dropdown.Trigger>
                                    <Button variant="outline" size="sm" className="h-9">
                                        {member.role}
                                        <ChevronDownIcon className="w-4 h-4 ml-2" />
                                    </Button>
                                </Dropdown.Trigger>
                                <Dropdown.Content align="left">
                                    <Dropdown.Item onClick={() => onUpdateMemberRole(member.userId, 'Admin')} icon={<CheckCircleIcon className="w-4 h-4"/>}>Admin</Dropdown.Item>
                                    <Dropdown.Item onClick={() => onUpdateMemberRole(member.userId, 'Member')} icon={<CheckCircleIcon className="w-4 h-4"/>}>Member</Dropdown.Item>
                                </Dropdown.Content>
                            </Dropdown>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          {!isOwner && (
                            <Button variant="ghost" size="icon" className="h-8 w-8 text-zinc-500 dark:text-zinc-400 hover:text-red-600 dark:hover:text-red-500" onClick={() => setMemberToRemove(member)}>
                              <TrashIcon className="w-4 h-4"/>
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>

      <InviteMemberModal
        isOpen={isInviteModalOpen}
        onClose={() => setIsInviteModalOpen(false)}
        onInvite={onInviteMember}
      />

      {memberToRemove && (
         <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-md p-6">
                <h3 className="text-lg font-medium text-zinc-900 dark:text-zinc-50">Remove Member</h3>
                <p className="mt-2 text-sm text-zinc-500 dark:text-zinc-400">
                    Are you sure you want to remove {allUsers.find(u => u.id === memberToRemove.userId)?.name || memberToRemove.email} from the team?
                </p>
                <div className="mt-4 flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setMemberToRemove(null)}>Cancel</Button>
                    <Button variant="destructive" onClick={handleRemoveConfirm}>Remove</Button>
                </div>
            </div>
        </div>
      )}
    </>
  );
};

export default TeamManagementPage;
