{"name": "ai-contract-legal-clause-generator", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev:client": "vite", "dev:server": "tsx watch server/index.ts", "dev": "concurrently -k -n client,server -c cyan,magenta \"npm:dev:client\" \"npm:dev:server\"", "db:init": "npx supabase init", "db:link": "npx supabase link --project-ref hrzspijfqgjducjixddr", "db:push": "npx supabase db push --remote", "db:seed": "npx supabase db push --include-seed --linked -p \"$SUPABASE_DB_PASSWORD\" --yes", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "lint:check": "eslint . --ext .js,.jsx,.ts,.tsx --max-warnings 500", "test:scaffold": "node scripts/testgen.mjs", "test": "vitest run --reporter=default", "test:coverage": "vitest run --coverage", "test:performance": "vitest run tests/performance.test.ts --reporter=verbose", "test:load": "tsx tests/load-testing.ts", "verify": "npm run lint && npm run test && npx tsc --noEmit", "prepare": "husky"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "bash -c 'npx tsc --noEmit'"]}, "dependencies": {"@google/genai": "^1.17.0", "@google/generative-ai": "^0.24.1", "@sentry/node": "^10.11.0", "@supabase/supabase-js": "^2.47.10", "@types/redis": "^4.0.10", "chart.js": "^4.5.0", "cors": "^2.8.5", "dompurify": "^3.2.6", "dotenv": "^16.4.7", "eventemitter3": "^5.0.1", "express": "^4.21.2", "express-rate-limit": "^7.4.1", "helmet": "^8.0.0", "pdfjs-dist": "^5.4.149", "pino": "^9.9.5", "rate-limit-redis": "^4.2.2", "react": "^19.1.1", "react-dom": "^19.1.1", "redis": "^5.8.2", "stripe": "^16.12.0", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^9.35.0", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/dompurify": "^3.0.5", "@types/eventemitter3": "^1.2.0", "@types/express": "^5.0.0", "@types/jsdom": "^21.1.7", "@types/node": "^22.10.5", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^8.43.0", "@typescript-eslint/parser": "^8.43.0", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.21", "concurrently": "^9.1.2", "eslint": "^9.35.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "husky": "^9.1.7", "jsdom": "^25.0.1", "lint-staged": "^16.1.6", "postcss": "^8.5.6", "supertest": "^7.0.0", "tailwindcss": "^3.4.17", "tsx": "^4.19.2", "typescript": "^5.7.3", "typescript-eslint": "^8.43.0", "vite": "^6.2.0", "vitest": "^3.2.4"}}