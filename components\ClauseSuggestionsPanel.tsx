import React, { useState } from 'react';
import { Button } from './ui/Button';
import { WandIcon } from './Icons';
import { apiFetch } from '../lib/api';
import { SuggestedClause, User, DashboardView } from '../types';

interface ClauseSuggestionsPanelProps {
  documentContent: string;
  onInsertClause: (clauseContent: string) => void;
  user: User;
  setView: (view: DashboardView) => void;
}

const ClauseSuggestionsPanel: React.FC<ClauseSuggestionsPanelProps> = ({ documentContent, onInsertClause, user, setView }) => {
  const [suggestions, setSuggestions] = useState<SuggestedClause[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFetchSuggestions = async () => {
    setIsLoading(true);
    setError(null);
    setSuggestions([]);
    try {
      const resp = await apiFetch<{ suggestions: SuggestedClause[] }>(`/api/ai/suggest-clauses`, { method: 'POST', body: JSON.stringify({ content: documentContent }) });
      setSuggestions(resp.suggestions || []);
    } catch {
      setError('Failed to fetch suggestions from AI.');
      // Error logged for debugging
    } finally {
      setIsLoading(false);
    }
  };

  const isPremiumFeature = user.planName === 'Premium' || user.planName === 'Enterprise';

  if (!isPremiumFeature) {
    return (
        <div className="p-6 flex-1 flex flex-col items-center justify-center text-center">
            <WandIcon className="w-12 h-12 text-slate-300 mb-4" />
            <h4 className="font-semibold text-slate-800">Unlock AI Clause Suggestions</h4>
            <p className="text-sm text-slate-500 mt-2 mb-4">Upgrade to our Premium plan to get AI-powered suggestions and improve your contracts.</p>
            <Button className="w-full bg-amber-500 hover:bg-amber-600 text-white" onClick={() => setView('subscription')}>
                Upgrade Now
            </Button>
        </div>
    );
  }

  return (
    <>
      <div className="p-4 border-b border-slate-200">
        <Button className="w-full" onClick={handleFetchSuggestions} disabled={isLoading}>
          {isLoading ? 'Analyzing...' : 'Suggest Clauses'}
        </Button>
        <p className="text-xs text-slate-500 mt-2 text-center">Analyzes the document and suggests relevant clauses to add.</p>
      </div>
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {isLoading && (
            <div className="text-center text-slate-500">
                <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                <p>Finding suggestions...</p>
            </div>
        )}
        {error && <p className="text-sm text-red-500">{error}</p>}
        {suggestions.map((clause, index) => (
          <div key={index} className="border rounded-lg p-3 bg-slate-50">
            <h4 className="font-semibold text-sm text-slate-800">{clause.title}</h4>
            <p className="text-xs text-slate-600 mt-1 mb-2">{clause.description}</p>
            <Button size="sm" variant="outline" onClick={() => onInsertClause(clause.content)}>
              Insert Clause
            </Button>
          </div>
        ))}
        {!isLoading && suggestions.length === 0 && !error && (
            <div className="text-center text-sm text-slate-400 pt-8">
                Click "Suggest Clauses" to get AI-powered recommendations.
            </div>
        )}
      </div>
    </>
  );
};

export default ClauseSuggestionsPanel;
