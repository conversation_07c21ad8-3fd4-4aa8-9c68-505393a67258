declare module 'rate-limit-redis' {
  import type { Store } from 'express-rate-limit';

  export type RedisReply = string | number | Buffer | null | undefined | RedisReply[];

  export interface RedisStoreOptions {
    prefix?: string;
    /**
     * Custom command executor. When provided, the store will call this function instead of using
     * an internal Redis client.
     */
    sendCommand?: (...args: string[]) => Promise<RedisReply>;
    /** A Redis client instance to reuse. */
    client?: unknown;
    resetExpiryOnChange?: boolean;
    expiryMs?: number;
  }

  export default class RedisStore implements Store {
    constructor(options?: RedisStoreOptions);
    incr(key: string): Promise<number>;
    increment(key: string): Promise<number>;
    decrement(key: string): Promise<void>;
    resetKey(key: string): Promise<void>;
    resetAll(): Promise<void>;
  }
}
