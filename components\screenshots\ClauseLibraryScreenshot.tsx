import React from 'react';

const ClauseLibraryScreenshot = () => {
    return (
        <div className="w-full h-full bg-zinc-50 p-6 overflow-hidden select-none">
            <div className="flex justify-between items-center mb-4">
                <div>
                    <h1 className="text-2xl font-bold text-zinc-900">Clause Library</h1>
                    <p className="text-sm text-zinc-600 mt-1">Manage your reusable, pre-approved legal clauses.</p>
                </div>
                <div className="bg-indigo-600 text-white text-sm font-semibold px-4 py-2 rounded-lg shadow">
                    New Clause
                </div>
            </div>

            <div className="bg-white rounded-lg shadow border border-zinc-200 p-4">
                <div className="w-full max-w-sm h-8 bg-zinc-100 rounded-md border border-zinc-200 flex items-center px-2 text-sm text-zinc-400 mb-4">
                    Search clauses...
                </div>
                <div className="inline-block">
                    <div className="bg-white rounded-lg shadow-lg border border-zinc-200 w-52">
                        <div className="p-4">
                            <h3 className="font-semibold text-sm text-zinc-800">Confidentiality Clause (Standard)</h3>
                            <p className="text-xs text-zinc-500 mt-1">The Receiving Party shall hold and maintain the Confidential Information ...</p>
                            <div className="mt-2 flex gap-2">
                                <span className="text-xs bg-zinc-100 text-zinc-600 px-2 py-0.5 rounded-full">NDA</span>
                                <span className="text-xs bg-zinc-100 text-zinc-600 px-2 py-0.5 rounded-full">Standard</span>
                            </div>
                        </div>
                        <div className="p-2 border-t border-zinc-100 flex justify-end gap-2 text-xs font-semibold">
                            <span className="text-indigo-600">Edit</span>
                            <span className="text-red-500">Delete</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ClauseLibraryScreenshot;