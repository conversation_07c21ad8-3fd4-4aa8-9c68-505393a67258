
import createDOMPurify from 'dompurify';

// Only import jsdom in Node.js (test/server) environments
let DOMPurify: ReturnType<typeof createDOMPurify>;
if (typeof window === 'undefined') {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const { JSDOM } = require('jsdom');
  const { window: jsdomWindow } = new JSDOM('');
  DOMPurify = createDOMPurify(jsdomWindow);
} else {
  DOMPurify = createDOMPurify(window);
}

export function sanitizeHtml(html: string): string {
  try {
    return DOMPurify.sanitize(html, { USE_PROFILES: { html: true } });
  } catch {
    return '';
  }
}

