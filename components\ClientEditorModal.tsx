import React, { useState, useEffect } from 'react';
import { Client } from '../types';
import { Button } from './ui/Button';
import { CloseIcon, UsersIcon } from './Icons';

interface ClientEditorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: Omit<Client, 'id' | 'createdAt'>) => void;
  existingClient?: Client | null;
}

const ClientEditorModal: React.FC<ClientEditorModalProps> = ({ isOpen, onClose, onSave, existingClient }) => {
    const [client, setClient] = useState<Omit<Client, 'id' | 'createdAt'>>({
        name: '',
        type: 'Company',
        contactPerson: '',
        email: '',
        phone: '',
        address: '',
    });

    useEffect(() => {
        if (existingClient) {
            setClient(existingClient);
        } else {
            setClient({ name: '', type: 'Company', contactPerson: '', email: '', phone: '', address: '' });
        }
    }, [existingClient, isOpen]);
    
    if (!isOpen) {return null;}

    const handleInputChange = (field: keyof Omit<Client, 'id' | 'createdAt'>, value: string) => {
        setClient(prev => ({...prev, [field]: value }));
    };

    const handleSave = () => {
        if(client.name.trim()) {
            onSave(client);
        }
    };
    
    return (
        <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-lg">
                <header className="flex items-center justify-between p-4 border-b dark:border-zinc-800">
                    <h2 className="text-lg font-semibold flex items-center gap-2"><UsersIcon className="w-5 h-5 text-brand-600"/>{existingClient ? 'Edit Client' : 'Create New Client'}</h2>
                    <Button variant="ghost" size="icon" onClick={onClose}><CloseIcon className="w-5 h-5"/></Button>
                </header>
                <main className="p-6 space-y-4 max-h-[60vh] overflow-y-auto">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div><label className="block text-sm font-medium dark:text-zinc-300">Client Name*</label><input type="text" value={client.name} onChange={e => handleInputChange('name', e.target.value)} className="mt-1 w-full p-2 border rounded-md dark:bg-zinc-800 dark:border-zinc-700" required /></div>
                        <div><label className="block text-sm font-medium dark:text-zinc-300">Type</label><select value={client.type} onChange={e => handleInputChange('type', e.target.value)} className="mt-1 w-full p-2 border rounded-md dark:bg-zinc-800 dark:border-zinc-700"><option>Company</option><option>Individual</option></select></div>
                    </div>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <div><label className="block text-sm font-medium dark:text-zinc-300">Contact Person</label><input type="text" value={client.contactPerson} onChange={e => handleInputChange('contactPerson', e.target.value)} className="mt-1 w-full p-2 border rounded-md dark:bg-zinc-800 dark:border-zinc-700" /></div>
                        <div><label className="block text-sm font-medium dark:text-zinc-300">Email</label><input type="email" value={client.email} onChange={e => handleInputChange('email', e.target.value)} className="mt-1 w-full p-2 border rounded-md dark:bg-zinc-800 dark:border-zinc-700" /></div>
                    </div>
                    <div><label className="block text-sm font-medium dark:text-zinc-300">Phone</label><input type="tel" value={client.phone} onChange={e => handleInputChange('phone', e.target.value)} className="mt-1 w-full p-2 border rounded-md dark:bg-zinc-800 dark:border-zinc-700" /></div>
                    <div><label className="block text-sm font-medium dark:text-zinc-300">Address</label><textarea value={client.address} onChange={e => handleInputChange('address', e.target.value)} rows={3} className="mt-1 w-full p-2 border rounded-md dark:bg-zinc-800 dark:border-zinc-700" /></div>
                </main>
                <footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t dark:border-zinc-800">
                    <Button variant="outline" onClick={onClose}>Cancel</Button>
                    <Button onClick={handleSave} disabled={!client.name.trim()}>Save Client</Button>
                </footer>
            </div>
        </div>
    );
};

export default ClientEditorModal;
