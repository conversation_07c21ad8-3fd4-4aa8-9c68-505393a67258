import React from 'react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-white dark:bg-zinc-950 border-t border-zinc-200 dark:border-zinc-800">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8 flex flex-col sm:flex-row justify-between items-center gap-4">
        <p className="text-sm text-zinc-500 dark:text-zinc-400 text-center sm:text-left">
          &copy; {new Date().getFullYear()} LexiGen. All rights reserved.
        </p>
        <div className="flex items-center gap-6">
            <a href="#terms" className="text-sm text-zinc-500 dark:text-zinc-400 hover:text-brand-600 dark:hover:text-brand-400 transition-colors">
                Terms & Conditions
            </a>
            <a href="#privacy" className="text-sm text-zinc-500 dark:text-zinc-400 hover:text-brand-600 dark:hover:text-brand-400 transition-colors">
                Privacy Policy
            </a>
        </div>
      </div>
    </footer>
  );
};

export default Footer;