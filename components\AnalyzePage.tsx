import React, { useState, useRef } from 'react';
import { User, DashboardView, ExternalAnalysisResult } from '../types';
import { Button } from './ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { LockSolidIcon, UploadCloudIcon, AlertTriangleIcon } from './Icons';
import { apiFetch } from '../lib/api';
import { cn } from '../lib/utils';

interface AnalyzePageProps {
  user: User;
  setView: (view: DashboardView) => void;
}

export const ResultDisplay: React.FC<{ result: ExternalAnalysisResult }> = ({ result }) => {
    const riskStyles = {
        High: 'border-red-500 bg-red-50 text-red-900',
        Medium: 'border-amber-500 bg-amber-50 text-amber-900',
        Low: 'border-sky-500 bg-sky-50 text-sky-900',
    };
    return (
        <div className="space-y-6">
            <Card>
                <CardHeader>
                    <CardTitle>Analysis Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                    <p><strong className="font-semibold text-zinc-800">Document Type:</strong> {result.documentType}</p>
                    <p className="text-zinc-600">{result.summary}</p>
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>Potential Risks</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                    {result.risks?.length > 0 ? result.risks.map((risk, i) => (
                        <div key={i} className={cn('p-4 rounded-lg border-l-4', riskStyles[risk.severity])}>
                            <p className="font-bold">{risk.severity} Risk</p>
                            <p className="mt-1">{risk.description}</p>
                        </div>
                    )) : <p className="text-zinc-500">No significant risks were identified.</p>}
                </CardContent>
            </Card>
            
            <Card>
                <CardHeader>
                    <CardTitle>Key Clauses</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                     {result.keyClauses?.length > 0 ? result.keyClauses.map((clause, i) => (
                        <div key={i} className="p-3 border-b border-zinc-200">
                            <p className="font-semibold text-zinc-800">{clause.title}</p>
                            <p className="text-sm text-zinc-600 mt-1">{clause.summary}</p>
                        </div>
                    )) : <p className="text-zinc-500">Could not extract key clauses.</p>}
                </CardContent>
            </Card>

            <Card>
                <CardHeader>
                    <CardTitle>Suggestions</CardTitle>
                </CardHeader>
                <CardContent>
                    <ul className="list-disc pl-5 space-y-2 text-zinc-700">
                        {result.suggestions?.length > 0 ? result.suggestions.map((s, i) => <li key={i}>{s}</li>) : <p className="text-zinc-500">No specific suggestions were generated.</p>}
                    </ul>
                </CardContent>
            </Card>
        </div>
    );
};


const AnalyzePage: React.FC<AnalyzePageProps> = ({ user, setView }) => {
    const [documentText, setDocumentText] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [result, setResult] = useState<ExternalAnalysisResult | null>(null);
    const [error, setError] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const isPremium = user.planName === 'Premium' || user.planName === 'Enterprise';

    const handleAnalyze = async () => {
        if (!documentText.trim()) {return;}
        setIsLoading(true);
        setError(null);
        setResult(null);
        try {
            const resp = await apiFetch<{ result: ExternalAnalysisResult }>('/api/ai/external-analyze', {
                method: 'POST',
                body: JSON.stringify({ content: documentText }),
            });
            if (!resp?.result) {
                throw new Error('Invalid response from AI.');
            }
            setResult(resp.result);
        } catch (e) {
            const fallbackMessage = e instanceof Error ? e.message : 'An unknown error occurred.';
            let parsedMessage = fallbackMessage;
            try {
                const parsed = JSON.parse(fallbackMessage);
                if (parsed && typeof parsed.error === 'string') {
                    parsedMessage = parsed.error;
                }
            } catch { /* Ignore JSON parsing errors */ }
            setError(parsedMessage || 'An unknown error occurred.');
        } finally {
            setIsLoading(false);
        }
    };
    
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (event) => {
                setDocumentText(event.target?.result as string);
            };
            reader.readAsText(file);
        }
    };

    if (!isPremium) {
        return (
            <div className="p-4 sm:p-6 lg:p-8 h-full flex items-center justify-center">
                <div className="text-center">
                    <LockSolidIcon className="w-12 h-12 mx-auto text-zinc-300 mb-4" />
                    <h3 className="text-xl font-semibold text-zinc-800">Unlock Document Analysis</h3>
                    <p className="text-zinc-500 mt-2 mb-4 max-w-md">
                        Upgrade to Premium to get AI-powered insights on any legal document.
                    </p>
                    <Button onClick={() => setView('subscription')}>Upgrade Now</Button>
                </div>
            </div>
        );
    }

    return (
        <div className="p-4 sm:p-6 lg:p-8 h-full flex flex-col lg:flex-row gap-8">
            <div className="flex-1 lg:w-1/2 flex flex-col">
                <h1 className="text-2xl font-bold text-zinc-900">Analyze a Document</h1>
                <p className="text-zinc-600 mt-1 mb-4">Paste your document text below or upload a file to get an AI-powered analysis.</p>
                <div className="flex-1 flex flex-col">
                    <textarea
                        value={documentText}
                        onChange={(e) => setDocumentText(e.target.value)}
                        placeholder="Paste your legal document text here..."
                        className="w-full flex-1 p-4 border border-zinc-300 rounded-t-lg focus:ring-brand-500 focus:border-brand-500 resize-none"
                    />
                    <div className="p-3 border border-t-0 border-zinc-300 rounded-b-lg bg-zinc-50 flex justify-between items-center">
                        <input type="file" ref={fileInputRef} onChange={handleFileChange} accept=".txt,.md,.html" className="hidden"/>
                        <Button variant="outline" onClick={() => fileInputRef.current?.click()}>
                            <UploadCloudIcon className="w-5 h-5 mr-2" />
                            Upload File
                        </Button>
                        <Button onClick={handleAnalyze} disabled={isLoading || !documentText.trim()}>
                            {isLoading ? 'Analyzing...' : 'Analyze Document'}
                        </Button>
                    </div>
                </div>
            </div>
            <div className="flex-1 lg:w-1/2 overflow-y-auto">
                {isLoading && (
                    <div className="h-full flex flex-col items-center justify-center text-center text-zinc-500">
                        <div className="w-10 h-10 border-4 border-brand-500 border-t-transparent rounded-full animate-spin"></div>
                        <p className="mt-4 font-semibold">AI is analyzing your document...</p>
                        <p className="text-sm">This may take a moment.</p>
                    </div>
                )}
                {error && (
                    <div className="h-full flex flex-col items-center justify-center text-center text-red-600 bg-red-50 p-6 rounded-lg">
                        <AlertTriangleIcon className="w-10 h-10 mb-2"/>
                        <p className="font-semibold">Analysis Failed</p>
                        <p className="text-sm">{error}</p>
                    </div>
                )}
                {result && <ResultDisplay result={result} />}
                {!isLoading && !result && !error && (
                    <div className="h-full flex flex-col items-center justify-center text-center text-zinc-400 border-2 border-dashed border-zinc-300 rounded-lg p-6">
                        <p className="font-semibold">Your analysis results will appear here.</p>
                    </div>
                )}
            </div>
        </div>
    );
};

export default AnalyzePage;
