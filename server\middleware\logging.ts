import { randomUUID } from 'crypto';
import type { Response, NextFunction } from 'express';
import type { AuthRequest } from './auth';
import { logger } from '../lib/logger';

export function requestLogger(req: AuthRequest, res: Response, next: NextFunction) {
  const requestId = randomUUID();
  const startTime = process.hrtime.bigint();

  req.requestId = requestId;
  res.setHeader('X-Request-ID', requestId);

  logger.info(
    {
      requestId,
      method: req.method,
      path: req.originalUrl,
      userId: req.user?.id ?? null,
    },
    'request started',
  );

  res.once('finish', () => {
    const durationMs = Number(process.hrtime.bigint() - startTime) / 1_000_000;

    logger.info(
      {
        requestId,
        method: req.method,
        path: req.originalUrl,
        status: res.statusCode,
        durationMs,
        userId: req.user?.id ?? null,
      },
      'request completed',
    );
  });

  next();
}
