import { describe, it, expect, beforeAll, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

vi.mock('../server/supabaseClient', () => {
  const getUserClient = (_token: string) => ({
    auth: { getUser: async () => ({ data: { user: { id: 'test-user' } }, error: null }) },
    from: (table: string) => {
      if (table === 'workflow_templates') {
        return {
          select: (_cols: string) => ({ order: async () => ({ data: [{ id: 'template-1', name: 'Test Template', status: 'active' }], error: null }) }),
          insert: (row: Record<string, unknown>) => ({ select: () => ({ single: async () => ({ data: { id: 'template-1', ...row }, error: null }) }) }),
          update: (patch: Record<string, unknown>) => ({ eq: (_col: string, id: string) => ({ select: () => ({ single: async () => ({ data: { id, ...patch }, error: null }) }) }) }),
          delete: () => ({ eq: async () => ({ error: null }) }),
        };
      }
      if (table === 'workflow_nodes') {
        return {
          select: (_cols: string) => ({ eq: (_col: string, _val: string) => ({ order: async () => ({ data: [{ id: 'node-1', type: 'start', position_x: 0, position_y: 0 }], error: null }) }) }),
          insert: (row: Record<string, unknown>) => ({ select: () => ({ single: async () => ({ data: { id: 'node-1', ...row }, error: null }) }) }),
          update: (patch: Record<string, unknown>) => ({ eq: (_col: string, id: string) => ({ select: () => ({ single: async () => ({ data: { id, ...patch }, error: null }) }) }) }),
          delete: () => ({ eq: async () => ({ error: null }) }),
        };
      }
      if (table === 'workflow_edges') {
        return {
          select: (_cols: string) => ({ eq: (_col: string, _val: string) => ({ order: async () => ({ data: [{ id: 'edge-1', source: 'node-1', target: 'node-2' }], error: null }) }) }),
          insert: (row: Record<string, unknown>) => ({ select: () => ({ single: async () => ({ data: { id: 'edge-1', ...row }, error: null }) }) }),
          update: (patch: Record<string, unknown>) => ({ eq: (_col: string, id: string) => ({ select: () => ({ single: async () => ({ data: { id, ...patch }, error: null }) }) }) }),
          delete: () => ({ eq: async () => ({ error: null }) }),
        };
      }
      if (table === 'workflow_instances') {
        return {
          select: (_cols: string) => ({ order: async () => ({ data: [{ id: 'instance-1', status: 'running' }], error: null }) }),
          insert: (row: Record<string, unknown>) => ({ select: () => ({ single: async () => ({ data: { id: 'instance-1', ...row }, error: null }) }) }),
          update: (patch: Record<string, unknown>) => ({ eq: (_col: string, id: string) => ({ select: () => ({ single: async () => ({ data: { id, ...patch }, error: null }) }) }) }),
          delete: () => ({ eq: async () => ({ error: null }) }),
        };
      }
      return {
        select: () => ({ data: [], error: null }),
        insert: () => ({ data: null, error: null }),
        update: () => ({ data: null, error: null }),
        delete: () => ({ data: null, error: null }),
      };
    },
  });
  return { getUserClient };
});

vi.mock('../middleware/auth', () => ({
  requireAuth: (req: any, res: any, next: any) => next(),
  getAccessToken: () => 'test-token',
}));

vi.mock('../middleware/plan', () => ({
  requirePremium: (req: any, res: any, next: any) => next(),
}));

beforeAll(() => {
  process.env.TEST_BYPASS_AUTH = '1';
});

describe('Workflows routes', () => {
  describe('Workflow Templates', () => {
    describe('GET /api/workflows/templates', () => {
      it('should return workflow templates', async () => {
        const res = await request(app)
          .get('/api/workflows/templates')
          .set('Authorization', 'Bearer test-token');
        expect(res.status).toBe(200);
        expect(res.body).toHaveProperty('templates');
        expect(Array.isArray(res.body.templates)).toBe(true);
      });
    });

    describe('POST /api/workflows/templates', () => {
      it('should create a new workflow template', async () => {
        const templateData = {
          teamId: '123e4567-e89b-12d3-a456-426614174000',
          name: 'New Template',
          status: 'active'
        };
        const res = await request(app)
          .post('/api/workflows/templates')
          .send(templateData)
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(201);
        expect(res.body).toHaveProperty('template');
        expect(res.body.template).toHaveProperty('name', 'New Template');
      });

      it('should return 400 for invalid template data', async () => {
        const res = await request(app)
          .post('/api/workflows/templates')
          .send({ name: '' })
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(400);
      });
    });

    describe('PUT /api/workflows/templates/:id', () => {
      it('should update workflow template', async () => {
        const updateData = { name: 'Updated Template' };
        const res = await request(app)
          .put('/api/workflows/templates/template-1')
          .send(updateData)
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(200);
        expect(res.body).toHaveProperty('template');
      });

      it('should return 400 for invalid update data', async () => {
        const res = await request(app)
          .put('/api/workflows/templates/template-1')
          .send({ teamId: 'invalid-uuid' })
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(400);
      });
    });

    describe('DELETE /api/workflows/templates/:id', () => {
      it('should delete workflow template', async () => {
        const res = await request(app)
          .delete('/api/workflows/templates/template-1')
          .set('Authorization', 'Bearer test-token');
        expect(res.status).toBe(204);
      });
    });
  });

  describe('Workflow Nodes', () => {
    describe('GET /api/workflows/templates/:templateId/nodes', () => {
      it('should return workflow nodes for template', async () => {
        const res = await request(app)
          .get('/api/workflows/templates/template-1/nodes')
          .set('Authorization', 'Bearer test-token');
        expect(res.status).toBe(200);
        expect(res.body).toHaveProperty('nodes');
        expect(Array.isArray(res.body.nodes)).toBe(true);
      });
    });

    describe('POST /api/workflows/nodes', () => {
      it('should create a new workflow node', async () => {
        const nodeData = {
          templateId: '123e4567-e89b-12d3-a456-426614174000',
          type: 'start',
          position: { x: 100, y: 200 },
          data: { label: 'Start Node' }
        };
        const res = await request(app)
          .post('/api/workflows/nodes')
          .send(nodeData)
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(201);
        expect(res.body).toHaveProperty('node');
      });

      it('should return 400 for invalid node data', async () => {
        const res = await request(app)
          .post('/api/workflows/nodes')
          .send({ type: '' })
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(400);
      });
    });

    describe('PUT /api/workflows/nodes/:id', () => {
      it('should update workflow node', async () => {
        const updateData = {
          type: 'process',
          position: { x: 150, y: 250 }
        };
        const res = await request(app)
          .put('/api/workflows/nodes/node-1')
          .send(updateData)
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(200);
        expect(res.body).toHaveProperty('node');
      });

      it('should return 400 for invalid position data', async () => {
        const res = await request(app)
          .put('/api/workflows/nodes/node-1')
          .send({ position: { x: 'invalid' } })
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(400);
      });
    });

    describe('DELETE /api/workflows/nodes/:id', () => {
      it('should delete workflow node', async () => {
        const res = await request(app)
          .delete('/api/workflows/nodes/node-1')
          .set('Authorization', 'Bearer test-token');
        expect(res.status).toBe(204);
      });
    });
  });

  describe('Workflow Edges', () => {
    describe('GET /api/workflows/templates/:templateId/edges', () => {
      it('should return workflow edges for template', async () => {
        const res = await request(app)
          .get('/api/workflows/templates/template-1/edges')
          .set('Authorization', 'Bearer test-token');
        expect(res.status).toBe(200);
        expect(res.body).toHaveProperty('edges');
        expect(Array.isArray(res.body.edges)).toBe(true);
      });
    });

    describe('POST /api/workflows/edges', () => {
      it('should create a new workflow edge', async () => {
        const edgeData = {
          templateId: '123e4567-e89b-12d3-a456-426614174000',
          source: 'node-1',
          target: 'node-2',
          sourceHandle: 'output',
          targetHandle: 'input'
        };
        const res = await request(app)
          .post('/api/workflows/edges')
          .send(edgeData)
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(201);
        expect(res.body).toHaveProperty('edge');
      });

      it('should return 400 for invalid edge data', async () => {
        const res = await request(app)
          .post('/api/workflows/edges')
          .send({ source: '' })
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(400);
      });
    });

    describe('PUT /api/workflows/edges/:id', () => {
      it('should update workflow edge', async () => {
        const updateData = {
          source: 'node-2',
          target: 'node-3'
        };
        const res = await request(app)
          .put('/api/workflows/edges/edge-1')
          .send(updateData)
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(200);
        expect(res.body).toHaveProperty('edge');
      });
    });

    describe('DELETE /api/workflows/edges/:id', () => {
      it('should delete workflow edge', async () => {
        const res = await request(app)
          .delete('/api/workflows/edges/edge-1')
          .set('Authorization', 'Bearer test-token');
        expect(res.status).toBe(204);
      });
    });
  });

  describe('Workflow Instances', () => {
    describe('GET /api/workflows/instances', () => {
      it('should return workflow instances', async () => {
        const res = await request(app)
          .get('/api/workflows/instances')
          .set('Authorization', 'Bearer test-token');
        expect(res.status).toBe(200);
        expect(res.body).toHaveProperty('instances');
        expect(Array.isArray(res.body.instances)).toBe(true);
      });
    });

    describe('POST /api/workflows/instances', () => {
      it('should create a new workflow instance', async () => {
        const instanceData = {
          templateId: '123e4567-e89b-12d3-a456-426614174000',
          documentId: '123e4567-e89b-12d3-a456-426614174001',
          status: 'running',
          currentNodeId: '123e4567-e89b-12d3-a456-426614174002'
        };
        const res = await request(app)
          .post('/api/workflows/instances')
          .send(instanceData)
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(201);
        expect(res.body).toHaveProperty('instance');
      });

      it('should return 400 for invalid instance data', async () => {
        const res = await request(app)
          .post('/api/workflows/instances')
          .send({ templateId: 'invalid-uuid' })
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(400);
      });
    });

    describe('PUT /api/workflows/instances/:id', () => {
      it('should update workflow instance', async () => {
        const updateData = {
          status: 'completed',
          currentNodeId: '123e4567-e89b-12d3-a456-************'
        };
        const res = await request(app)
          .put('/api/workflows/instances/instance-1')
          .send(updateData)
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(200);
        expect(res.body).toHaveProperty('instance');
      });

      it('should return 400 for invalid status', async () => {
        const res = await request(app)
          .put('/api/workflows/instances/instance-1')
          .send({ status: 'invalid-status' })
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(400);
      });
    });

    describe('DELETE /api/workflows/instances/:id', () => {
      it('should delete workflow instance', async () => {
        const res = await request(app)
          .delete('/api/workflows/instances/instance-1')
          .set('Authorization', 'Bearer test-token');
        expect(res.status).toBe(204);
      });
    });
  });
});