
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/Header.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> Header.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/150</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/150</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import React, { useState, useEffect } from 'react';<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >import { LegalIcon, MenuIcon, CloseIcon, ChevronDownIcon, DocumentIcon } from './Icons';</span>
import { User } from '../types';
<span class="cstat-no" title="statement not covered" >import Dropdown from './ui/Dropdown';</span>
&nbsp;
interface HeaderProps {
  setView: (view: 'home' | 'auth' | 'dashboard') =&gt; void;
  currentUser: User | null;
  onLogout: () =&gt; void;
}
&nbsp;
<span class="cstat-no" title="statement not covered" >const navItems = [</span>
<span class="cstat-no" title="statement not covered" >  { text: 'Solutions', id: 'solutions', subItems: [</span>
<span class="cstat-no" title="statement not covered" >    { href: '#solutions-legal', text: 'For Legal Teams' },</span>
<span class="cstat-no" title="statement not covered" >    { href: '#solutions-sales', text: 'For Sales Teams' },</span>
<span class="cstat-no" title="statement not covered" >    { href: '#solutions-procurement', text: 'For Procurement' },</span>
<span class="cstat-no" title="statement not covered" >  ]},</span>
<span class="cstat-no" title="statement not covered" >  { href: '#how-it-works', text: 'How It Works' },</span>
<span class="cstat-no" title="statement not covered" >  { href: '#pricing', text: 'Pricing' },</span>
<span class="cstat-no" title="statement not covered" >  { href: '#faq', text: 'FAQ' },</span>
<span class="cstat-no" title="statement not covered" >];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const Header: React.FC&lt;HeaderProps&gt; = ({ setView, currentUser, onLogout }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const [isScrolled, setIsScrolled] = useState(false);</span>
<span class="cstat-no" title="statement not covered" >  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const handleScroll = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      setIsScrolled(window.scrollY &gt; 10);</span>
<span class="cstat-no" title="statement not covered" >    };</span>
<span class="cstat-no" title="statement not covered" >    window.addEventListener('scroll', handleScroll);</span>
<span class="cstat-no" title="statement not covered" >    return () =&gt; window.removeEventListener('scroll', handleScroll);</span>
<span class="cstat-no" title="statement not covered" >  }, []);</span>
  
<span class="cstat-no" title="statement not covered" >  const handleNavClick = (e: React.MouseEvent&lt;HTMLAnchorElement&gt;, targetId: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    e.preventDefault();</span>
    
<span class="cstat-no" title="statement not covered" >    const navigateAndScroll = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >      setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >          const element = document.getElementById(targetId);</span>
<span class="cstat-no" title="statement not covered" >          element?.scrollIntoView({ behavior: 'smooth' });</span>
<span class="cstat-no" title="statement not covered" >      }, 50);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
<span class="cstat-no" title="statement not covered" >    if (currentUser &amp;&amp; window.location.hash) {</span>
      // If logged in and on a different page (e.g. #terms), go to home first
<span class="cstat-no" title="statement not covered" >      setView('home');</span>
<span class="cstat-no" title="statement not covered" >      navigateAndScroll();</span>
<span class="cstat-no" title="statement not covered" >    } else {</span>
<span class="cstat-no" title="statement not covered" >      navigateAndScroll();</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;header className={`sticky top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled || currentUser ? 'bg-white/80 dark:bg-zinc-900/80 backdrop-blur-md shadow-sm border-b border-zinc-200 dark:border-zinc-800' : 'bg-transparent'}`}&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="flex justify-between items-center h-16"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;button onClick={() =&gt; setView(currentUser ? 'dashboard' : 'home')} className="flex-shrink-0 flex items-center group cursor-pointer"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;LegalIcon className="h-8 w-8 text-brand-600" /&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;span className="ml-3 text-2xl font-bold text-zinc-900 dark:text-white"&gt;LexiGen&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/button&gt;</span>
          
<span class="cstat-no" title="statement not covered" >          {currentUser ? (</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center space-x-2 sm:space-x-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;Dropdown&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;Dropdown.Trigger&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="flex items-center gap-3 cursor-pointer"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="h-9 w-9 rounded-full bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center text-zinc-700 dark:text-zinc-200 font-semibold"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      {currentUser.avatarUrl ? (</span>
<span class="cstat-no" title="statement not covered" >                        &lt;img src={currentUser.avatarUrl} alt="User avatar" className="h-9 w-9 rounded-full object-cover" /&gt;</span>
                      ) : (
<span class="cstat-no" title="statement not covered" >                        &lt;span&gt;{(currentUser.name || currentUser.email || 'U').slice(0,1).toUpperCase()}&lt;/span&gt;</span>
                      )}
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="hidden sm:flex flex-col items-start"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="text-sm font-medium text-zinc-900 dark:text-white"&gt;{currentUser.name || currentUser.email}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;span className="text-xs text-zinc-500 dark:text-zinc-400"&gt;{currentUser.planName}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/Dropdown.Trigger&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;Dropdown.Content align="right"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;div className="px-4 py-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;p className="text-sm font-medium text-zinc-900 dark:text-white truncate"&gt;{currentUser.name || 'Account'}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="mt-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-zinc-100 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >                      {currentUser.planName}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;Dropdown.Separator /&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;Dropdown.Item onClick={() =&gt; setView('dashboard')} icon={&lt;DocumentIcon className="w-4 h-4"/&gt;}&gt;Dashboard&lt;/Dropdown.Item&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;Dropdown.Item onClick={onLogout} className="text-red-600 dark:text-red-400" icon={&lt;CloseIcon className="w-4 h-4"/&gt;}&gt;Log Out&lt;/Dropdown.Item&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/Dropdown.Content&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/Dropdown&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
          ) : (
<span class="cstat-no" title="statement not covered" >            &lt;&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;nav className="hidden md:flex md:items-center md:space-x-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                {navItems.map(item =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                    item.subItems ? (</span>
<span class="cstat-no" title="statement not covered" >                      &lt;React.Fragment key={item.text}&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;Dropdown&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;Dropdown.Trigger&gt;</span>
<span class="cstat-no" title="statement not covered" >                          &lt;div className="text-zinc-600 dark:text-zinc-300 hover:text-brand-600 dark:hover:text-brand-400 transition-colors px-3 py-2 rounded-md flex items-center gap-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            {item.text}</span>
<span class="cstat-no" title="statement not covered" >                            &lt;ChevronDownIcon className="w-4 h-4" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/Dropdown.Trigger&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;Dropdown.Content&gt;</span>
<span class="cstat-no" title="statement not covered" >                          {item.subItems.map(subItem =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                            &lt;Dropdown.Item key={subItem.href} onClick={(e) =&gt; handleNavClick(e as React.MouseEvent, subItem.href.slice(1))}&gt;</span>
<span class="cstat-no" title="statement not covered" >                              {subItem.text}</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/Dropdown.Item&gt;</span>
<span class="cstat-no" title="statement not covered" >                          ))}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/Dropdown.Content&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/Dropdown&gt;</span>
<span class="cstat-no" title="statement not covered" >                      &lt;/React.Fragment&gt;</span>
                    ) : (
<span class="cstat-no" title="statement not covered" >                      &lt;a key={item.href} href={item.href} onClick={(e) =&gt; handleNavClick(e, item.href?.slice(1) || '')} className="text-zinc-600 dark:text-zinc-300 hover:text-brand-600 dark:hover:text-brand-400 transition-colors px-3 py-2 rounded-md"&gt;{item.text}&lt;/a&gt;</span>
                    )
<span class="cstat-no" title="statement not covered" >                  ))}</span>
<span class="cstat-no" title="statement not covered" >              &lt;/nav&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="hidden md:flex items-center space-x-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;button onClick={() =&gt; setView('auth')} className="text-zinc-600 dark:text-zinc-300 hover:text-brand-600 dark:hover:text-brand-400 transition-colors font-medium"&gt;Log In&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;button onClick={() =&gt; setView('auth')} className="px-5 py-2.5 text-white font-medium bg-brand-600 hover:bg-brand-700 rounded-lg shadow-sm"&gt;</span>
                  Sign Up
<span class="cstat-no" title="statement not covered" >                &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/&gt;</span>
          )}
&nbsp;
<span class="cstat-no" title="statement not covered" >          &lt;div className="md:hidden"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;button onClick={() =&gt; setIsMobileMenuOpen(!isMobileMenuOpen)} className="p-2 rounded-md text-zinc-600 dark:text-zinc-300 hover:bg-zinc-100 dark:hover:bg-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >              {isMobileMenuOpen ? &lt;CloseIcon className="w-6 h-6"/&gt; : &lt;MenuIcon className="w-6 h-6"/&gt;}</span>
<span class="cstat-no" title="statement not covered" >            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
       {/* Mobile Menu */}
<span class="cstat-no" title="statement not covered" >      {!currentUser &amp;&amp; isMobileMenuOpen &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="md:hidden bg-white/95 dark:bg-zinc-900/95 backdrop-blur-md border-t border-zinc-200 dark:border-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="px-2 pt-2 pb-3 space-y-1 sm:px-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >             {navItems.map(item =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                item.subItems ? (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div key={item.text}&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;h3 className="px-3 py-2 text-sm font-semibold text-zinc-500 dark:text-zinc-400 uppercase tracking-wider"&gt;{item.text}&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;div className="pl-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            {item.subItems.map(subItem =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                                &lt;a </span>
<span class="cstat-no" title="statement not covered" >                                key={subItem.href} </span>
<span class="cstat-no" title="statement not covered" >                                href={subItem.href} </span>
<span class="cstat-no" title="statement not covered" >                                onClick={(e) =&gt; { handleNavClick(e, subItem.href.slice(1)); setIsMobileMenuOpen(false); }} </span>
<span class="cstat-no" title="statement not covered" >                                className="block px-3 py-2 rounded-md text-base font-medium text-zinc-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-800"</span>
                                &gt;
<span class="cstat-no" title="statement not covered" >                                {subItem.text}</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >                            ))}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                ) : (
<span class="cstat-no" title="statement not covered" >                    &lt;a </span>
<span class="cstat-no" title="statement not covered" >                        key={item.href} </span>
<span class="cstat-no" title="statement not covered" >                        href={item.href} </span>
<span class="cstat-no" title="statement not covered" >                        onClick={(e) =&gt; { handleNavClick(e, item.href?.slice(1) || ''); setIsMobileMenuOpen(false); }} </span>
<span class="cstat-no" title="statement not covered" >                        className="block px-3 py-2 rounded-md text-base font-medium text-zinc-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-800"</span>
                    &gt;
<span class="cstat-no" title="statement not covered" >                        {item.text}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/a&gt;</span>
                )
<span class="cstat-no" title="statement not covered" >            ))}</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="pt-4 pb-3 border-t border-zinc-200 dark:border-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center justify-center px-5 gap-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button onClick={() =&gt; { setView('auth'); setIsMobileMenuOpen(false); }} className="flex-1 text-center px-5 py-2.5 font-medium rounded-lg text-zinc-600 dark:text-zinc-300 hover:bg-zinc-100 dark:hover:bg-zinc-800"&gt;Log In&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button onClick={() =&gt; { setView('auth'); setIsMobileMenuOpen(false); }} className="flex-1 text-center px-5 py-2.5 text-white font-medium bg-brand-600 hover:bg-brand-700 rounded-lg shadow-sm"&gt;Sign Up&lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
      )}
<span class="cstat-no" title="statement not covered" >    &lt;/header&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default Header;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    