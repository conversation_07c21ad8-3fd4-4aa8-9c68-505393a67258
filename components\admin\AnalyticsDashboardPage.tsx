


import React, { memo, useEffect, useMemo, useRef } from 'react';
import { PricingPlan, Team, User } from '../../types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';

declare global {
  interface Window {
    Chart: typeof import('chart.js').Chart;
  }
}

interface AnalyticsDashboardPageProps {
  allUsers: User[];
  allTeams: Team[];
  pricingPlans: PricingPlan[];
}

const StatCard: React.FC<{ title: string; value: string | number; description?: string; }> = ({ title, value, description }) => (
  <Card>
    <CardHeader className="pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
    </CardHeader>
    <CardContent>
      <div className="text-3xl font-bold">{value}</div>
      {description && <p className="text-xs text-zinc-500 dark:text-zinc-400 mt-1">{description}</p>}
    </CardContent>
  </Card>
);

const UserSignupChart: React.FC<{ users: User[] }> = memo(({ users }) => {
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstanceRef = useRef<import('chart.js').Chart | null>(null);

  // Memoize chart data preparation to avoid recalculation on every render
  const chartData = useMemo(() => {
    const labels: string[] = [];
    const data: number[] = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (let i = 29; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));

      const count = users.filter(user => {
        if (!user.createdAt) { return false; }
        const userDate = new Date(user.createdAt);
        userDate.setHours(0, 0, 0, 0);
        return userDate.getTime() === date.getTime();
      }).length;
      data.push(count);
    }

    return { labels, data };
  }, [users]);

  useEffect(() => {
    if (!chartRef.current || !window.Chart) { return; }

    const ctx = chartRef.current.getContext('2d');
    if (!ctx) { return; }

    if (chartInstanceRef.current) { chartInstanceRef.current.destroy(); }

    chartInstanceRef.current = new window.Chart(ctx, {
      type: 'line',
      data: {
        labels: chartData.labels,
        datasets: [{
          label: 'New Users',
          data: chartData.data,
          borderColor: '#4f46e5',
          backgroundColor: 'rgba(79, 70, 229, 0.1)',
          fill: true,
          tension: 0.3,
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: { y: { beginAtZero: true } },
        plugins: { legend: { display: false } },
      }
    });

    return () => {
      if (chartInstanceRef.current) { chartInstanceRef.current.destroy(); }
    };
  }, [users]);

  return (
    <div className="h-80">
      <canvas ref={chartRef}></canvas>
    </div>
  );
});

const AnalyticsDashboardPage: React.FC<AnalyticsDashboardPageProps> = ({ allUsers, allTeams, pricingPlans }) => {
  const stats = useMemo(() => {
    const nonAdminUsers = allUsers.filter(u => !u.isSuperAdmin);

    const mrr = nonAdminUsers
      .reduce((acc, user) => {
        const plan = pricingPlans.find(p => p.name === user.planName);
        if (plan && plan.price !== 'Free' && plan.price !== 'Custom') {
          return acc + parseInt(plan.price.replace('$', ''));
        }
        return acc;
      }, 0);

    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const newUsers = nonAdminUsers.filter(u => u.createdAt && new Date(u.createdAt) > thirtyDaysAgo).length;

    const paidUsers = nonAdminUsers.filter(u => u.planName !== 'Registered User').length;
    const conversionRate = nonAdminUsers.length > 0 ? (paidUsers / nonAdminUsers.length) * 100 : 0;

    return {
      mrr: `$${mrr.toLocaleString()}`,
      newUsers,
      totalTenants: allTeams.length,
      conversionRate: `${conversionRate.toFixed(1)}%`,
    };
  }, [allUsers, allTeams, pricingPlans]);

  return (
    <div className="p-4 sm:p-6 lg:p-8 space-y-6">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard title="Monthly Recurring Revenue" value={stats.mrr} description="Simulated MRR" />
        <StatCard title="New Users (30 Days)" value={stats.newUsers} />
        <StatCard title="Total Tenants" value={stats.totalTenants} />
        <StatCard title="Conversion Rate" value={stats.conversionRate} description="Free to Paid" />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>User Signups</CardTitle>
          <CardDescription>New user registrations over the last 30 days.</CardDescription>
        </CardHeader>
        <CardContent>
          <UserSignupChart users={allUsers} />
        </CardContent>
      </Card>
    </div>
  );
};

export default AnalyticsDashboardPage;
