export type SseEvent = { event: string | null; data: unknown | null };

// Parse a single SSE block (separated by blank line) into event and data
export function parseSseBlock(block: string): SseEvent {
  const lines = block.split('\n');
  let event: string | null = null;
  const dataLines: string[] = [];
  for (const l of lines) {
    const line = l.trim();
    if (!line) {continue;}
    if (line.startsWith('event:')) {event = line.slice(6).trim();}
    if (line.startsWith('data:')) {dataLines.push(line.slice(5).trim());}
  }
  let data: unknown = null;
  if (dataLines.length > 0) {
    const joined = dataLines.join('\n');
    try { data = JSON.parse(joined); } catch { 
      // Intentionally ignore JSON parse errors and set data to null
      data = null; 
    }
  }
  return { event, data };
}

// Extract multiple blocks from a buffered string. Returns parsed events and the remainder buffer.
export function extractSseEvents(buffer: string): { events: SseEvent[]; rest: string } {
  // Support both \n\n and \r\n\r\n as block separators
  const parts = buffer.split(/\r?\n\r?\n/);
  const rest = parts.pop() || '';
  const events: SseEvent[] = [];
  for (const part of parts) {
    const block = part.trim();
    if (!block) {continue;}
    events.push(parseSseBlock(block));
  }
  return { events, rest };
}
