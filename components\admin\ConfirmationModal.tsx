
import React from 'react';
import { But<PERSON> } from '../ui/Button';
import { AlertTriangleIcon } from '../Icons';

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: React.ReactNode;
  confirmText?: string;
  variant?: 'destructive' | 'default';
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText = 'Confirm',
  variant = 'destructive'
}) => {
  if (!isOpen) {return null;}

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-md p-6">
        <div className="flex items-start gap-4">
          <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 sm:mx-0 sm:h-10 sm:w-10">
            <AlertTriangleIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
          </div>
          <div className="mt-0 text-center sm:text-left">
            <h3 className="text-lg leading-6 font-medium text-zinc-900 dark:text-zinc-50">{title}</h3>
            <div className="mt-2">
              <p className="text-sm text-zinc-500 dark:text-zinc-400">{message}</p>
            </div>
          </div>
        </div>
        <div className="mt-5 sm:mt-4 flex flex-col-reverse sm:flex-row-reverse gap-3">
          <Button variant={variant} onClick={onConfirm}>
            {confirmText}
          </Button>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationModal;