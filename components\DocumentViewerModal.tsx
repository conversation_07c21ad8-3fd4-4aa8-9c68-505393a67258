

import React, { useState, useEffect, useRef } from 'react';
import { CloseIcon, CopyIcon, PdfIcon, WordIcon, SaveIcon, ChevronDownIcon } from './Icons';
import LazyDocumentContent from './LazyDocumentContent';
import { Button } from './ui/Button';
import Input from './ui/Input';
import { exportAsPdf, exportAsDocx } from '../lib/file-export';
import { User } from '../types';
import Dropdown from './ui/Dropdown';

interface DocumentViewerModalProps {
  isOpen: boolean;
  onClose: () => void;
  contractContent: string; // This will always be Markdown from the AI
  onSave: (documentName: string, folderId: string | null, htmlContent: string, clientId: string | null) => void;
  user: User | null | undefined;
}

const DocumentViewerModal: React.FC<DocumentViewerModalProps> = ({ isOpen, onClose, contractContent, onSave, user }) => {
  const [documentName, setDocumentName] = useState('');
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(null);
  const [selectedFolderName, setSelectedFolderName] = useState('Uncategorized');
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
  const [selectedClientName, setSelectedClientName] = useState('No Client');
  const [isExporting, setIsExporting] = useState(false);
  
  const pageContainerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isOpen && contractContent) {
      const firstLine = contractContent.split('\n').find(line => line.trim() !== '');
      const suggestedName = firstLine && firstLine.startsWith('#')
        ? firstLine.replace(/##? /g, '').trim()
        : 'Untitled Document';
      setDocumentName(suggestedName.slice(0, 50));
      setSelectedFolderId(null);
      setSelectedFolderName('Uncategorized');
      setSelectedClientId(null);
      setSelectedClientName('No Client');
    }
  }, [isOpen, contractContent]);

  if (!isOpen) {return null;}

  const handleCopy = () => {
    navigator.clipboard.writeText(contractContent);
  };

  const handleExport = async (format: 'pdf' | 'docx') => {
    setIsExporting(true);
    const name = documentName || 'document';
    try {
        if (format === 'pdf' && pageContainerRef.current) {
            await exportAsPdf(pageContainerRef.current, name);
        } else if (format === 'docx' && contentRef.current) {
            await exportAsDocx(contentRef.current.innerHTML, name);
        }
    } catch {
        // Failed to export document
        alert(`An error occurred while exporting to ${format}. Please try again.`);
    } finally {
        setIsExporting(false);
    }
  };

  const handleSaveClick = () => {
    const formattedHtmlContent = contentRef.current?.innerHTML;
    if (documentName.trim() && formattedHtmlContent) {
      onSave(documentName.trim(), selectedFolderId, formattedHtmlContent, selectedClientId);
    }
  };

  const handleFolderSelect = (folderId: string | null) => {
      setSelectedFolderId(folderId);
      if (folderId === null) {
          setSelectedFolderName('Uncategorized');
      } else {
          const folder = user?.folders.find(f => f.id === folderId);
          setSelectedFolderName(folder?.name || 'Uncategorized');
      }
  };
  
  const handleClientSelect = (clientId: string | null) => {
      setSelectedClientId(clientId);
      if (clientId === null) {
          setSelectedClientName('No Client');
      } else {
          const client = user?.clients?.find(c => c.id === clientId);
          setSelectedClientName(client?.name || 'No Client');
      }
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-60 dark:bg-opacity-80 z-50 flex justify-center items-center p-4 transition-opacity"
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="document-title"
    >
      <div 
        className="bg-white dark:bg-zinc-900 rounded-2xl shadow-2xl w-full max-w-4xl h-[90vh] flex flex-col transform transition-transform scale-95 opacity-0 animate-fade-in-scale"
        onClick={(e) => e.stopPropagation()}
        style={{ animation: 'fade-in-scale 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards' }}
      >
        <header className="flex items-center justify-between p-4 border-b border-zinc-200 dark:border-zinc-800 flex-shrink-0">
          <h2 id="document-title" className="text-lg font-semibold text-zinc-800 dark:text-zinc-200">Review & Save Document</h2>
          <div className="flex items-center gap-2">
            <button onClick={handleCopy} className="p-2 text-zinc-500 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800 rounded-lg" title="Copy Text" aria-label="Copy document text">
              <CopyIcon className="w-5 h-5" />
            </button>
            <button onClick={onClose} className="p-2 text-zinc-500 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800 rounded-lg" title="Close" aria-label="Close document viewer">
              <CloseIcon className="w-6 h-6" />
            </button>
          </div>
        </header>
        <main className="flex-1 overflow-y-auto page-container p-0">
            <div className="a4-page" ref={pageContainerRef}>
                <LazyDocumentContent content={contractContent} ref={contentRef}/>
            </div>
        </main>
        <footer className="p-4 border-t border-zinc-200 dark:border-zinc-800 flex flex-col sm:flex-row items-center justify-between gap-3 bg-zinc-50 dark:bg-zinc-950 rounded-b-2xl">
            <div className="w-full sm:w-auto sm:flex-1 flex items-center gap-2">
                <Input
                  id="doc-name-input"
                  value={documentName}
                  onChange={(e) => setDocumentName(e.target.value)}
                  placeholder="Enter document name..."
                  className="bg-white dark:bg-zinc-800 border-zinc-300 dark:border-zinc-700 text-sm"
                  aria-label="Document name"
                />
            </div>
            <div className="w-full sm:w-auto flex items-center justify-end gap-2 flex-wrap">
                 {user && (
                  <Dropdown>
                      <Dropdown.Trigger>
                          <Button variant="outline" className="w-full sm:w-auto">
                              <span>Save to: {selectedFolderName}</span>
                              <ChevronDownIcon className="w-4 h-4 ml-2" />
                          </Button>
                      </Dropdown.Trigger>
                      <Dropdown.Content align="right" side="top">
                          <Dropdown.Item onClick={() => handleFolderSelect(null)} icon={<CloseIcon className="w-4 h-4"/>}>Uncategorized</Dropdown.Item>
                          {user.folders.map(folder => (
                              <Dropdown.Item key={folder.id} onClick={() => handleFolderSelect(folder.id)} icon={<SaveIcon className="w-4 h-4"/>}>
                                  {folder.name}
                              </Dropdown.Item>
                          ))}
                      </Dropdown.Content>
                  </Dropdown>
                )}
                {user && (user.clients || []).length > 0 && (
                    <Dropdown>
                        <Dropdown.Trigger>
                            <Button variant="outline" className="w-full sm:w-auto">
                                <span>Client: {selectedClientName}</span>
                                <ChevronDownIcon className="w-4 h-4 ml-2" />
                            </Button>
                        </Dropdown.Trigger>
                        <Dropdown.Content align="right" side="top">
                            <Dropdown.Item onClick={() => handleClientSelect(null)} icon={<CloseIcon className="w-4 h-4"/>}>No Client</Dropdown.Item>
                            {user.clients?.map(client => (
                                <Dropdown.Item key={client.id} onClick={() => handleClientSelect(client.id)} icon={<SaveIcon className="w-4 h-4"/>}>
                                    {client.name}
                                </Dropdown.Item>
                            ))}
                        </Dropdown.Content>
                    </Dropdown>
                )}
                <Dropdown>
                    <Dropdown.Trigger>
                        <Button variant="outline" disabled={isExporting}>
                            <span>{isExporting ? 'Exporting...' : 'Export'}</span>
                            <ChevronDownIcon className="w-4 h-4 ml-2" />
                        </Button>
                    </Dropdown.Trigger>
                    <Dropdown.Content align="right" side="top">
                        <Dropdown.Item onClick={() => handleExport('docx')} icon={<WordIcon className="w-4 h-4 text-blue-600"/>}>Export as Word</Dropdown.Item>
                        <Dropdown.Item onClick={() => handleExport('pdf')} icon={<PdfIcon className="w-4 h-4 text-red-600"/>}>Export as PDF</Dropdown.Item>
                    </Dropdown.Content>
                </Dropdown>
                <Button onClick={handleSaveClick} disabled={!documentName.trim() || !user} className="w-full sm:w-auto">
                    <SaveIcon className="w-5 h-5 mr-2" />
                    <span>{user ? 'Save Document' : 'Login to Save'}</span>
                </Button>
            </div>
        </footer>
      </div>
      <style>{`
        @keyframes fade-in-scale {
          from { transform: scale(0.95); opacity: 0; }
          to { transform: scale(1); opacity: 1; }
        }
        .animate-fade-in-scale {
          animation: fade-in-scale 0.3s cubic-bezier(0.16, 1, 0.3, 1) forwards;
        }
      `}</style>
    </div>
  );
};

export default DocumentViewerModal;
