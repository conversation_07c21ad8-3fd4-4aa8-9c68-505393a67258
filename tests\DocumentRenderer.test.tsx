// @vitest-environment jsdom
import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import DocumentRenderer from '../components/DocumentRenderer';
import React from 'react';

describe('DocumentRenderer', () => {
  it('renders DocumentRenderer component', () => {
    const ref = { current: document.createElement('div') };
    render(
      <DocumentRenderer
        content="Test content"
        isEditing={false}
        editorRef={ref as any}
        onClick={() => {}}
      />
    );
    // The content is rendered as HTML, so check for the container
    // Since the content is set via innerHTML, we check the ref's innerHTML
    expect(ref.current.innerHTML).toContain('Test content');
  });
});
