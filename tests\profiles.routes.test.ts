import { describe, it, expect, beforeAll, beforeEach, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

// Mock Supabase client layer used by routes and middleware
let mockIsAdmin = true;

vi.mock('../server/supabaseClient', () => {
  const supabaseAdmin = {
    from: (table: string) => ({
      select: (_cols: string) => ({
        eq: (_col: string, val: unknown) => ({
          single: async () => {
            if (table === 'pricing_plans' && val === 'Premium') {return { data: { name: 'Premium' }, error: null };}
            return { data: null, error: null };
          },
        }),
      }),
      update: (patch: Record<string, unknown>) => ({
        eq: (_col: string, val: unknown) => ({
          select: () => ({
            single: async () => ({ data: { id: val, ...patch }, error: null }),
          }),
        }),
      }),
    }),
  };

  const getUserClient = (_token: string) => ({
    auth: {
      getUser: async () => ({ data: { user: { id: 'test-user', email: '<EMAIL>' } }, error: null }),
    },
    from: (table: string) => ({
      select: (_cols: string) => ({
        eq: (_col: string, _val: unknown) => ({
          single: async () => {
            if (table === 'profiles') {return { data: { is_admin: mockIsAdmin }, error: null };}
            return { data: null, error: null };
          },
        }),
      }),
      upsert: (row: Record<string, unknown>) => ({
        select: () => ({
          single: async () => ({ data: { ...row }, error: null }),
        }),
      }),
      update: (patch: Record<string, unknown>) => ({
        eq: (_col: string, val: unknown) => ({
          select: () => ({
            single: async () => ({ data: { id: val, ...patch }, error: null }),
          }),
        }),
      }),
    }),
  });

  return { supabaseAdmin, getUserClient };
});

beforeAll(() => {
  process.env.TEST_BYPASS_AUTH = '1';
});

describe('Profiles routes', () => {
  beforeEach(() => { mockIsAdmin = true; });

  it('allows self plan change when valid', async () => {
    const res = await request(app)
      .put('/api/profile/me')
      .send({ name: 'New Name', planName: 'Premium' })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('profile');
    expect(res.body.profile).toHaveProperty('name', 'New Name');
    expect(res.body.profile).toHaveProperty('plan_name', 'Premium');
  });

  it('rejects invalid plan change', async () => {
    const res = await request(app)
      .put('/api/profile/me')
      .send({ planName: 'Invalid' })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(400);
  });

  it('admin route updates protected fields when is_admin', async () => {
    mockIsAdmin = true;
    (global as any).mockIsAdmin = true;
    const res = await request(app)
      .put('/api/admin/profiles/abc-123')
      .send({ planName: 'Premium', status: 'active' })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('profile');
    expect(res.body.profile).toHaveProperty('plan_name', 'Premium');
    expect(res.body.profile).toHaveProperty('status', 'active');
  });

  it('admin route forbidden when not admin', async () => {
    mockIsAdmin = false;
    (global as any).mockIsAdmin = false;
    const res = await request(app)
      .put('/api/admin/profiles/abc-123')
      .send({ planName: 'Premium' })
      .set('Content-Type', 'application/json')
      .set('Authorization', 'Bearer test-token');
    expect(res.status).toBe(403);
  });
});

