import { describe, it, expect, beforeAll, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

vi.mock('../server/supabaseClient', () => {
  const getUserClient = (_token: string) => ({
    from: (_table: string) => ({
      select: () => ({
        order: () => ({ data: [{ id: 'con1', connector_id: 'conn-1', credentials: {}, created_at: new Date().toISOString() }], error: null })
      }),
      insert: (row: Record<string, unknown>) => ({
        select: () => ({ single: () => Promise.resolve({ data: { id: 'con2', ...row, created_at: new Date().toISOString() }, error: null }) })
      })
    }),
    auth: {
      getUser: () => Promise.resolve({ data: { user: { id: 'u1' } } })
    }
  });
  return { getUserClient };
});

beforeAll(() => { process.env.TEST_BYPASS_AUTH = '1'; });

describe('Connections routes', () => {
  it('GET /api/connections returns connections', async () => {
    const res = await request(app).get('/api/connections');
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('connections');
    expect(Array.isArray(res.body.connections)).toBe(true);
  });

  it('POST /api/connections creates connection', async () => {
    const res = await request(app)
      .post('/api/connections')
      .send({
        connectorId: 'conn-1',
        credentials: { apiKey: 'abc' }
      })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(201);
    expect(res.body).toHaveProperty('connection');
  });
});
