
import React, { useState, useMemo } from 'react';
import { User, DashboardView, Clause } from '../types';
import { Button } from './ui/Button';
import { LockSolidIcon, PlusCircleIcon, SearchIcon, LibraryIcon } from './Icons';
import { Card, CardContent, CardHeader } from './ui/Card';
import ClauseEditorModal from './ClauseEditorModal';
// import { cn } from '../lib/utils'; // Unused utility

interface ClauseLibraryPageProps {
  user: User;
  setView: (view: DashboardView) => void;
  onCreateClause: (clauseData: Omit<Clause, 'id' | 'createdAt'>) => void;
  onUpdateClause: (clauseId: string, updates: Partial<Omit<Clause, 'id' | 'createdAt'>>) => void;
  onDeleteClause: (clauseId: string) => void;
}

const ClauseLibraryPage: React.FC<ClauseLibraryPageProps> = ({ user, setView, onCreateClause, onUpdateClause, onDeleteClause }) => {
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [clauseToEdit, setClauseToEdit] = useState<Clause | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [itemToDelete, setItemToDelete] = useState<Clause | null>(null);

  const isPremium = user.planName === 'Premium' || user.planName === 'Enterprise';

  const filteredClauses = useMemo(() => {
    const clauses = user.clauses || [];
    if (!searchTerm) {return clauses;}
    return clauses.filter(
      c =>
        c.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        c.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        c.tags.some(t => t.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [user.clauses, searchTerm]);

  const handleEdit = (clause: Clause) => {
    setClauseToEdit(clause);
    setIsEditorOpen(true);
  };

  const handleSave = (data: Omit<Clause, 'id' | 'createdAt'>) => {
    if (clauseToEdit) {
      onUpdateClause(clauseToEdit.id, data);
    } else {
      onCreateClause(data);
    }
    setIsEditorOpen(false);
    setClauseToEdit(null);
  };
  
  if (!isPremium) {
    return (
      <div className="p-4 sm:p-6 lg:p-8 h-full flex items-center justify-center">
        <div className="text-center">
          <LockSolidIcon className="w-12 h-12 mx-auto text-zinc-300 mb-4" />
          <h3 className="text-xl font-semibold text-zinc-800">Unlock the Clause Library</h3>
          <p className="text-zinc-500 mt-2 mb-4 max-w-md">
            Upgrade to Premium to create, manage, and reuse your own library of legal clauses to speed up your workflow.
          </p>
          <Button onClick={() => setView('subscription')}>
            Upgrade Now
          </Button>
        </div>
      </div>
    );
  }

  return (
    <>
    <div className="p-4 sm:p-6 lg:p-8 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-zinc-900">Clause Library</h1>
          <p className="text-zinc-600 mt-1">Manage your reusable, pre-approved legal clauses.</p>
        </div>
        <Button onClick={() => { setClauseToEdit(null); setIsEditorOpen(true); }}>
          <PlusCircleIcon className="w-5 h-5 mr-2" />
          New Clause
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <div className="relative w-full sm:max-w-xs">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <SearchIcon className="h-5 w-5 text-zinc-400" />
            </div>
            <input
              type="text"
              placeholder="Search clauses..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full rounded-md border-zinc-300 pl-10 focus:border-brand-500 focus:ring-brand-500 sm:text-sm p-2 bg-white"
            />
          </div>
        </CardHeader>
        <CardContent>
          {filteredClauses.length > 0 ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {filteredClauses.map(clause => (
                  <Card key={clause.id} className="flex flex-col">
                    <div className="p-6 flex-1 flex flex-col">
                      <h3 className="font-semibold text-zinc-900">{clause.title}</h3>
                      <div className="mt-2 text-sm text-zinc-600 flex-1" dangerouslySetInnerHTML={{ __html: clause.content.substring(0, 100) + '...' }} />
                      <div className="mt-4 flex flex-wrap gap-2">
                          {clause.tags.map(tag => (
                              <span key={tag} className="px-2 py-1 bg-zinc-100 text-zinc-700 text-xs font-medium rounded-full">{tag}</span>
                          ))}
                      </div>
                    </div>
                    <div className="p-2 border-t border-zinc-200 bg-zinc-50/50 flex justify-end rounded-b-xl">
                        <Button variant="ghost" size="sm" onClick={() => handleEdit(clause)}>Edit</Button>
                        <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700 hover:bg-red-50" onClick={() => setItemToDelete(clause)}>Delete</Button>
                    </div>
                  </Card>
                ))}
            </div>
          ) : (
            <div className="text-center py-16">
                <LibraryIcon className="mx-auto h-12 w-12 text-zinc-300" />
                <h3 className="mt-2 text-lg font-semibold text-zinc-800">Your Library is Empty</h3>
                <p className="mt-1 text-sm text-zinc-500">Create your first reusable clause to get started.</p>
                <Button className="mt-4" onClick={() => { setClauseToEdit(null); setIsEditorOpen(true); }}>
                    <PlusCircleIcon className="w-5 h-5 mr-2"/>
                    Create Clause
                </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
    
    <ClauseEditorModal
      isOpen={isEditorOpen}
      onClose={() => setIsEditorOpen(false)}
      onSave={handleSave}
      existingClause={clauseToEdit}
    />

    {itemToDelete && (
         <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-2xl shadow-xl w-full max-w-md p-6">
                <h3 className="text-lg font-medium">Delete Clause</h3>
                <p className="mt-2 text-sm text-zinc-500">
                    Are you sure you want to delete "{itemToDelete.title}"? This action cannot be undone.
                </p>
                <div className="mt-4 flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setItemToDelete(null)}>Cancel</Button>
                    <Button variant="destructive" onClick={() => { onDeleteClause(itemToDelete.id); setItemToDelete(null); }}>Delete</Button>
                </div>
            </div>
        </div>
    )}
    </>
  );
};

export default ClauseLibraryPage;