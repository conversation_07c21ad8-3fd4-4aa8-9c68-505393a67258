-- Add missing version_type column to document_versions table
-- This migration addresses the schema gap identified in the technical investigation

-- Add version_type column to document_versions table
ALTER TABLE public.document_versions 
ADD COLUMN IF NOT EXISTS version_type text NOT NULL DEFAULT 'auto' 
CHECK (version_type IN ('auto', 'manual'));

-- Create index for version_type for better query performance
CREATE INDEX IF NOT EXISTS document_versions_version_type_idx 
ON public.document_versions(version_type);

-- Update existing records to have default version_type
UPDATE public.document_versions 
SET version_type = 'auto' 
WHERE version_type IS NULL;

-- Add comment for documentation
COMMENT ON COLUMN public.document_versions.version_type IS 'Type of version creation: auto (system-generated) or manual (user-created)';

-- Verify the migration
DO $$
BEGIN
    -- Check if column exists and has correct constraint
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'document_versions' 
        AND column_name = 'version_type'
    ) THEN
        RAISE EXCEPTION 'Migration failed: version_type column not created';
    END IF;
    
    -- Check if constraint exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_schema = 'public' 
        AND constraint_name LIKE '%version_type%'
    ) THEN
        RAISE EXCEPTION 'Migration failed: version_type constraint not created';
    END IF;
    
    RAISE NOTICE 'Migration completed successfully: version_type column added to document_versions';
END $$;
