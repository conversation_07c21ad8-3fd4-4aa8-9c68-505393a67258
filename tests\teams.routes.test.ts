import { describe, it, expect, beforeAll, vi, afterAll } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

vi.mock('../server/supabaseClient', () => {
  const mockClient = {
    auth: { 
      getUser: async (token?: string) => {
        if (!token) {return { data: { user: null }, error: { message: 'No token provided' } };}
        return { data: { user: { id: 'test-user', email: '<EMAIL>' } }, error: null };
      }
    },
    from: (table: string) => {
      if (table === 'teams') {
        return {
          select: (_cols: string) => ({ 
            order: async () => ({ data: [{ id: 'team-1', name: 'Test Team', status: 'active' }], error: null }),
            eq: (_col: string, _val: string) => ({ single: async () => ({ data: { id: 'team-1', name: 'Test Team', status: 'active' }, error: null }) })
          }),
          insert: (row: Record<string, unknown>) => ({ select: () => ({ single: async () => ({ data: { id: 'team-1', ...row }, error: null }) }) }),
          update: (patch: Record<string, unknown>) => ({ eq: (_col: string, id: string) => ({ select: () => ({ single: async () => ({ data: { id, ...patch }, error: null }) }) }) }),
          delete: () => ({ eq: async () => ({ error: null }) }),
        };
      }
      if (table === 'team_members') {
        return {
          select: (_cols: string) => ({ eq: async () => ({ data: [{ id: 'member-1', team_id: 'team-1', user_id: 'test-user', role: 'Admin' }], error: null }) }),
          insert: (row: Record<string, unknown>) => ({ select: () => ({ single: async () => ({ data: { id: 'member-1', ...row }, error: null }) }) }),
          update: (patch: Record<string, unknown>) => ({ eq: (_col: string, _val: string) => ({ eq: (_col2: string, _val2: string) => ({ select: () => ({ single: async () => ({ data: { id: 'member-1', ...patch }, error: null }) }) }) }) }),
          delete: () => ({ eq: (_col: string, _val: string) => ({ eq: async () => ({ error: null }) }) }),
        };
      }
      if (table === 'team_api_keys') {
        return {
          select: (_cols: string) => ({ eq: (_col: string, _val: string) => ({ order: async () => ({ data: [{ id: 'key-1', name: 'Test Key', key: 'test-key' }], error: null }) }) }),
          insert: (row: Record<string, unknown>) => ({ select: () => ({ single: async () => ({ data: { id: 'key-1', ...row }, error: null }) }) }),
          delete: () => ({ eq: (_col: string, _val: string) => ({ eq: async () => ({ error: null }) }) }),
        };
      }
      if (table === 'team_sso_config') {
        return {
          select: (_cols: string) => ({ eq: (_col: string, _val: string) => ({ single: async () => ({ data: { id: 'sso-1', enabled: true }, error: null }) }) }),
          upsert: (row: Record<string, unknown>) => ({ select: () => ({ single: async () => ({ data: { id: 'sso-1', ...row }, error: null }) }) }),
        };
      }
      if (table === 'profiles') {
        return {
          select: (columns: string) => {
            if (columns.includes('plan_name')) {
              return {
                eq: (_col: string, _val: string) => ({ single: async () => ({ data: { plan_name: 'premium' }, error: null }) })
              };
            }
            if (columns.includes('is_admin')) {
              return {
                eq: (_col: string, _val: string) => ({ single: async () => ({ data: { is_admin: true }, error: null }) })
              };
            }
            return {
              eq: (_col: string, _val: string) => ({ single: async () => ({ data: { plan_name: 'premium', is_admin: true }, error: null }) })
            };
          },
        };
      }
      return {
        select: (_cols?: string) => ({ 
          eq: (_col: string, _val: string) => ({ single: async () => ({ data: null, error: null }) }),
          order: async () => ({ data: [], error: null })
        }),
        insert: () => ({ select: () => ({ single: async () => ({ data: null, error: null }) }) }),
        update: () => ({ eq: () => ({ select: () => ({ single: async () => ({ data: null, error: null }) }) }) }),
        delete: () => ({ eq: async () => ({ error: null }) }),
      };
    },
  };
  
  const getUserClient = (_token: string) => mockClient;
  const supabaseAdmin = mockClient;
  
  return { getUserClient, supabaseAdmin };
});

vi.mock('../middleware/plan', () => ({
  requirePremium: vi.fn((req: any, res: any, next: any) => next()),
  requireEnterprise: vi.fn((req: any, res: any, next: any) => next()),
}));

// Mock the getUserClient to return a mock that makes getPlanName return premium
vi.mock('../supabaseClient', () => ({
  getUserClient: vi.fn(() => ({
    auth: {
      getUser: vi.fn().mockResolvedValue({
        data: { user: { id: 'test-user-id' } }
      })
    },
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn().mockResolvedValue({
            data: { plan_name: 'premium' },
            error: null
          })
        }))
      }))
    }))
  })),
  supabaseAdmin: {
    from: vi.fn(() => ({
      insert: vi.fn().mockResolvedValue({ data: null, error: null }),
      update: vi.fn().mockResolvedValue({ data: null, error: null }),
      delete: vi.fn().mockResolvedValue({ data: null, error: null }),
    })),
  },
}));

vi.mock('../middleware/auth', () => ({
  requireAuth: (req: any, res: any, next: any) => next(),
  getAccessToken: () => 'test-token',
}));

beforeAll(() => {
  process.env.TEST_BYPASS_AUTH = '1';
});

afterAll(() => {
  delete process.env.TEST_BYPASS_AUTH;
});

describe('Teams routes', () => {
  describe('GET /api/teams', () => {
    it('should return teams list', async () => {
      const res = await request(app)
        .get('/api/teams')
        .set('Authorization', 'Bearer test-token');
      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('teams');
      expect(Array.isArray(res.body.teams)).toBe(true);
    });

    it('should return 401 without access token', async () => {
      // Temporarily disable auth bypass for this test
      const originalBypass = process.env.TEST_BYPASS_AUTH;
      delete process.env.TEST_BYPASS_AUTH;
      
      const res = await request(app).get('/api/teams');
      expect(res.status).toBe(401);
      
      // Restore auth bypass
      if (originalBypass) {process.env.TEST_BYPASS_AUTH = originalBypass;}
    });
  });

  describe('POST /api/teams', () => {
    it('should create a new team', async () => {
      const teamData = { name: 'New Team', status: 'active' };
      const res = await request(app)
        .post('/api/teams')
        .send(teamData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(201);
      expect(res.body).toHaveProperty('team');
      expect(res.body.team).toHaveProperty('name', 'New Team');
    });

    it('should return 400 for invalid data', async () => {
      const res = await request(app)
        .post('/api/teams')
        .send({ name: '' })
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(400);
    });

    it('should return 401 without access token', async () => {
      // Temporarily disable auth bypass for this test
      const originalBypass = process.env.TEST_BYPASS_AUTH;
      delete process.env.TEST_BYPASS_AUTH;
      
      const res = await request(app)
        .post('/api/teams')
        .send({ name: 'Test Team' });
      expect(res.status).toBe(401);
      
      // Restore auth bypass
      if (originalBypass) {process.env.TEST_BYPASS_AUTH = originalBypass;}
    });
  });

  describe('PUT /api/teams/:teamId', () => {
    it('should update team', async () => {
      const updateData = { name: 'Updated Team' };
      const res = await request(app)
        .put('/api/teams/team-1')
        .send(updateData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('team');
    });

    it('should return 400 for invalid data', async () => {
      const res = await request(app)
        .put('/api/teams/team-1')
        .send({ name: '' })
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(400);
    });
  });

  describe('DELETE /api/teams/:teamId', () => {
    it('should delete team', async () => {
      const res = await request(app)
        .delete('/api/teams/team-1')
        .set('Authorization', 'Bearer test-token');
      expect(res.status).toBe(204);
    });
  });

  describe('Team Members', () => {
    describe('GET /api/teams/:teamId/members', () => {
      it('should return team members', async () => {
        const res = await request(app)
          .get('/api/teams/team-1/members')
          .set('Authorization', 'Bearer test-token');
        expect(res.status).toBe(200);
        expect(res.body).toHaveProperty('members');
      });
    });

    describe('POST /api/teams/:teamId/members', () => {
      it('should add team member', async () => {
        const memberData = {
          userId: '123e4567-e89b-12d3-a456-426614174000',
          email: '<EMAIL>',
          role: 'Member'
        };
        const res = await request(app)
          .post('/api/teams/team-1/members')
          .send(memberData)
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(201);
        expect(res.body).toHaveProperty('member');
      });

      it('should return 400 for invalid member data', async () => {
        const res = await request(app)
          .post('/api/teams/team-1/members')
          .send({ email: 'invalid-email' })
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(400);
      });
    });

    describe('PUT /api/teams/:teamId/members/:userId', () => {
      it('should update team member role', async () => {
        const res = await request(app)
          .put('/api/teams/team-1/members/test-user')
          .send({ role: 'Admin' })
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(200);
        expect(res.body).toHaveProperty('member');
      });
    });

    describe('DELETE /api/teams/:teamId/members/:userId', () => {
      it('should remove team member', async () => {
        const res = await request(app)
          .delete('/api/teams/team-1/members/test-user')
          .set('Authorization', 'Bearer test-token');
        expect(res.status).toBe(204);
      });
    });
  });

  describe('Team API Keys', () => {
    describe('GET /api/teams/:teamId/api-keys', () => {
      it('should return team API keys', async () => {
        const res = await request(app)
          .get('/api/teams/team-1/api-keys')
          .set('Authorization', 'Bearer test-token');
        expect(res.status).toBe(200);
        expect(res.body).toHaveProperty('apiKeys');
      });
    });

    describe('POST /api/teams/:teamId/api-keys', () => {
      it('should create team API key', async () => {
        const keyData = { name: 'Test Key', key: 'test-api-key' };
        const res = await request(app)
          .post('/api/teams/team-1/api-keys')
          .send(keyData)
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(201);
        expect(res.body).toHaveProperty('apiKey');
      });

      it('should return 400 for invalid key data', async () => {
        const res = await request(app)
          .post('/api/teams/team-1/api-keys')
          .send({ name: '' })
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(400);
      });
    });

    describe('DELETE /api/teams/:teamId/api-keys/:id', () => {
      it('should delete team API key', async () => {
        const res = await request(app)
          .delete('/api/teams/team-1/api-keys/key-1')
          .set('Authorization', 'Bearer test-token');
        expect(res.status).toBe(204);
      });
    });
  });

  describe('Team SSO Config', () => {
    describe('GET /api/teams/:teamId/sso-config', () => {
      it('should return team SSO config', async () => {
        const res = await request(app)
          .get('/api/teams/team-1/sso-config')
          .set('Authorization', 'Bearer test-token');
        expect(res.status).toBe(200);
        expect(res.body).toHaveProperty('ssoConfig');
      });
    });

    describe('PUT /api/teams/:teamId/sso-config', () => {
      it('should update team SSO config', async () => {
        const ssoData = {
          enabled: true,
          idpUrl: 'https://example.com/sso',
          entityId: 'test-entity',
          certificate: 'test-cert'
        };
        const res = await request(app)
          .put('/api/teams/team-1/sso-config')
          .send(ssoData)
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(200);
        expect(res.body).toHaveProperty('ssoConfig');
      });

      it('should return 400 for invalid SSO data', async () => {
        const res = await request(app)
          .put('/api/teams/team-1/sso-config')
          .send({ enabled: 'invalid' })
          .set('Authorization', 'Bearer test-token')
          .set('Content-Type', 'application/json');
        expect(res.status).toBe(400);
      });
    });
  });
});