import path from 'path';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      define: {
        'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY)
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      },
      server: {
        // Enable history API fallback for client-side routing
        historyApiFallback: true,
      },
      preview: {
        // Enable history API fallback for production preview
        historyApiFallback: true,
      },
      test: {
        environment: 'jsdom',
        globals: true,
        setupFiles: [],
        deps: {
          inline: ['@google/genai']
        }
      }
    };
});
