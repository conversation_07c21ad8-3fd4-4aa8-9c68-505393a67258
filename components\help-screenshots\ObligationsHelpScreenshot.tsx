
import React from 'react';
import { CheckCircleIcon } from '../Icons';

const ObligationsHelpScreenshot = () => {
  return (
    <div className="relative my-6 p-4 border rounded-lg bg-zinc-50 dark:bg-zinc-900 overflow-hidden select-none text-xs">
      <h1 className="text-lg font-bold text-zinc-900 dark:text-zinc-100">All Obligations</h1>
      <p className="text-zinc-600 dark:text-zinc-400 mt-1 mb-4">A consolidated view of all contractual obligations and deadlines.</p>

      <div className="bg-white dark:bg-zinc-800 rounded-lg shadow border border-zinc-200 dark:border-zinc-700 p-3">
         <div className="w-full h-24 border-2 border-dashed border-zinc-200 dark:border-zinc-700 rounded-md flex items-center justify-center text-center text-zinc-400">
             <div>
                <CheckCircleIcon className="w-8 h-8 mx-auto" />
                <p>Obligation tracking table appears here</p>
             </div>
         </div>
      </div>
    </div>
  );
};

export default ObligationsHelpScreenshot;
