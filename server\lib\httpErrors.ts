import type { PostgrestError } from '@supabase/supabase-js';

type NormalizedSupabaseError = {
  code?: string;
  message?: string;
};

const SUPABASE_ERROR_MESSAGES: Record<string, string> = {
  '23505': 'A record with these details already exists.',
  '23503': 'A related record is missing or invalid.',
  '23502': 'A required value is missing.',
  '42501': 'You do not have permission to perform this action.',
  PGRST116: 'The requested resource could not be found.',
  PGRST301: 'You do not have access to this resource.',
};

const SUPABASE_MESSAGE_MATCHERS: Array<{ pattern: RegExp; message: string }> = [
  { pattern: /duplicate key value violates unique constraint/i, message: SUPABASE_ERROR_MESSAGES['23505'] },
  { pattern: /violates foreign key constraint/i, message: SUPABASE_ERROR_MESSAGES['23503'] },
  { pattern: /violates not-null constraint/i, message: SUPABASE_ERROR_MESSAGES['23502'] },
  { pattern: /row-level security policy/i, message: SUPABASE_ERROR_MESSAGES['42501'] },
];

export const DEFAULT_SUPABASE_ERROR_MESSAGE = 'An unexpected error occurred. Please try again.';

function normalizeSupabaseError(error: unknown): NormalizedSupabaseError | null {
  if (!error) {
    return null;
  }

  if (error instanceof Error) {
    return { message: error.message };
  }

  if (typeof error === 'string') {
    return { message: error };
  }

  if (typeof error === 'object') {
    const maybeError = error as Partial<PostgrestError>;
    const message = typeof maybeError.message === 'string' ? maybeError.message : undefined;
    const code = typeof maybeError.code === 'string' ? maybeError.code : undefined;

    if (!message && !code) {
      return null;
    }

    return { message, code };
  }

  return null;
}

export function getSupabaseErrorMessage(error: unknown, fallbackMessage = DEFAULT_SUPABASE_ERROR_MESSAGE): string {
  const normalized = normalizeSupabaseError(error);
  if (!normalized) {
    return fallbackMessage;
  }

  if (normalized.code) {
    const mapped = SUPABASE_ERROR_MESSAGES[normalized.code];
    if (mapped) {
      return mapped;
    }
  }

  if (normalized.message) {
    const matcher = SUPABASE_MESSAGE_MATCHERS.find(entry => entry.pattern.test(normalized.message));
    if (matcher) {
      return matcher.message;
    }
  }

  return fallbackMessage;
}
