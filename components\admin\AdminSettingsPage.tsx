


import React, { useState, useRef, useEffect } from 'react';
import { sanitizeHtml } from '../../lib/sanitize';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../ui/Card';
import { Button } from '../ui/Button';
import { Ta<PERSON>, Tabs<PERSON>ist, TabsTrigger, TabsContent } from '../ui/Tabs';
import SimpleEditorToolbar from '../ui/SimpleEditorToolbar';
import { StripeConfig } from '../../types';
import StripeSettings from './StripeSettings';

interface AdminSettingsPageProps {
  termsContent: string;
  privacyContent: string;
  onUpdateStaticContent: (type: 'terms' | 'privacy', content: string) => void;
  stripeConfig: StripeConfig;
  onUpdateStripeConfig: (config: StripeConfig) => void;
}

const StaticPageEditor: React.FC<{
    contentToEdit: string;
    onContentChange: (newContent: string) => void;
}> = ({ contentToEdit, onContentChange }) => {
    const [activeView, setActiveView] = useState<'design' | 'html'>('design');
    const editorRef = useRef<HTMLDivElement>(null);
    
    useEffect(() => {
        if (activeView === 'design' && editorRef.current && editorRef.current.innerHTML !== contentToEdit) {
            editorRef.current.innerHTML = sanitizeHtml(contentToEdit || '');
        }
    }, [activeView, contentToEdit]);
    
    return (
        <Tabs>
            <div className="flex justify-between items-center mb-2">
                <TabsList>
                <TabsTrigger onClick={() => setActiveView('design')} data-state={activeView === 'design' ? 'active' : 'inactive'}>Design View</TabsTrigger>
                <TabsTrigger onClick={() => setActiveView('html')} data-state={activeView === 'html' ? 'active' : 'inactive'}>HTML View</TabsTrigger>
                </TabsList>
            </div>

            <TabsContent className={activeView === 'design' ? 'block' : 'hidden'}>
                <SimpleEditorToolbar editorRef={editorRef} />
                <div
                    ref={editorRef}
                    onInput={(e) => onContentChange(e.currentTarget.innerHTML)}
                    contentEditable
                    suppressContentEditableWarning
                    className="w-full h-96 p-4 border border-zinc-300 dark:border-zinc-700 rounded-md overflow-y-auto focus:ring-2 focus:ring-brand-500 focus:border-brand-500 prose dark:prose-invert"
                    dangerouslySetInnerHTML={{ __html: sanitizeHtml(contentToEdit) }}
                />
            </TabsContent>
            <TabsContent className={activeView === 'html' ? 'block' : 'hidden'}>
                <textarea
                    value={contentToEdit}
                    onChange={(e) => onContentChange(e.target.value)}
                    rows={20}
                    className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-zinc-800 text-zinc-200 p-2 font-mono text-sm"
                />
            </TabsContent>
        </Tabs>
    )
};


// FIX: Changed component typing from React.FC to a standard function to allow attaching sub-components.
const AdminSettingsPage = (props: AdminSettingsPageProps) => {
  const { termsContent, privacyContent, onUpdateStaticContent, stripeConfig, onUpdateStripeConfig } = props;
  const [activeTab, setActiveTab] = useState<'content' | 'stripe'>('content');

  return (
    <div className="p-4 sm:p-6 lg:p-8 space-y-6">
       <div>
            <h1 className="text-3xl font-bold text-zinc-900 dark:text-white">Platform Settings</h1>
            <p className="text-zinc-600 dark:text-zinc-400 mt-1">Manage global settings, static page content, and integrations.</p>
        </div>
        <Tabs>
            <TabsList>
                <TabsTrigger onClick={() => setActiveTab('content')} data-state={activeTab === 'content' ? 'active' : 'inactive'}>Page Content</TabsTrigger>
                <TabsTrigger onClick={() => setActiveTab('stripe')} data-state={activeTab === 'stripe' ? 'active' : 'inactive'}>Stripe</TabsTrigger>
            </TabsList>
            
            <TabsContent className={activeTab === 'content' ? 'block' : 'hidden'}>
                <Card className="mt-4">
                    <CardHeader><CardTitle>Static Page Content</CardTitle><CardDescription>Edit the content for the public-facing legal pages.</CardDescription></CardHeader>
                    <CardContent>
                        <AdminSettingsPage.StaticContentEditor
                            termsContent={termsContent}
                            privacyContent={privacyContent}
                            onUpdateStaticContent={onUpdateStaticContent}
                        />
                    </CardContent>
                </Card>
            </TabsContent>
            
            <TabsContent className={activeTab === 'stripe' ? 'block' : 'hidden'}>
                <StripeSettings
                    stripeConfig={stripeConfig}
                    onUpdateStripeConfig={onUpdateStripeConfig}
                />
            </TabsContent>
        </Tabs>
    </div>
  );
};

// FIX: Define props type for the sub-component and use it.
type StaticContentEditorProps = Pick<AdminSettingsPageProps, 'termsContent' | 'privacyContent' | 'onUpdateStaticContent'>;

AdminSettingsPage.StaticContentEditor = ({ termsContent, privacyContent, onUpdateStaticContent }: StaticContentEditorProps) => {
    const [activePage, setActivePage] = useState<'terms' | 'privacy'>('terms');
    const [localTerms, setLocalTerms] = useState(termsContent);
    const [localPrivacy, setLocalPrivacy] = useState(privacyContent);
    const [isSaved, setIsSaved] = useState(false);
    
    const handleSave = () => {
        onUpdateStaticContent('terms', localTerms);
        onUpdateStaticContent('privacy', localPrivacy);
        setIsSaved(true);
        setTimeout(() => setIsSaved(false), 2000);
    };

    return (
        <div className="space-y-6">
            <Tabs>
              <TabsList>
                <TabsTrigger onClick={() => setActivePage('terms')} data-state={activePage === 'terms' ? 'active' : 'inactive'}>Terms & Conditions</TabsTrigger>
                <TabsTrigger onClick={() => setActivePage('privacy')} data-state={activePage === 'privacy' ? 'active' : 'inactive'}>Privacy Policy</TabsTrigger>
              </TabsList>
              <TabsContent className="mt-4">
                {activePage === 'terms' && <StaticPageEditor contentToEdit={localTerms} onContentChange={setLocalTerms} key="terms-editor" />}
                {activePage === 'privacy' && <StaticPageEditor contentToEdit={localPrivacy} onContentChange={setLocalPrivacy} key="privacy-editor"/>}
              </TabsContent>
            </Tabs>
            <div className="flex items-center gap-4 pt-4 border-t dark:border-zinc-800">
                <Button onClick={handleSave}>Save Content Changes</Button>
                {isSaved && <span className="text-sm font-medium text-green-600 dark:text-green-400">Content saved!</span>}
            </div>
        </div>
    );
};

export default AdminSettingsPage;
