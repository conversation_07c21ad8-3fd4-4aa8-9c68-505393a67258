import { describe, it, expect, beforeAll, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

vi.mock('../server/supabaseClient', () => {
  const getUserClient = (_token: string) => ({
    auth: { getUser: async () => ({ data: { user: { id: 'test-user' } }, error: null }) },
    from: (table: string) => {
      if (table === 'flows') {
        return {
          select: (_cols: string) => ({ order: async () => ({ data: [{ id: 'flow-1', name: 'Test Flow', status: 'active' }], error: null }) }),
          insert: (row: Record<string, unknown>) => ({ select: () => ({ single: async () => ({ data: { id: 'flow-1', ...row }, error: null }) }) }),
          update: (patch: Record<string, unknown>) => ({ eq: (_col: string, id: string) => ({ select: () => ({ single: async () => ({ data: { id, ...patch }, error: null }) }) }) }),
          delete: () => ({ eq: async () => ({ error: null }) }),
        };
      }
      return {
        select: () => ({ data: [], error: null }),
        insert: () => ({ data: null, error: null }),
        update: () => ({ data: null, error: null }),
        delete: () => ({ data: null, error: null }),
      };
    },
  });
  return { getUserClient };
});

vi.mock('../middleware/auth', () => ({
  requireAuth: (req: any, res: any, next: any) => next(),
  getAccessToken: () => 'test-token',
}));

vi.mock('../middleware/plan', () => ({
  requirePremium: (req: any, res: any, next: any) => next(),
}));

beforeAll(() => {
  process.env.TEST_BYPASS_AUTH = '1';
});

describe('Flows routes', () => {
  describe('GET /api/flows', () => {
    it('should return flows list', async () => {
      const res = await request(app)
        .get('/api/flows')
        .set('Authorization', 'Bearer test-token');
      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('flows');
      expect(Array.isArray(res.body.flows)).toBe(true);
    });
  });

  describe('POST /api/flows', () => {
    it('should create a new flow', async () => {
      const flowData = {
        name: 'New Flow',
        trigger: {
          connectionId: '123e4567-e89b-12d3-a456-************',
          triggerKey: 'webhook'
        },
        action: {
          connectionId: '123e4567-e89b-12d3-a456-************',
          actionKey: 'send_email'
        },
        fieldMapping: {
          'email': 'user.email',
          'name': 'user.name'
        },
        status: 'active'
      };
      const res = await request(app)
        .post('/api/flows')
        .send(flowData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(201);
      expect(res.body).toHaveProperty('flow');
      expect(res.body.flow).toHaveProperty('name', 'New Flow');
    });

    it('should create flow with default values', async () => {
      const flowData = {
        name: 'Simple Flow',
        trigger: {
          connectionId: '123e4567-e89b-12d3-a456-************',
          triggerKey: 'webhook'
        },
        action: {
          connectionId: '123e4567-e89b-12d3-a456-************',
          actionKey: 'send_email'
        }
      };
      const res = await request(app)
        .post('/api/flows')
        .send(flowData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(201);
      expect(res.body).toHaveProperty('flow');
    });

    it('should return 400 for invalid flow data', async () => {
      const res = await request(app)
        .post('/api/flows')
        .send({ name: '' })
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(400);
    });

    it('should return 400 for missing trigger', async () => {
      const flowData = {
        name: 'Invalid Flow',
        action: {
          connectionId: '123e4567-e89b-12d3-a456-************',
          actionKey: 'send_email'
        }
      };
      const res = await request(app)
        .post('/api/flows')
        .send(flowData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(400);
    });

    it('should return 400 for missing action', async () => {
      const flowData = {
        name: 'Invalid Flow',
        trigger: {
          connectionId: '123e4567-e89b-12d3-a456-************',
          triggerKey: 'webhook'
        }
      };
      const res = await request(app)
        .post('/api/flows')
        .send(flowData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(400);
    });

    it('should return 400 for invalid UUID in trigger', async () => {
      const flowData = {
        name: 'Invalid Flow',
        trigger: {
          connectionId: 'invalid-uuid',
          triggerKey: 'webhook'
        },
        action: {
          connectionId: '123e4567-e89b-12d3-a456-************',
          actionKey: 'send_email'
        }
      };
      const res = await request(app)
        .post('/api/flows')
        .send(flowData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(400);
    });

    it('should return 400 for invalid status enum', async () => {
      const flowData = {
        name: 'Invalid Flow',
        trigger: {
          connectionId: '123e4567-e89b-12d3-a456-************',
          triggerKey: 'webhook'
        },
        action: {
          connectionId: '123e4567-e89b-12d3-a456-************',
          actionKey: 'send_email'
        },
        status: 'invalid-status'
      };
      const res = await request(app)
        .post('/api/flows')
        .send(flowData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(400);
    });
  });

  describe('PUT /api/flows/:id', () => {
    it('should update flow', async () => {
      const updateData = {
        name: 'Updated Flow',
        status: 'inactive'
      };
      const res = await request(app)
        .put('/api/flows/flow-1')
        .send(updateData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('flow');
    });

    it('should update flow with trigger changes', async () => {
      const updateData = {
        trigger: {
          connectionId: '123e4567-e89b-12d3-a456-426614174002',
          triggerKey: 'new_webhook'
        }
      };
      const res = await request(app)
        .put('/api/flows/flow-1')
        .send(updateData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('flow');
    });

    it('should update flow with action changes', async () => {
      const updateData = {
        action: {
          connectionId: '123e4567-e89b-12d3-a456-426614174003',
          actionKey: 'new_action'
        }
      };
      const res = await request(app)
        .put('/api/flows/flow-1')
        .send(updateData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('flow');
    });

    it('should update flow with field mapping changes', async () => {
      const updateData = {
        fieldMapping: {
          'new_field': 'new_value'
        }
      };
      const res = await request(app)
        .put('/api/flows/flow-1')
        .send(updateData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('flow');
    });

    it('should return 400 for invalid update data', async () => {
      const res = await request(app)
        .put('/api/flows/flow-1')
        .send({ name: '' })
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(400);
    });

    it('should return 400 for invalid trigger UUID in update', async () => {
      const updateData = {
        trigger: {
          connectionId: 'invalid-uuid',
          triggerKey: 'webhook'
        }
      };
      const res = await request(app)
        .put('/api/flows/flow-1')
        .send(updateData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(400);
    });

    it('should return 400 for invalid status in update', async () => {
      const updateData = {
        status: 'invalid-status'
      };
      const res = await request(app)
        .put('/api/flows/flow-1')
        .send(updateData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(400);
    });
  });

  describe('DELETE /api/flows/:id', () => {
    it('should delete flow', async () => {
      const res = await request(app)
        .delete('/api/flows/flow-1')
        .set('Authorization', 'Bearer test-token');
      expect(res.status).toBe(204);
    });
  });
});