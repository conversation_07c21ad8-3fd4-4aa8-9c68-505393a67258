
import React from 'react';
import { cn } from '../../lib/utils';
import { AdminView } from '../../types';
import { BillingIcon, EditIcon, LegalIcon, ServerIcon, SettingsIcon, UsersIcon } from '../Icons';

interface AdminSidebarProps {
  currentView: AdminView;
  setView: (view: AdminView) => void;
}

const navItems = [
  { view: 'analytics' as const, label: 'Analytics', icon: ServerIcon },
  { view: 'billing' as const, label: 'Customers', icon: UsersIcon },
  { view: 'plans' as const, label: 'Plan Management', icon: BillingIcon },
  { view: 'cms' as const, label: 'CMS', icon: EditIcon },
  { view: 'settings' as const, label: 'Platform Settings', icon: SettingsIcon },
];

const AdminSidebar: React.FC<AdminSidebarProps> = ({ currentView, setView }) => {
  return (
    <aside className="w-64 flex-shrink-0 bg-white dark:bg-zinc-950 flex flex-col border-r border-zinc-200 dark:border-zinc-800">
      <div className="flex items-center h-16 px-4 border-b border-zinc-200 dark:border-zinc-800">
        <LegalIcon className="h-8 w-8 text-brand-600" />
        <span className="ml-3 text-xl font-bold text-zinc-900 dark:text-white">LexiGen <span className="text-sm font-normal text-zinc-500">Admin</span></span>
      </div>
      <nav className="flex-1 p-2 space-y-1">
        {navItems.map(item => (
          <a
            key={item.view}
            href="#"
            onClick={(e) => { e.preventDefault(); setView(item.view); }}
            className={cn(
              'flex items-center px-3 py-2.5 text-sm font-medium rounded-md',
              currentView === item.view
                ? 'bg-brand-50 text-brand-600 dark:bg-zinc-800 dark:text-white'
                : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800'
            )}
          >
            <item.icon className="mr-3 h-5 w-5" />
            {item.label}
          </a>
        ))}
      </nav>
    </aside>
  );
};

export default AdminSidebar;
