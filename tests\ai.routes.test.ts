import { describe, it, expect, beforeAll } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

beforeAll(() => {
  process.env.TEST_BYPASS_AUTH = '1';
  delete process.env.GEMINI_API_KEY;
});

describe('AI routes (no key configured)', () => {
  it('compare-versions returns 500 when AI not configured', async () => {
    const res = await request(app)
      .post('/api/ai/compare-versions')
      .send({ contentA: '<p>a</p>', contentB: '<p>b</p>' })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(500);
    expect(res.body).toHaveProperty('error');
  });

  it('analyze returns 500 when AI not configured', async () => {
    const res = await request(app)
      .post('/api/ai/analyze')
      .send({ content: '<p>doc</p>' })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(500);
    expect(res.body).toHaveProperty('error');
  });

  it('external-analyze returns 500 when AI not configured', async () => {
    const res = await request(app)
      .post('/api/ai/external-analyze')
      .send({ content: '<p>doc</p>' })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(500);
    expect(res.body).toHaveProperty('error');
  });
});

