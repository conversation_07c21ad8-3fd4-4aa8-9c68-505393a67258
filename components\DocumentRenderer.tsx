import React, { useEffect, memo } from 'react';
import { sanitizeHtml } from '../lib/sanitize';

interface DocumentRendererProps {
  content: string;
  isEditing: boolean;
  editorRef: React.RefObject<HTMLDivElement>;
  // FIX: Added onClick prop to allow parent component to handle clicks on the editor area.
  onClick: (e: React.MouseEvent<HTMLDivElement>) => void;
}

const DocumentRenderer: React.FC<DocumentRendererProps> = memo(({ content, isEditing, editorRef, onClick }) => {
  // This effect synchronizes the editor's content with the committed state.
  // It runs only when the component mounts or when the `content` prop changes
  // (e.g., on initial load or after cancelling an edit), NOT during typing.
  useEffect(() => {
    if (editorRef.current) {
      const sanitizedContent = sanitizeHtml(content || '');
      if (editorRef.current.innerHTML !== sanitizedContent) {
        editorRef.current.innerHTML = sanitizedContent;
      }
    }
  }, [content, editorRef]);

  return (
    <>
      <div
        ref={editorRef}
        className="document-editor-content a4-page"
        contentEditable={isEditing}
        suppressContentEditableWarning={true}
        onClick={onClick}
        // The onInput handler is removed from here to prevent re-renders on every keystroke.
        // The parent component now reads the content directly from the ref when saving.
        data-placeholder="Start typing your document here..."
      />
    </>
  );
});

export default DocumentRenderer;
