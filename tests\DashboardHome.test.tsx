// @vitest-environment jsdom
import { vi, describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import DashboardHome from '../components/DashboardHome';
import React from 'react';

describe('DashboardHome', () => {
  const mockUser = {
    id: 'u1',
    name: 'Test User',
    email: '<EMAIL>',
    documents: [],
    folders: [],
    // ...add other required User fields as needed
  };
  const setView = vi.fn();
  const onViewDocument = vi.fn();

  it('renders without crashing', () => {
    render(
      <DashboardHome
        user={mockUser as any}
        setView={setView}
        onViewDocument={onViewDocument}
      />
    );
    expect(screen.getByRole('heading', { name: /Upcoming Deadlines/i })).toBeInTheDocument();
  });
});
