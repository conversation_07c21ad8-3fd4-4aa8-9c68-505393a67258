// Legal Validation Framework Types
// Based on ISO/IEC 42001:2023, NIST AI RMF, and legal compliance standards

export type JurisdictionType = 'country' | 'state' | 'province' | 'territory' | 'federal' | 'international';

export type LegalSystem = 'common-law' | 'civil-law' | 'religious-law' | 'customary-law' | 'mixed';

export type ContractType = 
  | 'employment' 
  | 'service-agreement' 
  | 'nda' 
  | 'partnership' 
  | 'licensing' 
  | 'purchase-order' 
  | 'lease' 
  | 'merger-acquisition' 
  | 'loan-agreement' 
  | 'insurance' 
  | 'franchise' 
  | 'distribution' 
  | 'consulting' 
  | 'software-license' 
  | 'data-processing' 
  | 'other';

export type ValidationSeverity = 'critical' | 'high' | 'medium' | 'low' | 'info';

export type ComplianceStandard = 
  | 'GDPR' 
  | 'CCPA' 
  | 'HIPAA' 
  | 'SOX' 
  | 'PCI-DSS' 
  | 'ISO-27001' 
  | 'FINRA' 
  | 'EU-AI-Act' 
  | 'PIPEDA' 
  | 'LGPD' 
  | 'custom';

export interface Jurisdiction {
  id: string;
  name: string;
  code: string; // ISO 3166 country codes, state abbreviations, etc.
  type: JurisdictionType;
  parentJurisdiction?: string; // For states/provinces under countries
  legalSystem: LegalSystem;
  language: string; // Primary legal language
  isActive: boolean;
  lastUpdated: string;
}

export interface LegalRequirement {
  id: string;
  jurisdictionId: string;
  contractType: ContractType;
  requirementType: 'mandatory-clause' | 'prohibited-clause' | 'format-requirement' | 'disclosure-requirement';
  title: string;
  description: string;
  clausePattern?: string; // Regex or text pattern to match
  severity: ValidationSeverity;
  complianceStandards: ComplianceStandard[];
  effectiveDate: string;
  expiryDate?: string;
  source: string; // Legal citation or reference
  isActive: boolean;
}

export interface ValidationRule {
  id: string;
  name: string;
  description: string;
  ruleType: 'syntax' | 'semantic' | 'legal' | 'compliance' | 'contextual';
  jurisdictions: string[]; // Jurisdiction IDs this rule applies to
  contractTypes: ContractType[];
  pattern?: string; // Regex pattern for text matching
  aiPrompt?: string; // AI prompt for semantic validation
  severity: ValidationSeverity;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ValidationIssue {
  id: string;
  ruleId: string;
  severity: ValidationSeverity;
  title: string;
  description: string;
  suggestion?: string;
  location?: {
    startIndex: number;
    endIndex: number;
    lineNumber?: number;
    columnNumber?: number;
  };
  affectedText?: string;
  jurisdictions: string[];
  complianceStandards: ComplianceStandard[];
  isResolved: boolean;
  resolvedAt?: string;
  resolvedBy?: string;
}

export interface LegalValidationContext {
  documentId: string;
  contractType: ContractType;
  primaryJurisdiction: string;
  secondaryJurisdictions: string[];
  parties: ContractParty[];
  governingLaw?: string;
  disputeResolutionJurisdiction?: string;
  complianceRequirements: ComplianceStandard[];
  businessContext?: string;
  industryType?: string;
  transactionValue?: number;
  isInternational: boolean;
}

export interface ContractParty {
  id: string;
  name: string;
  type: 'individual' | 'corporation' | 'partnership' | 'government' | 'non-profit';
  jurisdiction: string;
  address?: string;
  registrationNumber?: string;
}

export interface LegalValidationResult {
  id: string;
  documentId: string;
  validationContext: LegalValidationContext;
  overallScore: number; // 0-100 compliance score
  status: 'passed' | 'failed' | 'warning' | 'pending';
  issues: ValidationIssue[];
  criticalIssueCount: number;
  highIssueCount: number;
  mediumIssueCount: number;
  lowIssueCount: number;
  validatedAt: string;
  validatedBy: string; // User ID or system
  aiModelVersion?: string;
  processingTimeMs: number;
  recommendations: LegalRecommendation[];
}

export interface LegalRecommendation {
  id: string;
  type: 'add-clause' | 'modify-clause' | 'remove-clause' | 'review-required' | 'legal-counsel';
  priority: ValidationSeverity;
  title: string;
  description: string;
  suggestedClause?: string;
  reasoning: string;
  jurisdictions: string[];
  complianceStandards: ComplianceStandard[];
  estimatedRisk: 'low' | 'medium' | 'high' | 'critical';
}

export interface ClauseValidation {
  clauseId: string;
  clauseTitle: string;
  clauseContent: string;
  isValid: boolean;
  jurisdictionalCompliance: JurisdictionalCompliance[];
  issues: ValidationIssue[];
  suggestions: string[];
  riskLevel: ValidationSeverity;
  lastValidated: string;
}

export interface JurisdictionalCompliance {
  jurisdictionId: string;
  jurisdictionName: string;
  isCompliant: boolean;
  requiredModifications: string[];
  prohibitedElements: string[];
  missingRequirements: string[];
  complianceScore: number; // 0-100
  lastChecked: string;
}

export interface LegalKnowledgeBase {
  id: string;
  jurisdiction: string;
  contractType: ContractType;
  standardClauses: StandardClause[];
  prohibitedClauses: ProhibitedClause[];
  requiredDisclosures: RequiredDisclosure[];
  formatRequirements: FormatRequirement[];
  lastUpdated: string;
  version: string;
}

export interface StandardClause {
  id: string;
  title: string;
  content: string;
  description: string;
  isRequired: boolean;
  alternatives: string[];
  jurisdictions: string[];
  contractTypes: ContractType[];
  tags: string[];
}

export interface ProhibitedClause {
  id: string;
  pattern: string;
  description: string;
  reason: string;
  jurisdictions: string[];
  contractTypes: ContractType[];
  severity: ValidationSeverity;
}

export interface RequiredDisclosure {
  id: string;
  title: string;
  content: string;
  triggerConditions: string[];
  jurisdictions: string[];
  contractTypes: ContractType[];
  complianceStandards: ComplianceStandard[];
}

export interface FormatRequirement {
  id: string;
  requirement: string;
  description: string;
  jurisdictions: string[];
  contractTypes: ContractType[];
  isEnforced: boolean;
}

// AI-specific validation interfaces
export interface AIValidationConfig {
  modelVersion: string;
  confidenceThreshold: number; // 0-1
  enableSemanticAnalysis: boolean;
  enableContextualValidation: boolean;
  enableJurisdictionalChecks: boolean;
  enableComplianceScanning: boolean;
  maxProcessingTimeMs: number;
  fallbackToHumanReview: boolean;
}

export interface AIValidationMetrics {
  totalValidations: number;
  averageProcessingTime: number;
  accuracyRate: number;
  falsePositiveRate: number;
  falseNegativeRate: number;
  humanOverrideRate: number;
  lastUpdated: string;
}

// Audit and compliance tracking
export interface ValidationAuditLog {
  id: string;
  documentId: string;
  validationId: string;
  action: 'validation-started' | 'validation-completed' | 'issue-found' | 'issue-resolved' | 'human-override';
  userId?: string;
  timestamp: string;
  details: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
}

export interface ComplianceReport {
  id: string;
  reportType: 'document' | 'portfolio' | 'jurisdiction' | 'time-period';
  generatedAt: string;
  generatedBy: string;
  scope: {
    documentIds?: string[];
    jurisdictions?: string[];
    dateRange?: {
      start: string;
      end: string;
    };
  };
  summary: {
    totalDocuments: number;
    compliantDocuments: number;
    nonCompliantDocuments: number;
    averageComplianceScore: number;
    criticalIssues: number;
    resolvedIssues: number;
  };
  findings: ValidationIssue[];
  recommendations: LegalRecommendation[];
  exportFormat?: 'pdf' | 'excel' | 'json';
}

// Integration with existing Document interface
export interface DocumentLegalValidation {
  documentId: string;
  latestValidationId?: string;
  validationHistory: string[]; // Array of validation result IDs
  complianceStatus: 'compliant' | 'non-compliant' | 'pending' | 'not-validated';
  lastValidated?: string;
  autoValidationEnabled: boolean;
  requiredJurisdictions: string[];
  complianceRequirements: ComplianceStandard[];
}