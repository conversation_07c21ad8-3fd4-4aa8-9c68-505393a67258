

import React, { useState, useMemo, useEffect } from 'react';
import { User, Team, PricingPlan } from '../../types';

// Modal state interfaces
interface ManageSubModalData {
  team: Team;
  owner: User | undefined;
}

interface TeamActionModalData {
  id: string;
  name: string;
  status: string;
}

interface UserActionModalData {
  id: string;
  email: string;
  status: string;
}

type ModalState = 
  | { type: 'manage-sub'; data: ManageSubModalData }
  | { type: 'toggle-team-status'; data: TeamActionModalData }
  | { type: 'toggle-user-status'; data: UserActionModalData }
  | { type: 'verify-user'; data: UserActionModalData }
  | { type: 'reset-password'; data: UserActionModalData }
  | null;
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../ui/Card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON><PERSON>rigger, TabsContent } from '../ui/Tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/Table';
import { CheckCircleIcon, XCircleIcon, MoreVerticalIcon, BanIcon, KeyIcon, SearchIcon } from '../Icons';
import Dropdown from '../ui/Dropdown';
import { Button } from '../ui/Button';
import ManageSubscriptionModal from './ManageSubscriptionModal';
import ConfirmationModal from './ConfirmationModal';
import { cn } from '../../lib/utils';

interface TenantManagementPageProps {
  allUsers: User[];
  allTeams: Team[];
  onUpdateSubscription: (teamId: string, planName: string) => void;
  onToggleUserStatus: (userId: string) => void;
  onToggleTeamStatus: (teamId: string) => void;
  onManualVerifyUser: (userId: string) => void;
  onTriggerPasswordReset: (email: string) => void;
  pricingPlans: PricingPlan[];
  initialSearchTerm: string | null;
  onClearSearchFilter: () => void;
}

const formatDate = (dateString?: string) => {
    if (!dateString) {return 'N/A';}
    return new Date(dateString).toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
};

const getDaysLeft = (dateString?: string) => {
    if (!dateString) {return { text: 'N/A', isUrgent: false };}
    const expiryDate = new Date(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    expiryDate.setHours(0, 0, 0, 0);
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays < 0) {return { text: 'Expired', isUrgent: true };}
    if (diffDays <= 30) {return { text: `${diffDays} days`, isUrgent: true };}
    return { text: `${diffDays} days`, isUrgent: false };
};


const TenantManagementPage: React.FC<TenantManagementPageProps> = (props) => {
  const { allUsers, allTeams, onUpdateSubscription, onToggleUserStatus, onToggleTeamStatus, onManualVerifyUser, onTriggerPasswordReset, pricingPlans, initialSearchTerm, onClearSearchFilter } = props;
  
  const [activeTab, setActiveTab] = useState('tenants');
  const [modalState, setModalState] = useState<ModalState>(null);
  const [teamSearch, setTeamSearch] = useState('');
  const [userSearch, setUserSearch] = useState('');

  useEffect(() => {
    if (initialSearchTerm) {
      const user = allUsers.find(u => (u.name || u.email) === initialSearchTerm);
      const team = allTeams.find(t => t.name === initialSearchTerm);
      if (user) {
        setActiveTab('users');
        setUserSearch(initialSearchTerm);
      } else if (team) {
        setActiveTab('tenants');
        setTeamSearch(initialSearchTerm);
      }
      onClearSearchFilter();
    }
  }, [initialSearchTerm, onClearSearchFilter, allUsers, allTeams]);

  const filteredTeams = useMemo(() => {
      if (!teamSearch) {return allTeams;}
      return allTeams.filter(t => t.name.toLowerCase().includes(teamSearch.toLowerCase()));
  }, [allTeams, teamSearch]);

  const filteredUsers = useMemo(() => {
      const nonAdminUsers = allUsers.filter(u => !u.isSuperAdmin);
      if (!userSearch) {return nonAdminUsers;}
      return nonAdminUsers.filter(u => u.name?.toLowerCase().includes(userSearch.toLowerCase()) || u.email.toLowerCase().includes(userSearch.toLowerCase()));
  }, [allUsers, userSearch]);

  const getTeamOwner = (team: Team): User | undefined => allUsers.find(u => u.id === team.ownerId);

  const getTeamDocCount = (team: Team): number => {
    const memberIds = [team.ownerId, ...team.members.map(m => m.userId)];
    return allUsers.reduce((count, user) => {
        if (memberIds.includes(user.id)) {
            return count + user.documents.length;
        }
        return count;
    }, 0);
  };
  
  const handleAction = (
    type: 'manage-sub' | 'toggle-team-status' | 'toggle-user-status' | 'verify-user' | 'reset-password',
    data: ManageSubModalData | TeamActionModalData | UserActionModalData
  ): void => {
    setModalState({ type, data } as ModalState);
  };

  const renderModals = () => {
    if (!modalState) {return null;}

    const { type, data } = modalState;
    switch(type) {
        case 'manage-sub':
            return <ManageSubscriptionModal isOpen={true} onClose={() => setModalState(null)} onSave={onUpdateSubscription} team={data.team} plans={pricingPlans} currentPlanName={data.owner?.planName || ''} />
        case 'toggle-team-status':
             return <ConfirmationModal isOpen={true} onClose={() => setModalState(null)} onConfirm={() => { onToggleTeamStatus(data.id); setModalState(null); }} title={`${data.status === 'active' ? 'Suspend' : 'Reactivate'} Team`} message={<>Are you sure you want to {data.status === 'active' ? 'suspend' : 'reactivate'} the team <strong>{data.name}</strong>?</>} confirmText={data.status === 'active' ? 'Suspend' : 'Reactivate'}/>
        case 'toggle-user-status':
             return <ConfirmationModal isOpen={true} onClose={() => setModalState(null)} onConfirm={() => { onToggleUserStatus(data.id); setModalState(null); }} title={`${data.status === 'active' ? 'Suspend' : 'Reactivate'} User`} message={<>Are you sure you want to {data.status === 'active' ? 'suspend' : 'reactivate'} <strong>{data.email}</strong>?</>} confirmText={data.status === 'active' ? 'Suspend' : 'Reactivate'}/>
        case 'verify-user':
            return <ConfirmationModal isOpen={true} onClose={() => setModalState(null)} onConfirm={() => { onManualVerifyUser(data.id); setModalState(null); }} title="Manually Verify User" message={<>Manually verify the email for <strong>{data.email}</strong>?</>} confirmText="Verify" variant="default"/>
        case 'reset-password':
            return <ConfirmationModal isOpen={true} onClose={() => setModalState(null)} onConfirm={() => { onTriggerPasswordReset(data.email); setModalState(null); }} title="Send Password Reset" message={<>Send a password reset link to <strong>{data.email}</strong>?</>} confirmText="Send Link" variant="default"/>
        default: return null;
    }
  };

  return (
    <>
      <div className="p-4 sm:p-6 lg:p-8 space-y-6">
        <Tabs>
          <TabsList>
            <TabsTrigger onClick={() => setActiveTab('tenants')} data-state={activeTab === 'tenants' ? 'active' : 'inactive'}>Tenant Management</TabsTrigger>
            <TabsTrigger onClick={() => setActiveTab('users')} data-state={activeTab === 'users' ? 'active' : 'inactive'}>User Management</TabsTrigger>
          </TabsList>
          
          <TabsContent className={activeTab === 'tenants' ? 'block' : 'hidden'}>
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                    <div>
                        <CardTitle>All Tenants (Teams)</CardTitle>
                        <CardDescription>Manage all registered teams on the platform.</CardDescription>
                    </div>
                    <div className="relative w-full sm:max-w-xs"><div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"><SearchIcon className="h-5 w-5 text-zinc-400" /></div><input type="text" placeholder="Search teams..." value={teamSearch} onChange={e => setTeamSearch(e.target.value)} className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 p-2" /></div>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader><TableRow><TableHead>Team</TableHead><TableHead>Owner</TableHead><TableHead>Members</TableHead><TableHead>Docs</TableHead><TableHead>Onboard Date</TableHead><TableHead>Plan Expiry</TableHead><TableHead>Days Left</TableHead><TableHead>Plan</TableHead><TableHead>Status</TableHead><TableHead className="text-right">Actions</TableHead></TableRow></TableHeader>
                  <TableBody>
                    {filteredTeams.map(team => {
                      const owner = getTeamOwner(team);
                      const daysLeft = getDaysLeft(owner?.planExpiryDate);
                      return (
                        <TableRow key={team.id}>
                          <TableCell className="font-medium">{team.name}</TableCell>
                          <TableCell>{owner?.email || 'N/A'}</TableCell>
                          <TableCell>{team.members.length + 1}</TableCell>
                          <TableCell>{getTeamDocCount(team)}</TableCell>
                          <TableCell>{formatDate(owner?.createdAt)}</TableCell>
                          <TableCell>{formatDate(owner?.planExpiryDate)}</TableCell>
                          <TableCell><span className={cn(daysLeft.isUrgent ? 'font-semibold text-red-600 dark:text-red-400' : '')}>{daysLeft.text}</span></TableCell>
                          <TableCell>{owner?.planName || 'N/A'}</TableCell>
                          <TableCell><span className={cn('px-2 py-1 text-xs font-semibold rounded-full capitalize', team.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300' : 'bg-zinc-100 text-zinc-700 dark:bg-zinc-800 dark:text-zinc-300')}>{team.status}</span></TableCell>
                          <TableCell className="text-right">
                            <Dropdown><Dropdown.Trigger><Button variant="ghost" size="icon" className="h-8 w-8"><MoreVerticalIcon className="w-4 h-4" /></Button></Dropdown.Trigger>
                              <Dropdown.Content align="right">
                                <Dropdown.Item onClick={() => handleAction('manage-sub', { team, owner })} icon={<KeyIcon className="w-4 h-4"/>}>Manage Subscription</Dropdown.Item>
                                <Dropdown.Item onClick={() => handleAction('toggle-team-status', team)} icon={<BanIcon className="w-4 h-4"/>}>{team.status === 'active' ? 'Suspend Team' : 'Reactivate Team'}</Dropdown.Item>
                              </Dropdown.Content>
                            </Dropdown>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent className={activeTab === 'users' ? 'block' : 'hidden'}>
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                    <div>
                        <CardTitle>All Users</CardTitle>
                        <CardDescription>Manage all registered users on the platform.</CardDescription>
                    </div>
                    <div className="relative w-full sm:max-w-xs"><div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"><SearchIcon className="h-5 w-5 text-zinc-400" /></div><input type="text" placeholder="Search by name or email..." value={userSearch} onChange={e => setUserSearch(e.target.value)} className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 p-2" /></div>
                </div>
              </CardHeader>
              <CardContent>
                 <Table>
                  <TableHeader><TableRow><TableHead>User</TableHead><TableHead>Team</TableHead><TableHead>Status</TableHead><TableHead>Verified</TableHead><TableHead className="text-right">Actions</TableHead></TableRow></TableHeader>
                  <TableBody>
                    {filteredUsers.map(user => {
                      const team = allTeams.find(t => t.id === user.teamId);
                      return (
                        <TableRow key={user.id}>
                          <TableCell><div className="font-medium">{user.name || 'No Name'}</div><div className="text-sm text-zinc-500">{user.email}</div></TableCell>
                          <TableCell>{team?.name || 'No Team'}</TableCell>
                           <TableCell><span className={cn('px-2 py-1 text-xs font-semibold rounded-full capitalize', user.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300' : 'bg-zinc-100 text-zinc-700 dark:bg-zinc-800 dark:text-zinc-300')}>{user.status}</span></TableCell>
                          <TableCell>
                            {user.isVerified 
                              ? <span className="flex items-center text-green-600"><CheckCircleIcon className="w-4 h-4 mr-1"/> Yes</span> 
                              : <span className="flex items-center text-amber-600"><XCircleIcon className="w-4 h-4 mr-1"/> No</span>}
                          </TableCell>
                           <TableCell className="text-right">
                             <Dropdown><Dropdown.Trigger><Button variant="ghost" size="icon" className="h-8 w-8"><MoreVerticalIcon className="w-4 h-4" /></Button></Dropdown.Trigger>
                              <Dropdown.Content align="right">
                                <Dropdown.Item onClick={() => handleAction('toggle-user-status', user)} icon={<BanIcon className="w-4 h-4"/>}>{user.status === 'active' ? 'Suspend User' : 'Reactivate User'}</Dropdown.Item>
                                {!user.isVerified && <Dropdown.Item onClick={() => handleAction('verify-user', user)} icon={<CheckCircleIcon className="w-4 h-4"/>}>Manually Verify</Dropdown.Item>}
                                <Dropdown.Item onClick={() => handleAction('reset-password', user)} icon={<KeyIcon className="w-4 h-4"/>}>Send Password Reset</Dropdown.Item>
                              </Dropdown.Content>
                            </Dropdown>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
      {renderModals()}
    </>
  );
};

export default TenantManagementPage;
