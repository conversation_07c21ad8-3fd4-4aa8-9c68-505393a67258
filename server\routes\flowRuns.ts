import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { requirePremium } from '../middleware/plan';
import { getUserClient } from '../supabaseClient';

const router = Router();

const insertSchema = z.object({
  flowId: z.string().uuid(),
  status: z.enum(['success','failed','running']).default('running'),
  startedAt: z.string().optional(),
  finishedAt: z.string().optional(),
  triggerData: z.record(z.any()).optional(),
  actionData: z.record(z.any()).optional(),
  error: z.string().nullable().optional(),
});
const updateSchema = insertSchema.partial();

router.get('/', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('flow_runs').select('*').order('started_at', { ascending: false });
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ runs: data });
});

router.post('/', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const parsed = insertSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data: auth } = await supa.auth.getUser();
  const userId = auth.user?.id;
  const { data, error } = await supa
    .from('flow_runs')
    .insert({
      flow_id: parsed.data.flowId,
      user_id: userId,
      status: parsed.data.status,
      started_at: parsed.data.startedAt ?? new Date().toISOString(),
      finished_at: parsed.data.finishedAt ?? null,
      trigger_data: parsed.data.triggerData ?? null,
      action_data: parsed.data.actionData ?? null,
      error: parsed.data.error ?? null,
    })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ run: data });
});

router.put('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = updateSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{ status: string; started_at: string; finished_at: string; trigger_data: Record<string, unknown>; action_data: Record<string, unknown>; error: string }> = {
    status: parsed.data.status,
    started_at: parsed.data.startedAt,
    finished_at: parsed.data.finishedAt,
    trigger_data: parsed.data.triggerData,
    action_data: parsed.data.actionData,
    error: parsed.data.error,
  };
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('flow_runs').update(patch).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ run: data });
});

router.delete('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('flow_runs').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

export default router;
