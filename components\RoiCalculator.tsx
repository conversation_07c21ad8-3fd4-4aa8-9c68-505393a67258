
import React, { useState, useEffect } from 'react';

const AnimatedCounter = ({ value }: { value: number }) => {
    const [count, setCount] = useState(0);
  
    useEffect(() => {
        let start = 0;
        const end = value;
        if (start === end) {return;}
    
        const duration = 1000; // 1 second
        const incrementTime = 10; // update every 10ms
        const totalIncrements = duration / incrementTime;
        const increment = end / totalIncrements;
    
        const timer = setInterval(() => {
            start += increment;
            if (start >= end) {
                setCount(end);
                clearInterval(timer);
            } else {
                setCount(Math.ceil(start));
            }
        }, incrementTime);
    
        return () => clearInterval(timer);
    }, [value]);
  
    return <span>{Math.round(count).toLocaleString()}</span>;
};


const RoiCalculator = () => {
    const [contracts, setContracts] = useState(25);
    const [hours, setHours] = useState(5);
    const [rate, setRate] = useState(150);

    const timeSaved = contracts * hours * 0.4; // Assuming 40% time savings
    const moneySaved = timeSaved * rate;

    return (
        <section className="py-20 sm:py-24 bg-zinc-100 dark:bg-zinc-900">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="grid lg:grid-cols-2 gap-12 items-center">
                    <div>
                        <h2 className="text-3xl font-extrabold text-zinc-900 dark:text-white sm:text-4xl">
                            Calculate Your Potential ROI
                        </h2>
                        <p className="mt-4 text-lg text-zinc-600 dark:text-zinc-400">
                            See how much time and money you can save by automating your contract workflow. Adjust the sliders to match your team's workload.
                        </p>
                        <div className="mt-8 space-y-6">
                            <div>
                                <label className="flex justify-between font-medium text-zinc-700 dark:text-zinc-300">
                                    <span>Contracts per Month</span>
                                    <span className="text-brand-600 dark:text-brand-400 font-bold">{contracts}</span>
                                </label>
                                <input
                                    type="range"
                                    min="1"
                                    max="200"
                                    value={contracts}
                                    onChange={(e) => setContracts(Number(e.target.value))}
                                    className="w-full h-2 bg-zinc-200 dark:bg-zinc-700 rounded-lg appearance-none cursor-pointer range-slider"
                                />
                            </div>
                             <div>
                                <label className="flex justify-between font-medium text-zinc-700 dark:text-zinc-300">
                                    <span>Hours per Contract</span>
                                    <span className="text-brand-600 dark:text-brand-400 font-bold">{hours}</span>
                                </label>
                                <input
                                    type="range"
                                    min="1"
                                    max="40"
                                    value={hours}
                                    onChange={(e) => setHours(Number(e.target.value))}
                                    className="w-full h-2 bg-zinc-200 dark:bg-zinc-700 rounded-lg appearance-none cursor-pointer range-slider"
                                />
                            </div>
                             <div>
                                <label className="flex justify-between font-medium text-zinc-700 dark:text-zinc-300">
                                    <span>Avg. Blended Hourly Rate</span>
                                    <span className="text-brand-600 dark:text-brand-400 font-bold">${rate}</span>
                                </label>
                                <input
                                    type="range"
                                    min="25"
                                    max="500"
                                    step="5"
                                    value={rate}
                                    onChange={(e) => setRate(Number(e.target.value))}
                                    className="w-full h-2 bg-zinc-200 dark:bg-zinc-700 rounded-lg appearance-none cursor-pointer range-slider"
                                />
                            </div>
                        </div>
                    </div>
                    <div className="bg-white dark:bg-zinc-950 rounded-2xl p-8 shadow-xl border border-zinc-200 dark:border-zinc-800 text-center">
                        <h3 className="text-lg font-semibold text-zinc-700 dark:text-zinc-300">Your Estimated Monthly Savings</h3>
                        <div className="mt-6 grid sm:grid-cols-2 gap-6">
                            <div>
                                <p className="text-5xl font-extrabold text-brand-600 dark:text-brand-400">
                                    <AnimatedCounter value={timeSaved} />
                                </p>
                                <p className="mt-2 text-lg font-medium text-zinc-800 dark:text-zinc-200">Hours Saved</p>
                            </div>
                             <div>
                                <p className="text-5xl font-extrabold text-brand-600 dark:text-brand-400">
                                    $<AnimatedCounter value={moneySaved} />
                                </p>
                                <p className="mt-2 text-lg font-medium text-zinc-800 dark:text-zinc-200">Money Saved</p>
                            </div>
                        </div>
                        <p className="mt-8 text-sm text-zinc-500 dark:text-zinc-400">
                            *Estimates are based on an average 40% efficiency gain observed by our customers. Actual results may vary.
                        </p>
                    </div>
                </div>
            </div>
            <style>{`
                .range-slider::-webkit-slider-thumb {
                    -webkit-appearance: none;
                    appearance: none;
                    width: 20px;
                    height: 20px;
                    background: #4f46e5;
                    cursor: pointer;
                    border-radius: 50%;
                }
                .range-slider::-moz-range-thumb {
                    width: 20px;
                    height: 20px;
                    background: #4f46e5;
                    cursor: pointer;
                    border-radius: 50%;
                    border: 0;
                }
            `}</style>
        </section>
    );
};

export default RoiCalculator;
