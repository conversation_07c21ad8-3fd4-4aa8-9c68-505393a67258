import { describe, it, expect } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

describe('Health and basic routes', () => {
  it('GET /health returns ok', async () => {
    const res = await request(app).get('/health');
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('ok', true);
    expect(res.headers).toHaveProperty('x-request-id');
    expect(res.headers['x-request-id']).toBeTruthy();
  });

  it('GET / returns service info', async () => {
    const res = await request(app).get('/');
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('service');
    expect(res.headers).toHaveProperty('x-request-id');
    expect(res.headers['x-request-id']).toBeTruthy();
  });
});

