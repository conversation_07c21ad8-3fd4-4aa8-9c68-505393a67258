-- Performance optimization indexes for frequently queried tables

-- Teams table indexes
CREATE INDEX IF NOT EXISTS teams_owner_id_idx ON teams(owner_id);
CREATE INDEX IF NOT EXISTS teams_created_at_idx ON teams(created_at);
CREATE INDEX IF NOT EXISTS teams_status_idx ON teams(status);

-- Team members indexes
CREATE INDEX IF NOT EXISTS team_members_team_id_idx ON team_members(team_id);
CREATE INDEX IF NOT EXISTS team_members_user_id_idx ON team_members(user_id);
CREATE INDEX IF NOT EXISTS team_members_role_idx ON team_members(role);
CREATE INDEX IF NOT EXISTS team_members_team_user_idx ON team_members(team_id, user_id);

-- Comments and comment threads indexes
CREATE INDEX IF NOT EXISTS comment_threads_document_id_idx ON comment_threads(document_id);
CREATE INDEX IF NOT EXISTS comment_threads_is_resolved_idx ON comment_threads(is_resolved);
CREATE INDEX IF NOT EXISTS comment_threads_created_at_idx ON comment_threads(created_at);

CREATE INDEX IF NOT EXISTS comments_thread_id_idx ON comments(thread_id);
CREATE INDEX IF NOT EXISTS comments_author_email_idx ON comments(author_email);
CREATE INDEX IF NOT EXISTS comments_created_at_idx ON comments(created_at);

-- Document collaborators indexes
CREATE INDEX IF NOT EXISTS document_collaborators_document_id_idx ON document_collaborators(document_id);
CREATE INDEX IF NOT EXISTS document_collaborators_email_idx ON document_collaborators(email);
CREATE INDEX IF NOT EXISTS document_collaborators_permission_idx ON document_collaborators(permission);

-- Activity logs indexes
CREATE INDEX IF NOT EXISTS activity_logs_document_id_idx ON activity_logs(document_id);
CREATE INDEX IF NOT EXISTS activity_logs_user_email_idx ON activity_logs(user_email);
CREATE INDEX IF NOT EXISTS activity_logs_type_idx ON activity_logs(type);
CREATE INDEX IF NOT EXISTS activity_logs_timestamp_idx ON activity_logs(timestamp);

-- Client assets indexes
CREATE INDEX IF NOT EXISTS client_assets_client_id_idx ON client_assets(client_id);
CREATE INDEX IF NOT EXISTS client_assets_user_id_idx ON client_assets(user_id);
CREATE INDEX IF NOT EXISTS client_assets_created_at_idx ON client_assets(created_at);

-- Notifications indexes (if table exists)
DO $$ BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications') THEN
        CREATE INDEX IF NOT EXISTS notifications_user_id_idx ON notifications(user_id);
        CREATE INDEX IF NOT EXISTS notifications_is_read_idx ON notifications(is_read);
        CREATE INDEX IF NOT EXISTS notifications_created_at_idx ON notifications(created_at);
        CREATE INDEX IF NOT EXISTS notifications_type_idx ON notifications(type);
    END IF;
END $$;

-- Workflow instances indexes
CREATE INDEX IF NOT EXISTS workflow_instances_document_id_idx ON workflow_instances(document_id);
CREATE INDEX IF NOT EXISTS workflow_instances_template_id_idx ON workflow_instances(template_id);
CREATE INDEX IF NOT EXISTS workflow_instances_user_id_idx ON workflow_instances(user_id);
CREATE INDEX IF NOT EXISTS workflow_instances_status_idx ON workflow_instances(status);
CREATE INDEX IF NOT EXISTS workflow_instances_created_at_idx ON workflow_instances(created_at);

-- Usage events indexes (for analytics and quota tracking)
CREATE INDEX IF NOT EXISTS usage_events_user_id_idx ON usage_events(user_id);
CREATE INDEX IF NOT EXISTS usage_events_event_idx ON usage_events(event);
CREATE INDEX IF NOT EXISTS usage_events_created_at_idx ON usage_events(created_at);

-- Composite indexes for common query patterns
-- Documents by user and status
create index if not exists documents_user_status_idx on public.documents(user_id, status);
-- Documents by user and folder
create index if not exists documents_user_folder_idx on public.documents(user_id, folder_id);
-- Documents by user and client
create index if not exists documents_user_client_idx on public.documents(user_id, client_id);
CREATE INDEX IF NOT EXISTS documents_user_created_idx ON documents(user_id, created_at);
CREATE INDEX IF NOT EXISTS comments_thread_created_idx ON comments(thread_id, created_at);
CREATE INDEX IF NOT EXISTS activity_logs_user_created_idx ON activity_logs(user_email, timestamp);
CREATE INDEX IF NOT EXISTS usage_events_user_created_idx ON usage_events(user_id, created_at);