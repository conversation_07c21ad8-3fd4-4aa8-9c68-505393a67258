
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/server/routes</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> LexiGen/server/routes</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">64.38% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>1394/2165</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">50.78% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>162/319</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>4/4</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">64.38% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>1394/2165</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="activityLogs.ts"><a href="activityLogs.ts.html">activityLogs.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="37" class="abs high">37/37</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="5" class="abs low">2/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="37" class="abs high">37/37</td>
	</tr>

<tr>
	<td class="file high" data-value="adminProfiles.ts"><a href="adminProfiles.ts.html">adminProfiles.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="43" class="abs high">43/43</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="5" class="abs medium">3/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="43" class="abs high">43/43</td>
	</tr>

<tr>
	<td class="file low" data-value="ai.ts"><a href="ai.ts.html">ai.ts</a></td>
	<td data-value="29.22" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 29%"></div><div class="cover-empty" style="width: 71%"></div></div>
	</td>
	<td data-value="29.22" class="pct low">29.22%</td>
	<td data-value="219" class="abs low">64/219</td>
	<td data-value="40.74" class="pct low">40.74%</td>
	<td data-value="27" class="abs low">11/27</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="29.22" class="pct low">29.22%</td>
	<td data-value="219" class="abs low">64/219</td>
	</tr>

<tr>
	<td class="file medium" data-value="approvers.ts"><a href="approvers.ts.html">approvers.ts</a></td>
	<td data-value="71.18" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 71%"></div><div class="cover-empty" style="width: 29%"></div></div>
	</td>
	<td data-value="71.18" class="pct medium">71.18%</td>
	<td data-value="59" class="abs medium">42/59</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="5" class="abs low">2/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="71.18" class="pct medium">71.18%</td>
	<td data-value="59" class="abs medium">42/59</td>
	</tr>

<tr>
	<td class="file medium" data-value="clauses.ts"><a href="clauses.ts.html">clauses.ts</a></td>
	<td data-value="65.07" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 65%"></div><div class="cover-empty" style="width: 35%"></div></div>
	</td>
	<td data-value="65.07" class="pct medium">65.07%</td>
	<td data-value="63" class="abs medium">41/63</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="6" class="abs low">2/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="65.07" class="pct medium">65.07%</td>
	<td data-value="63" class="abs medium">41/63</td>
	</tr>

<tr>
	<td class="file low" data-value="clientAssets.ts"><a href="clientAssets.ts.html">clientAssets.ts</a></td>
	<td data-value="37.73" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 37%"></div><div class="cover-empty" style="width: 63%"></div></div>
	</td>
	<td data-value="37.73" class="pct low">37.73%</td>
	<td data-value="159" class="abs low">60/159</td>
	<td data-value="31.57" class="pct low">31.57%</td>
	<td data-value="19" class="abs low">6/19</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="37.73" class="pct low">37.73%</td>
	<td data-value="159" class="abs low">60/159</td>
	</tr>

<tr>
	<td class="file high" data-value="clients.ts"><a href="clients.ts.html">clients.ts</a></td>
	<td data-value="83.13" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 83%"></div><div class="cover-empty" style="width: 17%"></div></div>
	</td>
	<td data-value="83.13" class="pct high">83.13%</td>
	<td data-value="83" class="abs high">69/83</td>
	<td data-value="26.66" class="pct low">26.66%</td>
	<td data-value="15" class="abs low">4/15</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="83.13" class="pct high">83.13%</td>
	<td data-value="83" class="abs high">69/83</td>
	</tr>

<tr>
	<td class="file low" data-value="collaborators.ts"><a href="collaborators.ts.html">collaborators.ts</a></td>
	<td data-value="26.66" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 26%"></div><div class="cover-empty" style="width: 74%"></div></div>
	</td>
	<td data-value="26.66" class="pct low">26.66%</td>
	<td data-value="60" class="abs low">16/60</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="26.66" class="pct low">26.66%</td>
	<td data-value="60" class="abs low">16/60</td>
	</tr>

<tr>
	<td class="file medium" data-value="comments.ts"><a href="comments.ts.html">comments.ts</a></td>
	<td data-value="54.08" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 54%"></div><div class="cover-empty" style="width: 46%"></div></div>
	</td>
	<td data-value="54.08" class="pct medium">54.08%</td>
	<td data-value="98" class="abs medium">53/98</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="6" class="abs low">2/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="54.08" class="pct medium">54.08%</td>
	<td data-value="98" class="abs medium">53/98</td>
	</tr>

<tr>
	<td class="file medium" data-value="connections.ts"><a href="connections.ts.html">connections.ts</a></td>
	<td data-value="70.21" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 70%"></div><div class="cover-empty" style="width: 30%"></div></div>
	</td>
	<td data-value="70.21" class="pct medium">70.21%</td>
	<td data-value="47" class="abs medium">33/47</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="5" class="abs low">2/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="70.21" class="pct medium">70.21%</td>
	<td data-value="47" class="abs medium">33/47</td>
	</tr>

<tr>
	<td class="file medium" data-value="customTemplates.ts"><a href="customTemplates.ts.html">customTemplates.ts</a></td>
	<td data-value="65.21" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 65%"></div><div class="cover-empty" style="width: 35%"></div></div>
	</td>
	<td data-value="65.21" class="pct medium">65.21%</td>
	<td data-value="46" class="abs medium">30/46</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="5" class="abs low">2/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="65.21" class="pct medium">65.21%</td>
	<td data-value="46" class="abs medium">30/46</td>
	</tr>

<tr>
	<td class="file low" data-value="documentVersions.ts"><a href="documentVersions.ts.html">documentVersions.ts</a></td>
	<td data-value="30.3" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 30%"></div><div class="cover-empty" style="width: 70%"></div></div>
	</td>
	<td data-value="30.3" class="pct low">30.3%</td>
	<td data-value="33" class="abs low">10/33</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="30.3" class="pct low">30.3%</td>
	<td data-value="33" class="abs low">10/33</td>
	</tr>

<tr>
	<td class="file high" data-value="documents.ts"><a href="documents.ts.html">documents.ts</a></td>
	<td data-value="94.64" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 94%"></div><div class="cover-empty" style="width: 6%"></div></div>
	</td>
	<td data-value="94.64" class="pct high">94.64%</td>
	<td data-value="112" class="abs high">106/112</td>
	<td data-value="42.85" class="pct low">42.85%</td>
	<td data-value="21" class="abs low">9/21</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="94.64" class="pct high">94.64%</td>
	<td data-value="112" class="abs high">106/112</td>
	</tr>

<tr>
	<td class="file low" data-value="flowRuns.ts"><a href="flowRuns.ts.html">flowRuns.ts</a></td>
	<td data-value="29.57" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 29%"></div><div class="cover-empty" style="width: 71%"></div></div>
	</td>
	<td data-value="29.57" class="pct low">29.57%</td>
	<td data-value="71" class="abs low">21/71</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="29.57" class="pct low">29.57%</td>
	<td data-value="71" class="abs low">21/71</td>
	</tr>

<tr>
	<td class="file high" data-value="flows.ts"><a href="flows.ts.html">flows.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="75" class="abs high">75/75</td>
	<td data-value="80.95" class="pct high">80.95%</td>
	<td data-value="21" class="abs high">17/21</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="75" class="abs high">75/75</td>
	</tr>

<tr>
	<td class="file high" data-value="folders.ts"><a href="folders.ts.html">folders.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="44" class="abs high">44/44</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="10" class="abs low">4/10</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="44" class="abs high">44/44</td>
	</tr>

<tr>
	<td class="file low" data-value="integrations.ts"><a href="integrations.ts.html">integrations.ts</a></td>
	<td data-value="28.35" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 28%"></div><div class="cover-empty" style="width: 72%"></div></div>
	</td>
	<td data-value="28.35" class="pct low">28.35%</td>
	<td data-value="67" class="abs low">19/67</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="28.35" class="pct low">28.35%</td>
	<td data-value="67" class="abs low">19/67</td>
	</tr>

<tr>
	<td class="file low" data-value="keydates.ts"><a href="keydates.ts.html">keydates.ts</a></td>
	<td data-value="33.33" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 33%"></div><div class="cover-empty" style="width: 67%"></div></div>
	</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="48" class="abs low">16/48</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="48" class="abs low">16/48</td>
	</tr>

<tr>
	<td class="file low" data-value="notifications.ts"><a href="notifications.ts.html">notifications.ts</a></td>
	<td data-value="30.18" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 30%"></div><div class="cover-empty" style="width: 70%"></div></div>
	</td>
	<td data-value="30.18" class="pct low">30.18%</td>
	<td data-value="53" class="abs low">16/53</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="30.18" class="pct low">30.18%</td>
	<td data-value="53" class="abs low">16/53</td>
	</tr>

<tr>
	<td class="file low" data-value="obligations.ts"><a href="obligations.ts.html">obligations.ts</a></td>
	<td data-value="35.29" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 35%"></div><div class="cover-empty" style="width: 65%"></div></div>
	</td>
	<td data-value="35.29" class="pct low">35.29%</td>
	<td data-value="68" class="abs low">24/68</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="35.29" class="pct low">35.29%</td>
	<td data-value="68" class="abs low">24/68</td>
	</tr>

<tr>
	<td class="file low" data-value="pricing.ts"><a href="pricing.ts.html">pricing.ts</a></td>
	<td data-value="39.72" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 39%"></div><div class="cover-empty" style="width: 61%"></div></div>
	</td>
	<td data-value="39.72" class="pct low">39.72%</td>
	<td data-value="73" class="abs low">29/73</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="39.72" class="pct low">39.72%</td>
	<td data-value="73" class="abs low">29/73</td>
	</tr>

<tr>
	<td class="file high" data-value="profiles.ts"><a href="profiles.ts.html">profiles.ts</a></td>
	<td data-value="88.52" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 88%"></div><div class="cover-empty" style="width: 12%"></div></div>
	</td>
	<td data-value="88.52" class="pct high">88.52%</td>
	<td data-value="61" class="abs high">54/61</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="5" class="abs medium">3/5</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="88.52" class="pct high">88.52%</td>
	<td data-value="61" class="abs high">54/61</td>
	</tr>

<tr>
	<td class="file high" data-value="quota.ts"><a href="quota.ts.html">quota.ts</a></td>
	<td data-value="92.85" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 92%"></div><div class="cover-empty" style="width: 8%"></div></div>
	</td>
	<td data-value="92.85" class="pct high">92.85%</td>
	<td data-value="28" class="abs high">26/28</td>
	<td data-value="28.57" class="pct low">28.57%</td>
	<td data-value="7" class="abs low">2/7</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="92.85" class="pct high">92.85%</td>
	<td data-value="28" class="abs high">26/28</td>
	</tr>

<tr>
	<td class="file high" data-value="signatures.ts"><a href="signatures.ts.html">signatures.ts</a></td>
	<td data-value="90" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 90%"></div><div class="cover-empty" style="width: 10%"></div></div>
	</td>
	<td data-value="90" class="pct high">90%</td>
	<td data-value="60" class="abs high">54/60</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="10" class="abs low">4/10</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="90" class="pct high">90%</td>
	<td data-value="60" class="abs high">54/60</td>
	</tr>

<tr>
	<td class="file low" data-value="stripeConfig.ts"><a href="stripeConfig.ts.html">stripeConfig.ts</a></td>
	<td data-value="45.94" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 45%"></div><div class="cover-empty" style="width: 55%"></div></div>
	</td>
	<td data-value="45.94" class="pct low">45.94%</td>
	<td data-value="37" class="abs low">17/37</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="45.94" class="pct low">45.94%</td>
	<td data-value="37" class="abs low">17/37</td>
	</tr>

<tr>
	<td class="file high" data-value="teams.ts"><a href="teams.ts.html">teams.ts</a></td>
	<td data-value="86.17" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 86%"></div><div class="cover-empty" style="width: 14%"></div></div>
	</td>
	<td data-value="86.17" class="pct high">86.17%</td>
	<td data-value="188" class="abs high">162/188</td>
	<td data-value="50.76" class="pct medium">50.76%</td>
	<td data-value="65" class="abs medium">33/65</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="86.17" class="pct high">86.17%</td>
	<td data-value="188" class="abs high">162/188</td>
	</tr>

<tr>
	<td class="file high" data-value="templates.ts"><a href="templates.ts.html">templates.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="48" class="abs high">48/48</td>
	<td data-value="73.33" class="pct medium">73.33%</td>
	<td data-value="15" class="abs medium">11/15</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="48" class="abs high">48/48</td>
	</tr>

<tr>
	<td class="file high" data-value="workflows.ts"><a href="workflows.ts.html">workflows.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="185" class="abs high">185/185</td>
	<td data-value="64.61" class="pct medium">64.61%</td>
	<td data-value="65" class="abs medium">42/65</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="185" class="abs high">185/185</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    