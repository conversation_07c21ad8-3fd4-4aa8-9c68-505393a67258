import { describe, it, expect, beforeAll, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

vi.mock('../server/supabaseClient', () => {
  const getUserClient = (_token: string) => ({
    auth: { getUser: async () => ({ data: { user: { id: 'test-user' } }, error: null }) },
    from: (table: string) => {
      if (table === 'profiles') {
        return {
          select: () => ({
            eq: () => ({
              single: async () => ({ data: { plan_name: 'Registered User' }, error: null })
            })
          })
        };
      }
      if (table === 'usage_events') {
        return {
          select: () => ({
            gte: () => ({ count: 0, error: null })
          })
        };
      }
      if (table === 'documents') {
        return {
          select: () => ({
            eq: () => ({
              single: async () => ({
                data: { id: 'doc-1', name: 'Existing', content: '<p>content</p>', status: 'draft', value: null, client_id: null },
                error: null
              })
            })
          }),
          insert: (row: Record<string, unknown>) => ({
            select: () => ({
              single: async () => ({
                data: { id: 'doc-1', ...row, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
                error: null
              })
            })
          }),
          update: (patch: Record<string, unknown>) => ({
            eq: (_col: string, id: string) => ({
              select: () => ({
                single: async () => ({ data: { id, ...patch }, error: null })
              })
            })
          }),
          delete: () => ({
            eq: async () => ({ error: null })
          })
        };
      }
      return {};
    }
  });
  return { getUserClient };
});

beforeAll(() => { process.env.TEST_BYPASS_AUTH = '1'; });

describe('Documents CRUD routes', () => {

  it('POST /api/documents creates a document', async () => {
    const res = await request(app)
      .post('/api/documents')
      .send({ name: 'New Doc', content: '<p>content</p>', status: 'draft', clientId: '11111111-1111-1111-1111-111111111111', folderId: '22222222-2222-2222-2222-222222222222' })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(201);
    expect(res.body).toHaveProperty('document');
    expect(res.body.document).toHaveProperty('name', 'New Doc');
  });

  it('PUT /api/documents/:id updates fields', async () => {
    const res = await request(app)
      .put('/api/documents/doc-1')
      .send({ clientId: '11111111-1111-1111-1111-111111111111', name: 'Updated' })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(200);
    expect(res.body.document).toHaveProperty('client_id', '11111111-1111-1111-1111-111111111111');
  });

  it('DELETE /api/documents/:id removes document', async () => {
    const res = await request(app).delete('/api/documents/doc-1');
    expect(res.status).toBe(204);
  });
});

