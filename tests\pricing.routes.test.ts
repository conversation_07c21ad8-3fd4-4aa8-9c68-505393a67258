import { describe, it, expect, beforeAll, beforeEach, vi } from 'vitest';
import request from 'supertest';
import { DEFAULT_SUPABASE_ERROR_MESSAGE } from '../server/lib/httpErrors';

const { fetchPublicPlans, fetchPrivatePlans } = vi.hoisted(() => {
  const defaultResponse = { data: [{ id: 'p1', name: 'Basic' }], error: null };
  return {
    fetchPublicPlans: vi.fn().mockResolvedValue(defaultResponse),
    fetchPrivatePlans: vi.fn().mockResolvedValue(defaultResponse),
  };
});

vi.mock('../server/supabaseClient', () => {
  const supabaseAdmin = {
    from: (_table: string) => ({
      select: (_: string) => ({
        order: (_column: string, _options?: { ascending?: boolean }) => ({
          order: fetchPublicPlans,
        }),
      }),
    }),
  };
  const getUserClient = (_token: string) => ({
    from: (_table: string) => ({
      select: (_: string) => ({
        order: (_column: string, _options?: { ascending?: boolean }) => ({
          order: fetchPrivatePlans,
        }),
      }),
    }),
  });
  return { supabaseAdmin, getUserClient };
});

import { app } from '../server/app';

beforeAll(() => { process.env.TEST_BYPASS_AUTH = '1'; });

beforeEach(() => {
  fetchPublicPlans.mockReset();
  fetchPrivatePlans.mockReset();
  fetchPublicPlans.mockResolvedValue({ data: [{ id: 'p1', name: 'Basic' }], error: null });
  fetchPrivatePlans.mockResolvedValue({ data: [{ id: 'p1', name: 'Basic' }], error: null });
});

describe('Pricing routes', () => {
  it('GET /api/pricing-plans/public returns public plans', async () => {
    const res = await request(app).get('/api/pricing-plans/public');
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('pricingPlans');
  });

  it('GET /api/pricing-plans/public returns generic message for unexpected Supabase error', async () => {
    fetchPublicPlans.mockResolvedValueOnce({ data: null, error: { message: 'db down', code: '99999' } as any });

    const res = await request(app).get('/api/pricing-plans/public');

    expect(res.status).toBe(500);
    expect(res.body.error).toBe(DEFAULT_SUPABASE_ERROR_MESSAGE);
  });
});
