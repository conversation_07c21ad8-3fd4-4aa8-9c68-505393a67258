import { Router } from 'express';
import { z } from 'zod';
import <PERSON><PERSON> from 'stripe';
import { requireAuth, AuthRequest } from '../middleware/auth';
import { requireAdmin } from '../middleware/admin';
import { logger } from '../lib/logger';

const router = Router();

const refundSchema = z.object({
  email: z.string().email(),
});

router.post('/', requireAuth, requireAdmin, async (req: AuthRequest, res) => {
  const parsed = refundSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}

  if (!process.env.STRIPE_SECRET_KEY) {
    logger.error('Stripe secret key not configured');
    return res.status(500).json({ error: 'Payment provider not configured' });
  }

  try {
    const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, { apiVersion: '2024-06-20' });
    const customers = await stripe.customers.list({ email: parsed.data.email, limit: 1 });
    if (customers.data.length === 0) {
      logger.error({ email: parsed.data.email }, 'Customer not found for refund');
      return res.status(404).json({ error: 'Customer not found' });
    }

    const customerId = customers.data[0].id;
    const payments = await stripe.paymentIntents.list({ customer: customerId, limit: 1 });
    if (payments.data.length === 0) {
      logger.error({ email: parsed.data.email }, 'No payments found for refund');
      return res.status(404).json({ error: 'No payments found for customer' });
    }

    const paymentIntentId = payments.data[0].id;
    const refund = await stripe.refunds.create({ payment_intent: paymentIntentId });

    logger.info({ refundId: refund.id, email: parsed.data.email }, 'Refund successful');
    res.json({ success: true, refundId: refund.id });
  } catch (err) {
    const message = err instanceof Error ? err.message : 'Unknown error';
    logger.error({ err, email: parsed.data.email }, 'Refund failed');
    res.status(500).json({ error: message });
  }
});

export default router;
