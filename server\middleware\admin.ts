import type { Response, NextFunction } from 'express';
import { getUserClient } from '../supabaseClient';
import { AuthRequest, getAccessToken } from './auth';

export async function requireAdmin(req: AuthRequest, res: Response, next: NextFunction) {
  try {
    if (
      process.env.TEST_BYPASS_AUTH === '1' ||
      !process.env.SUPABASE_URL ||
      !process.env.SUPABASE_SERVICE_ROLE_KEY
    ) {
      // In test mode, check the mock flag
      const mockIsAdmin = (global as typeof global & { mockIsAdmin?: boolean }).mockIsAdmin;
      if (!mockIsAdmin) {
        return res.status(403).json({ error: 'Forbidden: admin only' });
      }
      return next();
    }
    
    const userId = req.user?.id;
    if (!userId) {return res.status(401).json({ error: 'Unauthorized' });}
    
    const supa = getUserClient(getAccessToken(req));
    const { data, error } = await supa.from('profiles').select('is_admin').eq('id', userId).single();
    if (error) {return res.status(500).json({ error: error.message });}
    if (!data?.is_admin) {return res.status(403).json({ error: 'Forbidden: admin only' });}
    next();
  } catch (err) {
    next(err);
  }
}

