import { PricingPlan, Testimonial } from '../types';
import { apiFetch } from './api';

type ApiPricingPlan = {
  id?: string;
  name?: string;
  price?: string;
  price_detail?: string;
  priceDetail?: string;
  features?: unknown;
  cta?: string;
  is_featured?: boolean;
  isFeatured?: boolean;
  sort_order?: number;
  sortOrder?: number;
};

type CmsPricingResponse = { pricingPlans?: ApiPricingPlan[] } | ApiPricingPlan[];

type CmsTestimonial = {
  id?: string;
  quote?: string;
  name?: string;
  title?: string;
  avatar?: string;
  audiences?: unknown;
  audience?: string;
  highlight?: boolean;
};

type CmsTestimonialsResponse = { testimonials?: CmsTestimonial[] } | CmsTestimonial[];

const CMS_BASE_PATH = '/cms';
const CMS_PRICING_PATH = `${CMS_BASE_PATH}/pricing-plans.json`;
const CMS_TESTIMONIALS_PATH = `${CMS_BASE_PATH}/testimonials.json`;

const slugify = (value: string) => value.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-+|-+$/g, '');

const normalizePricingPlan = (row: ApiPricingPlan, index: number): PricingPlan | null => {
  const name = typeof row.name === 'string' ? row.name.trim() : '';
  const price = typeof row.price === 'string' ? row.price.trim() : '';
  if (!name || !price) {return null;}
  const priceDetailRaw = typeof row.priceDetail === 'string' ? row.priceDetail : row.price_detail;
  const priceDetail = typeof priceDetailRaw === 'string' && priceDetailRaw.trim() ? priceDetailRaw.trim() : undefined;
  const features = Array.isArray(row.features)
    ? row.features.map((feature) => (typeof feature === 'string' ? feature.trim() : '')).filter((feature) => Boolean(feature))
    : [];
  const cta = typeof row.cta === 'string' && row.cta.trim() ? row.cta.trim() : 'Learn More';
  const isFeatured = typeof row.isFeatured === 'boolean' ? row.isFeatured : Boolean(row.is_featured);
  const sortOrder = typeof row.sortOrder === 'number' ? row.sortOrder : typeof row.sort_order === 'number' ? row.sort_order : undefined;
  const idSource = typeof row.id === 'string' && row.id.trim() ? row.id.trim() : slugify(name) || `plan-${index}`;
  return {
    id: idSource,
    name,
    price,
    priceDetail,
    features,
    cta,
    isFeatured,
    sortOrder,
  };
};

const normalizeTestimonial = (row: CmsTestimonial, index: number): Testimonial | null => {
  const quote = typeof row.quote === 'string' ? row.quote.trim() : '';
  const name = typeof row.name === 'string' ? row.name.trim() : '';
  if (!quote || !name) {return null;}
  const title = typeof row.title === 'string' && row.title.trim() ? row.title.trim() : undefined;
  const avatar = typeof row.avatar === 'string' && row.avatar.trim() ? row.avatar.trim() : undefined;
  const audiencesRaw = Array.isArray(row.audiences)
    ? row.audiences
    : typeof row.audience === 'string' && row.audience.trim()
      ? [row.audience]
      : [];
  const audiences = (Array.isArray(audiencesRaw) ? audiencesRaw : [])
    .map((audience) => (typeof audience === 'string' ? audience.trim() : ''))
    .filter((audience) => Boolean(audience));
  const idSource = typeof row.id === 'string' && row.id.trim()
    ? row.id.trim()
    : slugify(`${name}-${title || index}`) || `testimonial-${index}`;
  return {
    id: idSource,
    quote,
    name,
    title,
    avatar,
    audiences: audiences.length ? audiences : undefined,
    highlight: Boolean(row.highlight),
  };
};

const sortPricingPlans = (plans: PricingPlan[]): PricingPlan[] => {
  return [...plans].sort((a, b) => {
    const orderA = typeof a.sortOrder === 'number' ? a.sortOrder : Number.MAX_SAFE_INTEGER;
    const orderB = typeof b.sortOrder === 'number' ? b.sortOrder : Number.MAX_SAFE_INTEGER;
    if (orderA !== orderB) {return orderA - orderB;}
    return a.name.localeCompare(b.name);
  });
};

const loadPricingPlansFromApi = async (): Promise<PricingPlan[]> => {
  try {
    const resp = await apiFetch<{ pricingPlans?: ApiPricingPlan[] }>(`/api/pricing-plans/public`);
    const rows = Array.isArray(resp?.pricingPlans) ? resp.pricingPlans : [];
    return rows
      .map((row, index) => normalizePricingPlan(row, index))
      .filter((plan): plan is PricingPlan => Boolean(plan));
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn('Failed to fetch pricing plans from API, falling back to CMS JSON.', error);
    return [];
  }
};

const loadPricingPlansFromCms = async (): Promise<PricingPlan[]> => {
  try {
    const response = await fetch(CMS_PRICING_PATH);
    if (!response.ok) {
      throw new Error(`Request failed with status ${response.status}`);
    }
    const data = await response.json() as CmsPricingResponse;
    const rows = Array.isArray(data) ? data : Array.isArray(data?.pricingPlans) ? data.pricingPlans : [];
    return rows
      .map((row, index) => normalizePricingPlan(row, index))
      .filter((plan): plan is PricingPlan => Boolean(plan));
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn('Failed to fetch pricing plans from CMS JSON.', error);
    return [];
  }
};

export const fetchPublicPricingPlans = async (): Promise<PricingPlan[]> => {
  const plansFromApi = await loadPricingPlansFromApi();
  if (plansFromApi.length) {return sortPricingPlans(plansFromApi);}
  const fallbackPlans = await loadPricingPlansFromCms();
  return sortPricingPlans(fallbackPlans);
};

export const fetchTestimonials = async (): Promise<Testimonial[]> => {
  try {
    const response = await fetch(CMS_TESTIMONIALS_PATH);
    if (!response.ok) {
      throw new Error(`Request failed with status ${response.status}`);
    }
    const data = await response.json() as CmsTestimonialsResponse;
    const rows = Array.isArray(data) ? data : Array.isArray(data?.testimonials) ? data.testimonials : [];
    return rows
      .map((row, index) => normalizeTestimonial(row, index))
      .filter((testimonial): testimonial is Testimonial => Boolean(testimonial));
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn('Failed to fetch testimonials from CMS JSON.', error);
    return [];
  }
};
