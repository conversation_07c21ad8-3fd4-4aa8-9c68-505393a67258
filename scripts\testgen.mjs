#!/usr/bin/env node
import fs from 'node:fs';
import path from 'node:path';

const [, , nameArg] = process.argv;
if (!nameArg) {
  console.error('Usage: npm run test:scaffold -- <feature-name>');
  process.exit(1);
}

const fileName = nameArg.replace(/[^a-z0-9-_]/gi, '-').toLowerCase();
const target = path.join(process.cwd(), 'tests', `${fileName}.test.ts`);

if (fs.existsSync(target)) {
  console.error(`Test file already exists: ${target}`);
  process.exit(1);
}

const template = `import { describe, it, expect, beforeAll } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

// TODO: Add vi.mock for '../server/supabaseClient' or other services as needed

beforeAll(() => { process.env.TEST_BYPASS_AUTH = '1'; });

describe('${fileName} feature', () => {
  it('placeholder', async () => {
    const res = await request(app).get('/health');
    expect(res.status).toBe(200);
  });
});
`;

fs.mkdirSync(path.dirname(target), { recursive: true });
fs.writeFileSync(target, template, 'utf8');
console.log(`Created ${target}`);

