import { Connector } from '../../types';
import { LegalIcon } from '../Icons';
import { DocumentStatusEvent, DocumentStatusBroadcast, eventBus } from '../../lib/eventBus';
import { API_BASE } from '../../lib/api';

// This is a special "internal" connector for LexiGen itself.
// It allows users to use LexiGen events as triggers for flows.
export const lexigenConnector: Connector = {
  id: 'lexigen',
  name: 'LexiGen',
  description: 'Trigger flows based on events within your LexiGen workspace.',
  icon: LegalIcon,
  auth: {
    type: 'apiKey', // Internal auth, no user input needed
  },
  triggers: [
    {
      key: 'documentStatusChanged',
      name: 'Document Status Changed',
      description: 'Triggers when a document\'s status is updated.',
      type: 'webhook', // Events delivered via SSE stream or internal bus
      operation: {
        perform: async (emit: (data: DocumentStatusBroadcast) => void) => {
          // In the browser, subscribe via SSE stream
          if (typeof window !== 'undefined' && typeof EventSource !== 'undefined') {
            let streamUrl = '/api/events';
            try {
              streamUrl = new URL('/api/events', API_BASE).toString();
            } catch {
              // Fallback to relative URL if API_BASE is not a valid absolute URL
            }

            const es = new EventSource(streamUrl, { withCredentials: true });
            const handler = (e: MessageEvent) => {
              try {
                const payload = JSON.parse(e.data) as DocumentStatusBroadcast;
                emit(payload);
              } catch {
                // Ignore parse errors
              }
            };
            es.addEventListener('documentStatusChanged', handler);
            return () => {
              es.removeEventListener('documentStatusChanged', handler);
              es.close();
            };
          }

          // On the server or in tests, listen to the internal event bus
          const handler = (payload: DocumentStatusEvent) => {
            const { audienceUserId: _audience, ...broadcastPayload } = payload;
            emit(broadcastPayload);
          };
          eventBus.on('documentStatusChanged', handler);
          return () => {
            eventBus.off('documentStatusChanged', handler);
          };
        },
        outputFields: [
          { key: 'documentId', label: 'Document ID', type: 'string' },
          { key: 'documentName', label: 'Document Name', type: 'string' },
          { key: 'newStatus', label: 'New Status', type: 'string' },
          { key: 'previousStatus', label: 'Previous Status', type: 'string' },
          { key: 'documentValue', label: 'Document Value', type: 'number' },
          { key: 'clientId', label: 'Client ID', type: 'string' },
        ],
      },
    },
  ],
};
