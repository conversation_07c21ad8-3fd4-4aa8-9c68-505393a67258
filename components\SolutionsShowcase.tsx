import React from 'react';
import { LegalIcon, BriefcaseIcon, ShoppingCartIcon, CheckIcon } from './Icons';
import ClauseLibraryScreenshot from './screenshots/ClauseLibraryScreenshot';
import TemplatesScreenshot from './screenshots/TemplatesScreenshot';
import LifecycleScreenshot from './screenshots/LifecycleScreenshot';
import { cn } from '../lib/utils';
import { Testimonial } from '../types';

const getInitials = (name: string) => {
  const parts = name.trim().split(' ').filter(Boolean);
  if (!parts.length) {return '?';}
  const [first, second] = parts;
  return (first[0] + (second ? second[0] : '')).toUpperCase();
};

interface PersonaFeatureProps {
  icon: React.ElementType;
  title: string;
  description: string;
  screenshot: React.ReactNode;
  testimonial?: Testimonial;
  reverse?: boolean;
}

const PersonaFeature: React.FC<PersonaFeatureProps> = ({ icon: Icon, title, description, screenshot, testimonial, reverse = false }) => {
  return (
    <div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center">
      <div className={cn('order-2', reverse ? 'lg:order-2' : 'lg:order-1')}>
        <div className="inline-flex items-center gap-3 bg-brand-50 dark:bg-brand-900/30 px-4 py-2 rounded-full border border-brand-200 dark:border-brand-800">
          <Icon className="w-6 h-6 text-brand-600 dark:text-brand-400" />
          <h3 className="text-2xl font-bold text-zinc-900 dark:text-white">{title}</h3>
        </div>
        <p className="mt-4 text-lg text-zinc-600 dark:text-zinc-400">{description}</p>
        <ul className="mt-6 space-y-3">
          <li className="flex items-center gap-3"><CheckIcon className="w-5 h-5 text-brand-500" /><span className="text-zinc-700 dark:text-zinc-300">Centralized Clause Library</span></li>
          <li className="flex items-center gap-3"><CheckIcon className="w-5 h-5 text-brand-500" /><span className="text-zinc-700 dark:text-zinc-300">AI-Powered Risk Analysis</span></li>
          <li className="flex items-center gap-3"><CheckIcon className="w-5 h-5 text-brand-500" /><span className="text-zinc-700 dark:text-zinc-300">Customizable Approval Workflows</span></li>
        </ul>
        {testimonial && (
          <blockquote className="mt-8 p-6 bg-zinc-50 dark:bg-zinc-900 rounded-xl border border-zinc-200 dark:border-zinc-800">
            <p className="text-zinc-700 dark:text-zinc-300 italic">“{testimonial.quote}”</p>
            <footer className="mt-4 flex items-center gap-3">
              {testimonial.avatar ? (
                <img className="w-12 h-12 rounded-full object-cover" src={testimonial.avatar} alt={testimonial.name} />
              ) : (
                <div className="w-12 h-12 rounded-full bg-brand-100 text-brand-700 flex items-center justify-center font-semibold">
                  {getInitials(testimonial.name)}
                </div>
              )}
              <div>
                <div className="font-semibold text-zinc-900 dark:text-white">{testimonial.name}</div>
                {testimonial.title && <div className="text-zinc-600 dark:text-zinc-400">{testimonial.title}</div>}
              </div>
            </footer>
          </blockquote>
        )}
      </div>
      <div className={cn('order-1', reverse ? 'lg:order-1' : 'lg:order-2')}>
        <div className="p-4 bg-zinc-100 dark:bg-zinc-900 rounded-2xl shadow-xl">{screenshot}</div>
      </div>
    </div>
  );
};

interface SolutionsShowcaseProps {
  testimonials: Testimonial[];
}

const SolutionsShowcase: React.FC<SolutionsShowcaseProps> = ({ testimonials }) => {
  const findTestimonial = (audience: string) => testimonials.find((testimonial) => testimonial.audiences?.includes(audience));
  const legalTestimonial = findTestimonial('legal');
  const salesTestimonial = findTestimonial('sales');
  const procurementTestimonial = findTestimonial('procurement');

  return (
    <section id="solutions" className="py-20 sm:py-32 bg-white dark:bg-zinc-950">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-zinc-900 dark:text-white sm:text-4xl lg:text-5xl">
            A Solution for Every Team
          </h2>
          <p className="mt-4 text-lg text-zinc-600 dark:text-zinc-400 max-w-3xl mx-auto">
            LexiGen empowers Legal, Sales, and Procurement teams to manage the entire contract lifecycle with speed and confidence.
          </p>
        </div>

        <div className="mt-20 space-y-24">
          <div id="solutions-legal">
            <PersonaFeature
              icon={LegalIcon}
              title="For Legal Teams"
              description="Mitigate risk and maintain control with a single source of truth for all contracts. Standardize language with a pre-approved clause library and accelerate reviews with powerful AI analysis."
              screenshot={<ClauseLibraryScreenshot />}
              testimonial={legalTestimonial}
            />
          </div>
          <div id="solutions-sales">
            <PersonaFeature
              icon={BriefcaseIcon}
              title="For Sales Teams"
              description="Close deals faster by eliminating contract bottlenecks. Empower your sales reps to self-serve approved templates, reducing legal's workload and accelerating the sales cycle from proposal to signature."
              screenshot={<TemplatesScreenshot />}
              testimonial={salesTestimonial}
              reverse
            />
          </div>
          <div id="solutions-procurement">
            <PersonaFeature
              icon={ShoppingCartIcon}
              title="For Procurement"
              description="Streamline vendor onboarding and manage obligations effectively. Gain complete visibility into your supplier agreements, track key dates with an AI-powered lifecycle dashboard, and never miss a renewal."
              screenshot={<LifecycleScreenshot />}
              testimonial={procurementTestimonial}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default SolutionsShowcase;
