import { describe, it, expect, beforeAll, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

vi.mock('../server/supabaseClient', () => {
  const getUserClient = (_token: string) => ({
    from: (_table: string) => ({
      select: () => ({
        order: () => ({ data: [{ id: 'c1', title: 'Clause 1', content: 'Content', tags: [], created_at: new Date().toISOString() }], error: null })
      }),
      insert: (row: { title: string; content: string; tags: string[] }) => ({
        select: () => ({ single: () => Promise.resolve({ data: { id: 'c2', ...row, created_at: new Date().toISOString() }, error: null }) })
      })
    })
  });
  return { getUserClient };
});

beforeAll(() => { process.env.TEST_BYPASS_AUTH = '1'; });

describe('Clauses routes', () => {
  it('GET /api/clauses returns clauses', async () => {
    const res = await request(app).get('/api/clauses');
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('clauses');
    expect(Array.isArray(res.body.clauses)).toBe(true);
  });

  it('POST /api/clauses creates clause', async () => {
    const res = await request(app)
      .post('/api/clauses')
      .send({
        title: 'Test Clause',
        content: 'Some content',
        tags: ['tag1']
      })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(201);
    expect(res.body).toHaveProperty('clause');
  });
});
