
import React from 'react';
import { Button } from './Button';
import { 
    BoldIcon, ItalicIcon, UnderlineIcon,
    H1Icon, H2Icon, ListIcon,
    UndoIcon, RedoIcon
} from '../Icons';

interface SimpleEditorToolbarProps {
  editorRef: React.RefObject<HTMLDivElement>;
}

const SimpleEditorToolbar: React.FC<SimpleEditorToolbarProps> = ({ editorRef }) => {
  const applyFormat = (command: string, value?: string) => {
    if (editorRef.current) {
      editorRef.current.focus();
      document.execCommand(command, false, value);
    }
  };

  const ToolbarButton: React.FC<{onClick: () => void; title: string; children: React.ReactNode}> = ({onClick, title, children}) => (
      <Button variant="ghost" size="icon" onClick={onClick} title={title} type="button" className="h-9 w-9 dark:text-zinc-300 dark:hover:bg-zinc-700">
          {children}
      </Button>
  );

  const Divider = () => <div className="w-px h-6 bg-zinc-200 dark:bg-zinc-700 mx-1"></div>;

  return (
    <div className="mb-2 bg-zinc-50 dark:bg-zinc-800 p-1 rounded-lg border border-zinc-200 dark:border-zinc-700 flex items-center flex-wrap gap-0.5">
      <ToolbarButton onClick={() => applyFormat('undo')} title="Undo"><UndoIcon className="w-5 h-5" /></ToolbarButton>
      <ToolbarButton onClick={() => applyFormat('redo')} title="Redo"><RedoIcon className="w-5 h-5" /></ToolbarButton>
      <Divider />
      <ToolbarButton onClick={() => applyFormat('bold')} title="Bold"><BoldIcon className="w-5 h-5" /></ToolbarButton>
      <ToolbarButton onClick={() => applyFormat('italic')} title="Italic"><ItalicIcon className="w-5 h-5" /></ToolbarButton>
      <ToolbarButton onClick={() => applyFormat('underline')} title="Underline"><UnderlineIcon className="w-5 h-5" /></ToolbarButton>
      <Divider />
      <ToolbarButton onClick={() => applyFormat('formatBlock', '<h1>')} title="Heading 1"><H1Icon className="w-5 h-5" /></ToolbarButton>
      <ToolbarButton onClick={() => applyFormat('formatBlock', '<h2>')} title="Heading 2"><H2Icon className="w-5 h-5" /></ToolbarButton>
      <ToolbarButton onClick={() => applyFormat('formatBlock', '<p>')} title="Paragraph">P</ToolbarButton>
      <ToolbarButton onClick={() => applyFormat('insertUnorderedList')} title="Bulleted List"><ListIcon className="w-5 h-5" /></ToolbarButton>
    </div>
  );
};

export default SimpleEditorToolbar;