import React, { useState, useMemo } from 'react';
import { User, Client } from '../types';
import { Button } from './ui/Button';
import { Card, CardContent, CardHeader } from './ui/Card';
import { PlusCircleIcon, SearchIcon, EditIcon, TrashIcon, UsersIcon } from './Icons';
import ErrorBoundary from './ui/ErrorBoundary';
import ClientEditorModal from './ClientEditorModal';
import ConfirmationModal from './admin/ConfirmationModal';

interface ClientsPageProps {
  user: User;
  onCreateClient: (clientData: Omit<Client, 'id' | 'createdAt'>) => void;
  onUpdateClient: (clientId: string, updates: Partial<Omit<Client, 'id' | 'createdAt'>>) => void;
  onDeleteClient: (clientId: string) => void;
  onViewClient?: (client: Client) => void;
}

// Row component for client list
const ClientRow: React.FC<{ 
  client: Client;
  onViewClient?: (client: Client) => void;
  handleEdit: (client: Client) => void;
  setClientToDelete: (client: Client) => void;
}> = ({ client, onViewClient, handleEdit, setClientToDelete }) => {
  // Check if client exists and has required properties
  if (!client || !client.id || !client.name) {
    return (
      <div className="border-b border-zinc-200 dark:border-zinc-800">
        <div className="flex items-center min-h-[60px] px-4 text-gray-500">
          Invalid client data
        </div>
      </div>
    );
  }
  
  return (
    <div className="border-b border-zinc-200 dark:border-zinc-800">
      <div 
        className={`flex items-center min-h-[60px] px-4 hover:bg-zinc-50 dark:hover:bg-zinc-900/50 ${
          onViewClient ? 'cursor-pointer' : ''
        }`}
        onClick={() => onViewClient && onViewClient(client)}
      >
        <div className="flex-1 font-medium">{client.name}</div>
        <div className="flex-1">{client.type}</div>
        <div className="flex-1">{client.contactPerson || 'N/A'}</div>
        <div className="flex-1">{client.email || 'N/A'}</div>
        <div className="flex-1 text-right">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={(e) => { e.stopPropagation(); handleEdit(client); }} 
            className="h-8 w-8"
          >
            <EditIcon className="w-4 h-4" />
          </Button>
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={(e) => { e.stopPropagation(); setClientToDelete(client); }} 
            className="h-8 w-8 text-red-600 hover:text-red-700"
          >
            <TrashIcon className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

const ClientsPage: React.FC<ClientsPageProps> = ({ user, onCreateClient, onUpdateClient, onDeleteClient, onViewClient }) => {
    const [isEditorOpen, setIsEditorOpen] = useState(false);
    const [clientToEdit, setClientToEdit] = useState<Client | null>(null);
    const [clientToDelete, setClientToDelete] = useState<Client | null>(null);
    const [searchTerm, setSearchTerm] = useState('');

    const filteredClients = useMemo(() => {
        // Ensure user and user.clients exist before processing
        if (!user || !user.clients) {
            return [];
        }
        const clients = user.clients || [];
        if (!searchTerm) {return clients;}
        return clients.filter(c =>
            c && c.name && (
                c.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                c.email?.toLowerCase().includes(searchTerm.toLowerCase())
            )
        );
    }, [user, user.clients, searchTerm]);

    const handleEdit = (client: Client) => {
        setClientToEdit(client);
        setIsEditorOpen(true);
    };

    const handleCreate = () => {
        setClientToEdit(null);
        setIsEditorOpen(true);
    };
    
    const handleSave = (data: Omit<Client, 'id' | 'createdAt'>) => {
        if (clientToEdit) {
            onUpdateClient(clientToEdit.id, data);
        } else {
            onCreateClient(data);
        }
        setIsEditorOpen(false);
    };

    const handleDeleteConfirm = () => {
        if (clientToDelete) {
            onDeleteClient(clientToDelete.id);
            setClientToDelete(null);
        }
    };

    return (
        <>
            <div className="p-4 sm:p-6 lg:p-8 space-y-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-zinc-900 dark:text-white">Clients</h1>
                        <p className="text-zinc-600 dark:text-zinc-400 mt-1">Manage your clients and their information.</p>
                    </div>
                    <Button onClick={handleCreate}>
                        <PlusCircleIcon className="w-5 h-5 mr-2" /> New Client
                    </Button>
                </div>

                <Card>
                    <CardHeader>
                        <div className="relative w-full sm:max-w-xs">
                            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"><SearchIcon className="h-5 w-5 text-zinc-400" /></div>
                            <input type="text" placeholder="Search clients..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 p-2" />
                        </div>
                    </CardHeader>
                    <CardContent>
                        {filteredClients && filteredClients.length > 0 ? (
                          <div className="border rounded-lg">
                            {/* Table Header */}
                            <div className="border-b border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900/50">
                              <div className="flex items-center min-h-[50px] px-4 font-medium text-sm">
                                <div className="flex-1">Name</div>
                                <div className="flex-1">Type</div>
                                <div className="flex-1">Contact Person</div>
                                <div className="flex-1">Email</div>
                                <div className="flex-1 text-right">Actions</div>
                              </div>
                            </div>
                            {/* Table Body */}
                            <div className="max-h-[400px] overflow-y-auto">
                              <ErrorBoundary fallback={
                                <div className="flex items-center justify-center h-full text-gray-500">
                                  Unable to load clients list
                                </div>
                              }>
                                {filteredClients.map((client) => (
                                  <ClientRow
                                    key={client.id}
                                    client={client}
                                    onViewClient={onViewClient}
                                    handleEdit={handleEdit}
                                    setClientToDelete={setClientToDelete}
                                  />
                                ))}
                              </ErrorBoundary>
                            </div>
                          </div>
                        ) : (
                          <div className="h-48 flex flex-col items-center justify-center text-center">
                            <UsersIcon className="mx-auto h-12 w-12 text-zinc-300 dark:text-zinc-700" />
                            <h3 className="mt-2 font-semibold">No clients found</h3>
                            <p className="text-sm text-zinc-500">Get started by creating a new client.</p>
                          </div>
                        )}
                    </CardContent>
                </Card>
            </div>
            <ClientEditorModal isOpen={isEditorOpen} onClose={() => setIsEditorOpen(false)} onSave={handleSave} existingClient={clientToEdit} />
            {clientToDelete && (
                <ConfirmationModal
                    isOpen={true}
                    onClose={() => setClientToDelete(null)}
                    onConfirm={handleDeleteConfirm}
                    title="Delete Client"
                    message={<>Are you sure you want to delete <strong>{clientToDelete.name}</strong>? This will also unassign them from any documents. This action cannot be undone.</>}
                    confirmText="Delete Client"
                />
            )}
        </>
    );
};

export default ClientsPage;
