
import React, { useState, useEffect } from 'react';
import { SsoConfig } from '../types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/Card';
import { Button } from './ui/Button';
import { cn } from '../lib/utils';

interface SsoSettingsProps {
  ssoConfig: SsoConfig | undefined;
  onUpdateSsoConfig: (config: SsoConfig) => void;
}

const SsoSettings: React.FC<SsoSettingsProps> = ({ ssoConfig, onUpdateSsoConfig }) => {
  const [config, setConfig] = useState<SsoConfig>({
    enabled: false,
    idpUrl: '',
    entityId: '',
    certificate: '',
  });
  const [isSaved, setIsSaved] = useState(false);

  useEffect(() => {
    if (ssoConfig) {
      setConfig(ssoConfig);
    }
  }, [ssoConfig]);
  
  const handleInputChange = (field: keyof Omit<SsoConfig, 'enabled'>, value: string) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  const handleToggle = (enabled: boolean) => {
    setConfig(prev => ({ ...prev, enabled }));
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdateSsoConfig(config);
    setIsSaved(true);
    setTimeout(() => setIsSaved(false), 2000);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Security (SSO)</CardTitle>
        <CardDescription>Configure Single Sign-On for your organization.</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4 max-w-lg">
           <div className="flex items-center justify-between p-3 bg-zinc-50 rounded-lg border dark:bg-zinc-800/50 dark:border-zinc-800">
                <div>
                    <label className="font-medium text-zinc-800 dark:text-zinc-200">Enable SSO</label>
                    <p className="text-sm text-zinc-500 dark:text-zinc-400">Allow users to sign in with your Identity Provider.</p>
                </div>
                <button
                    type="button"
                    role="switch"
                    aria-checked={config.enabled}
                    onClick={() => handleToggle(!config.enabled)}
                    className={cn('relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors', config.enabled ? 'bg-brand-600' : 'bg-zinc-200 dark:bg-zinc-700')}
                >
                    <span className={cn('pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition', config.enabled ? 'translate-x-5' : 'translate-x-0')}/>
                </button>
            </div>
          <div>
            <label htmlFor="idpUrl" className="block text-sm font-medium text-zinc-700 dark:text-zinc-300">Identity Provider URL (SSO URL)</label>
            <input
              type="text"
              id="idpUrl"
              value={config.idpUrl}
              onChange={(e) => handleInputChange('idpUrl', e.target.value)}
              className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2"
              placeholder="https://idp.example.com/sso"
              disabled={!config.enabled}
            />
          </div>
          <div>
            <label htmlFor="entityId" className="block text-sm font-medium text-zinc-700 dark:text-zinc-300">Identity Provider Issuer (Entity ID)</label>
            <input
              type="text"
              id="entityId"
              value={config.entityId}
              onChange={(e) => handleInputChange('entityId', e.target.value)}
              className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2"
              placeholder="urn:idp:example"
              disabled={!config.enabled}
            />
          </div>
          <div>
            <label htmlFor="certificate" className="block text-sm font-medium text-zinc-700 dark:text-zinc-300">X.509 Certificate</label>
            <textarea
              id="certificate"
              value={config.certificate}
              onChange={(e) => handleInputChange('certificate', e.target.value)}
              className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2 font-mono"
              rows={5}
              placeholder="-----BEGIN CERTIFICATE-----..."
              disabled={!config.enabled}
            />
          </div>
          <div className="flex items-center gap-4 pt-2">
            <Button type="submit">Save Changes</Button>
            {isSaved && <span className="text-sm font-medium text-green-600 dark:text-green-400">Configuration saved!</span>}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default SsoSettings;
