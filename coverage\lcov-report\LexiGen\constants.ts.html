
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/constants.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> / <a href="index.html">LexiGen</a> constants.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>581/581</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>581/581</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a>
<a name='L365'></a><a href='#L365'>365</a>
<a name='L366'></a><a href='#L366'>366</a>
<a name='L367'></a><a href='#L367'>367</a>
<a name='L368'></a><a href='#L368'>368</a>
<a name='L369'></a><a href='#L369'>369</a>
<a name='L370'></a><a href='#L370'>370</a>
<a name='L371'></a><a href='#L371'>371</a>
<a name='L372'></a><a href='#L372'>372</a>
<a name='L373'></a><a href='#L373'>373</a>
<a name='L374'></a><a href='#L374'>374</a>
<a name='L375'></a><a href='#L375'>375</a>
<a name='L376'></a><a href='#L376'>376</a>
<a name='L377'></a><a href='#L377'>377</a>
<a name='L378'></a><a href='#L378'>378</a>
<a name='L379'></a><a href='#L379'>379</a>
<a name='L380'></a><a href='#L380'>380</a>
<a name='L381'></a><a href='#L381'>381</a>
<a name='L382'></a><a href='#L382'>382</a>
<a name='L383'></a><a href='#L383'>383</a>
<a name='L384'></a><a href='#L384'>384</a>
<a name='L385'></a><a href='#L385'>385</a>
<a name='L386'></a><a href='#L386'>386</a>
<a name='L387'></a><a href='#L387'>387</a>
<a name='L388'></a><a href='#L388'>388</a>
<a name='L389'></a><a href='#L389'>389</a>
<a name='L390'></a><a href='#L390'>390</a>
<a name='L391'></a><a href='#L391'>391</a>
<a name='L392'></a><a href='#L392'>392</a>
<a name='L393'></a><a href='#L393'>393</a>
<a name='L394'></a><a href='#L394'>394</a>
<a name='L395'></a><a href='#L395'>395</a>
<a name='L396'></a><a href='#L396'>396</a>
<a name='L397'></a><a href='#L397'>397</a>
<a name='L398'></a><a href='#L398'>398</a>
<a name='L399'></a><a href='#L399'>399</a>
<a name='L400'></a><a href='#L400'>400</a>
<a name='L401'></a><a href='#L401'>401</a>
<a name='L402'></a><a href='#L402'>402</a>
<a name='L403'></a><a href='#L403'>403</a>
<a name='L404'></a><a href='#L404'>404</a>
<a name='L405'></a><a href='#L405'>405</a>
<a name='L406'></a><a href='#L406'>406</a>
<a name='L407'></a><a href='#L407'>407</a>
<a name='L408'></a><a href='#L408'>408</a>
<a name='L409'></a><a href='#L409'>409</a>
<a name='L410'></a><a href='#L410'>410</a>
<a name='L411'></a><a href='#L411'>411</a>
<a name='L412'></a><a href='#L412'>412</a>
<a name='L413'></a><a href='#L413'>413</a>
<a name='L414'></a><a href='#L414'>414</a>
<a name='L415'></a><a href='#L415'>415</a>
<a name='L416'></a><a href='#L416'>416</a>
<a name='L417'></a><a href='#L417'>417</a>
<a name='L418'></a><a href='#L418'>418</a>
<a name='L419'></a><a href='#L419'>419</a>
<a name='L420'></a><a href='#L420'>420</a>
<a name='L421'></a><a href='#L421'>421</a>
<a name='L422'></a><a href='#L422'>422</a>
<a name='L423'></a><a href='#L423'>423</a>
<a name='L424'></a><a href='#L424'>424</a>
<a name='L425'></a><a href='#L425'>425</a>
<a name='L426'></a><a href='#L426'>426</a>
<a name='L427'></a><a href='#L427'>427</a>
<a name='L428'></a><a href='#L428'>428</a>
<a name='L429'></a><a href='#L429'>429</a>
<a name='L430'></a><a href='#L430'>430</a>
<a name='L431'></a><a href='#L431'>431</a>
<a name='L432'></a><a href='#L432'>432</a>
<a name='L433'></a><a href='#L433'>433</a>
<a name='L434'></a><a href='#L434'>434</a>
<a name='L435'></a><a href='#L435'>435</a>
<a name='L436'></a><a href='#L436'>436</a>
<a name='L437'></a><a href='#L437'>437</a>
<a name='L438'></a><a href='#L438'>438</a>
<a name='L439'></a><a href='#L439'>439</a>
<a name='L440'></a><a href='#L440'>440</a>
<a name='L441'></a><a href='#L441'>441</a>
<a name='L442'></a><a href='#L442'>442</a>
<a name='L443'></a><a href='#L443'>443</a>
<a name='L444'></a><a href='#L444'>444</a>
<a name='L445'></a><a href='#L445'>445</a>
<a name='L446'></a><a href='#L446'>446</a>
<a name='L447'></a><a href='#L447'>447</a>
<a name='L448'></a><a href='#L448'>448</a>
<a name='L449'></a><a href='#L449'>449</a>
<a name='L450'></a><a href='#L450'>450</a>
<a name='L451'></a><a href='#L451'>451</a>
<a name='L452'></a><a href='#L452'>452</a>
<a name='L453'></a><a href='#L453'>453</a>
<a name='L454'></a><a href='#L454'>454</a>
<a name='L455'></a><a href='#L455'>455</a>
<a name='L456'></a><a href='#L456'>456</a>
<a name='L457'></a><a href='#L457'>457</a>
<a name='L458'></a><a href='#L458'>458</a>
<a name='L459'></a><a href='#L459'>459</a>
<a name='L460'></a><a href='#L460'>460</a>
<a name='L461'></a><a href='#L461'>461</a>
<a name='L462'></a><a href='#L462'>462</a>
<a name='L463'></a><a href='#L463'>463</a>
<a name='L464'></a><a href='#L464'>464</a>
<a name='L465'></a><a href='#L465'>465</a>
<a name='L466'></a><a href='#L466'>466</a>
<a name='L467'></a><a href='#L467'>467</a>
<a name='L468'></a><a href='#L468'>468</a>
<a name='L469'></a><a href='#L469'>469</a>
<a name='L470'></a><a href='#L470'>470</a>
<a name='L471'></a><a href='#L471'>471</a>
<a name='L472'></a><a href='#L472'>472</a>
<a name='L473'></a><a href='#L473'>473</a>
<a name='L474'></a><a href='#L474'>474</a>
<a name='L475'></a><a href='#L475'>475</a>
<a name='L476'></a><a href='#L476'>476</a>
<a name='L477'></a><a href='#L477'>477</a>
<a name='L478'></a><a href='#L478'>478</a>
<a name='L479'></a><a href='#L479'>479</a>
<a name='L480'></a><a href='#L480'>480</a>
<a name='L481'></a><a href='#L481'>481</a>
<a name='L482'></a><a href='#L482'>482</a>
<a name='L483'></a><a href='#L483'>483</a>
<a name='L484'></a><a href='#L484'>484</a>
<a name='L485'></a><a href='#L485'>485</a>
<a name='L486'></a><a href='#L486'>486</a>
<a name='L487'></a><a href='#L487'>487</a>
<a name='L488'></a><a href='#L488'>488</a>
<a name='L489'></a><a href='#L489'>489</a>
<a name='L490'></a><a href='#L490'>490</a>
<a name='L491'></a><a href='#L491'>491</a>
<a name='L492'></a><a href='#L492'>492</a>
<a name='L493'></a><a href='#L493'>493</a>
<a name='L494'></a><a href='#L494'>494</a>
<a name='L495'></a><a href='#L495'>495</a>
<a name='L496'></a><a href='#L496'>496</a>
<a name='L497'></a><a href='#L497'>497</a>
<a name='L498'></a><a href='#L498'>498</a>
<a name='L499'></a><a href='#L499'>499</a>
<a name='L500'></a><a href='#L500'>500</a>
<a name='L501'></a><a href='#L501'>501</a>
<a name='L502'></a><a href='#L502'>502</a>
<a name='L503'></a><a href='#L503'>503</a>
<a name='L504'></a><a href='#L504'>504</a>
<a name='L505'></a><a href='#L505'>505</a>
<a name='L506'></a><a href='#L506'>506</a>
<a name='L507'></a><a href='#L507'>507</a>
<a name='L508'></a><a href='#L508'>508</a>
<a name='L509'></a><a href='#L509'>509</a>
<a name='L510'></a><a href='#L510'>510</a>
<a name='L511'></a><a href='#L511'>511</a>
<a name='L512'></a><a href='#L512'>512</a>
<a name='L513'></a><a href='#L513'>513</a>
<a name='L514'></a><a href='#L514'>514</a>
<a name='L515'></a><a href='#L515'>515</a>
<a name='L516'></a><a href='#L516'>516</a>
<a name='L517'></a><a href='#L517'>517</a>
<a name='L518'></a><a href='#L518'>518</a>
<a name='L519'></a><a href='#L519'>519</a>
<a name='L520'></a><a href='#L520'>520</a>
<a name='L521'></a><a href='#L521'>521</a>
<a name='L522'></a><a href='#L522'>522</a>
<a name='L523'></a><a href='#L523'>523</a>
<a name='L524'></a><a href='#L524'>524</a>
<a name='L525'></a><a href='#L525'>525</a>
<a name='L526'></a><a href='#L526'>526</a>
<a name='L527'></a><a href='#L527'>527</a>
<a name='L528'></a><a href='#L528'>528</a>
<a name='L529'></a><a href='#L529'>529</a>
<a name='L530'></a><a href='#L530'>530</a>
<a name='L531'></a><a href='#L531'>531</a>
<a name='L532'></a><a href='#L532'>532</a>
<a name='L533'></a><a href='#L533'>533</a>
<a name='L534'></a><a href='#L534'>534</a>
<a name='L535'></a><a href='#L535'>535</a>
<a name='L536'></a><a href='#L536'>536</a>
<a name='L537'></a><a href='#L537'>537</a>
<a name='L538'></a><a href='#L538'>538</a>
<a name='L539'></a><a href='#L539'>539</a>
<a name='L540'></a><a href='#L540'>540</a>
<a name='L541'></a><a href='#L541'>541</a>
<a name='L542'></a><a href='#L542'>542</a>
<a name='L543'></a><a href='#L543'>543</a>
<a name='L544'></a><a href='#L544'>544</a>
<a name='L545'></a><a href='#L545'>545</a>
<a name='L546'></a><a href='#L546'>546</a>
<a name='L547'></a><a href='#L547'>547</a>
<a name='L548'></a><a href='#L548'>548</a>
<a name='L549'></a><a href='#L549'>549</a>
<a name='L550'></a><a href='#L550'>550</a>
<a name='L551'></a><a href='#L551'>551</a>
<a name='L552'></a><a href='#L552'>552</a>
<a name='L553'></a><a href='#L553'>553</a>
<a name='L554'></a><a href='#L554'>554</a>
<a name='L555'></a><a href='#L555'>555</a>
<a name='L556'></a><a href='#L556'>556</a>
<a name='L557'></a><a href='#L557'>557</a>
<a name='L558'></a><a href='#L558'>558</a>
<a name='L559'></a><a href='#L559'>559</a>
<a name='L560'></a><a href='#L560'>560</a>
<a name='L561'></a><a href='#L561'>561</a>
<a name='L562'></a><a href='#L562'>562</a>
<a name='L563'></a><a href='#L563'>563</a>
<a name='L564'></a><a href='#L564'>564</a>
<a name='L565'></a><a href='#L565'>565</a>
<a name='L566'></a><a href='#L566'>566</a>
<a name='L567'></a><a href='#L567'>567</a>
<a name='L568'></a><a href='#L568'>568</a>
<a name='L569'></a><a href='#L569'>569</a>
<a name='L570'></a><a href='#L570'>570</a>
<a name='L571'></a><a href='#L571'>571</a>
<a name='L572'></a><a href='#L572'>572</a>
<a name='L573'></a><a href='#L573'>573</a>
<a name='L574'></a><a href='#L574'>574</a>
<a name='L575'></a><a href='#L575'>575</a>
<a name='L576'></a><a href='#L576'>576</a>
<a name='L577'></a><a href='#L577'>577</a>
<a name='L578'></a><a href='#L578'>578</a>
<a name='L579'></a><a href='#L579'>579</a>
<a name='L580'></a><a href='#L580'>580</a>
<a name='L581'></a><a href='#L581'>581</a>
<a name='L582'></a><a href='#L582'>582</a>
<a name='L583'></a><a href='#L583'>583</a>
<a name='L584'></a><a href='#L584'>584</a>
<a name='L585'></a><a href='#L585'>585</a>
<a name='L586'></a><a href='#L586'>586</a>
<a name='L587'></a><a href='#L587'>587</a>
<a name='L588'></a><a href='#L588'>588</a>
<a name='L589'></a><a href='#L589'>589</a>
<a name='L590'></a><a href='#L590'>590</a>
<a name='L591'></a><a href='#L591'>591</a>
<a name='L592'></a><a href='#L592'>592</a>
<a name='L593'></a><a href='#L593'>593</a>
<a name='L594'></a><a href='#L594'>594</a>
<a name='L595'></a><a href='#L595'>595</a>
<a name='L596'></a><a href='#L596'>596</a>
<a name='L597'></a><a href='#L597'>597</a>
<a name='L598'></a><a href='#L598'>598</a>
<a name='L599'></a><a href='#L599'>599</a>
<a name='L600'></a><a href='#L600'>600</a>
<a name='L601'></a><a href='#L601'>601</a>
<a name='L602'></a><a href='#L602'>602</a>
<a name='L603'></a><a href='#L603'>603</a>
<a name='L604'></a><a href='#L604'>604</a>
<a name='L605'></a><a href='#L605'>605</a>
<a name='L606'></a><a href='#L606'>606</a>
<a name='L607'></a><a href='#L607'>607</a>
<a name='L608'></a><a href='#L608'>608</a>
<a name='L609'></a><a href='#L609'>609</a>
<a name='L610'></a><a href='#L610'>610</a>
<a name='L611'></a><a href='#L611'>611</a>
<a name='L612'></a><a href='#L612'>612</a>
<a name='L613'></a><a href='#L613'>613</a>
<a name='L614'></a><a href='#L614'>614</a>
<a name='L615'></a><a href='#L615'>615</a>
<a name='L616'></a><a href='#L616'>616</a>
<a name='L617'></a><a href='#L617'>617</a>
<a name='L618'></a><a href='#L618'>618</a>
<a name='L619'></a><a href='#L619'>619</a>
<a name='L620'></a><a href='#L620'>620</a>
<a name='L621'></a><a href='#L621'>621</a>
<a name='L622'></a><a href='#L622'>622</a>
<a name='L623'></a><a href='#L623'>623</a>
<a name='L624'></a><a href='#L624'>624</a>
<a name='L625'></a><a href='#L625'>625</a>
<a name='L626'></a><a href='#L626'>626</a>
<a name='L627'></a><a href='#L627'>627</a>
<a name='L628'></a><a href='#L628'>628</a>
<a name='L629'></a><a href='#L629'>629</a>
<a name='L630'></a><a href='#L630'>630</a>
<a name='L631'></a><a href='#L631'>631</a>
<a name='L632'></a><a href='#L632'>632</a>
<a name='L633'></a><a href='#L633'>633</a>
<a name='L634'></a><a href='#L634'>634</a>
<a name='L635'></a><a href='#L635'>635</a>
<a name='L636'></a><a href='#L636'>636</a>
<a name='L637'></a><a href='#L637'>637</a>
<a name='L638'></a><a href='#L638'>638</a>
<a name='L639'></a><a href='#L639'>639</a>
<a name='L640'></a><a href='#L640'>640</a>
<a name='L641'></a><a href='#L641'>641</a>
<a name='L642'></a><a href='#L642'>642</a>
<a name='L643'></a><a href='#L643'>643</a>
<a name='L644'></a><a href='#L644'>644</a>
<a name='L645'></a><a href='#L645'>645</a>
<a name='L646'></a><a href='#L646'>646</a>
<a name='L647'></a><a href='#L647'>647</a>
<a name='L648'></a><a href='#L648'>648</a>
<a name='L649'></a><a href='#L649'>649</a>
<a name='L650'></a><a href='#L650'>650</a>
<a name='L651'></a><a href='#L651'>651</a>
<a name='L652'></a><a href='#L652'>652</a>
<a name='L653'></a><a href='#L653'>653</a>
<a name='L654'></a><a href='#L654'>654</a>
<a name='L655'></a><a href='#L655'>655</a>
<a name='L656'></a><a href='#L656'>656</a>
<a name='L657'></a><a href='#L657'>657</a>
<a name='L658'></a><a href='#L658'>658</a>
<a name='L659'></a><a href='#L659'>659</a>
<a name='L660'></a><a href='#L660'>660</a>
<a name='L661'></a><a href='#L661'>661</a>
<a name='L662'></a><a href='#L662'>662</a>
<a name='L663'></a><a href='#L663'>663</a>
<a name='L664'></a><a href='#L664'>664</a>
<a name='L665'></a><a href='#L665'>665</a>
<a name='L666'></a><a href='#L666'>666</a>
<a name='L667'></a><a href='#L667'>667</a>
<a name='L668'></a><a href='#L668'>668</a>
<a name='L669'></a><a href='#L669'>669</a>
<a name='L670'></a><a href='#L670'>670</a>
<a name='L671'></a><a href='#L671'>671</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">&nbsp;
&nbsp;
&nbsp;
import { ChatMessage, MessageRole, Template, HelpCategory, PricingPlan, WorkflowTemplate } from './types';
// FIX: Added missing icon imports to resolve module resolution errors.
import { DashboardIcon, FileTextIcon, UsersIcon, WandIcon, SettingsIcon, IntegrationsIcon } from './components/Icons';
import DashboardHelpScreenshot from './components/help-screenshots/DashboardHelpScreenshot';
import GenerateHelpScreenshot from './components/help-screenshots/GenerateHelpScreenshot';
import ClauseInsertionScreenshot from './components/help-screenshots/ClauseInsertionScreenshot';
import VersioningHelpScreenshot from './components/help-screenshots/VersioningHelpScreenshot';
import FoldersHelpScreenshot from './components/help-screenshots/FoldersHelpScreenshot';
import SearchHelpScreenshot from './components/help-screenshots/SearchHelpScreenshot';
import ClientsHelpScreenshot from './components/help-screenshots/ClientsHelpScreenshot';
import SharingScreenshot from './components/help-screenshots/SharingScreenshot';
import CommentingScreenshot from './components/help-screenshots/CommentingScreenshot';
import SignaturesHelpScreenshot from './components/help-screenshots/SignaturesHelpScreenshot';
import TeamManagementHelpScreenshot from './components/help-screenshots/TeamManagementHelpScreenshot';
import AnalysisHelpScreenshot from './components/help-screenshots/AnalysisHelpScreenshot';
import ClauseLibraryHelpScreenshot from './components/help-screenshots/ClauseLibraryHelpScreenshot';
import LifecycleHelpScreenshot from './components/help-screenshots/LifecycleHelpScreenshot';
import ObligationsHelpScreenshot from './components/help-screenshots/ObligationsHelpScreenshot';
import WorkflowsHelpScreenshot from './components/help-screenshots/WorkflowsHelpScreenshot';
import SettingsHelpScreenshot from './components/help-screenshots/SettingsHelpScreenshot';
import ApiSsoHelpScreenshot from './components/help-screenshots/ApiSsoHelpScreenshot';
import IntegrationsHelpScreenshot from './components/help-screenshots/IntegrationsHelpScreenshot';
&nbsp;
export const ANONYMOUS_USER_QUOTA = 3;
&nbsp;
export const SYSTEM_PROMPT = `You are an expert AI legal assistant named 'Lexi'. Your purpose is to help users draft professional legal contracts and clauses.
- When a user asks for a contract or a significant legal clause, format it clearly using Markdown. Use headings, lists, and bold text for clarity.
- IMPORTANT: When you provide the final, complete contract draft, you MUST enclose it within special markers. Start the contract block with '---CONTRACT START---' and end it with '---CONTRACT END---'.
- For simple questions or clarifications, respond in conversationally without the special markers.
- Always include the following disclaimer at the end of any legal document or clause you generate: 'Disclaimer: This document was generated by an AI and is not a substitute for professional legal advice. Please consult with a qualified attorney before using this contract.'
- Be professional, accurate, and helpful.`;
&nbsp;
export const INITIAL_MESSAGE: ChatMessage = {
    id: 'initial-message',
    role: MessageRole.MODEL,
    content: "Hello! I'm Lexi, your AI legal assistant. How can I help you today? You can ask me to draft a Non-Disclosure Agreement, a Freelance Contract, or any other legal clause you need."
};
&nbsp;
export const INITIAL_PRICING_PLANS: PricingPlan[] = [
    {
        id: 'plan_free',
        name: 'Registered User',
        price: 'Free',
        priceDetail: '/ month',
        features: ['5 document generations/month', 'Document History', 'Standard Templates', 'Email Support'],
        cta: 'Sign Up for Free',
        isFeatured: false,
    },
    {
        id: 'plan_premium',
        name: 'Premium',
        price: '$49',
        priceDetail: '/ month',
        features: ['Unlimited Generations', 'AI Document Analysis', 'Contract Lifecycle', 'Clause Library &amp; Custom Templates', 'Team Collaboration', 'Priority Support'],
        cta: 'Go Premium',
        isFeatured: true,
    },
    {
        id: 'plan_enterprise',
        name: 'Enterprise',
        price: 'Custom',
        priceDetail: '',
        features: ['Everything in Premium', 'Custom Integrations &amp; API', 'Dedicated Account Manager', 'Advanced Security &amp; SSO'],
        cta: 'Contact Sales',
        isFeatured: false,
    },
];
&nbsp;
export const TESTIMONIALS = [
    {
        quote: "This tool has saved us countless hours and thousands in legal fees. The AI-generated contracts are surprisingly robust and easy to customize.",
        name: "Jane Doe",
        title: "CEO, Startup Inc.",
        avatar: "https://picsum.photos/100/100?random=1"
    },
    {
        quote: "As a freelancer, drafting contracts used to be a major headache. Now, I can generate a professional agreement in minutes. It's a game-changer.",
        name: "John Smith",
        title: "Freelance Developer",
        avatar: "https://picsum.photos/100/100?random=2"
    },
    {
        quote: "The quality of the legal clauses is top-notch. We use it to supplement our in-house legal team's work, speeding up our entire workflow.",
        name: "Emily White",
        title: "Paralegal, Tech Corp",
        avatar: "https://picsum.photos/100/100?random=3"
    },
    {
        quote: "LexiGen's lifecycle management and AI analysis tools have given our procurement team unprecedented visibility into our vendor contracts, saving us money and reducing risk.",
        name: "David Chen",
        title: "Procurement Manager, Global Goods",
        avatar: "https://picsum.photos/100/100?random=4"
    }
];
&nbsp;
export const HOW_IT_WORKS_STEPS = [
  {
    title: 'Draft with AI',
    description: 'Describe your needs in plain English. Our AI assistant will generate a professional legal document in seconds, tailored to your requirements.',
    icon: 'SparklesIcon'
  },
  {
    title: 'Review &amp; Refine',
    description: 'Easily edit the generated document, add custom clauses from your library, and collaborate with your team in real-time with comments.',
    icon: 'EditIcon'
  },
  {
    title: 'Manage &amp; Sign',
    description: 'Track document statuses on a visual pipeline, manage versions, and send for legally-binding e-signatures, all within one platform.',
    icon: 'SignatureIcon'
  }
];
&nbsp;
export const FAQ_DATA = [
  {
    question: 'Is LexiGen a law firm?',
    answer: 'No, LexiGen is not a law firm and does not provide legal advice. The AI-generated content is for informational purposes only and should be considered a starting point. We always recommend consulting with a qualified attorney for your specific legal needs.'
  },
  {
    question: 'How accurate are the AI-generated documents?',
    answer: 'Our AI is trained on a vast corpus of legal documents and aims for high accuracy and relevance. However, it is not infallible. All generated documents should be carefully reviewed for accuracy and suitability for your specific jurisdiction and situation.'
  },
  {
    question: 'Can I use the documents for commercial purposes?',
    answer: 'Yes, once you generate a document, you have the rights to use and modify it for your personal or commercial needs. However, you are solely responsible for the legal validity of the final document.'
  },
  {
    question: 'Is my data secure?',
    answer: 'Yes, we take data security very seriously. We use industry-standard encryption for data in transit and at rest. Please review our Privacy Policy for more detailed information on how we handle your data.'
  },
  {
    question: 'Can I cancel my subscription at any time?',
    answer: 'Absolutely. You can manage your subscription from your account settings. If you cancel, you will retain access to your plan\'s features until the end of the current billing period.'
  }
];
&nbsp;
export const HELP_TOPICS: HelpCategory[] = [
  {
    category: 'Getting Started',
    icon: DashboardIcon,
    topics: [
      { id: 'welcome', title: 'Welcome to LexiGen', content: `&lt;h2&gt;Welcome to LexiGen!&lt;/h2&gt;&lt;p&gt;This guide will help you get started with the platform's key features. Use the navigation on the left to explore different topics.&lt;/p&gt;&lt;p&gt;LexiGen is an all-in-one platform for drafting, managing, and analyzing legal documents using the power of AI. Here's a quick overview of what you can do:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Generate Documents:&lt;/strong&gt; Use our AI assistant to draft contracts from scratch.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Collaborate:&lt;/strong&gt; Work with clients and team members in real-time.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Automate:&lt;/strong&gt; Set up workflows to automate approvals and other repetitive tasks.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Analyze:&lt;/strong&gt; Get AI-powered insights on third-party documents to identify risks.&lt;/li&gt;&lt;/ul&gt;` },
      { 
        id: 'dashboard', 
        title: 'Navigating the Dashboard', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Navigating the Dashboard&lt;/h2&gt;&lt;p&gt;The main dashboard is your command center. It provides a quick summary of your account activity.&lt;/p&gt;` },
          { type: 'screenshot', component: DashboardHelpScreenshot },
          { type: 'text', value: `&lt;ol&gt;&lt;li&gt;&lt;strong&gt;Stat Cards:&lt;/strong&gt; At the top, you'll find key metrics like the total number of documents, folders created, and your monthly generation quota.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Recent Activity Chart:&lt;/strong&gt; This chart visualizes the number of documents you've created over the past 7 days.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Sidebar Navigation:&lt;/strong&gt; Use the sidebar on the left to access all the main features of the application.&lt;/li&gt;&lt;/ol&gt;` }
        ]
      },
    ]
  },
  {
    category: 'Core Features',
    icon: FileTextIcon,
    topics: [
       { 
        id: 'generate', 
        title: 'Generating Documents with AI', 
        content: [
            { type: 'text', value: `&lt;h2&gt;Generating a New Document&lt;/h2&gt;&lt;p&gt;You can create a new legal document by describing your needs to the AI assistant.&lt;/p&gt;` },
            { type: 'screenshot', component: GenerateHelpScreenshot },
            { type: 'text', value: `&lt;ol&gt;&lt;li&gt;Navigate to the 'Generate Document' page.&lt;/li&gt;&lt;li&gt;In the &lt;strong&gt;chat input box&lt;/strong&gt;, describe your needs. Be as specific as possible. For example, "Draft a simple non-disclosure agreement between two companies for a new project."&lt;/li&gt;&lt;li&gt;The AI will generate a document which you can then review, edit, and save.&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;You can also start from the 'Templates' page by selecting a pre-made template, which will pre-fill the prompt for you.&lt;/p&gt;` }
        ] 
      },
      { 
        id: 'editor', 
        title: 'Using the Document Editor', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Using the Document Editor&lt;/h2&gt;&lt;p&gt;The document editor is where you refine your contracts. It provides a rich set of tools:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Toolbar:&lt;/strong&gt; The toolbar at the top provides standard text formatting options like bold, italics, headings, and lists.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Editing:&lt;/strong&gt; Click the 'Edit' button to enter editing mode. You can type directly into the document.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Saving:&lt;/strong&gt; Click the 'Save' icon to save your changes. This creates a new version in the document's history.&lt;/li&gt;&lt;/ul&gt;`},
          { type: 'text', value: `&lt;h4&gt;Inserting Clauses (Premium)&lt;/h4&gt;&lt;p&gt;While in editing mode, you can easily insert clauses from your library. Click the &lt;strong&gt;'Insert Clause' button&lt;/strong&gt; in the header to open your library, search for the clause you need, and insert it directly into your document.&lt;/p&gt;` },
          { type: 'screenshot', component: ClauseInsertionScreenshot },
        ]
      },
       { 
        id: 'versioning', 
        title: 'Versioning &amp; History', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Versioning &amp; History&lt;/h2&gt;&lt;p&gt;LexiGen automatically saves a new version of your document every time you click 'Save'. You can view, compare, and restore previous versions at any time.&lt;/p&gt;`},
          { type: 'screenshot', component: VersioningHelpScreenshot },
          { type: 'text', value: `&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Access Versions:&lt;/strong&gt; Open a document and use the 'Versions' tab in the right-hand utility panel.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Compare:&lt;/strong&gt; Click 'Compare' on an older version to see a redline comparison against the current version.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Restore:&lt;/strong&gt; Click 'Restore' to revert the document's content to that of an older version. This will create a new version history entry.&lt;/li&gt;&lt;/ul&gt;` }
        ] 
      },
      { 
        id: 'folders', 
        title: 'Organizing with Folders', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Organizing with Folders&lt;/h2&gt;&lt;p&gt;The 'Documents' page is your central hub for all saved documents.&lt;/p&gt;` },
          { type: 'screenshot', component: FoldersHelpScreenshot },
          { type: 'text', value: `&lt;ol&gt;&lt;li&gt;&lt;strong&gt;Create a Folder:&lt;/strong&gt; Click the &lt;strong&gt;'New Folder' button&lt;/strong&gt; to create a new folder.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Navigate Folders:&lt;/strong&gt; Use the &lt;strong&gt;folder list&lt;/strong&gt; on the left to filter the documents shown in the main table.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Move Documents:&lt;/strong&gt; From the document list, click the three-dot menu on a document and select 'Move to...' to change its folder.&lt;/li&gt;&lt;/ol&gt;` }
        ] 
      },
       { 
        id: 'search', 
        title: 'Searching Your Workspace', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Searching Your Workspace&lt;/h2&gt;&lt;p&gt;The global search bar at the top of the dashboard allows you to quickly find documents, folders, clauses, and help articles.&lt;/p&gt;`},
          { type: 'screenshot', component: SearchHelpScreenshot },
          { type: 'text', value: `&lt;p&gt;Simply start typing your query, and the results will appear in a dropdown, categorized for easy navigation.&lt;/p&gt;` }
        ] 
      },
    ]
  },
   {
    category: 'Collaboration',
    icon: UsersIcon,
    topics: [
      { 
        id: 'clients', 
        title: 'Managing Clients', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Managing Clients&lt;/h2&gt;&lt;p&gt;The 'Clients' page allows you to create and manage a central repository of your clients, both companies and individuals.&lt;/p&gt;`},
          { type: 'screenshot', component: ClientsHelpScreenshot },
          { type: 'text', value: `&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Create Clients:&lt;/strong&gt; Navigate to 'Workspace' &gt; 'Clients' and click 'New Client'.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Associate with Documents:&lt;/strong&gt; You can link a document to a client when you first save it, or from the document detail page.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Use in Workflows:&lt;/strong&gt; Client information can be used as a condition in your automated workflows (e.g., "If client is Innovate Corp, request approval from Legal").&lt;/li&gt;&lt;/ul&gt;` }
        ] 
      },
      { 
        id: 'sharing', 
        title: 'Sharing &amp; Permissions', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Sharing &amp; Permissions&lt;/h2&gt;&lt;p&gt;You can collaborate on documents with others by inviting them via email. They must have a LexiGen account to access the document.&lt;/p&gt;`},
           { type: 'screenshot', component: SharingScreenshot },
          { type: 'text', value: `&lt;ol&gt;&lt;li&gt;In the document editor, click the &lt;strong&gt;Share button&lt;/strong&gt; in the header.&lt;/li&gt;&lt;li&gt;Enter the email address of the user you want to invite.&lt;/li&gt;&lt;li&gt;Choose their permission level: 'Can View' or 'Can Edit'.&lt;/li&gt;&lt;li&gt;Click 'Invite'.&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;Collaborators with 'Edit' access can make changes and add comments. 'View' access is read-only.&lt;/p&gt;` }
        ] 
      },
      { 
        id: 'comments', 
        title: 'Commenting &amp; Discussions', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Commenting &amp; Discussions&lt;/h2&gt;&lt;p&gt;Comments are a great way to discuss specific parts of a document with your team.&lt;/p&gt;&lt;h4&gt;Adding a Comment&lt;/h4&gt;&lt;ol&gt;&lt;li&gt;While in 'Edit' mode, &lt;strong&gt;highlight the text&lt;/strong&gt; you want to comment on.&lt;/li&gt;&lt;li&gt;Click the &lt;strong&gt;comment icon&lt;/strong&gt; on the pop-up toolbar.&lt;/li&gt;&lt;/ol&gt;` },
          { type: 'screenshot', component: CommentingScreenshot },
          { type: 'text', value: `&lt;p&gt;This creates a new thread in the right-hand 'Comments' panel. Anyone with access can reply. Once a discussion is finished, click 'Resolve' to hide the thread.&lt;/p&gt;` }
        ]
      },
      { 
        id: 'signatures', 
        title: 'Requesting E-Signatures', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Requesting E-Signatures&lt;/h2&gt;&lt;p&gt;Once a document is finalized, you can send it for legally binding e-signatures directly from the platform.&lt;/p&gt;`},
           { type: 'screenshot', component: SignaturesHelpScreenshot },
          { type: 'text', value: `&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Request Signatures:&lt;/strong&gt; From the document editor, click the 'Signature' icon. Add the email addresses and roles for each signer.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Tracking:&lt;/strong&gt; Once sent, the document is locked for editing. You can track the status of each signature in the 'Signatures' tab of the utility panel.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Completion:&lt;/strong&gt; When all parties have signed, the document status is automatically updated to 'Completed'.&lt;/li&gt;&lt;/ul&gt;` }
        ] 
      },
      { 
        id: 'team', 
        title: 'Team Management', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Team Management (Premium)&lt;/h2&gt;&lt;p&gt;On Premium and Enterprise plans, you can manage a team from the 'Team' page.&lt;/p&gt;` },
          { type: 'screenshot', component: TeamManagementHelpScreenshot },
          { type: 'text', value: `&lt;ul&gt;&lt;li&gt;Click the &lt;strong&gt;'Invite Member' button&lt;/strong&gt; to add new users.&lt;/li&gt;&lt;li&gt;You can assign roles like 'Admin' or 'Member'. 'Admins' can also manage team members and settings.&lt;/li&gt;&lt;li&gt;The team owner can remove members or change their roles from the main table.&lt;/li&gt;&lt;/ul&gt;` }
        ]
      },
    ]
  },
  {
    category: 'AI &amp; Automation',
    icon: WandIcon,
    topics: [
      { 
        id: 'analysis', 
        title: 'Analyzing Documents', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Analyzing a Document (Premium)&lt;/h2&gt;&lt;p&gt;The 'Analyze Document' feature helps you understand third-party contracts by identifying risks and summarizing key clauses.&lt;/p&gt;` },
          { type: 'screenshot', component: AnalysisHelpScreenshot },
          { type: 'text', value: `&lt;ol&gt;&lt;li&gt;Navigate to the 'Analyze Document' page.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Paste the full text&lt;/strong&gt; of the legal document into the text area, or use the &lt;strong&gt;'Upload File' button&lt;/strong&gt;.&lt;/li&gt;&lt;li&gt;Click 'Analyze Document'.&lt;/li&gt;&lt;/ol&gt;&lt;p&gt;The AI will review the text and provide a structured analysis, including a summary, potential risks, and actionable suggestions.&lt;/p&gt;` }
        ] 
      },
      { 
        id: 'clauselib', 
        title: 'Clause Library &amp; Suggestions', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Clause Library (Premium)&lt;/h2&gt;&lt;p&gt;The 'Clause Library' allows you to save and reuse your own pre-approved legal clauses, ensuring consistency across documents.&lt;/p&gt;` },
          { type: 'screenshot', component: ClauseLibraryHelpScreenshot },
          { type: 'text', value: `&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Create a Clause:&lt;/strong&gt; Go to the library and click 'New Clause'.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Save from Editor:&lt;/strong&gt; When editing, you can highlight text and use the pop-up toolbar to 'Save as Clause'.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Insert a Clause:&lt;/strong&gt; When editing, click the 'Insert Clause' button to search and add clauses to your document.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;AI Suggestions:&lt;/strong&gt; In the document editor's utility panel, the 'AI Suggestions' tool can analyze your draft and recommend additional clauses to include.&lt;/li&gt;&lt;/ul&gt;` }
        ]
      },
       { 
        id: 'lifecycle', 
        title: 'Contract Lifecycle', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Contract Lifecycle (Premium)&lt;/h2&gt;&lt;p&gt;The 'Lifecycle' page gives you a high-level view of your contract pipeline using a Kanban board.&lt;/p&gt;` },
          { type: 'screenshot', component: LifecycleHelpScreenshot },
          { type: 'text', value: `&lt;p&gt;You can &lt;strong&gt;drag and drop documents&lt;/strong&gt; between statuses (e.g., from 'Draft' to 'Out for Signature'). When a document is 'Completed', our AI automatically extracts key dates and obligations.&lt;/p&gt;` }
        ]
      },
       { 
        id: 'obligations', 
        title: 'Obligation Tracking', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Obligation Tracking (Premium)&lt;/h2&gt;&lt;p&gt;Never miss a contractual deadline. When a contract is marked 'Completed', our AI automatically extracts key obligations and their due dates.&lt;/p&gt;` },
          { type: 'screenshot', component: ObligationsHelpScreenshot },
          { type: 'text', value: `&lt;p&gt;You can view all upcoming deadlines from the 'Obligations' page in your workspace. You can also see a document's specific obligations in the utility panel of the document editor.&lt;/p&gt;` }
        ]
      },
      { 
        id: 'workflows', 
        title: 'Automating with Workflows', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Automating with Workflows (Premium)&lt;/h2&gt;&lt;p&gt;The 'Workflows' page allows you to build powerful, multi-step automations for your contract processes.&lt;/p&gt;` },
          { type: 'screenshot', component: WorkflowsHelpScreenshot },
          { type: 'text', value: `&lt;p&gt;Use the visual builder to create custom flows. For example, you can design a workflow that automatically requests approval from the legal team for any contract with a value over $10,000.&lt;/p&gt;` }
        ]
      },
    ]
  },
   {
    category: 'Account &amp; Settings',
    icon: SettingsIcon,
    topics: [
      { id: 'subscription', title: 'Managing Your Subscription', content: `&lt;h2&gt;Managing Your Subscription&lt;/h2&gt;&lt;p&gt;You can view and manage your subscription plan from the 'Subscription' page.&lt;/p&gt;&lt;ul&gt;&lt;li&gt;See your current plan and its features.&lt;/li&gt;&lt;li&gt;Compare plans and upgrade if needed.&lt;/li&gt;&lt;li&gt;The platform will simulate an upgrade process for demonstration purposes.&lt;/li&gt;&lt;/ul&gt;` },
      { 
        id: 'settings', 
        title: 'Account Settings', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Account Settings&lt;/h2&gt;&lt;p&gt;The 'Settings' page allows you to manage your account details via a clear, tabbed interface.&lt;/p&gt;` },
          { type: 'screenshot', component: SettingsHelpScreenshot },
          { type: 'text', value: `&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Profile:&lt;/strong&gt; Update your name, username, and professional details.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Appearance:&lt;/strong&gt; Choose between light, dark, and system themes.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Notifications:&lt;/strong&gt; Control which email notifications you receive.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Security:&lt;/strong&gt; Change your password or delete your account.&lt;/li&gt;&lt;/ul&gt;` }
        ]
      },
      { id: 'notifications', title: 'Notifications', content: `&lt;h2&gt;Notifications&lt;/h2&gt;&lt;p&gt;You can view your most recent notifications by clicking the bell icon in the header. The 'Notifications' page under your workspace provides a complete history of all your notifications.&lt;/p&gt;` },
      { 
        id: 'api-sso', 
        title: 'API &amp; SSO', 
        content: [
          { type: 'text', value: `&lt;h2&gt;API &amp; SSO (Enterprise)&lt;/h2&gt;&lt;p&gt;For Enterprise customers, additional tabs appear in Settings for advanced configuration.&lt;/p&gt;` },
          { type: 'screenshot', component: ApiSsoHelpScreenshot },
          { type: 'text', value: `&lt;ul&gt;&lt;li&gt;&lt;strong&gt;API &amp; Integrations:&lt;/strong&gt; Generate and manage API keys to connect LexiGen to other software.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Security (SSO):&lt;/strong&gt; Configure Single Sign-On to allow your team to log in using your company's identity provider.&lt;/li&gt;&lt;/ul&gt;` }
        ]
      },
    ]
  },
  {
    category: 'Integrations',
    icon: IntegrationsIcon,
    topics: [
      { 
        id: 'integrations-setup', 
        title: 'Integrations: Setup and Data Mapping', 
        content: [
          { type: 'text', value: `&lt;h2&gt;Summary&lt;/h2&gt;&lt;p&gt;Integrating LexiGen with third-party applications allows you to automate critical business workflows by synchronizing data between systems. This can significantly reduce manual data entry, prevent errors, and accelerate processes like invoicing, client onboarding, and financial reporting.&lt;/p&gt;&lt;p&gt;This guide provides a comprehensive overview of how to set up, configure, and manage these integrations.&lt;/p&gt;&lt;h2&gt;Prerequisites&lt;/h2&gt;&lt;p&gt;Before you begin, ensure you meet the following requirements:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Plan:&lt;/strong&gt; You must be on the &lt;strong&gt;Enterprise&lt;/strong&gt; plan.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Role:&lt;/strong&gt; You must have an &lt;strong&gt;Admin&lt;/strong&gt; role within your team.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;API Keys:&lt;/strong&gt; You need permissions to view and generate API keys. See our guide on &lt;a href="#/help/api-sso"&gt;API &amp; SSO&lt;/a&gt; for more details.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Third-Party Account:&lt;/strong&gt; You need an active account with administrative access to the third-party application you wish to connect.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;OAuth Scopes:&lt;/strong&gt; During authorization, you will be asked to grant permissions. LexiGen requires the following scopes: &lt;code&gt;{{OAUTH_SCOPE_READ_WRITE}}&lt;/code&gt;, &lt;code&gt;{{OAUTH_SCOPE_INVOICING}}&lt;/code&gt;.&lt;/li&gt;&lt;/ul&gt;&lt;h2&gt;Supported Systems&lt;/h2&gt;&lt;p&gt;LexiGen currently supports direct integrations with the following systems:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;QuickBooks Online&lt;/li&gt;&lt;li&gt;Zoho Books &lt;code&gt;(Coming Soon)&lt;/code&gt;&lt;/li&gt;&lt;li&gt;Moneybird &lt;code&gt;(Coming Soon)&lt;/code&gt;&lt;/li&gt;&lt;li&gt;{{THIRD_PARTY_SYSTEM_4}} &lt;code&gt;(Coming Soon)&lt;/code&gt;&lt;/li&gt;&lt;/ul&gt;&lt;div class="p-4 bg-zinc-100 dark:bg-zinc-800/50 rounded-lg my-4"&gt;&lt;p&gt;&lt;strong&gt;Tip:&lt;/strong&gt; You can connect to thousands of other apps using our API keys with an automation platform like Zapier or Make.&lt;/p&gt;&lt;/div&gt;&lt;h2&gt;Data Mapping&lt;/h2&gt;&lt;p&gt;Data is synchronized between LexiGen and the connected application according to the mappings below.&lt;/p&gt;&lt;h4&gt;Clients / Customers&lt;/h4&gt;&lt;div class="overflow-x-auto"&gt;&lt;table class="w-full"&gt;&lt;thead&gt;&lt;tr&gt;&lt;th class="p-2 text-left"&gt;Source (LexiGen)&lt;/th&gt;&lt;th class="p-2 text-left"&gt;Mapped Field (Third-Party)&lt;/th&gt;&lt;th class="p-2 text-left"&gt;Direction&lt;/th&gt;&lt;th class="p-2 text-left"&gt;Sync Frequency&lt;/th&gt;&lt;th class="p-2 text-left"&gt;Conflict Rule&lt;/th&gt;&lt;/tr&gt;&lt;/thead&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td class="p-2"&gt;&lt;code&gt;client.name&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;&lt;code&gt;Customer.DisplayName&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;LexiGen -&amp;gt;&lt;/td&gt;&lt;td class="p-2"&gt;On Create/Update&lt;/td&gt;&lt;td class="p-2"&gt;Source of Truth: LexiGen&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td class="p-2"&gt;&lt;code&gt;client.email&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;&lt;code&gt;Customer.PrimaryEmailAddr&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;Bidirectional&lt;/td&gt;&lt;td class="p-2"&gt;On Update&lt;/td&gt;&lt;td class="p-2"&gt;Most Recently Updated&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td class="p-2"&gt;&lt;code&gt;client.address&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;&lt;code&gt;Customer.BillAddr&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;LexiGen -&amp;gt;&lt;/td&gt;&lt;td class="p-2"&gt;On Create/Update&lt;/td&gt;&lt;td class="p-2"&gt;Source of Truth: LexiGen&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;/div&gt;&lt;h4&gt;Documents / Contracts&lt;/h4&gt;&lt;div class="overflow-x-auto"&gt;&lt;table class="w-full"&gt;&lt;thead&gt;&lt;tr&gt;&lt;th class="p-2 text-left"&gt;Source (LexiGen)&lt;/th&gt;&lt;th class="p-2 text-left"&gt;Mapped Field (Third-Party)&lt;/th&gt;&lt;th class="p-2 text-left"&gt;Direction&lt;/th&gt;&lt;th class="p-2 text-left"&gt;Sync Frequency&lt;/th&gt;&lt;th class="p-2 text-left"&gt;Conflict Rule&lt;/th&gt;&lt;/tr&gt;&lt;/thead&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td class="p-2"&gt;&lt;code&gt;document.name&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;&lt;code&gt;Estimate.DocNumber&lt;/code&gt; or &lt;code&gt;Note.Title&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;LexiGen -&amp;gt;&lt;/td&gt;&lt;td class="p-2"&gt;On Status Change to &lt;code&gt;Completed&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;N/A (Creates new record)&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td class="p-2"&gt;&lt;code&gt;document.value&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;&lt;code&gt;Invoice.TotalAmt&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;LexiGen -&amp;gt;&lt;/td&gt;&lt;td class="p-2"&gt;On Status Change to &lt;code&gt;Completed&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;N/A (Creates new record)&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td class="p-2"&gt;&lt;code&gt;document.status&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;Triggers Invoice Creation&lt;/td&gt;&lt;td class="p-2"&gt;N/A&lt;/td&gt;&lt;td class="p-2"&gt;On Event&lt;/td&gt;&lt;td class="p-2"&gt;N/A&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;/div&gt;&lt;h2&gt;Security Considerations&lt;/h2&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;Data in Transit:&lt;/strong&gt; All data transferred between LexiGen and third-party services is encrypted using TLS 1.2 or higher.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Authentication Tokens:&lt;/strong&gt; OAuth tokens and API keys are encrypted at rest using AES-256.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Token Revocation:&lt;/strong&gt; You can revoke an integration's access at any time from the &lt;code&gt;{{MENU_PATH}}&lt;/code&gt; page. This will immediately invalidate the stored tokens.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Rate Limits:&lt;/strong&gt; API calls to third-party services are subject to their respective rate limits. LexiGen uses an intelligent queueing system to manage this, but heavy usage may result in temporary delays. For more details, see our developer documentation on &lt;a href="/dev/api-limits"&gt;API rate limits&lt;/a&gt;.&lt;/li&gt;&lt;/ul&gt;` },
          { type: 'text', value: '&lt;h2&gt;Step-by-Step Setup&lt;/h2&gt;&lt;p&gt;Follow these steps to connect a new application.&lt;/p&gt;' },
          { type: 'screenshot', component: IntegrationsHelpScreenshot },
          { type: 'text', value: `&lt;ol&gt;&lt;li&gt;&lt;strong&gt;Navigate to the Integrations Page.&lt;/strong&gt;&lt;br/&gt;&lt;strong&gt;Action:&lt;/strong&gt; Click on the \`Integrations\` menu item.&lt;br/&gt;&lt;strong&gt;UI Path:&lt;/strong&gt; &lt;code&gt;{{MENU_PATH}}&lt;/code&gt;&lt;br/&gt;&lt;strong&gt;Expected Result:&lt;/strong&gt; You will see a gallery of available applications to connect.&lt;br/&gt;&lt;strong&gt;Recovery Tip:&lt;/strong&gt; If you don't see the \`Integrations\` option, please verify you are on the Enterprise plan and have Admin permissions.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Select and Connect an Application.&lt;/strong&gt;&lt;br/&gt;&lt;strong&gt;Action:&lt;/strong&gt; Find the application you wish to integrate (e.g., QuickBooks Online) and click the &lt;code&gt;{{BUTTON_LABEL_CONNECT}}&lt;/code&gt; button.&lt;br/&gt;&lt;strong&gt;UI Path:&lt;/strong&gt; &lt;code&gt;{{MENU_PATH}}&lt;/code&gt; -&amp;gt; Click &lt;code&gt;{{BUTTON_LABEL_CONNECT}}&lt;/code&gt;&lt;br/&gt;&lt;strong&gt;Expected Result:&lt;/strong&gt; You will be redirected to the third-party application's authorization screen in a new window or tab.&lt;br/&gt;&lt;strong&gt;Recovery Tip:&lt;/strong&gt; If the new window does not open, check if your browser is blocking pop-ups.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Authorize the Connection.&lt;/strong&gt;&lt;br/&gt;&lt;strong&gt;Action:&lt;/strong&gt; Log in to your third-party account if prompted, and approve the requested permissions for LexiGen.&lt;br/&gt;&lt;strong&gt;UI Path:&lt;/strong&gt; Third-party authorization screen.&lt;br/&gt;&lt;strong&gt;Expected Result:&lt;/strong&gt; The window will close, and you will be returned to LexiGen. The application will now show a "Connected" status.&lt;br/&gt;&lt;strong&gt;Recovery Tip:&lt;/strong&gt; If authorization fails, ensure you are using an account with administrative privileges in the third-party application.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Configure Settings and Field Mapping.&lt;/strong&gt;&lt;br/&gt;&lt;strong&gt;Action:&lt;/strong&gt; Once connected, click on the &lt;code&gt;{{BUTTON_LABEL_CONFIGURE}}&lt;/code&gt; button to open the integration's settings. Map the fields from LexiGen to the corresponding fields in the connected application.&lt;br/&gt;&lt;strong&gt;UI Path:&lt;/strong&gt; &lt;code&gt;{{MENU_PATH}}&lt;/code&gt; -&amp;gt; Click &lt;code&gt;{{BUTTON_LABEL_CONFIGURE}}&lt;/code&gt;&lt;br/&gt;&lt;strong&gt;Expected Result:&lt;/strong&gt; A modal or page appears with dropdowns for mapping fields like Client Name, Document Value, etc.&lt;br/&gt;&lt;strong&gt;Recovery Tip:&lt;/strong&gt; If a required field is missing, you may need to configure it in the third-party application first.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Enable and Test the Sync.&lt;/strong&gt;&lt;br/&gt;&lt;strong&gt;Action:&lt;/strong&gt; After configuring the mappings, toggle the &lt;code&gt;{{TOGGLE_LABEL_ENABLE_SYNC}}&lt;/code&gt; switch to "On" and click &lt;code&gt;{{BUTTON_LABEL_SAVE_SETTINGS}}&lt;/code&gt;. Then, use the &lt;code&gt;{{BUTTON_LABEL_TEST_SYNC}}&lt;/code&gt; button.&lt;br/&gt;&lt;strong&gt;UI Path:&lt;/strong&gt; Integration configuration modal.&lt;br/&gt;&lt;strong&gt;Expected Result:&lt;/strong&gt; A success message indicates that a test record was successfully created in the third-party system.&lt;br/&gt;&lt;strong&gt;Recovery Tip:&lt;/strong&gt; If the test fails, double-check your field mappings for any errors or missing required fields.&lt;/li&gt;&lt;/ol&gt;&lt;h2&gt;Verification Checklist&lt;/h2&gt;&lt;ul&gt;&lt;li&gt;[ ] Is the application status shown as "Connected" on the \`Integrations\` page?&lt;/li&gt;&lt;li&gt;[ ] Did the test sync create a record in the third-party application as expected?&lt;/li&gt;&lt;li&gt;[ ] Are all required fields in the mapping configuration correctly assigned?&lt;/li&gt;&lt;li&gt;[ ] Is the &lt;code&gt;{{TOGGLE_LABEL_ENABLE_SYNC}}&lt;/code&gt; switch in the "On" position?&lt;/li&gt;&lt;/ul&gt;&lt;h2&gt;Troubleshooting&lt;/h2&gt;&lt;div class="overflow-x-auto"&gt;&lt;table class="w-full"&gt;&lt;thead&gt;&lt;tr&gt;&lt;th class="p-2 text-left"&gt;Error Message&lt;/th&gt;&lt;th class="p-2 text-left"&gt;Cause &amp; Resolution&lt;/th&gt;&lt;/tr&gt;&lt;/thead&gt;&lt;tbody&gt;&lt;tr&gt;&lt;td class="p-2"&gt;&lt;code&gt;Connection Failed: 401 Unauthorized&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;The credentials or token are invalid. &lt;strong&gt;Fix:&lt;/strong&gt; Disconnect and reconnect the application to re-authorize.&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td class="p-2"&gt;&lt;code&gt;Sync Failed: Missing required field {{FIELD_NAME}}&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;The field mapping is incomplete. &lt;strong&gt;Fix:&lt;/strong&gt; Go to the integration's configuration and ensure &lt;code&gt;{{FIELD_NAME}}&lt;/code&gt; is mapped.&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td class="p-2"&gt;&lt;code&gt;429 Too Many Requests&lt;/code&gt;&lt;/td&gt;&lt;td class="p-2"&gt;The third-party API rate limit was exceeded. &lt;strong&gt;Fix:&lt;/strong&gt; This is usually temporary. The sync will automatically retry after a short delay.&lt;/td&gt;&lt;/tr&gt;&lt;/tbody&gt;&lt;/table&gt;&lt;/div&gt;&lt;h2&gt;Frequently Asked Questions (FAQs)&lt;/h2&gt;&lt;ol&gt;&lt;li&gt;&lt;strong&gt;How often does data sync?&lt;/strong&gt;&lt;br/&gt;Data syncs based on specific events (e.g., when a document's status changes to \`Completed\`) and during a nightly reconciliation process. See the Data Mapping tables for details.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;Can I disable an integration temporarily?&lt;/strong&gt;&lt;br/&gt;Yes. Navigate to &lt;code&gt;{{MENU_PATH}}&lt;/code&gt;, click &lt;code&gt;{{BUTTON_LABEL_CONFIGURE}}&lt;/code&gt; on the integration, and turn off the &lt;code&gt;{{TOGGLE_LABEL_ENABLE_SYNC}}&lt;/code&gt; switch.&lt;/li&gt;&lt;li&gt;&lt;strong&gt;What happens if I disconnect an integration?&lt;/strong&gt;&lt;br/&gt;All data syncing will stop immediately. LexiGen will securely delete the stored authentication tokens. No previously synced data will be removed from either system.&lt;/li&gt;&lt;/ol&gt;&lt;h2&gt;Change Log&lt;/h2&gt;&lt;ul&gt;&lt;li&gt;&lt;strong&gt;October 26, 2023:&lt;/strong&gt; Initial version of the integrations guide published.&lt;/li&gt;&lt;/ul&gt;` },
        ]
      },
    ]
  },
];
&nbsp;
&nbsp;
export const INITIAL_PUBLIC_TEMPLATES: Template[] = [
  {
    id: 'nda-1',
    title: 'Mutual Non-Disclosure Agreement (NDA)',
    description: 'A standard agreement for two parties who will be sharing confidential information with each other.',
    category: 'Business Agreements',
    prompt: 'Draft a standard, mutual Non-Disclosure Agreement (NDA) between two parties. Include clauses for definition of confidential information, obligations of receiving party, exclusions, term, and jurisdiction.',
    requiredPlan: 'Registered User',
  },
  {
    id: 'freelance-1',
    title: 'Freelance Graphic Design Contract',
    description: 'A contract for hiring a freelance graphic designer, covering scope of work, payment terms, and intellectual property.',
    category: 'Freelance &amp; Consulting',
    prompt: 'Create a freelance contract for a graphic designer. The client will pay a fixed fee. The contract should cover project scope, deliverables, payment schedule, intellectual property rights transfer upon final payment, and a section on client revisions.',
    requiredPlan: 'Registered User',
  },
  {
    id: 'lease-1',
    title: 'Residential Lease Agreement',
    description: 'A basic agreement for leasing a residential property, outlining terms, rent, and responsibilities.',
    category: 'Real Estate',
    prompt: 'Generate a basic residential lease agreement. It should include fields for landlord and tenant names, property address, lease term, monthly rent amount, security deposit, and rules regarding property use.',
    requiredPlan: 'Registered User',
  },
  {
    id: 'privacy-1',
    title: 'Website Privacy Policy',
    description: 'A standard privacy policy for a website that collects user data, compliant with common regulations.',
    category: 'Website &amp; Online',
    prompt: 'Draft a comprehensive website privacy policy. It must cover what data is collected (e.g., personal information, cookies), how it is used, data sharing practices, user rights, and contact information for privacy-related inquiries.',
    requiredPlan: 'Registered User',
  },
  {
    id: 'saas-1',
    title: 'SaaS Terms of Service',
    description: 'A comprehensive Terms of Service agreement for a Software-as-a-Service (SaaS) product.',
    category: 'Business Agreements',
    prompt: 'Generate a detailed Terms of Service (ToS) for a SaaS application. Include clauses on user accounts, subscription terms, acceptable use policy, intellectual property ownership, limitation of liability, and termination.',
    requiredPlan: 'Premium',
  },
  {
    id: 'consulting-1',
    title: 'Consulting Agreement',
    description: 'A detailed agreement for providing professional consulting services to a client.',
    category: 'Freelance &amp; Consulting',
    prompt: 'Create a professional consulting agreement. Specify the scope of services, consultant and client responsibilities, compensation structure (retainer plus hourly), confidentiality, and term of the agreement.',
    requiredPlan: 'Premium',
  },
  {
    id: 'employment-1',
    title: 'Employment Offer Letter',
    description: 'A formal offer of employment for a new full-time employee, including salary, benefits, and start date.',
    category: 'Human Resources',
    prompt: 'Draft a formal employment offer letter for a full-time salaried position. Include sections for job title, duties, start date, salary, bonus structure, benefits overview (health insurance, 401k), and an at-will employment statement.',
    requiredPlan: 'Registered User',
  },
  {
    id: 'photo-release-1',
    title: 'Photography Model Release Form',
    description: 'A form granting a photographer the right to use photos of a model for commercial purposes.',
    category: 'Creative',
    prompt: 'Generate a photography model release form. The model grants the photographer rights to use their likeness in photos for any lawful purpose, including advertising. Include fields for model name, photographer name, date, and signatures.',
    requiredPlan: 'Registered User',
  },
  {
    id: 'commercial-lease-1',
    title: 'Commercial Lease Agreement',
    description: 'An agreement for leasing commercial property, such as an office or retail space.',
    category: 'Real Estate',
    prompt: 'Draft a commercial lease agreement for an office space. Include clauses for lease term, base rent, operating expense responsibilities (Triple Net Lease), permitted use of premises, maintenance obligations, and insurance requirements.',
    requiredPlan: 'Premium',
  },
  {
    id: 'influencer-1',
    title: 'Influencer Marketing Agreement',
    description: 'A contract between a brand and a social media influencer for a marketing campaign.',
    category: 'Creative',
    prompt: 'Create an influencer marketing agreement. The brand will provide free products and a flat fee for a specific number of social media posts. The contract should define the content requirements, posting schedule, FTC disclosure guidelines, content ownership, and payment terms.',
    requiredPlan: 'Premium',
  },
   {
    id: 'mna-1',
    title: 'Merger and Acquisition (M&amp;A) LOI',
    description: 'A non-binding Letter of Intent for a potential merger or acquisition between two companies.',
    category: 'Business Agreements',
    prompt: 'Draft a non-binding Letter of Intent (LOI) for a potential acquisition of a smaller tech company by a larger one. Include a proposed purchase price, structure of the deal (stock purchase), key conditions, a due diligence period, and an exclusivity (no-shop) clause.',
    requiredPlan: 'Premium',
  },
   {
    id: 'dev-contract-1',
    title: 'Software Development Agreement',
    description: 'A contract for building a custom software application for a client.',
    category: 'Freelance &amp; Consulting',
    prompt: 'Generate a detailed software development agreement. The project will be billed on a time and materials basis. The contract must include: development services, project timeline and milestones, acceptance testing procedures, intellectual property rights, confidentiality, and post-launch support terms.',
    requiredPlan: 'Premium',
  },
];
&nbsp;
export const PRECONFIGURED_WORKFLOWS: Omit&lt;WorkflowTemplate, 'id'&gt;[] = [
    {
        name: 'Sales Contract Approval (&gt; $10k)',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'trigger-1', type: 'trigger', position: { x: 275, y: 50 }, data: { label: 'On Approval Request', triggerType: 'approval-requested' } },
            { id: 'condition-1', type: 'condition', position: { x: 275, y: 200 }, data: { label: 'Value &gt; $10,000', conditionField: 'value', conditionOperator: '&gt;', conditionValue: 10000 } },
            { id: 'approval-manager', type: 'approval', position: { x: 50, y: 350 }, data: { label: 'Sales Manager Approval', approverEmail: '<EMAIL>' } },
            { id: 'approval-legal-1', type: 'approval', position: { x: 275, y: 500 }, data: { label: 'Legal Approval', approverEmail: '<EMAIL>' } },
            { id: 'approval-legal-2', type: 'approval', position: { x: 500, y: 350 }, data: { label: 'Legal Approval', approverEmail: '<EMAIL>' } },
        ],
        edges: [
            { id: 'e-t1-c1', source: 'trigger-1', target: 'condition-1' },
            { id: 'e-c1-am', source: 'condition-1', sourceHandle: 'true', target: 'approval-manager' },
            { id: 'e-c1-al2', source: 'condition-1', sourceHandle: 'false', target: 'approval-legal-2' },
            { id: 'e-am-al1', source: 'approval-manager', target: 'approval-legal-1' },
        ],
    },
    {
        name: 'Vendor Onboarding',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'trigger-1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Document Creation', triggerType: 'document-created' } },
            { id: 'approval-1', type: 'approval', position: { x: 50, y: 200 }, data: { label: 'Security Team Approval', approverEmail: '<EMAIL>' } },
            { id: 'update-1', type: 'update-field', position: { x: 50, y: 350 }, data: { label: 'Set Status to In Review', updateField: 'status', updateValue: 'in-review' } },
        ],
        edges: [
            { id: 'e-t1-a1', source: 'trigger-1', target: 'update-1' },
            { id: 'e-u1-a1', source: 'update-1', target: 'approval-1' },
        ],
    },
     {
        name: 'Auto-Archive Completed Contracts',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'trigger-1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Status Change', triggerType: 'status-changed' } },
            { id: 'condition-1', type: 'condition', position: { x: 50, y: 200 }, data: { label: 'Status is "Completed"', conditionField: 'status', conditionOperator: 'is', conditionValue: 'completed' } },
            { id: 'delay-1', type: 'delay', position: { x: 50, y: 350 }, data: { label: 'Wait for 30 days', delayDays: 30 } },
            { id: 'update-1', type: 'update-field', position: { x: 50, y: 500 }, data: { label: 'Set Status to Archived', updateField: 'status', updateValue: 'archived' } },
        ],
        edges: [
            { id: 'e-t1-c1', source: 'trigger-1', target: 'condition-1' },
            { id: 'e-c1-d1', source: 'condition-1', sourceHandle: 'true', target: 'delay-1' },
            { id: 'e-d1-u1', source: 'delay-1', target: 'update-1' },
        ],
    },
    {
        name: 'HR Offer Letter Process',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'n1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Approval Request', triggerType: 'approval-requested' } },
            { id: 'n2', type: 'condition', position: { x: 50, y: 200 }, data: { label: "If Folder is 'HR Offers'", conditionField: 'folderId', conditionOperator: 'is', conditionValue: 'folder_hr_offers_placeholder' } },
            { id: 'n3', type: 'approval', position: { x: 50, y: 350 }, data: { label: 'Hiring Manager Approval', approverEmail: '<EMAIL>' } },
            { id: 'n4', type: 'approval', position: { x: 50, y: 500 }, data: { label: 'HR Director Approval', approverEmail: '<EMAIL>' } },
        ],
        edges: [
            { id: 'e1-2', source: 'n1', target: 'n2' },
            { id: 'e2-3', source: 'n2', sourceHandle: 'true', target: 'n3' },
            { id: 'e3-4', source: 'n3', target: 'n4' },
        ],
    },
    {
        name: 'High-Value Sales Alert',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'n1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Document Creation', triggerType: 'document-created' } },
            { id: 'n2', type: 'condition', position: { x: 50, y: 200 }, data: { label: 'If Value &gt; $50,000', conditionField: 'value', conditionOperator: '&gt;', conditionValue: 50000 } },
            { id: 'n3', type: 'notification', position: { x: 50, y: 350 }, data: { label: 'Notify Sales Director', notificationRecipient: '<EMAIL>' } },
        ],
        edges: [
            { id: 'e1-2', source: 'n1', target: 'n2' },
            { id: 'e2-3', source: 'n2', sourceHandle: 'true', target: 'n3' },
        ],
    },
    {
        name: 'NDA Auto-Filing &amp; Sharing',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'n1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Document Creation', triggerType: 'document-created' } },
            { id: 'n2', type: 'condition', position: { x: 50, y: 200 }, data: { label: "If Name contains 'NDA'", conditionField: 'name', conditionOperator: 'contains', conditionValue: 'NDA' } },
            { id: 'n3', type: 'move-to-folder', position: { x: 50, y: 350 }, data: { label: "Move to 'NDAs' Folder", targetFolderId: 'folder_nda_placeholder' } },
            { id: 'n4', type: 'add-collaborator', position: { x: 50, y: 500 }, data: { label: 'Share with Legal', collaboratorEmail: '<EMAIL>', collaboratorPermission: 'view' } },
        ],
        edges: [
            { id: 'e1-2', source: 'n1', target: 'n2' },
            { id: 'e2-3', source: 'n2', sourceHandle: 'true', target: 'n3' },
            { id: 'e3-4', source: 'n3', target: 'n4' },
        ],
    },
    {
        name: 'Stale Draft Reminder',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'n1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Document Creation', triggerType: 'document-created' } },
            { id: 'n2', type: 'delay', position: { x: 50, y: 200 }, data: { label: 'Wait for 14 Days', delayDays: 14 } },
            { id: 'n3', type: 'condition', position: { x: 50, y: 350 }, data: { label: "If Status is 'draft'", conditionField: 'status', conditionOperator: 'is', conditionValue: 'draft' } },
            { id: 'n4', type: 'notification', position: { x: 50, y: 500 }, data: { label: 'Notify Document Owner', notificationRecipient: '<EMAIL>' } },
        ],
        edges: [
            { id: 'e1-2', source: 'n1', target: 'n2' },
            { id: 'e2-3', source: 'n2', target: 'n3' },
            { id: 'e3-4', source: 'n3', sourceHandle: 'true', target: 'n4' },
        ],
    },
    {
        name: 'Procurement Contract Review',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'n1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Approval Request', triggerType: 'approval-requested' } },
            { id: 'n2', type: 'approval', position: { x: 50, y: 200 }, data: { label: 'Procurement Head Approval', approverEmail: '<EMAIL>' } },
            { id: 'n3', type: 'approval', position: { x: 50, y: 350 }, data: { label: 'Legal Approval', approverEmail: '<EMAIL>' } },
        ],
        edges: [
            { id: 'e1-2', source: 'n1', target: 'n2' },
            { id: 'e2-3', source: 'n2', target: 'n3' },
        ]
    },
    {
        name: 'Marketing Agreement Auto-Tag',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'n1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Document Creation', triggerType: 'document-created' } },
            { id: 'n2', type: 'condition', position: { x: 50, y: 200 }, data: { label: "If Name contains 'Marketing'", conditionField: 'name', conditionOperator: 'contains', conditionValue: 'Marketing' } },
            { id: 'n3', type: 'add-tag', position: { x: 50, y: 350 }, data: { label: "Add 'Marketing' Tag", tagName: 'Marketing' } },
        ],
        edges: [
            { id: 'e1-2', source: 'n1', target: 'n2' },
            { id: 'e2-3', source: 'n2', sourceHandle: 'true', target: 'n3' },
        ],
    },
    {
        name: 'Client Onboarding Kickoff',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'n1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Status Change', triggerType: 'status-changed' } },
            { id: 'n2', type: 'condition', position: { x: 50, y: 200 }, data: { label: "If Status is 'completed'", conditionField: 'status', conditionOperator: 'is', conditionValue: 'completed' } },
            { id: 'n3', type: 'add-collaborator', position: { x: 50, y: 350 }, data: { label: 'Add Project Manager', collaboratorEmail: '<EMAIL>', collaboratorPermission: 'edit' } },
            { id: 'n4', type: 'notification', position: { x: 50, y: 500 }, data: { label: 'Notify Kickoff Team', notificationRecipient: '<EMAIL>' } },
        ],
        edges: [
            { id: 'e1-2', source: 'n1', target: 'n2' },
            { id: 'e2-3', source: 'n2', sourceHandle: 'true', target: 'n3' },
            { id: 'e3-4', source: 'n3', target: 'n4' },
        ],
    }
];
&nbsp;
export const INITIAL_TERMS_CONTENT = `
&lt;p&gt;Please read these Terms and Conditions ("Terms", "Terms and Conditions") carefully before using the LexiGen application (the "Service") operated by LexiGen ("us", "we", or "our").&lt;/p&gt;
&lt;p&gt;Your access to and use of the Service is conditioned on your acceptance of and compliance with these Terms. These Terms apply to all visitors, users, and others who access or use the Service.&lt;/p&gt;
&lt;p&gt;&lt;strong&gt;By accessing or using the Service you agree to be bound by these Terms. If you disagree with any part of the terms then you may not access the Service.&lt;/strong&gt;&lt;/p&gt;
&lt;h2&gt;1. AI-Generated Content and Legal Disclaimer&lt;/h2&gt;
&lt;p&gt;THE SERVICE USES ARTIFICIAL INTELLIGENCE TO GENERATE DRAFTS OF LEGAL DOCUMENTS AND CLAUSES ("AI CONTENT"). YOU ACKNOWLEDGE AND AGREE TO THE FOLLOWING:&lt;/p&gt;
&lt;ul&gt;
    &lt;li&gt;&lt;strong&gt;Not Legal Advice:&lt;/strong&gt; AI Content is provided for informational purposes only and does not constitute legal advice. The Service is not a law firm and does not provide legal services.&lt;/li&gt;
    &lt;li&gt;&lt;strong&gt;No Attorney-Client Relationship:&lt;/strong&gt; Your use of the Service does not create an attorney-client relationship between you and LexiGen.&lt;/li&gt;
    &lt;li&gt;&lt;strong&gt;Consult a Qualified Attorney:&lt;/strong&gt; The AI Content is a starting point and may not be appropriate for your specific situation. It may contain errors, omissions, or be outdated. You are solely responsible for reviewing, editing, and ensuring the legal accuracy and enforceability of any document you create. We strongly recommend you consult with a qualified, licensed attorney before using or relying on any AI Content.&lt;/li&gt;
    &lt;li&gt;&lt;strong&gt;No Warranty:&lt;/strong&gt; We make no warranties or representations regarding the accuracy, completeness, or suitability of the AI Content for any purpose.&lt;/li&gt;
&lt;/ul&gt;
&lt;h2&gt;2. User Accounts&lt;/h2&gt;
&lt;p&gt;When you create an account with us, you must provide information that is accurate, complete, and current at all times. Failure to do so constitutes a breach of the Terms, which may result in immediate termination of your account on our Service.&lt;/p&gt;
&lt;p&gt;You are responsible for safeguarding the password that you use to access the Service and for any activities or actions under your password. You agree not to disclose your password to any third party.&lt;/p&gt;
&lt;h2&gt;3. Subscriptions and Payments&lt;/h2&gt;
&lt;p&gt;Some parts of the Service are billed on a subscription basis ("Subscription(s)"). You will be billed in advance on a recurring and periodic basis ("Billing Cycle").&lt;/p&gt;
&lt;p&gt;We reserve the right to change our subscription plans or adjust pricing for our services in any manner and at any time as we may determine in our sole and absolute discretion.&lt;/p&gt;
&lt;h2&gt;4. Intellectual Property&lt;/h2&gt;
&lt;p&gt;The Service and its original content (excluding content provided by users), features, and functionality are and will remain the exclusive property of LexiGen. The user retains all ownership rights to the final documents they create using the Service, subject to the disclaimers in Section 1.&lt;/p&gt;
&lt;h2&gt;5. Prohibited Uses&lt;/h2&gt;
&lt;p&gt;You agree not to use the Service for any unlawful purpose or to solicit others to perform or participate in any unlawful acts. You are prohibited from using the site or its content to infringe upon or violate our intellectual property rights or the intellectual property rights of others.&lt;/p&gt;
&lt;h2&gt;6. Limitation Of Liability&lt;/h2&gt;
&lt;p&gt;IN NO EVENT SHALL LEXIGEN, NOR ITS DIRECTORS, EMPLOYEES, PARTNERS, AGENTS, SUPPLIERS, OR AFFILIATES, BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL OR PUNITIVE DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS, DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES, RESULTING FROM YOUR ACCESS TO OR USE OF OR INABILITY TO ACCESS OR USE THE SERVICE OR ANY AI CONTENT.&lt;/p&gt;
&lt;h2&gt;7. Termination&lt;/h2&gt;
&lt;p&gt;We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.&lt;/p&gt;
&lt;h2&gt;8. Changes To Terms&lt;/h2&gt;
&lt;p&gt;We reserve the right, at our sole discretion, to modify or replace these Terms at any time. We will provide notice of any changes by posting the new Terms and Conditions on this page.&lt;/p&gt;
&lt;h2&gt;Contact Us&lt;/h2&gt;
&lt;p&gt;If you have any questions about these Terms, please contact <NAME_EMAIL>.&lt;/p&gt;
`;
&nbsp;
export const INITIAL_PRIVACY_CONTENT = `
&lt;p&gt;This Privacy Policy describes Our policies and procedures on the collection, use and disclosure of Your information when You use the Service and tells You about Your privacy rights and how the law protects You.&lt;/p&gt;
&lt;p&gt;We use Your Personal data to provide and improve the Service. By using the Service, You agree to the collection and use of information in accordance with this Privacy Policy.&lt;/p&gt;
&lt;h2&gt;Collecting and Using Your Personal Data&lt;/h2&gt;
&lt;h3&gt;Types of Data Collected&lt;/h3&gt;
&lt;h4&gt;Personal Data&lt;/h4&gt;
&lt;p&gt;While using Our Service, We may ask You to provide Us with certain personally identifiable information that can be used to contact or identify You. Personally identifiable information may include, but is not limited to:&lt;/p&gt;
&lt;ul&gt;
    &lt;li&gt;Email address&lt;/li&gt;
    &lt;li&gt;First name and last name&lt;/li&gt;
    &lt;li&gt;Usage Data&lt;/li&gt;
&lt;/ul&gt;
&lt;h4&gt;Usage Data&lt;/h4&gt;
&lt;p&gt;Usage Data is collected automatically when using the Service. Usage Data may include information such as Your Device's Internet Protocol address (e.g. IP address), browser type, browser version, the pages of our Service that You visit, the time and date of Your visit, the time spent on those pages, unique device identifiers and other diagnostic data.&lt;/p&gt;
&lt;h3&gt;Use of Your Personal Data&lt;/h3&gt;
&lt;p&gt;The Company may use Personal Data for the following purposes:&lt;/p&gt;
&lt;ul&gt;
    &lt;li&gt;To provide and maintain our Service, including to monitor the usage of our Service.&lt;/li&gt;
    &lt;li&gt;To manage Your Account: to manage Your registration as a user of the Service.&lt;/li&gt;
    &lt;li&gt;For the performance of a contract: the development, compliance and undertaking of the purchase contract for the products, items or services You have purchased or of any other contract with Us through the Service.&lt;/li&gt;
    &lt;li&gt;To contact You: To contact You by email regarding updates or informative communications related to the functionalities, products or contracted services, including the security updates, when necessary or reasonable for their implementation.&lt;/li&gt;
&lt;/ul&gt;
&lt;h2&gt;Security of Your Personal Data&lt;/h2&gt;
&lt;p&gt;The security of Your Personal Data is important to Us, but remember that no method of transmission over the Internet, or method of electronic storage is 100% secure. While We strive to use commercially acceptable means to protect Your Personal Data, We cannot guarantee its absolute security.&lt;/p&gt;
&lt;h2&gt;Changes to this Privacy Policy&lt;/h2&gt;
&lt;p&gt;We may update Our Privacy Policy from time to time. We will notify You of any changes by posting the new Privacy Policy on this page.&lt;/p&gt;
&lt;h2&gt;Contact Us&lt;/h2&gt;
&lt;p&gt;If you have any questions about this Privacy Policy, you can contact us at: <EMAIL>&lt;/p&gt;
`;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    