import type { NextFunction, Response } from 'express';
import { getUserClient } from '../supabaseClient';
import { AuthRequest, getAccessToken } from './auth';

async function getPlanName(req: AuthRequest): Promise<string | null> {
  try {
    const supa = getUserClient(getAccessToken(req));
    const { data: auth } = await supa.auth.getUser();
    const userId = auth.user?.id;
    if (!userId) {return null;}
    const { data, error } = await supa.from('profiles').select('plan_name').eq('id', userId).single();
    if (error) {
      console.error('Error fetching user plan:', error);
      return 'Registered User';
    }
    return (data?.plan_name as string) || 'Registered User';
  } catch (error) {
    console.error('Error in getPlanName:', error);
    return 'Registered User';
  }
}

export async function requirePremium(req: AuthRequest, res: Response, next: NextFunction) {
  // Bypass for testing
  if (process.env.TEST_BYPASS_AUTH === '1') {
    return next();
  }

  try {
    const plan = await getPlanName(req);
    if (!plan || plan === 'Registered User') {
      return res.status(403).json({ error: 'Premium plan required' });
    }
    next();
  } catch (err) {
    console.error('Error in requirePremium middleware:', err);
    // Return 403 instead of 500 to avoid exposing internal errors
    return res.status(403).json({ error: 'Premium plan required' });
  }
}

export async function requireEnterprise(req: AuthRequest, res: Response, next: NextFunction) {
  // Bypass for testing
  if (process.env.TEST_BYPASS_AUTH === '1') {
    return next();
  }

  try {
    const plan = await getPlanName(req);
    if (plan !== 'Enterprise') {return res.status(403).json({ error: 'Enterprise plan required' });}
    next();
  } catch (err) {
    next(err);
  }
}
