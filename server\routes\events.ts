import { Router, Response } from 'express';
import { requireAuth, AuthRequest } from '../middleware/auth';
import { subscribeToDocumentStatusChanges, DocumentStatusEvent } from '../../lib/eventBus';

const router = Router();

type ClientMap = Map<string, Set<Response>>;
const clientsByUser: ClientMap = new Map();

function getClientBucket(userId: string): Set<Response> {
  let bucket = clientsByUser.get(userId);
  if (!bucket) {
    bucket = new Set<Response>();
    clientsByUser.set(userId, bucket);
  }
  return bucket;
}

function removeClient(userId: string, res: Response): void {
  const bucket = clientsByUser.get(userId);
  if (!bucket) {
    return;
  }

  bucket.delete(res);
  if (bucket.size === 0) {
    clientsByUser.delete(userId);
  }
}

function writeToClient(userId: string, res: Response, data: string): void {
  if (res.writableEnded) {
    removeClient(userId, res);
    return;
  }

  try {
    res.write(data);
  } catch {
    removeClient(userId, res);
  }
}

subscribeToDocumentStatusChanges((payload: DocumentStatusEvent) => {
  const { audienceUserId, ...eventPayload } = payload;
  const data = `event: documentStatusChanged\n` +
    `data: ${JSON.stringify(eventPayload)}\n\n`;

  const bucket = clientsByUser.get(audienceUserId);
  if (!bucket || bucket.size === 0) {
    return;
  }

  for (const res of bucket) {
    writeToClient(audienceUserId, res, data);
  }
});

router.get('/', requireAuth, (req: AuthRequest, res) => {
  const userId = req.user?.id;
  if (!userId) {
    res.status(401).end();
    return;
  }

  res.set({
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    Connection: 'keep-alive',
    'X-Accel-Buffering': 'no',
  });
  res.flushHeaders?.();
  res.write('retry: 5000\n\n');

  const bucket = getClientBucket(userId);
  bucket.add(res);

  const heartbeat = setInterval(() => {
    res.write(': heartbeat\n\n');
  }, 30000);

  const cleanup = () => {
    clearInterval(heartbeat);
    removeClient(userId, res);
  };

  req.on('close', cleanup);
  req.on('end', cleanup);
  req.on('error', cleanup);
  res.on('error', cleanup);
});

export default router;
