
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/HelpCenterPage.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> HelpCenterPage.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">45% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>54/120</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">50% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>5/10</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">28.57% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>2/7</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">45% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>54/120</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">&nbsp;
import React, { useState, useMemo, useEffect } from 'react';
import { HELP_TOPICS } from '../constants';
import { SearchIcon, ArrowLeftIcon } from './Icons';
import { cn } from '../lib/utils';
import { HelpTopic, HelpCategory } from '../types';
import { sanitizeHtml } from '../lib/sanitize';
// FIX: Import the 'Button' component to resolve the 'Cannot find name' error.
import { Button } from './ui/Button';
&nbsp;
interface HelpCenterPageProps {
  initialTopicId?: string | null;
}
&nbsp;
const HelpCenterPage: React.FC&lt;HelpCenterPageProps&gt; = ({ initialTopicId }) =&gt; {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState&lt;HelpCategory | null&gt;(null);
    const [activeTopic, setActiveTopic] = useState&lt;HelpTopic | null&gt;(null);
&nbsp;
    // Effect to handle deep linking to a specific topic
    useEffect(() =&gt; {
        if (initialTopicId) <span class="branch-0 cbranch-no" title="branch not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            for (const category of HELP_TOPICS) {</span>
<span class="cstat-no" title="statement not covered" >                const topic = category.topics.find(t =&gt; t.id === initialTopicId);</span>
<span class="cstat-no" title="statement not covered" >                if (topic) {</span>
<span class="cstat-no" title="statement not covered" >                    setSelectedCategory(category);</span>
<span class="cstat-no" title="statement not covered" >                    setActiveTopic(topic);</span>
<span class="cstat-no" title="statement not covered" >                    break;</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
    }, [initialTopicId]);
&nbsp;
    const filteredHelpTopics = useMemo((): HelpCategory[] =&gt; {
        if (!searchTerm.trim()) {
            return HELP_TOPICS;
<span class="branch-0 cbranch-no" title="branch not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        const lowercasedFilter = searchTerm.toLowerCase();</span>
        
<span class="cstat-no" title="statement not covered" >        const _isMatch = <span class="fstat-no" title="function not covered" >(content: string | Array&lt;{type: string; value: string}&gt;): boolean =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >            if (typeof content === 'string') {return content.toLowerCase().includes(lowercasedFilter);}</span>
<span class="cstat-no" title="statement not covered" >            if (Array.isArray(content)) {return content.some(block =&gt; block.type === 'text' &amp;&amp; block.value.toLowerCase().includes(lowercasedFilter));}</span>
<span class="cstat-no" title="statement not covered" >            return false;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        return HELP_TOPICS</span>
<span class="cstat-no" title="statement not covered" >            .map(category =&gt; ({</span>
<span class="cstat-no" title="statement not covered" >                ...category,</span>
<span class="cstat-no" title="statement not covered" >                topics: category.topics.filter(topic =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                    const titleMatch = topic.title.toLowerCase().includes(lowercasedFilter);</span>
<span class="cstat-no" title="statement not covered" >                    const contentMatch = Array.isArray(topic.content) </span>
<span class="cstat-no" title="statement not covered" >                        ? topic.content.some(block =&gt; block.type === 'text' &amp;&amp; block.value?.toLowerCase().includes(lowercasedFilter))</span>
<span class="cstat-no" title="statement not covered" >                        : typeof topic.content === 'string' &amp;&amp; topic.content.toLowerCase().includes(lowercasedFilter);</span>
<span class="cstat-no" title="statement not covered" >                    return titleMatch || contentMatch;</span>
<span class="cstat-no" title="statement not covered" >                })</span>
<span class="cstat-no" title="statement not covered" >            }))</span>
<span class="cstat-no" title="statement not covered" >            .filter(category =&gt; category.topics.length &gt; 0);</span>
    }, [searchTerm]);
&nbsp;
    const handleSelectCategory = <span class="fstat-no" title="function not covered" >(category: HelpCategory) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        setSelectedCategory(category);</span>
<span class="cstat-no" title="statement not covered" >        setActiveTopic(category.topics[0]);</span>
<span class="cstat-no" title="statement not covered" >    };</span>
&nbsp;
    const HubView = () =&gt; (
        &lt;div className="max-w-4xl mx-auto"&gt;
            &lt;div className="text-center mb-12"&gt;
                &lt;h1 className="text-4xl font-extrabold text-zinc-900 dark:text-white"&gt;How can we help?&lt;/h1&gt;
                &lt;div className="relative mt-6 max-w-lg mx-auto"&gt;
                    &lt;div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4"&gt;
                        &lt;SearchIcon className="h-5 w-5 text-zinc-400" /&gt;
                    &lt;/div&gt;
                    &lt;input
                        type="search"
                        placeholder="Search for articles..."
                        value={searchTerm}
                        onChange={<span class="fstat-no" title="function not covered" >(e) =&gt; setSearchTerm(e.target.value)}</span>
                        className="block w-full rounded-full border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-11 focus:border-brand-500 focus:ring-brand-500 text-lg p-4"
                    /&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"&gt;
                {filteredHelpTopics.map(category =&gt; (
                    &lt;button key={category.category} onClick={<span class="fstat-no" title="function not covered" >() =&gt; handleSelectCategory(category)} c</span>lassName="p-6 bg-white dark:bg-zinc-900 rounded-xl border border-zinc-200 dark:border-zinc-800 text-left hover:border-brand-500 hover:shadow-lg transition-all transform hover:-translate-y-1"&gt;
                        &lt;category.icon className="w-8 h-8 text-brand-600 dark:text-brand-400 mb-3" /&gt;
                        &lt;h2 className="font-semibold text-zinc-800 dark:text-zinc-200"&gt;{category.category}&lt;/h2&gt;
                        &lt;p className="text-sm text-zinc-500 dark:text-zinc-400 mt-1"&gt;{category.topics.length} articles&lt;/p&gt;
                    &lt;/button&gt;
                ))}
            &lt;/div&gt;
            {<span class="branch-0 cbranch-no" title="branch not covered" >searchTerm &amp;&amp; filteredHelpTopics.length === <span class="branch-0 cbranch-no" title="branch not covered" >0 &amp;&amp; (</span></span>
<span class="cstat-no" title="statement not covered" >                 &lt;p className="text-center text-zinc-500"&gt;No articles found for "{searchTerm}".&lt;/p&gt;</span>
            )}
        &lt;/div&gt;
    );
&nbsp;
    const ArticleView = <span class="fstat-no" title="function not covered" >() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (!selectedCategory || !activeTopic) {return null;}</span>
        
<span class="cstat-no" title="statement not covered" >        return (</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex h-full gap-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;aside className="w-72 flex-shrink-0 bg-white dark:bg-zinc-950 p-4 rounded-xl border border-zinc-200 dark:border-zinc-800 flex flex-col"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;Button variant="ghost" onClick={() =&gt; setSelectedCategory(null)} className="mb-4 text-zinc-600 dark:text-zinc-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;ArrowLeftIcon className="w-4 h-4 mr-2"/&gt;</span>
                        All Categories
<span class="cstat-no" title="statement not covered" >                    &lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;h2 className="px-2 font-semibold text-zinc-800 dark:text-zinc-200"&gt;{selectedCategory.category}&lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;nav className="flex-1 overflow-y-auto mt-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;ul className="space-y-1"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            {selectedCategory.topics.map(topic =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                                &lt;li key={topic.id}&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;a href="#" onClick={(e) =&gt; { e.preventDefault(); setActiveTopic(topic); }} className={cn('block p-2 text-sm rounded-md', activeTopic.id === topic.id ? 'bg-brand-50 text-brand-700 font-medium dark:bg-brand-900/50 dark:text-brand-300' : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800')}&gt;</span>
<span class="cstat-no" title="statement not covered" >                                        {topic.title}</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;/a&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >                            ))}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/ul&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/nav&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/aside&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;main className="flex-1 bg-white dark:bg-zinc-950 p-6 md:p-8 rounded-xl border border-zinc-200 dark:border-zinc-800 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;article className="prose prose-zinc dark:prose-invert max-w-none"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        {Array.isArray(activeTopic.content) ? (</span>
<span class="cstat-no" title="statement not covered" >                            activeTopic.content.map((block, index) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                            if (block.type === 'text') {return &lt;div key={index} dangerouslySetInnerHTML={{ __html: sanitizeHtml(block.value) }} /&gt;;}</span>
<span class="cstat-no" title="statement not covered" >                            if (block.type === 'screenshot') { const Screenshot = block.component; return &lt;div className="my-8" key={index}&gt;&lt;Screenshot /&gt;&lt;/div&gt;; }</span>
<span class="cstat-no" title="statement not covered" >                            return null;</span>
<span class="cstat-no" title="statement not covered" >                            })</span>
                        ) : (
<span class="cstat-no" title="statement not covered" >                            &lt;div dangerouslySetInnerHTML={{ __html: sanitizeHtml(activeTopic.content) }} /&gt;</span>
                        )}
<span class="cstat-no" title="statement not covered" >                    &lt;/article&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/main&gt;</span>
<span class="cstat-no" title="statement not covered" >                 &lt;style&gt;{`</span>
                    .prose h2 { font-size: 1.5rem; font-weight: 700; margin-top: 2em; margin-bottom: 1em; padding-bottom: 0.3em; border-bottom: 1px solid #e4e4e7; }
                    .prose h4 { font-size: 1.1rem; font-weight: 600; margin-top: 1.5em; margin-bottom: 0.5em; }
                    .dark .prose h2 { border-bottom-color: #3f3f46; }
                    .prose p, .prose li { color: #3f3f46; line-height: 1.7; }
                    .dark .prose p, .dark .prose li { color: #d4d4d8; }
                    .prose ul { list-style-type: disc; padding-left: 1.5rem; }
                    .prose ol { list-style-type: decimal; padding-left: 1.5rem; }
                    .prose code { background-color: #f4f4f5; padding: 0.2em 0.4em; margin: 0; font-size: 85%; border-radius: 6px; }
                    .dark .prose code { background-color: #27272a; }
                    .prose pre { background-color: #f4f4f5; padding: 1rem; border-radius: 8px; overflow-x: auto; }
                    .dark .prose pre { background-color: #18181b; }
                    .prose pre code { background-color: transparent; padding: 0; }
<span class="cstat-no" title="statement not covered" >               `}&lt;/style&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
        );
<span class="cstat-no" title="statement not covered" >    };</span>
    
    return (
        &lt;div className="p-4 sm:p-6 lg:p-8 h-full"&gt;
            {<span class="branch-0 cbranch-no" title="branch not covered" >selectedCategory ? &lt;ArticleView /&gt; : &lt;</span>HubView /&gt;}
        &lt;/div&gt;
    );
};
&nbsp;
export default HelpCenterPage;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    