-- Fix Critical Workflow Issues Migration
-- This migration addresses the schema gaps and missing functionality identified in the technical investigation

-- 1. Ensure version_type column exists in document_versions table (from previous investigation)
ALTER TABLE public.document_versions 
ADD COLUMN IF NOT EXISTS version_type text NOT NULL DEFAULT 'auto' 
CHECK (version_type IN ('auto', 'manual'));

-- Create index for version_type for better query performance
CREATE INDEX IF NOT EXISTS document_versions_version_type_idx 
ON public.document_versions(version_type);

-- Update existing records to have default version_type
UPDATE public.document_versions 
SET version_type = 'auto' 
WHERE version_type IS NULL;

-- 2. Ensure activity_logs table has proper indexes for performance
CREATE INDEX IF NOT EXISTS activity_logs_document_timestamp_composite_idx 
ON public.activity_logs(document_id, timestamp DESC);

-- 3. Add any missing RLS policies for activity logs (ensure they exist)
DO $$
BEGIN
    -- Check if the activity_logs_access policy exists, if not create it
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'activity_logs' 
        AND policyname = 'activity_logs_access'
    ) THEN
        CREATE POLICY activity_logs_access ON public.activity_logs FOR ALL USING (
            document_id IS NULL OR EXISTS(
                SELECT 1 FROM public.documents d 
                WHERE d.id = activity_logs.document_id 
                AND d.user_id = auth.uid()
            )
        ) WITH CHECK (
            document_id IS NULL OR EXISTS(
                SELECT 1 FROM public.documents d 
                WHERE d.id = activity_logs.document_id 
                AND d.user_id = auth.uid()
            )
        );
    END IF;
END $$;

-- 4. Ensure comment_threads and comments tables have proper indexes
CREATE INDEX IF NOT EXISTS comment_threads_document_created_idx 
ON public.comment_threads(document_id, created_at DESC);

CREATE INDEX IF NOT EXISTS comments_thread_created_idx 
ON public.comments(thread_id, created_at ASC);

-- 5. Add composite index for document queries with activity logs
CREATE INDEX IF NOT EXISTS documents_user_updated_idx 
ON public.documents(user_id, updated_at DESC);

-- 6. Verify all critical tables exist and have proper structure
DO $$
BEGIN
    -- Verify activity_logs table structure
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'activity_logs' 
        AND column_name = 'document_id'
    ) THEN
        RAISE EXCEPTION 'Migration failed: activity_logs table missing document_id column';
    END IF;
    
    -- Verify comment_threads table structure
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'comment_threads' 
        AND column_name = 'text_selection'
    ) THEN
        RAISE EXCEPTION 'Migration failed: comment_threads table missing text_selection column';
    END IF;
    
    -- Verify document_versions table structure
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'document_versions' 
        AND column_name = 'version_type'
    ) THEN
        RAISE EXCEPTION 'Migration failed: document_versions table missing version_type column';
    END IF;
    
    RAISE NOTICE 'Migration completed successfully: All critical workflow issues addressed';
END $$;
