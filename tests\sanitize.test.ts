import { describe, it, expect } from 'vitest';
import { sanitizeHtml } from '../lib/sanitize';

describe('sanitizeHtml', () => {
  it('strips script tags and event handlers', () => {
    const input = `<div onclick="alert('x')">Hi<script>alert('xss')</script></div>`;
    const out = sanitizeHtml(input);
    expect(out).not.toContain('<script>');
    expect(out.toLowerCase()).not.toContain('onclick');
    expect(out).toContain('Hi');
  });

  it('preserves basic formatting', () => {
    const input = '<p><strong>Bold</strong> and <em>em</em></p>';
    const out = sanitizeHtml(input);
    expect(out).toContain('<strong>Bold</strong>');
    expect(out).toContain('<em>em</em>');
  });
});

