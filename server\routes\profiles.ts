import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient, supabaseAdmin } from '../supabaseClient';
import { getFromCache, setCache, deleteFromCache, cacheKeys, CACHE_TTL } from '../lib/redis';

const router = Router();

const notificationPrefsSchema = z.object({
  comments: z.boolean().optional(),
  shares: z.boolean().optional(),
  signatures: z.boolean().optional(),
  team: z.boolean().optional(),
  marketing: z.boolean().optional(),
}).partial();

// Only allow safe self-editable fields via this route
const updateSchema = z.object({
  name: z.string().optional(),
  username: z.string().optional(),
  avatarUrl: z.string().optional(),
  theme: z.enum(['light','dark','system']).optional(),
  jobTitle: z.string().optional(),
  company: z.string().optional(),
  bio: z.string().optional(),
  websiteUrl: z.string().optional(),
  linkedinUrl: z.string().optional(),
  notificationPreferences: notificationPrefsSchema.optional(),
  planName: z.string().optional(),
});

router.get('/me', requireAuth, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data: auth } = await supa.auth.getUser();
  const userId = auth.user?.id;
  const cacheKey = cacheKeys.userProfile(userId);
  
  // Try to get from cache first
  const cachedProfile = await getFromCache(cacheKey);
  if (cachedProfile) {
    return res.json({ profile: cachedProfile });
  }

  const { data, error } = await supa.from('profiles').select('id, name, username, avatar_url, status, plan_name, quota_used, quota_total, theme, job_title, company, bio, website_url, linkedin_url, notification_prefs, created_at').eq('id', userId).single();
  if (error && error.code !== 'PGRST116') {return res.status(500).json({ error: error.message });}
  
  // Cache the result
  if (data) {
    await setCache(cacheKey, data, CACHE_TTL.USER_PROFILE);
  }
  
  res.json({ profile: data || null });
});

router.put('/me', requireAuth, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data: auth } = await supa.auth.getUser();
  const userId = auth.user?.id;
  const parsed = updateSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{
    name: string;
    username: string;
    avatar_url: string;
    theme: string;
    job_title: string;
    company: string;
    bio: string;
    website_url: string;
    linkedin_url: string;
    notification_prefs: Record<string, boolean>;
    plan_name: string;
  }> = {
    name: parsed.data.name,
    username: parsed.data.username,
    avatar_url: parsed.data.avatarUrl,
    theme: parsed.data.theme,
    job_title: parsed.data.jobTitle,
    company: parsed.data.company,
    bio: parsed.data.bio,
    website_url: parsed.data.websiteUrl,
    linkedin_url: parsed.data.linkedinUrl,
    notification_prefs: parsed.data.notificationPreferences,
    plan_name: parsed.data.planName,
  };
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  if (parsed.data.planName) {
    const { data: plan } = await supabaseAdmin
      .from('pricing_plans')
      .select('name')
      .eq('name', parsed.data.planName)
      .single();
    if (!plan) {return res.status(400).json({ error: 'Invalid plan name' });}
  }
  // upsert profile row
  const { data, error } = await supa
    .from('profiles')
    .upsert({ id: userId, ...patch }, { onConflict: 'id' })
    .select('id, name, username, avatar_url, status, plan_name, quota_used, quota_total, theme, job_title, company, bio, website_url, linkedin_url, notification_prefs, created_at')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  
  // Invalidate user profile cache
  await deleteFromCache(cacheKeys.userProfile(userId));
  
  res.json({ profile: data });
});

export default router;
