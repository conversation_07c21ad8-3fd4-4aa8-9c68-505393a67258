import EventEmitter from 'eventemitter3';

export interface DocumentStatusEvent {
  documentId: string;
  documentName: string;
  newStatus: string;
  previousStatus: string;
  documentValue: number;
  clientId: string;
  /**
   * Identifier of the authenticated user that should receive this update.
   */
  audienceUserId: string;
}

export type DocumentStatusBroadcast = Omit<DocumentStatusEvent, 'audienceUserId'>;

type DocumentStatusListener = (payload: DocumentStatusEvent) => void;

interface Events {
  documentStatusChanged: DocumentStatusEvent;
}

type RedisModule = typeof import('redis');
type RedisClientType = ReturnType<RedisModule['createClient']>;

const DOCUMENT_STATUS_CHANNEL = 'events:document-status-changed';
const isServer = typeof window === 'undefined';
const redisEnabled = isServer && typeof process !== 'undefined'
  ? process.env?.REDIS_ENABLED === 'true'
  : false;
const redisUrl = redisEnabled && typeof process !== 'undefined'
  ? process.env?.REDIS_URL || 'redis://127.0.0.1:6379'
  : undefined;

const globalCrypto = typeof globalThis !== 'undefined'
  ? (globalThis.crypto as { randomUUID?: () => string } | undefined)
  : undefined;
const instanceId = globalCrypto?.randomUUID
  ? globalCrypto.randomUUID()
  : Math.random().toString(36).slice(2);

const emitter = new EventEmitter<Events>();

let publisherPromise: Promise<RedisClientType | null> | null = null;
let subscriberPromise: Promise<RedisClientType | null> | null = null;

interface BrokerEnvelope {
  origin: string;
  payload: DocumentStatusEvent;
}

function reportEventBusIssue(message: string, error?: unknown): void {
  if (!redisEnabled || typeof process === 'undefined' || !process.stderr) {
    return;
  }

  const detail = error
    ? `: ${error instanceof Error ? error.stack ?? error.message : String(error)}`
    : '';

  try {
    process.stderr.write(`[eventBus] ${message}${detail}\n`);
  } catch {
    // Ignore write errors; logging is best-effort only
  }
}

async function createRedisClient(): Promise<RedisClientType | null> {
  if (!redisEnabled || !redisUrl) {
    return null;
  }

  const { createClient } = await import('redis');
  const client = createClient({ url: redisUrl });

  client.on('error', (error) => {
    reportEventBusIssue('Redis client error', error);
  });

  try {
    await client.connect();
    return client;
  } catch (error) {
    reportEventBusIssue('Failed to connect to Redis', error);
    try {
      await client.quit();
    } catch {
      // Ignore quit errors from failed connection attempts
    }
    return null;
  }
}

async function getPublisher(): Promise<RedisClientType | null> {
  if (publisherPromise) {
    return publisherPromise;
  }

  publisherPromise = (async () => {
    const client = await createRedisClient();
    if (!client) {
      publisherPromise = null;
      return null;
    }
    return client;
  })();

  publisherPromise.catch(() => {
    publisherPromise = null;
  });

  return publisherPromise;
}

function ensureSubscriber(): void {
  if (subscriberPromise || !redisEnabled || !redisUrl) {
    return;
  }

  subscriberPromise = (async () => {
    const client = await createRedisClient();
    if (!client) {
      subscriberPromise = null;
      return null;
    }

    try {
      await client.subscribe(DOCUMENT_STATUS_CHANNEL, (message) => {
        try {
          const parsed = JSON.parse(message) as BrokerEnvelope;
          if (parsed.origin === instanceId) {
            return;
          }
          emitter.emit('documentStatusChanged', parsed.payload);
        } catch (error) {
          reportEventBusIssue('Failed to process broker payload', error);
        }
      });
      return client;
    } catch (error) {
      reportEventBusIssue('Failed to subscribe to Redis channel', error);
      try {
        await client.quit();
      } catch {
        // Ignore quit errors from failed subscription attempts
      }
      subscriberPromise = null;
      return null;
    }
  })();

  subscriberPromise.catch(() => {
    subscriberPromise = null;
  });
}

function sendToBroker(payload: DocumentStatusEvent): void {
  if (!redisEnabled || !redisUrl) {
    return;
  }

  void (async () => {
    try {
      const publisher = await getPublisher();
      if (!publisher) {
        return;
      }
      const envelope: BrokerEnvelope = { origin: instanceId, payload };
      await publisher.publish(DOCUMENT_STATUS_CHANNEL, JSON.stringify(envelope));
    } catch (error) {
      reportEventBusIssue('Failed to publish documentStatusChanged event', error);
    }
  })();
}

type DocumentStatusEventName = 'documentStatusChanged';

interface BrokeredEventBus {
  on(event: DocumentStatusEventName, listener: DocumentStatusListener): void;
  off(event: DocumentStatusEventName, listener: DocumentStatusListener): void;
  emit(event: DocumentStatusEventName, payload: DocumentStatusEvent): boolean;
}

export const eventBus: BrokeredEventBus = {
  on(event, listener) {
    emitter.on(event, listener);
    ensureSubscriber();
  },
  off(event, listener) {
    emitter.off(event, listener);
  },
  emit(event, payload) {
    const handled = emitter.emit(event, payload);
    if (event === 'documentStatusChanged') {
      sendToBroker(payload);
    }
    return handled;
  },
};

export const subscribeToDocumentStatusChanges = (
  listener: DocumentStatusListener,
): (() => void) => {
  eventBus.on('documentStatusChanged', listener);
  return () => {
    eventBus.off('documentStatusChanged', listener);
  };
};

