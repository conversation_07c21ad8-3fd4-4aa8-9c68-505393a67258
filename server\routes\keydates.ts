import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient } from '../supabaseClient';

const router = Router();

const insertSchema = z.object({
  documentId: z.string().uuid(),
  event: z.string().min(1),
  date: z.string().min(1),
});
const updateSchema = z.object({ event: z.string().min(1).optional(), date: z.string().min(1).optional() });

router.get('/', requireAuth, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('document_key_dates').select('*').order('date');
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ keyDates: data });
});

router.post('/', requireAuth, async (req: AuthRequest, res) => {
  const parsed = insertSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa
    .from('document_key_dates')
    .insert({ document_id: parsed.data.documentId, event: parsed.data.event, date: parsed.data.date })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ keyDate: data });
});

router.put('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = updateSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{ event: string; date: string }> = { event: parsed.data.event, date: parsed.data.date };
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('document_key_dates').update(patch).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ keyDate: data });
});

router.delete('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('document_key_dates').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

export default router;

