-- Enable required extensions
create extension if not exists pgcrypto;

-- Documents table
create table if not exists public.documents (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  name text not null,
  content text not null default '',
  folder_id uuid null,
  status text not null default 'draft',
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);

create index if not exists documents_user_id_idx on public.documents(user_id);
create index if not exists documents_updated_at_idx on public.documents(updated_at desc);

alter table public.documents enable row level security;

drop policy if exists "docs_select_own" on public.documents;
create policy "docs_select_own" on public.documents for select using (
  user_id = auth.uid()
);

drop policy if exists "docs_insert_own" on public.documents;
create policy "docs_insert_own" on public.documents for insert with check (
  user_id = auth.uid()
);

drop policy if exists "docs_update_own" on public.documents;
create policy "docs_update_own" on public.documents for update using (
  user_id = auth.uid()
);

drop policy if exists "docs_delete_own" on public.documents;
create policy "docs_delete_own" on public.documents for delete using (
  user_id = auth.uid()
);

-- Document versions
create table if not exists public.document_versions (
  id uuid primary key default gen_random_uuid(),
  document_id uuid not null references public.documents(id) on delete cascade,
  content text not null,
  saved_at timestamptz not null default now()
);

create index if not exists document_versions_doc_id_idx on public.document_versions(document_id);

alter table public.document_versions enable row level security;

drop policy if exists "docvers_select_own" on public.document_versions;
create policy "docvers_select_own" on public.document_versions for select using (
  exists(
    select 1 from public.documents d
    where d.id = document_versions.document_id
      and d.user_id = auth.uid()
  )
);

drop policy if exists "docvers_insert_own" on public.document_versions;
create policy "docvers_insert_own" on public.document_versions for insert with check (
  exists(
    select 1 from public.documents d
    where d.id = document_versions.document_id
      and d.user_id = auth.uid()
  )
);

-- Clauses table
create table if not exists public.clauses (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  title text not null,
  content text not null,
  tags text[] not null default '{}',
  created_at timestamptz not null default now()
);

create index if not exists clauses_user_id_idx on public.clauses(user_id);

alter table public.clauses enable row level security;

drop policy if exists "clauses_select_own" on public.clauses;
create policy "clauses_select_own" on public.clauses for select using (
  user_id = auth.uid()
);

drop policy if exists "clauses_mutate_own" on public.clauses;
create policy "clauses_mutate_own" on public.clauses for all using (
  user_id = auth.uid()
) with check (
  user_id = auth.uid()
);

-- Folders table
create table if not exists public.folders (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  name text not null,
  created_at timestamptz not null default now()
);

create index if not exists folders_user_id_idx on public.folders(user_id);

alter table public.folders enable row level security;

drop policy if exists "folders_select_own" on public.folders;
create policy "folders_select_own" on public.folders for select using (
  user_id = auth.uid()
);

drop policy if exists "folders_mutate_own" on public.folders;
  create policy "folders_mutate_own" on public.folders for all using (
  user_id = auth.uid()
 ) with check (
  user_id = auth.uid()
 );

-- Pricing plans (publicly readable)
create table if not exists public.pricing_plans (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  price text not null,
  price_detail text,
  features text[] not null default '{}',
  cta text not null,
  is_featured boolean not null default false,
  sort_order int not null default 0
);
alter table public.pricing_plans enable row level security;
drop policy if exists pricing_public_select on public.pricing_plans;
  create policy pricing_public_select on public.pricing_plans for select using (true);

  -- Profiles: ensure notification_prefs column exists
  do $$ begin
    alter table public.profiles add column if not exists notification_prefs jsonb not null default '{}'::jsonb;
  exception when undefined_table then null; end $$;

-- Usage events to track generations (for local dev)
create table if not exists public.usage_events (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  event text not null check (event in ('generation')),
  created_at timestamptz not null default now()
);
alter table public.usage_events enable row level security;
drop policy if exists usage_events_self_select on public.usage_events;
create policy usage_events_self_select on public.usage_events for select using (user_id = auth.uid());
drop policy if exists usage_events_self_insert on public.usage_events;
create policy usage_events_self_insert on public.usage_events for insert with check (user_id = auth.uid());
