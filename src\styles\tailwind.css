@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global font family */
body { font-family: 'Inter', sans-serif; }

/* Form controls baseline styling for consistency across the app */
@layer base {
  input[type='text'],
  input[type='email'],
  input[type='password'],
  input[type='url'],
  input[type='number'],
  input[type='search'],
  input:not([type]),
  textarea,
  select {
    @apply w-full rounded-md border border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 text-zinc-900 dark:text-zinc-100 placeholder-zinc-400 dark:placeholder-zinc-500 focus:outline-none focus:ring-2 focus:ring-brand-500 focus:border-brand-500 disabled:opacity-60 disabled:cursor-not-allowed;
    padding: 0.5rem 0.75rem; /* px-3 py-2 */
  }

  input[type='file'] {
    @apply block w-full text-sm text-zinc-600 dark:text-zinc-300 file:mr-4 file:py-2 file:px-3 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-zinc-100 file:text-zinc-700 hover:file:bg-zinc-200 dark:file:bg-zinc-800 dark:file:text-zinc-200 dark:hover:file:bg-zinc-700;
  }

  label {
    @apply text-sm font-medium text-zinc-700 dark:text-zinc-300;
  }
}
