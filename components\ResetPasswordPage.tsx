

import React, { useState } from 'react';
import { LockIcon, CheckCircleIcon } from './Icons';

interface ResetPasswordPageProps {
  token: string | null;
  handleResetPassword: (token: string, newPassword: string) => boolean;
  setView: (view: 'auth') => void;
  authError: string | null;
  setAuthError: (error: string | null) => void;
}

const ResetPasswordPage: React.FC<ResetPasswordPageProps> = ({ token, handleResetPassword, setView, authError, setAuthError }) => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setAuthError(null);

    if (password !== confirmPassword) {
      setAuthError("Passwords do not match.");
      return;
    }
    if (password.length < 8) {
        setAuthError("Password must be at least 8 characters long.");
        return;
    }
    if (!token) {
        setAuthError("No reset token provided.");
        return;
    }
    
    if (handleResetPassword(token, password)) {
        setIsSuccess(true);
    }
  };

  if (isSuccess) {
     return (
        <section className="py-20 bg-zinc-50 dark:bg-zinc-900 min-h-[calc(100vh-144px)] flex items-center justify-center">
            <div className="w-full max-w-md bg-white dark:bg-zinc-950 p-8 md:p-10 rounded-2xl shadow-lg border border-zinc-200 dark:border-zinc-800 flex flex-col items-center text-center">
                <CheckCircleIcon className="w-16 h-16 text-green-500" />
                <h2 className="text-2xl font-bold text-zinc-900 dark:text-white mt-6">Password Reset!</h2>
                <p className="text-zinc-600 dark:text-zinc-400 mt-2">Your password has been successfully updated. You can now log in with your new password.</p>
                <button
                onClick={() => setView('auth')}
                className="mt-8 w-full max-w-xs flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-brand-600 hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500"
                >
                Go to Login
                </button>
            </div>
        </section>
     );
  }

  return (
    <section className="py-20 bg-zinc-50 dark:bg-zinc-900 min-h-[calc(100vh-144px)] flex items-center justify-center">
        <div className="w-full max-w-md bg-white dark:bg-zinc-950 p-8 md:p-10 rounded-2xl shadow-lg border border-zinc-200 dark:border-zinc-800">
            <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-zinc-900 dark:text-white">Set New Password</h2>
                <p className="text-zinc-500 dark:text-zinc-400 mt-2">Create a new, strong password.</p>
            </div>
             <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                    <label htmlFor="password-reset" className="block text-sm font-medium text-zinc-700 dark:text-zinc-300">New Password</label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                            <LockIcon className="h-5 w-5 text-zinc-400" />
                        </div>
                        <input 
                            type="password" 
                            id="password-reset" 
                            autoComplete="new-password"
                            required 
                            className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 focus:border-brand-500 focus:ring-brand-500 sm:text-sm p-3" 
                            placeholder="••••••••"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                        />
                    </div>
                </div>
                <div>
                    <label htmlFor="password-reset-confirm" className="block text-sm font-medium text-zinc-700 dark:text-zinc-300">Confirm New Password</label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                        <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                            <LockIcon className="h-5 w-5 text-zinc-400" />
                        </div>
                        <input 
                            type="password" 
                            id="password-reset-confirm" 
                            autoComplete="new-password"
                            required 
                            className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 focus:border-brand-500 focus:ring-brand-500 sm:text-sm p-3" 
                            placeholder="••••••••"
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                        />
                    </div>
                </div>
                 {authError && <p className="text-sm text-red-600 text-center">{authError}</p>}
                <div>
                    <button type="submit" className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-brand-600 hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500">
                        Reset Password
                    </button>
                </div>
            </form>
        </div>
    </section>
  );
};

export default ResetPasswordPage;