import React, { useState, useEffect, useRef, useCallback } from 'react';

interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  placeholder?: React.ReactNode;
  threshold?: number;
  rootMargin?: string;
  onLoad?: () => void;
  onError?: () => void;
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  className = '',
  placeholder,
  threshold = 0.1,
  rootMargin = '50px',
  onLoad,
  onError
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setHasError(true);
    onError?.();
  }, [onError]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
        }
      },
      {
        threshold,
        rootMargin
      }
    );

    const currentContainer = containerRef.current;
    if (currentContainer) {
      observer.observe(currentContainer);
    }

    return () => {
      if (currentContainer) {
        observer.unobserve(currentContainer);
      }
    };
  }, [threshold, rootMargin]);

  const defaultPlaceholder = (
    <div className={`bg-zinc-200 dark:bg-zinc-700 animate-pulse flex items-center justify-center ${className}`}>
      <div className="text-zinc-400 dark:text-zinc-500 text-sm">Loading...</div>
    </div>
  );

  const errorPlaceholder = (
    <div className={`bg-zinc-100 dark:bg-zinc-800 border-2 border-dashed border-zinc-300 dark:border-zinc-600 flex items-center justify-center ${className}`}>
      <div className="text-zinc-400 dark:text-zinc-500 text-sm">Failed to load image</div>
    </div>
  );

  return (
    <div ref={containerRef} className={className}>
      {hasError ? (
        errorPlaceholder
      ) : isInView ? (
        <>
          {!isLoaded && (placeholder || defaultPlaceholder)}
          <img
            ref={imgRef}
            src={src}
            alt={alt}
            className={`${className} ${isLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
            onLoad={handleLoad}
            onError={handleError}
            style={{ display: isLoaded ? 'block' : 'none' }}
          />
        </>
      ) : (
        placeholder || defaultPlaceholder
      )}
    </div>
  );
};

export default LazyImage;