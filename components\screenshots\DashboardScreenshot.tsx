import React from 'react';

const StatCard = ({ title, value }: { title: string, value: string }) => (
    <div className="bg-white rounded-lg shadow p-4">
        <p className="text-xs text-zinc-500">{title}</p>
        <p className="text-2xl font-bold text-zinc-800">{value}</p>
    </div>
);

const ChartBar = ({ height, active }: { height: string, active?: boolean }) => (
    <div className="flex-1 h-full flex items-end">
        <div
            className={`w-3/4 mx-auto rounded-t-sm ${active ? 'bg-indigo-400' : 'bg-indigo-200'}`}
            style={{ height }}
        ></div>
    </div>
);

const DashboardScreenshot = () => {
    return (
        <div className="w-full h-full bg-zinc-100 p-6 overflow-hidden select-none">
            <div className="flex justify-between items-center mb-4">
                <div>
                    <h1 className="text-xl font-bold text-zinc-800">Welcome back, Test User!</h1>
                    <p className="text-sm text-zinc-500">Here's a summary of your account activity.</p>
                </div>
                <div className="bg-indigo-600 text-white text-sm font-semibold px-4 py-2 rounded-lg shadow">
                    Generate New Document
                </div>
            </div>

            <div className="grid grid-cols-4 gap-4 mb-6">
                <StatCard title="Total Documents" value="4" />
                <StatCard title="Folders Created" value="2" />
                <StatCard title="Templates Used" value="0" />
                <div className="bg-white rounded-lg shadow p-4">
                    <p className="text-xs text-zinc-500">Monthly Quota</p>
                    <p className="text-sm text-zinc-600 mt-1">4 of 10 generations used.</p>
                    <div className="w-full bg-zinc-200 rounded-full h-1.5 mt-2">
                        <div className="bg-indigo-600 h-1.5 rounded-full" style={{ width: '40%' }}></div>
                    </div>
                </div>
            </div>

            <div className="bg-white rounded-lg shadow p-4">
                <h2 className="text-base font-semibold text-zinc-700">Recent Activity</h2>
                <p className="text-xs text-zinc-500 mb-4">Documents created in the last 7 days.</p>
                <div className="h-28 flex items-end gap-2 border-l border-b border-zinc-200 p-2">
                    <ChartBar height="0%" />
                    <ChartBar height="25%" />
                    <ChartBar height="25%" />
                    <ChartBar height="0%" />
                    <ChartBar height="60%" active />
                    <ChartBar height="0%" />
                    <ChartBar height="0%" />
                </div>
            </div>
        </div>
    );
};

export default DashboardScreenshot;