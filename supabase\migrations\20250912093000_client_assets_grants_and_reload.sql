-- Ensure client_assets is visible to the API and refresh PostgREST schema

-- Grant schema usage (idempotent; ignore errors if already granted)
do $$ begin
  begin
    execute 'grant usage on schema public to anon, authenticated, service_role';
  exception when others then
    -- ignore if permissions already set
    null;
  end;
end $$;

-- Grant CRUD privileges on public.client_assets to API roles (idempotent)
do $$ begin
  if exists (
    select 1 from information_schema.tables
    where table_schema = 'public' and table_name = 'client_assets'
  ) then
    begin
      execute 'grant select, insert, update, delete on table public.client_assets to anon, authenticated, service_role';
    exception when others then
      -- ignore if already granted
      null;
    end;
  end if;
end $$;

-- Force PostgREST to reload its schema cache so new tables/privileges are recognized immediately
select pg_notify('pgrst', 'reload schema');

