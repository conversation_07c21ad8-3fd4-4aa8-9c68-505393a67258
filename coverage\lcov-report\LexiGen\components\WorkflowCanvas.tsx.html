
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/WorkflowCanvas.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> WorkflowCanvas.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">49.66% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>148/298</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">57.69% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>15/26</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">18.75% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>3/16</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">49.66% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>148/298</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a>
<a name='L259'></a><a href='#L259'>259</a>
<a name='L260'></a><a href='#L260'>260</a>
<a name='L261'></a><a href='#L261'>261</a>
<a name='L262'></a><a href='#L262'>262</a>
<a name='L263'></a><a href='#L263'>263</a>
<a name='L264'></a><a href='#L264'>264</a>
<a name='L265'></a><a href='#L265'>265</a>
<a name='L266'></a><a href='#L266'>266</a>
<a name='L267'></a><a href='#L267'>267</a>
<a name='L268'></a><a href='#L268'>268</a>
<a name='L269'></a><a href='#L269'>269</a>
<a name='L270'></a><a href='#L270'>270</a>
<a name='L271'></a><a href='#L271'>271</a>
<a name='L272'></a><a href='#L272'>272</a>
<a name='L273'></a><a href='#L273'>273</a>
<a name='L274'></a><a href='#L274'>274</a>
<a name='L275'></a><a href='#L275'>275</a>
<a name='L276'></a><a href='#L276'>276</a>
<a name='L277'></a><a href='#L277'>277</a>
<a name='L278'></a><a href='#L278'>278</a>
<a name='L279'></a><a href='#L279'>279</a>
<a name='L280'></a><a href='#L280'>280</a>
<a name='L281'></a><a href='#L281'>281</a>
<a name='L282'></a><a href='#L282'>282</a>
<a name='L283'></a><a href='#L283'>283</a>
<a name='L284'></a><a href='#L284'>284</a>
<a name='L285'></a><a href='#L285'>285</a>
<a name='L286'></a><a href='#L286'>286</a>
<a name='L287'></a><a href='#L287'>287</a>
<a name='L288'></a><a href='#L288'>288</a>
<a name='L289'></a><a href='#L289'>289</a>
<a name='L290'></a><a href='#L290'>290</a>
<a name='L291'></a><a href='#L291'>291</a>
<a name='L292'></a><a href='#L292'>292</a>
<a name='L293'></a><a href='#L293'>293</a>
<a name='L294'></a><a href='#L294'>294</a>
<a name='L295'></a><a href='#L295'>295</a>
<a name='L296'></a><a href='#L296'>296</a>
<a name='L297'></a><a href='#L297'>297</a>
<a name='L298'></a><a href='#L298'>298</a>
<a name='L299'></a><a href='#L299'>299</a>
<a name='L300'></a><a href='#L300'>300</a>
<a name='L301'></a><a href='#L301'>301</a>
<a name='L302'></a><a href='#L302'>302</a>
<a name='L303'></a><a href='#L303'>303</a>
<a name='L304'></a><a href='#L304'>304</a>
<a name='L305'></a><a href='#L305'>305</a>
<a name='L306'></a><a href='#L306'>306</a>
<a name='L307'></a><a href='#L307'>307</a>
<a name='L308'></a><a href='#L308'>308</a>
<a name='L309'></a><a href='#L309'>309</a>
<a name='L310'></a><a href='#L310'>310</a>
<a name='L311'></a><a href='#L311'>311</a>
<a name='L312'></a><a href='#L312'>312</a>
<a name='L313'></a><a href='#L313'>313</a>
<a name='L314'></a><a href='#L314'>314</a>
<a name='L315'></a><a href='#L315'>315</a>
<a name='L316'></a><a href='#L316'>316</a>
<a name='L317'></a><a href='#L317'>317</a>
<a name='L318'></a><a href='#L318'>318</a>
<a name='L319'></a><a href='#L319'>319</a>
<a name='L320'></a><a href='#L320'>320</a>
<a name='L321'></a><a href='#L321'>321</a>
<a name='L322'></a><a href='#L322'>322</a>
<a name='L323'></a><a href='#L323'>323</a>
<a name='L324'></a><a href='#L324'>324</a>
<a name='L325'></a><a href='#L325'>325</a>
<a name='L326'></a><a href='#L326'>326</a>
<a name='L327'></a><a href='#L327'>327</a>
<a name='L328'></a><a href='#L328'>328</a>
<a name='L329'></a><a href='#L329'>329</a>
<a name='L330'></a><a href='#L330'>330</a>
<a name='L331'></a><a href='#L331'>331</a>
<a name='L332'></a><a href='#L332'>332</a>
<a name='L333'></a><a href='#L333'>333</a>
<a name='L334'></a><a href='#L334'>334</a>
<a name='L335'></a><a href='#L335'>335</a>
<a name='L336'></a><a href='#L336'>336</a>
<a name='L337'></a><a href='#L337'>337</a>
<a name='L338'></a><a href='#L338'>338</a>
<a name='L339'></a><a href='#L339'>339</a>
<a name='L340'></a><a href='#L340'>340</a>
<a name='L341'></a><a href='#L341'>341</a>
<a name='L342'></a><a href='#L342'>342</a>
<a name='L343'></a><a href='#L343'>343</a>
<a name='L344'></a><a href='#L344'>344</a>
<a name='L345'></a><a href='#L345'>345</a>
<a name='L346'></a><a href='#L346'>346</a>
<a name='L347'></a><a href='#L347'>347</a>
<a name='L348'></a><a href='#L348'>348</a>
<a name='L349'></a><a href='#L349'>349</a>
<a name='L350'></a><a href='#L350'>350</a>
<a name='L351'></a><a href='#L351'>351</a>
<a name='L352'></a><a href='#L352'>352</a>
<a name='L353'></a><a href='#L353'>353</a>
<a name='L354'></a><a href='#L354'>354</a>
<a name='L355'></a><a href='#L355'>355</a>
<a name='L356'></a><a href='#L356'>356</a>
<a name='L357'></a><a href='#L357'>357</a>
<a name='L358'></a><a href='#L358'>358</a>
<a name='L359'></a><a href='#L359'>359</a>
<a name='L360'></a><a href='#L360'>360</a>
<a name='L361'></a><a href='#L361'>361</a>
<a name='L362'></a><a href='#L362'>362</a>
<a name='L363'></a><a href='#L363'>363</a>
<a name='L364'></a><a href='#L364'>364</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">&nbsp;
&nbsp;
import React, { useState, useRef, useCallback } from 'react';
import { WorkflowTemplate, WorkflowNode, WorkflowEdge, WorkflowNodeType, WorkflowTriggerType, User } from '../types';
import { Button } from './ui/Button';
// FIX: Added missing icons and removed unused ones from the import statement.
import { ArrowLeftIcon, SaveIcon, PlayIcon, GitBranchIcon, UserCheckIcon, MailIcon, TimerIcon, EditIcon, SignatureIcon, UserPlusIcon, FolderMoveIcon, TagsIcon, LayoutGridIcon } from './Icons';
import * as Nodes from './ui/WorkflowNodes';
import { cn } from '../lib/utils';
// FIX: Changed the import for PropertiesPanel to correct a module resolution issue where the default export was not being found due to a syntax error in the source file.
import PropertiesPanel from './PropertiesPanel';
&nbsp;
interface WorkflowCanvasProps {
  user: User;
  template: WorkflowTemplate;
  onSave: (template: WorkflowTemplate) =&gt; void;
  onBack: () =&gt; void;
}
&nbsp;
const nodeComponents = {
  trigger: Nodes.TriggerNode,
  condition: Nodes.ConditionNode,
  approval: Nodes.ApprovalNode,
  notification: Nodes.NotificationNode,
  // FIX: Added missing DelayNode to the components map.
  delay: Nodes.DelayNode,
  // FIX: Added missing UpdateFieldNode to the components map.
  'update-field': Nodes.UpdateFieldNode,
  // FIX: Added missing WaitForSignatureNode to the components map.
  'wait-for-signature': Nodes.WaitForSignatureNode,
  'add-collaborator': Nodes.AddCollaboratorNode,
  'move-to-folder': Nodes.MoveToFolderNode,
  'add-tag': Nodes.AddTagNode,
};
&nbsp;
const paletteItems: { type: WorkflowNodeType, label: string, icon: React.FC&lt;{className?:string}&gt;, iconClass: string }[] = [
    { type: 'condition', label: 'Condition', icon: GitBranchIcon, iconClass: 'text-amber-500' },
    { type: 'delay', label: 'Delay', icon: TimerIcon, iconClass: 'text-orange-500' },
    { type: 'approval', label: 'Approval', icon: UserCheckIcon, iconClass: 'text-sky-500' },
    { type: 'update-field', label: 'Update Field', icon: EditIcon, iconClass: 'text-purple-500' },
    { type: 'notification', label: 'Send Email', icon: MailIcon, iconClass: 'text-teal-500' },
    { type: 'wait-for-signature', label: 'e-Signature', icon: SignatureIcon, iconClass: 'text-indigo-500' },
    { type: 'add-collaborator', label: 'Add Collaborator', icon: UserPlusIcon, iconClass: 'text-cyan-500' },
    { type: 'move-to-folder', label: 'Move to Folder', icon: FolderMoveIcon, iconClass: 'text-lime-500' },
    { type: 'add-tag', label: 'Add Tag', icon: TagsIcon, iconClass: 'text-pink-500' },
];
&nbsp;
const availableNodesMap: Record&lt;WorkflowTriggerType, WorkflowNodeType[]&gt; = {
    'document-created': ['condition', 'delay', 'approval', 'update-field', 'notification', 'wait-for-signature', 'add-collaborator', 'move-to-folder', 'add-tag'],
    'approval-requested': ['condition', 'delay', 'approval', 'update-field', 'notification'],
    'status-changed': ['condition', 'delay', 'notification', 'update-field', 'move-to-folder', 'add-tag'],
};
&nbsp;
&nbsp;
const WorkflowCanvas: React.FC&lt;WorkflowCanvasProps&gt; = ({ user, template, onSave, onBack }) =&gt; {
    const [nodes, setNodes] = useState&lt;WorkflowNode[]&gt;(template.nodes);
    const [edges, setEdges] = useState&lt;WorkflowEdge[]&gt;(template.edges);
    const [name, setName] = useState(template.name);
    const [selectedNodeId, setSelectedNodeId] = useState&lt;string | null&gt;(null);
    const [selectedEdgeId, setSelectedEdgeId] = useState&lt;string | null&gt;(null);
    const [isConnecting, setIsConnecting] = useState&lt;{ sourceId: string; sourceHandle?: string; x: number; y: number } | null&gt;(null);
&nbsp;
    // Scrollable workspace canvas; inner workspace grows to fit nodes
    const canvasRef = useRef&lt;HTMLDivElement&gt;(null);
    const dragInfoRef = useRef&lt;{ nodeId: string; startPos: { x: number; y: number }; startMouse: { x: number; y: number }; isDragging: boolean; } | null&gt;(null);
    const connectionInfoRef = useRef&lt;{ sourceId: string; sourceHandle?: string; } | null&gt;(null);
&nbsp;
    const handleMouseMove = useCallback((e: MouseEvent) =&gt; {
<span class="cstat-no" title="statement not covered" >        if (!dragInfoRef.current) {return;}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        const { startMouse, startPos, nodeId } = dragInfoRef.current;</span>
<span class="cstat-no" title="statement not covered" >        const dx = e.clientX - startMouse.x;</span>
<span class="cstat-no" title="statement not covered" >        const dy = e.clientY - startMouse.y;</span>
        
<span class="cstat-no" title="statement not covered" >        if (!dragInfoRef.current.isDragging &amp;&amp; Math.sqrt(dx*dx + dy*dy) &gt; 5) {</span>
<span class="cstat-no" title="statement not covered" >            dragInfoRef.current.isDragging = true;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (dragInfoRef.current.isDragging) {</span>
<span class="cstat-no" title="statement not covered" >            let newX = startPos.x + dx;</span>
<span class="cstat-no" title="statement not covered" >            let newY = startPos.y + dy;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            if (canvasRef.current) {</span>
<span class="cstat-no" title="statement not covered" >                const canvasRect = canvasRef.current.getBoundingClientRect();</span>
<span class="cstat-no" title="statement not covered" >                const nodeWidth = 220;</span>
<span class="cstat-no" title="statement not covered" >                const nodeHeight = 80;</span>
<span class="cstat-no" title="statement not covered" >                newX = Math.max(0, Math.min(newX, canvasRect.width - nodeWidth));</span>
<span class="cstat-no" title="statement not covered" >                newY = Math.max(0, Math.min(newY, canvasRect.height - nodeHeight));</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            setNodes(prevNodes =&gt; prevNodes.map(n =&gt; (n.id === nodeId ? { ...n, position: { x: newX, y: newY } } : n)));</span>
<span class="cstat-no" title="statement not covered" >        }</span>
    }, []);
&nbsp;
    const handleMouseUp = useCallback(() =&gt; {
<span class="cstat-no" title="statement not covered" >        if (dragInfoRef.current &amp;&amp; !dragInfoRef.current.isDragging) {</span>
<span class="cstat-no" title="statement not covered" >            setSelectedNodeId(dragInfoRef.current.nodeId);</span>
<span class="cstat-no" title="statement not covered" >            setSelectedEdgeId(null);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        dragInfoRef.current = null;</span>
<span class="cstat-no" title="statement not covered" >        window.removeEventListener('mousemove', handleMouseMove);</span>
<span class="cstat-no" title="statement not covered" >        window.removeEventListener('mouseup', handleMouseUp);</span>
    }, [handleMouseMove]);
    
    const handleNodeMouseDown = <span class="fstat-no" title="function not covered" >(e: React.MouseEvent, node: WorkflowNode) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (node.type === 'trigger') { // Trigger node is not draggable, but selectable</span>
<span class="cstat-no" title="statement not covered" >            setSelectedNodeId(node.id);</span>
<span class="cstat-no" title="statement not covered" >            setSelectedEdgeId(null);</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        e.preventDefault();</span>
<span class="cstat-no" title="statement not covered" >        e.stopPropagation();</span>
<span class="cstat-no" title="statement not covered" >        dragInfoRef.current = { nodeId: node.id, startPos: node.position, startMouse: { x: e.clientX, y: e.clientY }, isDragging: false };</span>
<span class="cstat-no" title="statement not covered" >        window.addEventListener('mousemove', handleMouseMove);</span>
<span class="cstat-no" title="statement not covered" >        window.addEventListener('mouseup', handleMouseUp);</span>
<span class="cstat-no" title="statement not covered" >    };</span>
    
    const handleConnectionMouseMove = useCallback((e: MouseEvent) =&gt; {
<span class="cstat-no" title="statement not covered" >        if (!connectionInfoRef.current || !canvasRef.current) {return;}</span>
<span class="cstat-no" title="statement not covered" >        const canvasRect = canvasRef.current.getBoundingClientRect();</span>
<span class="cstat-no" title="statement not covered" >        setIsConnecting({</span>
<span class="cstat-no" title="statement not covered" >            sourceId: connectionInfoRef.current.sourceId,</span>
<span class="cstat-no" title="statement not covered" >            sourceHandle: connectionInfoRef.current.sourceHandle,</span>
<span class="cstat-no" title="statement not covered" >            x: e.clientX - canvasRect.left,</span>
<span class="cstat-no" title="statement not covered" >            y: e.clientY - canvasRect.top,</span>
<span class="cstat-no" title="statement not covered" >        });</span>
    }, []);
&nbsp;
    const handleConnectionMouseUp = useCallback((e: MouseEvent) =&gt; {
<span class="cstat-no" title="statement not covered" >        const target = e.target as HTMLElement;</span>
<span class="cstat-no" title="statement not covered" >        const handleEl = target.closest('[data-handle-type="target"]');</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        if (handleEl &amp;&amp; connectionInfoRef.current) {</span>
<span class="cstat-no" title="statement not covered" >            const targetNodeEl = handleEl.closest('[data-node-id]');</span>
<span class="cstat-no" title="statement not covered" >            if (targetNodeEl) {</span>
<span class="cstat-no" title="statement not covered" >                const targetId = targetNodeEl.getAttribute('data-node-id');</span>
<span class="cstat-no" title="statement not covered" >                if (!targetId) { return; }</span>
<span class="cstat-no" title="statement not covered" >                const targetHandle = handleEl.getAttribute('data-handle-id') || undefined;</span>
<span class="cstat-no" title="statement not covered" >                const { sourceId, sourceHandle } = connectionInfoRef.current;</span>
                
<span class="cstat-no" title="statement not covered" >                 if (sourceId !== targetId) {</span>
<span class="cstat-no" title="statement not covered" >                    const newEdge: WorkflowEdge = {</span>
<span class="cstat-no" title="statement not covered" >                        id: `edge-${sourceId}-${sourceHandle || 'o'}-${targetId}-${targetHandle || 'i'}`,</span>
<span class="cstat-no" title="statement not covered" >                        source: sourceId,</span>
<span class="cstat-no" title="statement not covered" >                        sourceHandle: sourceHandle,</span>
<span class="cstat-no" title="statement not covered" >                        target: targetId,</span>
<span class="cstat-no" title="statement not covered" >                        targetHandle,</span>
<span class="cstat-no" title="statement not covered" >                    };</span>
<span class="cstat-no" title="statement not covered" >                    if (!edges.some(edge =&gt; edge.id === newEdge.id)) {</span>
<span class="cstat-no" title="statement not covered" >                        setEdges(eds =&gt; [...eds, newEdge]);</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >        }</span>
        
<span class="cstat-no" title="statement not covered" >        setIsConnecting(null);</span>
<span class="cstat-no" title="statement not covered" >        connectionInfoRef.current = null;</span>
<span class="cstat-no" title="statement not covered" >        window.removeEventListener('mousemove', handleConnectionMouseMove);</span>
<span class="cstat-no" title="statement not covered" >        window.removeEventListener('mouseup', handleConnectionMouseUp);</span>
    }, [edges, handleConnectionMouseMove]);
&nbsp;
    const handleStartConnection = <span class="fstat-no" title="function not covered" >(e: React.MouseEvent, sourceId: string, sourceHandle?: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        e.stopPropagation();</span>
<span class="cstat-no" title="statement not covered" >        connectionInfoRef.current = { sourceId, sourceHandle };</span>
<span class="cstat-no" title="statement not covered" >        window.addEventListener('mousemove', handleConnectionMouseMove);</span>
<span class="cstat-no" title="statement not covered" >        window.addEventListener('mouseup', handleConnectionMouseUp);</span>
<span class="cstat-no" title="statement not covered" >    };</span>
&nbsp;
    const _handleDrop = <span class="fstat-no" title="function not covered" >(e: React.DragEvent&lt;HTMLDivElement&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        e.preventDefault();</span>
<span class="cstat-no" title="statement not covered" >        const canvasBounds = canvasRef.current?.getBoundingClientRect();</span>
<span class="cstat-no" title="statement not covered" >        if (!canvasBounds) {return;}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        const type = e.dataTransfer.getData('application/reactflow') as WorkflowNodeType;</span>
<span class="cstat-no" title="statement not covered" >        if (type) {</span>
<span class="cstat-no" title="statement not covered" >            const position = { x: e.clientX - canvasBounds.left - 110, y: e.clientY - canvasBounds.top - 40 };</span>
<span class="cstat-no" title="statement not covered" >            const newNode: WorkflowNode = {</span>
<span class="cstat-no" title="statement not covered" >                id: `${type}-${crypto.randomUUID()}`,</span>
<span class="cstat-no" title="statement not covered" >                type,</span>
<span class="cstat-no" title="statement not covered" >                position,</span>
<span class="cstat-no" title="statement not covered" >                data: { label: `New ${type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' ')}` }</span>
<span class="cstat-no" title="statement not covered" >            };</span>
<span class="cstat-no" title="statement not covered" >            setNodes(nds =&gt; [...nds, newNode]);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >    };</span>
    
    const getHandlePosition = (nodeId: string, handleType: 'source' | 'target', handleId?: string) =&gt; {
        const node = nodes.find(n =&gt; n.id === nodeId);
        if (!node) <span class="branch-0 cbranch-no" title="branch not covered" >{return { x: 0, y: 0 };}</span>
        
        let xOffset = 110;
        if (handleType === 'source' &amp;&amp; node.type === 'condition') <span class="branch-0 cbranch-no" title="branch not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            xOffset = handleId === 'true' ? 65 : 155;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
        const yOffset = handleType === 'source' ? 80 : 0;
        
        return { x: node.position.x + xOffset, y: node.position.y + yOffset };
    };
&nbsp;
    const handleAutoArrange = <span class="fstat-no" title="function not covered" >() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (nodes.length &lt;= 1) {return;}</span>
    
<span class="cstat-no" title="statement not covered" >        const nodeWidth = 220; const nodeHeight = 80;</span>
<span class="cstat-no" title="statement not covered" >        const hSpacing = 80; const vSpacing = 100;</span>
    
<span class="cstat-no" title="statement not covered" >        const adj: Record&lt;string, string[]&gt; = {};</span>
<span class="cstat-no" title="statement not covered" >        const inDegree: Record&lt;string, number&gt; = {};</span>
<span class="cstat-no" title="statement not covered" >        nodes.forEach(node =&gt; { adj[node.id] = []; inDegree[node.id] = 0; });</span>
<span class="cstat-no" title="statement not covered" >        edges.forEach(edge =&gt; { adj[edge.source].push(edge.target); inDegree[edge.target]++; });</span>
    
<span class="cstat-no" title="statement not covered" >        const root = nodes.find(n =&gt; inDegree[n.id] === 0);</span>
        // eslint-disable-next-line no-console
<span class="cstat-no" title="statement not covered" >        if (!root) { console.error("No root node found for auto-layout"); return; }</span>
    
<span class="cstat-no" title="statement not covered" >        const levels: Record&lt;string, number&gt; = {};</span>
<span class="cstat-no" title="statement not covered" >        const queue: string[] = [root.id];</span>
<span class="cstat-no" title="statement not covered" >        levels[root.id] = 0;</span>
<span class="cstat-no" title="statement not covered" >        const visited = new Set&lt;string&gt;();</span>
<span class="cstat-no" title="statement not covered" >        let head = 0;</span>
    
<span class="cstat-no" title="statement not covered" >        while (head &lt; queue.length) {</span>
<span class="cstat-no" title="statement not covered" >            const u = queue[head++];</span>
<span class="cstat-no" title="statement not covered" >            visited.add(u);</span>
<span class="cstat-no" title="statement not covered" >            (adj[u] || []).forEach(v =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                if (!visited.has(v)) {</span>
<span class="cstat-no" title="statement not covered" >                    levels[v] = (levels[u] || 0) + 1;</span>
<span class="cstat-no" title="statement not covered" >                    queue.push(v);</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >            });</span>
<span class="cstat-no" title="statement not covered" >        }</span>
    
<span class="cstat-no" title="statement not covered" >        const nodesByLevel: Record&lt;number, string[]&gt; = {};</span>
<span class="cstat-no" title="statement not covered" >        let maxLevel = 0;</span>
<span class="cstat-no" title="statement not covered" >        nodes.forEach(node =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            const level = levels[node.id] ?? -1;</span>
<span class="cstat-no" title="statement not covered" >            if (level === -1) {return;} // Skip disconnected nodes</span>
<span class="cstat-no" title="statement not covered" >            if (!nodesByLevel[level]) {nodesByLevel[level] = [];}</span>
<span class="cstat-no" title="statement not covered" >            nodesByLevel[level].push(node.id);</span>
<span class="cstat-no" title="statement not covered" >            maxLevel = Math.max(maxLevel, level);</span>
<span class="cstat-no" title="statement not covered" >        });</span>
    
<span class="cstat-no" title="statement not covered" >        const canvasWidth = canvasRef.current?.clientWidth || 1000;</span>
<span class="cstat-no" title="statement not covered" >        const newNodes = nodes.map(node =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            const level = levels[node.id];</span>
<span class="cstat-no" title="statement not covered" >            if (level === undefined) {return node;}</span>
    
<span class="cstat-no" title="statement not covered" >            const levelNodes = nodesByLevel[level];</span>
<span class="cstat-no" title="statement not covered" >            const nodeIndex = levelNodes.indexOf(node.id);</span>
<span class="cstat-no" title="statement not covered" >            const levelWidth = levelNodes.length * nodeWidth + (levelNodes.length - 1) * hSpacing;</span>
<span class="cstat-no" title="statement not covered" >            const startX = (canvasWidth - levelWidth) / 2;</span>
    
<span class="cstat-no" title="statement not covered" >            return {</span>
<span class="cstat-no" title="statement not covered" >                ...node,</span>
<span class="cstat-no" title="statement not covered" >                position: {</span>
<span class="cstat-no" title="statement not covered" >                    x: startX + nodeIndex * (nodeWidth + hSpacing),</span>
<span class="cstat-no" title="statement not covered" >                    y: level * (nodeHeight + vSpacing) + 50,</span>
<span class="cstat-no" title="statement not covered" >                },</span>
<span class="cstat-no" title="statement not covered" >            };</span>
<span class="cstat-no" title="statement not covered" >        });</span>
<span class="cstat-no" title="statement not covered" >        setNodes(newNodes);</span>
<span class="cstat-no" title="statement not covered" >    };</span>
&nbsp;
    const handleSave = <span class="fstat-no" title="function not covered" >() =&gt; onSave({ ...template, name, nodes, edges });</span>
    const selectedNode = nodes.find(n =&gt; n.id === selectedNodeId);
    
    const triggerNode = nodes.find(n =&gt; n.type === 'trigger');
    const triggerType = triggerNode?.data.triggerType;
    const availablePaletteItems = triggerType ? paletteItems.filter(item =&gt; availableNodesMap[triggerType<span class="branch-0 cbranch-no" title="branch not covered" >]?.includes(i</span>tem.type)<span class="branch-0 cbranch-no" title="branch not covered" >) : paletteItems;</span>
&nbsp;
&nbsp;
    return (
        &lt;div className="h-full flex flex-col bg-zinc-50 dark:bg-zinc-900"&gt;
            &lt;header className="p-4 border-b border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-950 flex items-center justify-between"&gt;
                &lt;div className="flex items-center gap-2"&gt;&lt;Button variant="ghost" size="icon" onClick={onBack}&gt;&lt;ArrowLeftIcon className="w-5 h-5"/&gt;&lt;/Button&gt;&lt;input type="text" value={name} onChange={<span class="fstat-no" title="function not covered" >e =&gt; setName(e.target.value)} c</span>lassName="text-xl font-bold bg-transparent focus:outline-none focus:ring-2 ring-brand-500 rounded px-2" /&gt;&lt;/div&gt;
                &lt;div className="flex items-center gap-2"&gt;
                    &lt;Button variant="outline" onClick={handleAutoArrange}&gt;&lt;LayoutGridIcon className="w-5 h-5 mr-2"/&gt;Auto-arrange&lt;/Button&gt;
                    &lt;Button onClick={handleSave}&gt;&lt;SaveIcon className="w-5 h-5 mr-2" /&gt; Save Workflow&lt;/Button&gt;
                &lt;/div&gt;
            &lt;/header&gt;
            &lt;div className="flex-1 flex overflow-hidden"&gt;
                &lt;aside className="w-60 p-4 border-r border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-950"&gt;
                    &lt;h3 className="font-semibold mb-2"&gt;Triggers&lt;/h3&gt;
                    &lt;div className="p-3 border rounded-lg flex items-center gap-2 bg-zinc-100 dark:bg-zinc-800 cursor-not-allowed text-zinc-500"&gt;&lt;PlayIcon className="w-5 h-5 text-green-500"/&gt;On Event&lt;/div&gt;
                    &lt;h3 className="font-semibold mb-2 mt-4"&gt;Logic &amp; Actions&lt;/h3&gt;
                    &lt;div className="space-y-2"&gt;{availablePaletteItems.map(item =&gt; (&lt;div key={item.type} onDragStart={(e) =&gt; e.dataTransfer.setData('application/reactflow', item.type)} draggable className={`p-3 border rounded-lg flex items-center gap-2 cursor-grab bg-white dark:bg-zinc-900 hover:bg-zinc-50 dark:hover:bg-zinc-800`}&gt;&lt;item.icon className={cn("w-5 h-5", item.iconClass)}/&gt;{item.label}&lt;/div&gt;))}&lt;/div&gt;
                &lt;/aside&gt;
                {/* Scroll container for the canvas */}
                &lt;main className="flex-1 relative overflow-auto"&gt;
                    {/* Workspace grows with content; scrollbars appear only when needed */}
                    {(() =&gt; {
                        const nodeWidth = 220;
                        const nodeHeight = 80;
                        const pad = 200;
                        const maxPos = nodes.reduce(
                          (acc, n) =&gt; ({
                            x: Math.max(acc.x, n.position.x || 0),
                            y: Math.max(acc.y, n.position.y || 0),
                          }),
                          { x: 0, y: 0 }
                        );
                        const workspaceWidth = Math.max(0, maxPos.x + nodeWidth + pad);
                        const workspaceHeight = Math.max(0, maxPos.y + nodeHeight + pad);
                        return (
                          &lt;div
                            ref={canvasRef}
                            className="relative"
                            style={{ minWidth: '100%', minHeight: '100%', width: <span class="branch-0 cbranch-no" title="branch not covered" >workspaceWidth || '100%', h</span>eight: <span class="branch-0 cbranch-no" title="branch not covered" >workspaceHeight || '100%' }</span>}
                          &gt;
                        {/* Base background color */}
                        &lt;div className="absolute inset-0 bg-white dark:bg-zinc-950"&gt;&lt;/div&gt;
                        {/* Dotted grid using currentColor for easy theming */}
                        &lt;div className="absolute inset-0 pointer-events-none text-zinc-200 dark:text-zinc-700" style={{ backgroundImage: 'radial-gradient(currentColor 1px, transparent 1px)', backgroundSize: '20px 20px' }} /&gt;
                        &lt;svg className="absolute w-full h-full pointer-events-none"&gt;
                        {edges.map(edge =&gt; {
                            const sourcePos = getHandlePosition(edge.source, 'source', edge.sourceHandle);
                            const targetPos = getHandlePosition(edge.target, 'target', edge.targetHandle);
                            if (!sourcePos || !targetPos) <span class="branch-0 cbranch-no" title="branch not covered" >{return null;}</span>
                            const { x: x1, y: y1 } = sourcePos; const { x: x2, y: y2 } = targetPos;
                            const isSelected = selectedEdgeId === edge.id;
                            return (&lt;g key={edge.id} className="cursor-pointer" onClick={<span class="fstat-no" title="function not covered" >(e) =&gt; { e.stopPropagation(); setSelectedEdgeId(edge.id); setSelectedNodeId(null); }}&gt;&lt;</span>path d={`M ${x1},${y1} C ${x1},${y1+50} ${x2},${y2-50} ${x2},${y2}`} stroke={<span class="branch-0 cbranch-no" title="branch not covered" >isSelected ? '#6366f1' : "</span>#a1a1aa"} strokeWidth="2" fill="none" className="pointer-events-auto" /&gt;&lt;path d={`M ${x1},${y1} C ${x1},${y1+50} ${x2},${y2-50} ${x2},${y2}`} stroke="transparent" strokeWidth="12" fill="none" className="pointer-events-auto" /&gt;&lt;/g&gt;);
                        })}
                        {<span class="branch-0 cbranch-no" title="branch not covered" >isConnecting &amp;&amp; (<span class="branch-0 cbranch-no" title="branch not covered" >() =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >                            const sourcePos = getHandlePosition(isConnecting.sourceId, 'source', isConnecting.sourceHandle);</span>
<span class="cstat-no" title="statement not covered" >                            if (!sourcePos) {return null;}</span>
<span class="cstat-no" title="statement not covered" >                            const { x: x1, y: y1 } = sourcePos; const { x: x2, y: y2 } = isConnecting;</span>
<span class="cstat-no" title="statement not covered" >                            return &lt;path d={`M ${x1},${y1} C ${x1},${y1+50} ${x2},${y2-50} ${x2},${y2}`} stroke="#6366f1" strokeWidth="2" fill="none" /&gt;;</span>
<span class="cstat-no" title="statement not covered" >                        })()}</span>
                        &lt;/svg&gt;
                        {nodes.map(node =&gt; {
                         const NodeComponent = nodeComponents[node.type];
                         return (&lt;div 
                            key={node.id} 
                            data-node-id={node.id}
                            onMouseDown={<span class="fstat-no" title="function not covered" >e =&gt; handleNodeMouseDown(e, node)} </span>
                            onClick={<span class="fstat-no" title="function not covered" >(e) =&gt; e.stopPropagation() } // Stop propagation to prevent canvas click</span>
                            className={cn("absolute cursor-grab transition-all duration-150", selectedNodeId === node.<span class="branch-0 cbranch-no" title="branch not covered" >id ? "ring-2 ring-brand-500 rounded-lg shadow-2xl z-10" : "</span>z-0")} 
                            style={{ transform: `translate(${node.position.x}px, ${node.position.y}px)` }}
                         &gt;
                            &lt;NodeComponent 
                                data={node.data} 
                                onStartConnection={<span class="fstat-no" title="function not covered" >(e, handle) =&gt; handleStartConnection(e, node.id, handle)} </span>
                                user={user}
                             /&gt;
                         &lt;/div&gt;);
                        })}
                          &lt;/div&gt;
                        );
                    })()}
                &lt;/main&gt;
                &lt;PropertiesPanel 
                    user={user}
                    selectedNode={selectedNode} 
                    selectedEdgeId={selectedEdgeId} 
                    onUpdateNode={<span class="fstat-no" title="function not covered" >(data) =&gt; selectedNodeId &amp;&amp; setNodes(nds =&gt; nds.map(n =&gt; n.id === selectedNodeId ? {...n, data: {...n.data, ...data}} : n))} </span>
                    onDeleteNode={<span class="fstat-no" title="function not covered" >() =&gt; { setNodes(nds =&gt; nds.filter(n =&gt; n.id !== selectedNodeId)); setEdges(eds =&gt; eds.filter(e =&gt; e.source !== selectedNodeId &amp;&amp; e.target !== selectedNodeId)); setSelectedNodeId(null);}} </span>
                    onDeleteEdge={<span class="fstat-no" title="function not covered" >() =&gt; { setEdges(eds =&gt; eds.filter(e =&gt; e.id !== selectedEdgeId)); setSelectedEdgeId(null); }}</span>
                /&gt;
            &lt;/div&gt;
            {/* Removed old utility classes for grid background in favor of inline radial-gradient above */}
        &lt;/div&gt;
    );
};
&nbsp;
export default WorkflowCanvas;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    