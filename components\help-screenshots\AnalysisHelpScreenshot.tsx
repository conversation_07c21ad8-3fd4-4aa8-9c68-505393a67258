
import React from 'react';
import Indicator from './Indicator';
import { UploadCloudIcon } from '../Icons';

const AnalysisHelpScreenshot: React.FC = () => {
    return (
        <div className="relative my-6 p-4 border rounded-lg bg-zinc-50 dark:bg-zinc-900 flex gap-4 overflow-hidden select-none text-xs h-72">
            <Indicator number={1} position="top-1/2 left-1/4" arrow="down" />
            <Indicator number={2} position="bottom-12 left-16" arrow="up" />
            <Indicator number={3} position="top-1/2 right-1/4" arrow="down" />
            
            <div className="flex-1 flex flex-col">
                <h1 className="font-bold text-zinc-900 dark:text-zinc-100">Analyze a Document</h1>
                <div className="flex-1 flex flex-col mt-2">
                    <div className="flex-1 p-2 border border-zinc-300 dark:border-zinc-700 rounded-t-lg bg-white dark:bg-zinc-800 text-zinc-400">
                        Paste your legal document text here...
                    </div>
                    <div className="p-2 border border-t-0 border-zinc-300 dark:border-zinc-700 rounded-b-lg bg-zinc-100 dark:bg-zinc-800/50 flex justify-between items-center">
                        <div className="border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700 text-zinc-600 dark:text-zinc-200 font-semibold px-2 py-1 rounded-md flex items-center">
                            <UploadCloudIcon className="w-4 h-4 mr-1" /> Upload
                        </div>
                        <div className="bg-brand-600 text-white font-semibold px-2 py-1 rounded-md">Analyze</div>
                    </div>
                </div>
            </div>
            <div className="flex-1">
                 <div className="h-full w-full border-2 border-dashed border-zinc-300 dark:border-zinc-700 rounded-lg flex items-center justify-center">
                    <p className="text-zinc-400">Analysis results will appear here.</p>
                 </div>
            </div>
        </div>
    );
};

export default AnalysisHelpScreenshot;
