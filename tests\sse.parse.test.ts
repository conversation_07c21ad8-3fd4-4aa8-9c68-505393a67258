import { describe, it, expect } from 'vitest';
import { extractSseEvents, parseSseBlock } from '../lib/sse';

describe('SSE parsing helpers', () => {
  it('parses single block with data only', () => {
    const ev = parseSseBlock('data: {"text":"Hello"}')
    expect(ev.event).toBeNull();
    expect(ev.data).toEqual({ text: 'Hello' });
  });

  it('parses event and data', () => {
    const ev = parseSseBlock('event: done\ndata: {}');
    expect(ev.event).toBe('done');
    expect(ev.data).toEqual({});
  });

  it('extracts multiple events and rest buffer', () => {
    const input = 'data: {"text":"A"}\n\n' + 'event: done\ndata: {}\n\n' + 'data: {"text":"partial"}';
    const { events, rest } = extractSseEvents(input);
    expect(events.length).toBe(2);
    expect(events[0].data).toEqual({ text: 'A' });
    expect(events[1].event).toBe('done');
    expect(rest).toContain('partial');
  });
});

