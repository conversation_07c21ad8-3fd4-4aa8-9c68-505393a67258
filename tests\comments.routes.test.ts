import { describe, it, expect, beforeAll, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

vi.mock('../server/supabaseClient', () => {
  const getUserClient = (_token: string) => ({
    from: (_table: string) => ({
      select: (_cols: string) => ({ eq: (_c: string, _v: unknown) => ({ order: async () => ({ data: [], error: null }) }) }),
      insert: (row: Record<string, unknown>) => ({ select: () => ({ single: async () => ({ data: { id: 't1', ...row }, error: null }) }) }),
      update: (patch: Record<string, unknown>) => ({ eq: (_c: string, _id: string) => ({ select: () => ({ single: async () => ({ data: patch, error: null }) }) }) }),
      delete: () => ({ eq: async () => ({ error: null }) }),
    }),
  });
  return { getUserClient };
});

beforeAll(() => { process.env.TEST_BYPASS_AUTH = '1'; });

describe('Comments & Threads routes', () => {
  it('POST /api/threads creates thread', async () => {
    const res = await request(app)
      .post('/api/threads')
  .send({ documentId: '11111111-1111-1111-1111-111111111111', textSelection: 'para' })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(201);
    expect(res.body).toHaveProperty('thread');
  });

  it('POST /api/comments creates comment', async () => {
    const res = await request(app)
      .post('/api/comments')
  .send({ threadId: '11111111-1111-1111-1111-111111111111', authorEmail: '<EMAIL>', content: 'Nice!' })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(201);
    expect(res.body).toHaveProperty('comment');
  });
});
