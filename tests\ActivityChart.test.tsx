// @vitest-environment jsdom
import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import ActivityChart from '../components/ActivityChart';
import React from 'react';

describe('ActivityChart', () => {
  it('renders ActivityChart component', () => {
    render(<ActivityChart documents={[]} />);
    // The chart renders a canvas, so check for canvas element
    expect(document.querySelector('canvas')).toBeInTheDocument();
  });
});
