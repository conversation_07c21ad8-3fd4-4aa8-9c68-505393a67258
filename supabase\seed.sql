-- Optional seed script for local development.
-- Intentionally minimal to avoid failing `supabase db push` when seeds are enabled.

-- Safe, idempotent seeds using WHERE NOT EXISTS guards

-- Admin user (email confirmation = false)
-- Creates an admin user in auth.users and corresponding profile with is_admin = true
do $$
declare
  _uid uuid;
begin
  select id into _uid from auth.users where email = '<EMAIL>' limit 1;
  if _uid is null then
    with new_user as (
      insert into auth.users (
        id,
        email,
        encrypted_password,
        email_confirmed_at,
        raw_app_meta_data,
        raw_user_meta_data,
        aud,
        role
      ) values (
        gen_random_uuid(),
        '<EMAIL>',
        crypt('superadmin123', gen_salt('bf')),
        null,
        '{"provider":"email","providers":["email"]}'::jsonb,
        '{}'::jsonb,
        'authenticated',
        'authenticated'
      )
      returning id
    )
    insert into public.profiles (id, name, username, is_admin, plan_name, status)
    select id, 'Super Admin', 'admin', true, 'Enterprise', 'active' from new_user
    on conflict (id) do update set is_admin = excluded.is_admin;
  else
    -- Ensure the existing profile is marked as admin
    insert into public.profiles (id, is_admin, name, username, plan_name, status)
    values (_uid, true, 'Super Admin', 'admin', 'Enterprise', 'active')
    on conflict (id) do update set is_admin = excluded.is_admin;
  end if;
end $$;

-- Templates
insert into public.templates (title, description, category, prompt, required_plan)
select 'Mutual NDA', 'Standard two-way NDA', 'Contracts', 'Draft a mutual NDA between two parties with standard confidentiality, term, exclusions, and governing law.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Mutual NDA');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Freelance Services Agreement', 'Scope, payment, IP, termination', 'Agreements', 'Draft a concise freelance services agreement covering scope, payment terms, IP ownership, confidentiality, and termination.', 'Premium'
where not exists (select 1 from public.templates where title = 'Freelance Services Agreement');

insert into public.templates (title, description, category, prompt, required_plan)
select 'Last Will and Testament', 'Simple individual will', 'Wills', 'Draft a simple last will and testament including executor designation, beneficiaries, and residual clause (jurisdiction-neutral).', 'Registered User'
where not exists (select 1 from public.templates where title = 'Last Will and Testament');

-- Healthcare (15)
insert into public.templates (title, description, category, prompt, required_plan)
select 'Business Associate Agreement (HIPAA)', 'BAA for PHI handling', 'Healthcare', 'Draft a HIPAA-compliant Business Associate Agreement between a Covered Entity and a Business Associate. Include definitions, permitted uses/disclosures, safeguards, breach notification (incl. timelines), subcontractors, termination, and return/destroy of PHI.', 'Premium'
where not exists (select 1 from public.templates where title = 'Business Associate Agreement (HIPAA)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Informed Consent Form', 'Patient treatment consent', 'Healthcare', 'Create an informed consent form for a medical procedure. Use clear, non-technical language. Include risks, benefits, alternatives, aftercare, consent withdrawal, and signature/date fields. Add placeholders for procedure, provider, and patient.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Informed Consent Form');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Telehealth Services Agreement', 'Provider-patient telemedicine terms', 'Healthcare', 'Draft a telehealth services agreement outlining eligibility, technology requirements, privacy, security, emergency limitations, consent, fees, and jurisdiction. Use jurisdiction-neutral language and clear disclaimers about emergencies.', 'Premium'
where not exists (select 1 from public.templates where title = 'Telehealth Services Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Medical Director Agreement', 'Physician director services', 'Healthcare', 'Prepare a Medical Director Agreement covering duties, schedule, reporting, compensation, compliance, credentialing, insurance, non-solicit, and termination for cause/convenience. Include regulatory compliance and anti-kickback representations.', 'Premium'
where not exists (select 1 from public.templates where title = 'Medical Director Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Clinical Trial NDA', 'Confidentiality for trial data', 'Healthcare', 'Draft a mutual NDA specific to clinical trials. Address protocol confidentiality, subject data, intellectual property, publication rights, regulatory disclosures, and residual knowledge. Include survival and equitable relief.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Clinical Trial NDA');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Patient Privacy Notice', 'HIPAA Notice of Privacy Practices', 'Healthcare', 'Write a Notice of Privacy Practices compliant with HIPAA. Explain uses/disclosures, patient rights, complaints process, authorization revocation, and contact info. Use plain language and headings for readability.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Patient Privacy Notice');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Healthcare Services Agreement', 'Provider services to facility', 'Healthcare', 'Draft a healthcare services agreement between a provider and a facility. Include scope, staffing, compliance, billing, quality metrics, audits, insurance, and term/termination. Add anti-kickback and Stark compliance clauses.', 'Premium'
where not exists (select 1 from public.templates where title = 'Healthcare Services Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Data Use Agreement (HIPAA Limited Data Set)', 'Limited Data Set terms', 'Healthcare', 'Create a HIPAA DUA for Limited Data Sets. Specify permitted uses, safeguards, redisclosure limits, reporting of violations, subcontractors, and termination. Provide placeholders for data categories and purpose.', 'Premium'
where not exists (select 1 from public.templates where title = 'Data Use Agreement (HIPAA Limited Data Set)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'IRB Authorization Letter', 'Institutional Review Board authorization', 'Healthcare', 'Draft an IRB authorization/approval letter template including protocol title, principal investigator, approval date, conditions, reporting obligations, and expiration/continuing review info. Use formal institutional tone.', 'Registered User'
where not exists (select 1 from public.templates where title = 'IRB Authorization Letter');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Physician Employment Agreement', 'Physician hire terms', 'Healthcare', 'Create a physician employment agreement covering duties, call schedule, compensation/bonuses, malpractice tail, credentialing, non-compete (jurisdiction dependent), non-solicit, compliance, and termination. Include fair market value representation.', 'Premium'
where not exists (select 1 from public.templates where title = 'Physician Employment Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Business Continuity Plan (Clinic)', 'BCP for clinical operations', 'Healthcare', 'Draft a Business Continuity Plan for a small clinic. Include risk assessment, critical systems, communication tree, backup procedures, alternative sites, and recovery steps. Provide checklists and roles.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Business Continuity Plan (Clinic)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Medical Device Evaluation Agreement', 'Device trial with provider', 'Healthcare', 'Prepare an agreement for evaluation of a medical device in a clinical setting. Include safety responsibilities, training, data collection ownership, no-charge loan terms, IP, confidentiality, and disclaimers.', 'Premium'
where not exists (select 1 from public.templates where title = 'Medical Device Evaluation Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Patient Financial Responsibility Form', 'Billing acknowledgement', 'Healthcare', 'Create a patient financial responsibility acknowledgement describing coverage verification limits, copays, deductibles, non-covered services, billing disputes, and payment options. Use accessible language and signature lines.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Patient Financial Responsibility Form');
insert into public.templates (title, description, category, prompt, required_plan)
select 'De-Identification Attestation', 'HIPAA de-identification statement', 'Healthcare', 'Draft an attestation that data meets HIPAA de-identification standards (Safe Harbor or Expert Determination). Include scope, methods, residual risk statement, and signer qualifications.', 'Premium'
where not exists (select 1 from public.templates where title = 'De-Identification Attestation');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Clinic Vendor Agreement', 'Vendor services to clinic', 'Healthcare', 'Draft a vendor agreement for a medical clinic covering services, SLAs, access to PHI limits, security obligations, incident reporting, insurance, indemnity, and termination assistance. Include a BAA if needed.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Clinic Vendor Agreement');

-- Technology / SaaS (15)
insert into public.templates (title, description, category, prompt, required_plan)
select 'SaaS Master Subscription Agreement', 'MSA for SaaS product', 'Technology', 'Draft a SaaS MSA including license/rights, acceptable use, availability SLAs, support, data security, privacy, IP, feedback license, warranties, indemnities, liability caps, pricing, renewals, and termination.', 'Premium'
where not exists (select 1 from public.templates where title = 'SaaS Master Subscription Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Service Level Agreement (SLA)', 'Availability and support metrics', 'Technology', 'Create an SLA for a cloud service. Define uptime, maintenance windows, response/restore times, exclusions, service credits, claim process, and reporting. Be precise and measurable.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Service Level Agreement (SLA)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Data Processing Addendum (GDPR)', 'Controller-Processor DPA', 'Technology', 'Draft a GDPR-compliant DPA covering subject matter, duration, nature, purpose, data categories, data subject rights support, security measures, sub-processors, transfers, audits, breach notice, and deletion/return.', 'Premium'
where not exists (select 1 from public.templates where title = 'Data Processing Addendum (GDPR)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Acceptable Use Policy (AUP)', 'User behavior policy', 'Technology', 'Write an AUP for a SaaS platform. Prohibit abuse, security violations, illegal content, spamming, scraping, and disruptions. Describe enforcement, reporting, and escalation.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Acceptable Use Policy (AUP)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Mutual NDA (Tech)', 'NDA for tech collaboration', 'Technology', 'Create a mutual NDA tailored to software and product roadmaps, source code, APIs, security posture, and customer lists. Include residuals, exclusions, and export control.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Mutual NDA (Tech)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Pilot Agreement (SaaS)', 'Limited pilot terms', 'Technology', 'Draft a pilot agreement with limited license, sandbox use, no production warranty, feedback license, data export, confidentiality, and conversion mechanics with pricing triggers.', 'Premium'
where not exists (select 1 from public.templates where title = 'Pilot Agreement (SaaS)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Reseller Agreement', 'Channel resale terms', 'Technology', 'Prepare a reseller agreement covering territory, exclusivity, branding, ordering, pricing, support responsibilities, training, compliance, and indemnity. Include end-user agreement pass-through.', 'Premium'
where not exists (select 1 from public.templates where title = 'Reseller Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Professional Services SOW', 'Implementation scope of work', 'Technology', 'Create a Statement of Work template for implementation services. Include scope, milestones, deliverables, assumptions, acceptance criteria, change control, and fees/expenses.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Professional Services SOW');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Open Source Compliance Policy', 'Use and contribution policy', 'Technology', 'Draft a policy for open-source use and contributions. Include license review, approval workflow, attribution, obligations, vulnerability handling, and contribution guidelines.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Open Source Compliance Policy');
insert into public.templates (title, description, category, prompt, required_plan)
select 'API License Agreement', 'Terms for API usage', 'Technology', 'Write an API license agreement detailing key restrictions, rate limits, keys/rotation, data caching/storage, attribution, security, and termination. Provide clear definitions and examples.', 'Premium'
where not exists (select 1 from public.templates where title = 'API License Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Security Incident Response Plan', 'IR plan for SaaS', 'Technology', 'Create a security incident response plan with roles, severity levels, triage, containment, eradication, recovery, postmortems, and communication templates. Reference industry standards.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Security Incident Response Plan');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Subprocessor Disclosure', 'List of subprocessors', 'Technology', 'Generate a subprocessor disclosure template showing services, locations, and purposes. Add change notification and objection process consistent with GDPR.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Subprocessor Disclosure');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Penetration Testing Rules of Engagement', 'Pentest scope and rules', 'Technology', 'Draft rules of engagement for third-party pentesting: scope, targets, methods, scheduling, data handling, reporting, and legal indemnities. Include sensitive systems exclusions.', 'Premium'
where not exists (select 1 from public.templates where title = 'Penetration Testing Rules of Engagement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Data Retention & Deletion Policy', 'Lifecycle and deletion SLAs', 'Technology', 'Write a data retention/deletion policy specifying categories, retention periods, legal holds, backups, and deletion SLAs including user-export pathways. Keep concise tables.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Data Retention & Deletion Policy');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Customer DPA Questionnaire', 'Customer security questionnaire', 'Technology', 'Create a structured questionnaire to collect controller-required details for a DPA: processing activities, locations, subprocessors, TOMs, certifications, and contact points.', 'Premium'
where not exists (select 1 from public.templates where title = 'Customer DPA Questionnaire');

-- Real Estate (15)
insert into public.templates (title, description, category, prompt, required_plan)
select 'Residential Lease Agreement', 'Fixed-term residential lease', 'Real Estate', 'Draft a residential lease with term, rent, deposit, maintenance, utilities, quiet enjoyment, entry rights, assignment/sublet, default/remedies, and local law disclosures placeholders.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Residential Lease Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Commercial Lease Agreement', 'Office/retail/industrial lease', 'Real Estate', 'Create a commercial lease including permitted use, build-out, CAM charges, taxes, insurance, indemnity, compliance, assignment, default, and restoration.', 'Premium'
where not exists (select 1 from public.templates where title = 'Commercial Lease Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Purchase & Sale Agreement (Residential)', 'Home purchase terms', 'Real Estate', 'Draft a residential purchase agreement with price, contingencies, inspections, disclosures, title, escrow, prorations, closing, and remedies. Add placeholders for dates and addenda.', 'Premium'
where not exists (select 1 from public.templates where title = 'Purchase & Sale Agreement (Residential)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Real Estate NDA', 'Confidentiality for property info', 'Real Estate', 'Write an NDA for property marketing materials and financials shared during diligence. Include non-circumvention, no-solicit, and return/destroy of materials.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Real Estate NDA');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Property Management Agreement', 'Landlord-agent management terms', 'Real Estate', 'Prepare a property management agreement detailing scope (rent collection, maintenance), fees, bank accounts, authority limits, reporting, insurance, compliance, and termination.', 'Premium'
where not exists (select 1 from public.templates where title = 'Property Management Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Short-Term Rental Agreement', 'Vacation/short-term rental terms', 'Real Estate', 'Create a short-term rental agreement with house rules, occupancy limits, deposits, cancellations, liability waivers, and local ordinance compliance.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Short-Term Rental Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Brokerage Listing Agreement', 'Exclusive right to sell', 'Real Estate', 'Draft a listing agreement covering term, commission, exclusions, marketing, disclosures, dual agency, and early termination. Include regulatory compliance placeholders.', 'Premium'
where not exists (select 1 from public.templates where title = 'Brokerage Listing Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Letter of Intent (LOI) - Lease', 'Non-binding lease LOI', 'Real Estate', 'Write a non-binding LOI summarizing key lease terms: premises, term, rent, TI allowance, options, and exclusivity window. Include confidentiality and good-faith negotiation.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Letter of Intent (LOI) - Lease');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Rent Increase Notice', 'Landlord rent increase letter', 'Real Estate', 'Prepare a compliant rent increase notice with effective date, new rent, basis, and options. Include placeholders for jurisdictional notice periods.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Rent Increase Notice');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Eviction Notice (For Cause)', 'Notice to cure or quit', 'Real Estate', 'Draft a cause-based notice outlining violations, cure periods, consequences, and service method. Keep neutral and include statutory references placeholders.', 'Premium'
where not exists (select 1 from public.templates where title = 'Eviction Notice (For Cause)');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Lease Amendment', 'Modify lease terms', 'Real Estate', 'Create a lease amendment template to change term, rent, occupants, or rules. Reference original lease, effective date, and confirmation of unchanged terms.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Lease Amendment');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Tenant Estoppel Certificate', 'Tenant confirmations for lenders/buyers', 'Real Estate', 'Write an estoppel certificate confirming lease in force, defaults, deposits, options, and offsets. Provide signature blocks and attach copy of lease if needed.', 'Premium'
where not exists (select 1 from public.templates where title = 'Tenant Estoppel Certificate');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Move-In/Move-Out Checklist', 'Condition checklist for rentals', 'Real Estate', 'Provide a checklist to record unit condition at move-in and move-out including photos, meter readings, keys, and damages. Include signature fields.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Move-In/Move-Out Checklist');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Commercial Sublease Agreement', 'Sublease for commercial space', 'Real Estate', 'Draft a commercial sublease addressing landlord consent, permitted use, rent flow-through, improvements, liability, and remedies. Incorporate master lease terms by reference.', 'Premium'
where not exists (select 1 from public.templates where title = 'Commercial Sublease Agreement');
insert into public.templates (title, description, category, prompt, required_plan)
select 'Security Deposit Return Letter', 'Deposit disposition notice', 'Real Estate', 'Create a letter itemizing deposit deductions with supporting basis and payment details. Include deadlines and dispute process placeholders per local law.', 'Registered User'
where not exists (select 1 from public.templates where title = 'Security Deposit Return Letter');

-- Pricing Plans
insert into public.pricing_plans (name, price, price_detail, features, cta, is_featured, sort_order)
select 'Registered User', 'Free', '/ month', ARRAY[
  '5 document generations/month',
  'Standard templates',
  'Document history & versions',
  'E-signatures',
  'Comments & notifications',
  'Email support'
]::text[], 'Get Started', false, 1
where not exists (select 1 from public.pricing_plans where name = 'Registered User');

insert into public.pricing_plans (name, price, price_detail, features, cta, is_featured, sort_order)
select 'Premium', '$49', '/ month', ARRAY[
  'Unlimited generations',
  'AI document analysis & suggestions',
  'Clause library & custom templates',
  'Contract lifecycle & approvals',
  'Obligations tracking & key dates',
  'Workflow automation',
  'Team collaboration',
  'Integrations',
  'Priority support'
]::text[], 'Upgrade', true, 2
where not exists (select 1 from public.pricing_plans where name = 'Premium');

insert into public.pricing_plans (name, price, price_detail, features, cta, is_featured, sort_order)
select 'Enterprise', 'Custom', '', ARRAY[
  'Everything in Premium',
  'Advanced security & SSO',
  'API & custom integrations',
  'Audit logs & controls',
  'Dedicated account manager'
]::text[], 'Contact Sales', false, 3
where not exists (select 1 from public.pricing_plans where name = 'Enterprise');
