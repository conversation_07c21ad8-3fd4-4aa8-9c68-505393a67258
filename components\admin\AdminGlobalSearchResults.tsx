
import React from 'react';
import { AdminSearchResult } from '../../types';
import { UsersIcon, BillingIcon, TemplateIcon, EditIcon } from '../Icons';

interface AdminGlobalSearchResultsProps {
  results: AdminSearchResult[];
  onResultClick: (result: AdminSearchResult) => void;
  searchTerm: string;
}

const AdminGlobalSearchResults: React.FC<AdminGlobalSearchResultsProps> = ({ results, onResultClick, searchTerm }) => {
    const groupedResults = results.reduce((acc, result) => {
        (acc[result.type] = acc[result.type] || []).push(result);
        return acc;
    }, {} as Record<string, AdminSearchResult[]>);

    const resultTypes = [
        { type: 'user', title: 'Users', icon: UsersIcon },
        { type: 'plan', title: 'Plans', icon: BillingIcon },
        { type: 'template', title: 'Templates', icon: TemplateIcon },
        { type: 'page', title: 'Admin Pages', icon: EditIcon },
    ];

    return (
        <div className="absolute top-full mt-2 w-full max-w-md max-h-96 overflow-y-auto rounded-lg shadow-lg bg-white dark:bg-zinc-800 ring-1 ring-black dark:ring-zinc-700 ring-opacity-5 z-20">
            {results.length > 0 ? (
                <div className="p-2">
                    {resultTypes.map(resultType => (
                        groupedResults[resultType.type] && (
                            <div key={resultType.type}>
                                <h4 className="px-2 py-1 text-xs font-semibold text-zinc-500 dark:text-zinc-400">{resultType.title}</h4>
                                <ul className="mb-2">
                                    {groupedResults[resultType.type].map(result => (
                                        <li key={`${result.type}-${result.id}`}>
                                            <button
                                                onClick={() => onResultClick(result)}
                                                className="w-full text-left flex items-center gap-3 px-2 py-1.5 rounded-md hover:bg-zinc-100 dark:hover:bg-zinc-700"
                                            >
                                                <resultType.icon className="w-4 h-4 text-zinc-600 dark:text-zinc-300 flex-shrink-0" />
                                                <div className="flex-1 truncate">
                                                    <p className="text-sm text-zinc-800 dark:text-zinc-100 truncate">{result.title}</p>
                                                    {result.context && <p className="text-xs text-zinc-500 dark:text-zinc-400 truncate">{result.context}</p>}
                                                </div>
                                            </button>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        )
                    ))}
                </div>
            ) : (
                <div className="p-4 text-center text-sm text-zinc-500 dark:text-zinc-400">
                    No results found for "{searchTerm}"
                </div>
            )}
        </div>
    );
};

export default AdminGlobalSearchResults;
