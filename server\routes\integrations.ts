import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { requirePremium } from '../middleware/plan';
import { getUserClient } from '../supabaseClient';

const router = Router();

const insertSchema = z.object({
  name: z.string().min(1),
  description: z.string().min(1),
  status: z.enum(['connected','disconnected']).default('disconnected'),
  syncStatus: z.enum(['syncing','synced','error']).nullable().optional(),
  config: z.record(z.any()).optional(),
});
const updateSchema = insertSchema.partial();

router.get('/', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('integrations').select('*').order('id');
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ integrations: data });
});

router.post('/', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const parsed = insertSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data: auth } = await supa.auth.getUser();
  const userId = auth.user?.id;
  const { data, error } = await supa
    .from('integrations')
    .insert({
      user_id: userId,
      name: parsed.data.name,
      description: parsed.data.description,
      status: parsed.data.status,
      sync_status: parsed.data.syncStatus ?? null,
      config: parsed.data.config ?? {},
    })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ integration: data });
});

router.put('/:id', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = updateSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{
    name: string;
    description: string;
    status: string;
    sync_status: string | null;
    config: Record<string, unknown>;
  }> = {
    name: parsed.data.name,
    description: parsed.data.description,
    status: parsed.data.status,
    sync_status: parsed.data.syncStatus,
    config: parsed.data.config,
  };
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('integrations').update(patch).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ integration: data });
});

router.delete('/:id', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('integrations').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

export default router;
