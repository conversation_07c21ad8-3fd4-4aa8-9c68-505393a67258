import React, { useEffect, useState, useRef, useCallback } from 'react';
import type { PDFDocumentProxy } from 'pdfjs-dist';

type PdfThumbnailsProps = {
  clientId: string;
  assetId: string;
  url: string; // storage://contractgini/... or direct
  maxPages?: number;
  onSelect: (page: number) => void;
};

type ThumbnailState = {
  page: number;
  dataUrl: string | null;
  isLoading: boolean;
  isVisible: boolean;
};

const PdfThumbnails: React.FC<PdfThumbnailsProps> = ({ clientId, assetId, url, maxPages = 6, onSelect }) => {
  const [thumbs, setThumbs] = useState<ThumbnailState[]>([]);
  const [pdfDoc, setPdfDoc] = useState<PDFDocumentProxy | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const thumbnailRefs = useRef<Map<number, HTMLDivElement>>(new Map());

  // Initialize PDF document and thumbnail placeholders
  useEffect(() => {
    let mounted = true;
    (async () => {
      try {
        let href = url;
        if (url.startsWith('storage://contractgini/')) {
          try {
            const { apiFetch } = await import('../lib/api');
            const resp = await apiFetch<{ url: string }>(`/api/clients/${clientId}/assets/${assetId}/signed-url?ttl=${60 * 5}`);
            if (resp?.url) {href = resp.url;}
          } catch {
            // Ignore errors when fetching signed URL, fallback to original URL
          }
        }
        const pdfjs = await import('pdfjs-dist');
        const workerSrc = (await import('pdfjs-dist/build/pdf.worker.min.mjs')).default;
        pdfjs.GlobalWorkerOptions.workerSrc = workerSrc;
        const loadingTask = pdfjs.getDocument(href);
        const pdf = await loadingTask.promise;
        
        if (mounted) {
          setPdfDoc(pdf);
          const count = Math.min(pdf.numPages, maxPages);
          const initialThumbs: ThumbnailState[] = [];
          for (let i = 1; i <= count; i++) {
            initialThumbs.push({
              page: i,
              dataUrl: null,
              isLoading: false,
              isVisible: false
            });
          }
          setThumbs(initialThumbs);
        }
      } catch {
        if (mounted) {
          setPdfDoc(null);
          setThumbs([]);
        }
      }
    })();
    return () => { mounted = false; };
  }, [url, maxPages, clientId, assetId]);

  // Function to load a specific thumbnail
  const loadThumbnail = useCallback(async (pageNumber: number) => {
    if (!pdfDoc) {return;}
    
    setThumbs(prev => prev.map(thumb => 
      thumb.page === pageNumber 
        ? { ...thumb, isLoading: true }
        : thumb
    ));

    try {
      const page = await pdfDoc.getPage(pageNumber);
      const viewport = page.getViewport({ scale: 0.2 });
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (!ctx) {return;}
      
      canvas.width = viewport.width;
      canvas.height = viewport.height;
      await page.render({ canvas, canvasContext: ctx, viewport }).promise;
      const dataUrl = canvas.toDataURL('image/png');
      
      setThumbs(prev => prev.map(thumb => 
        thumb.page === pageNumber 
          ? { ...thumb, dataUrl, isLoading: false }
          : thumb
      ));
    } catch {
      setThumbs(prev => prev.map(thumb => 
        thumb.page === pageNumber 
          ? { ...thumb, isLoading: false }
          : thumb
      ));
    }
  }, [pdfDoc]);

  // Set up intersection observer
  useEffect(() => {
    if (!pdfDoc || thumbs.length === 0) {return;}

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          const pageNumber = parseInt(entry.target.getAttribute('data-page') || '0');
          if (entry.isIntersecting) {
            const thumb = thumbs.find(t => t.page === pageNumber);
            if (thumb && !thumb.dataUrl && !thumb.isLoading) {
              loadThumbnail(pageNumber);
            }
          }
        });
      },
      {
        root: null,
        rootMargin: '50px',
        threshold: 0.1
      }
    );

    // Observe all thumbnail containers
    thumbnailRefs.current.forEach((element) => {
      if (observerRef.current) {
        observerRef.current.observe(element);
      }
    });

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [pdfDoc, thumbs, loadThumbnail]);

  // Ref callback to register thumbnail elements
  const setThumbnailRef = useCallback((element: HTMLDivElement | null, pageNumber: number) => {
    if (element) {
      thumbnailRefs.current.set(pageNumber, element);
    } else {
      thumbnailRefs.current.delete(pageNumber);
    }
  }, []);

  if (thumbs.length === 0) {return null;}
  return (
    <div className="w-28 h-full overflow-y-auto border-r border-zinc-200 dark:border-zinc-800 p-2 space-y-2 bg-white dark:bg-zinc-900">
      {thumbs.map(thumb => (
        <div
          key={thumb.page}
          ref={(el) => setThumbnailRef(el, thumb.page)}
          data-page={thumb.page}
          className="block w-full"
        >
          <button onClick={() => onSelect(thumb.page)} className="block w-full focus:outline-none">
            <div className="w-full aspect-[3/4] rounded border border-zinc-200 dark:border-zinc-800 bg-zinc-100 dark:bg-zinc-800 flex items-center justify-center">
              {thumb.isLoading ? (
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-zinc-400"></div>
              ) : thumb.dataUrl ? (
                <img 
                  src={thumb.dataUrl} 
                  alt={`Page ${thumb.page}`} 
                  className="w-full h-full object-cover rounded" 
                />
              ) : (
                <div className="text-zinc-400 text-xs">Page {thumb.page}</div>
              )}
            </div>
            <div className="text-xs text-center text-zinc-500 mt-1">{thumb.page}</div>
          </button>
        </div>
      ))}
    </div>
  );
};

export default PdfThumbnails;
