# Message Broker Deployment Guide

The event bus used by LexiGen fans out `documentStatusChanged` events through Redis pub/sub so that any API replica can stream updates to connected clients.

## 1. Provision Redis

Choose one of the following options:

- **Managed service** – e.g., Upstash, Redis Cloud, AWS ElastiCache. Copy the connection URL that includes credentials (TLS URLs are supported).
- **Self-hosted container** – run Redis alongside the app with Docker:
  ```bash
  docker run -d \
    --name lexigen-redis \
    -p 6379:6379 \
    redis:7
  ```

Ensure the Redis instance is reachable from every API server.

## 2. Configure environment variables

On each server (and in CI/CD secrets):

```bash
REDIS_ENABLED=true
REDIS_URL="redis://:<password>@<host>:<port>"
```

- `REDIS_ENABLED` must be `true` for the event bus to connect.
- `REDIS_URL` defaults to `redis://127.0.0.1:6379` if omitted. Use a TLS URL (e.g., `rediss://`) when required by your provider.

After updating the environment, restart the API process so the new settings take effect.

## 3. Verify the broker connection

1. Deploy the API with the new environment variables.
2. Trigger a document status transition (e.g., change a document to "approved").
3. Tail the server logs; the `server/routes/events.ts` SSE endpoint should stream a single `documentStatusChanged` event to connected clients. Any Redis connection issues are logged with the `[eventBus]` prefix.

## 4. High availability tips

- For production, prefer managed Redis clusters with automatic failover.
- If you run Redis yourself, configure persistence (AOF or RDB) and monitoring.
- Keep the Redis security group/firewall restricted to application servers; never expose the service publicly without authentication.
