
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/ForgotPassword.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> ForgotPassword.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/65</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/65</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">&nbsp;
&nbsp;
<span class="cstat-no" title="statement not covered" >import React, { useState } from 'react';</span>
<span class="cstat-no" title="statement not covered" >import { MailIcon, UserIcon } from './Icons';</span>
&nbsp;
interface ForgotPasswordProps {
  onSwitchToLogin: () =&gt; void;
  handleForgotPassword: (email: string) =&gt; boolean;
  authError: string | null;
}
&nbsp;
<span class="cstat-no" title="statement not covered" >const ForgotPassword: React.FC&lt;ForgotPasswordProps&gt; = ({ onSwitchToLogin, handleForgotPassword, authError }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const [email, setEmail] = useState('');</span>
<span class="cstat-no" title="statement not covered" >  const [isSubmitted, setIsSubmitted] = useState(false);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  const handleSubmit = (e: React.FormEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    e.preventDefault();</span>
<span class="cstat-no" title="statement not covered" >    if (handleForgotPassword(email)) {</span>
<span class="cstat-no" title="statement not covered" >        setIsSubmitted(true);</span>
<span class="cstat-no" title="statement not covered" >    }</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (isSubmitted) {</span>
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >       &lt;div className="text-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;MailIcon className="mx-auto h-12 w-12 text-brand-500" /&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;h2 className="mt-4 text-2xl font-bold text-zinc-900 dark:text-white"&gt;Check your inbox&lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;p className="mt-2 text-zinc-600 dark:text-zinc-400"&gt;</span>
<span class="cstat-no" title="statement not covered" >                If an account with the email &lt;span className="font-semibold text-zinc-800 dark:text-zinc-200"&gt;{email}&lt;/span&gt; exists, we've sent a link to reset your password.</span>
<span class="cstat-no" title="statement not covered" >            &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;p className="mt-6 text-center text-sm text-zinc-600 dark:text-zinc-400"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;button onClick={onSwitchToLogin} className="font-medium text-brand-600 hover:text-brand-700 focus:outline-none"&gt;</span>
                    Back to Login
<span class="cstat-no" title="statement not covered" >                &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
    );
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="w-full max-w-md"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="text-center mb-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;h2 className="text-3xl font-bold text-zinc-900 dark:text-white"&gt;Forgot Password&lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;p className="text-zinc-500 dark:text-zinc-400 mt-2"&gt;Enter your email and we'll send you a reset link.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;form onSubmit={handleSubmit} className="space-y-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;label htmlFor="email-forgot" className="block text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Email Address&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="mt-1 relative rounded-md shadow-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >             &lt;div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;UserIcon className="h-5 w-5 text-zinc-400" /&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;input </span>
<span class="cstat-no" title="statement not covered" >              type="email" </span>
<span class="cstat-no" title="statement not covered" >              id="email-forgot" </span>
<span class="cstat-no" title="statement not covered" >              name="email" </span>
<span class="cstat-no" title="statement not covered" >              autoComplete="email"</span>
<span class="cstat-no" title="statement not covered" >              required </span>
<span class="cstat-no" title="statement not covered" >              className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 focus:border-brand-500 focus:ring-brand-500 sm:text-sm p-3" </span>
<span class="cstat-no" title="statement not covered" >              placeholder="<EMAIL>"</span>
<span class="cstat-no" title="statement not covered" >              value={email}</span>
<span class="cstat-no" title="statement not covered" >              onChange={(e) =&gt; setEmail(e.target.value)}</span>
<span class="cstat-no" title="statement not covered" >            /&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        {authError &amp;&amp; &lt;p className="text-sm text-red-600 text-center"&gt;{authError}&lt;/p&gt;}</span>
<span class="cstat-no" title="statement not covered" >        &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;button type="submit" className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-brand-600 hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500"&gt;</span>
            Send Reset Link
<span class="cstat-no" title="statement not covered" >          &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/form&gt;</span>
<span class="cstat-no" title="statement not covered" >       &lt;p className="mt-6 text-center text-sm text-zinc-600 dark:text-zinc-400"&gt;</span>
<span class="cstat-no" title="statement not covered" >        Remember your password?{' '}</span>
<span class="cstat-no" title="statement not covered" >        &lt;button onClick={onSwitchToLogin} className="font-medium text-brand-600 hover:text-brand-700 focus:outline-none"&gt;</span>
          Log in
<span class="cstat-no" title="statement not covered" >        &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >};</span>
<span class="cstat-no" title="statement not covered" >export default ForgotPassword;</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    