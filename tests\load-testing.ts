#!/usr/bin/env node
/**
 * Load Testing Script for LexiGen
 * 
 * This script simulates real user interactions to test system performance
 * under various load conditions. It measures response times, throughput,
 * and system stability.
 */

import { performance } from 'perf_hooks';
import { setTimeout } from 'timers/promises';

// Load testing configuration
const LOAD_TEST_CONFIG = {
  BASE_URL: process.env.LOAD_TEST_URL || 'http://localhost:5173',
  CONCURRENT_USERS: parseInt(process.env.CONCURRENT_USERS || '100'),
  TEST_DURATION: parseInt(process.env.TEST_DURATION || '300'), // 5 minutes
  RAMP_UP_TIME: parseInt(process.env.RAMP_UP_TIME || '60'), // 1 minute
  THINK_TIME: parseInt(process.env.THINK_TIME || '2000'), // 2 seconds between actions
  MAX_RESPONSE_TIME: parseInt(process.env.MAX_RESPONSE_TIME || '5000'), // 5 seconds
  ERROR_THRESHOLD: parseFloat(process.env.ERROR_THRESHOLD || '0.05'), // 5% error rate
};

// Test scenarios
const USER_SCENARIOS = [
  {
    name: 'Document Browser',
    weight: 40, // 40% of users
    actions: [
      'login',
      'viewDashboard',
      'browseDocuments',
      'searchDocuments',
      'viewDocument',
      'logout'
    ]
  },
  {
    name: 'Document Creator',
    weight: 30, // 30% of users
    actions: [
      'login',
      'viewDashboard',
      'createDocument',
      'editDocument',
      'saveDocument',
      'logout'
    ]
  },
  {
    name: 'Template User',
    weight: 20, // 20% of users
    actions: [
      'login',
      'viewTemplates',
      'useTemplate',
      'customizeTemplate',
      'saveDocument',
      'logout'
    ]
  },
  {
    name: 'Collaborator',
    weight: 10, // 10% of users
    actions: [
      'login',
      'viewSharedDocuments',
      'addComment',
      'reviewDocument',
      'approveDocument',
      'logout'
    ]
  }
];

// Performance metrics
interface LoadTestMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  requestsPerSecond: number;
  errorRate: number;
  responseTimePercentiles: {
    p50: number;
    p90: number;
    p95: number;
    p99: number;
  };
  scenarioMetrics: Map<string, {
    requests: number;
    avgResponseTime: number;
    errorRate: number;
  }>;
}

// Request result interface
interface RequestResult {
  scenario: string;
  action: string;
  responseTime: number;
  success: boolean;
  error?: string;
  timestamp: number;
}

class LoadTester {
  private results: RequestResult[] = [];
  private activeUsers = 0;
  private testStartTime = 0;
  private testEndTime = 0;

  constructor(private config: typeof LOAD_TEST_CONFIG) {}

  /**
   * Simulate a user action (HTTP request)
   */
  private async simulateRequest(
    scenario: string,
    action: string,
    _userId: number
  ): Promise<RequestResult> {
    const startTime = performance.now();
    const timestamp = Date.now();

    try {
      // Simulate different types of requests with varying response times
      let simulatedDelay: number;
      
      switch (action) {
        case 'login':
          simulatedDelay = 200 + Math.random() * 300; // 200-500ms
          break;
        case 'viewDashboard':
          simulatedDelay = 300 + Math.random() * 500; // 300-800ms
          break;
        case 'browseDocuments':
          simulatedDelay = 500 + Math.random() * 1000; // 500-1500ms
          break;
        case 'searchDocuments':
          simulatedDelay = 400 + Math.random() * 800; // 400-1200ms
          break;
        case 'createDocument':
          simulatedDelay = 600 + Math.random() * 1200; // 600-1800ms
          break;
        case 'editDocument':
          simulatedDelay = 100 + Math.random() * 200; // 100-300ms
          break;
        case 'saveDocument':
          simulatedDelay = 800 + Math.random() * 1500; // 800-2300ms
          break;
        case 'viewTemplates':
          simulatedDelay = 400 + Math.random() * 600; // 400-1000ms
          break;
        case 'useTemplate':
          simulatedDelay = 700 + Math.random() * 1000; // 700-1700ms
          break;
        default:
          simulatedDelay = 200 + Math.random() * 400; // 200-600ms
      }

      // Add some network variability
      simulatedDelay += Math.random() * 100;

      // Simulate occasional slow requests (5% chance)
      if (Math.random() < 0.05) {
        simulatedDelay *= 3;
      }

      // Simulate request
      await setTimeout(simulatedDelay);

      const responseTime = performance.now() - startTime;
      
      // Simulate occasional failures (2% chance)
      const success = Math.random() > 0.02 && responseTime < this.config.MAX_RESPONSE_TIME;

      return {
        scenario,
        action,
        responseTime,
        success,
        error: success ? undefined : 'Simulated failure or timeout',
        timestamp
      };
    } catch (error) {
      const responseTime = performance.now() - startTime;
      return {
        scenario,
        action,
        responseTime,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp
      };
    }
  }

  /**
   * Simulate a user session following a specific scenario
   */
  private async simulateUserSession(scenario: typeof USER_SCENARIOS[0], userId: number): Promise<void> {
    console.log(`User ${userId} starting scenario: ${scenario.name}`);
    
    for (const action of scenario.actions) {
      const result = await this.simulateRequest(scenario.name, action, userId);
      this.results.push(result);
      
      // Think time between actions
      if (action !== 'logout') {
        const thinkTime = this.config.THINK_TIME + (Math.random() - 0.5) * 1000;
        await setTimeout(Math.max(100, thinkTime));
      }
    }
    
    console.log(`User ${userId} completed scenario: ${scenario.name}`);
  }

  /**
   * Select a random scenario based on weights
   */
  private selectScenario(): typeof USER_SCENARIOS[0] {
    const random = Math.random() * 100;
    let cumulative = 0;
    
    for (const scenario of USER_SCENARIOS) {
      cumulative += scenario.weight;
      if (random <= cumulative) {
        return scenario;
      }
    }
    
    return USER_SCENARIOS[0]; // Fallback
  }

  /**
   * Calculate performance metrics from results
   */
  private calculateMetrics(): LoadTestMetrics {
    const responseTimes = this.results.map(r => r.responseTime);
    const successfulRequests = this.results.filter(r => r.success);
    const failedRequests = this.results.filter(r => !r.success);
    
    responseTimes.sort((a, b) => a - b);
    
    const testDuration = (this.testEndTime - this.testStartTime) / 1000; // seconds
    
    // Calculate percentiles
    const getPercentile = (arr: number[], percentile: number): number => {
      const index = Math.ceil((percentile / 100) * arr.length) - 1;
      return arr[Math.max(0, index)] || 0;
    };

    // Calculate scenario-specific metrics
    const scenarioMetrics = new Map<string, {
      requests: number;
      avgResponseTime: number;
      errorRate: number;
    }>();

    for (const scenario of USER_SCENARIOS) {
      const scenarioResults = this.results.filter(r => r.scenario === scenario.name);
      const scenarioSuccessful = scenarioResults.filter(r => r.success);
      
      if (scenarioResults.length > 0) {
        scenarioMetrics.set(scenario.name, {
          requests: scenarioResults.length,
          avgResponseTime: scenarioResults.reduce((sum, r) => sum + r.responseTime, 0) / scenarioResults.length,
          errorRate: (scenarioResults.length - scenarioSuccessful.length) / scenarioResults.length
        });
      }
    }

    return {
      totalRequests: this.results.length,
      successfulRequests: successfulRequests.length,
      failedRequests: failedRequests.length,
      averageResponseTime: responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length,
      minResponseTime: Math.min(...responseTimes),
      maxResponseTime: Math.max(...responseTimes),
      requestsPerSecond: this.results.length / testDuration,
      errorRate: failedRequests.length / this.results.length,
      responseTimePercentiles: {
        p50: getPercentile(responseTimes, 50),
        p90: getPercentile(responseTimes, 90),
        p95: getPercentile(responseTimes, 95),
        p99: getPercentile(responseTimes, 99)
      },
      scenarioMetrics
    };
  }

  /**
   * Print detailed test results
   */
  private printResults(metrics: LoadTestMetrics): void {
    console.log('\n' + '='.repeat(60));
    console.log('LOAD TEST RESULTS');
    console.log('='.repeat(60));
    
    console.log('\nTest Configuration:');
    console.log(`- Concurrent Users: ${this.config.CONCURRENT_USERS}`);
    console.log(`- Test Duration: ${this.config.TEST_DURATION}s`);
    console.log(`- Ramp-up Time: ${this.config.RAMP_UP_TIME}s`);
    console.log(`- Think Time: ${this.config.THINK_TIME}ms`);
    
    console.log('\nOverall Performance:');
    console.log(`- Total Requests: ${metrics.totalRequests}`);
    console.log(`- Successful Requests: ${metrics.successfulRequests}`);
    console.log(`- Failed Requests: ${metrics.failedRequests}`);
    console.log(`- Error Rate: ${(metrics.errorRate * 100).toFixed(2)}%`);
    console.log(`- Requests/Second: ${metrics.requestsPerSecond.toFixed(2)}`);
    
    console.log('\nResponse Times:');
    console.log(`- Average: ${metrics.averageResponseTime.toFixed(2)}ms`);
    console.log(`- Minimum: ${metrics.minResponseTime.toFixed(2)}ms`);
    console.log(`- Maximum: ${metrics.maxResponseTime.toFixed(2)}ms`);
    console.log(`- 50th Percentile: ${metrics.responseTimePercentiles.p50.toFixed(2)}ms`);
    console.log(`- 90th Percentile: ${metrics.responseTimePercentiles.p90.toFixed(2)}ms`);
    console.log(`- 95th Percentile: ${metrics.responseTimePercentiles.p95.toFixed(2)}ms`);
    console.log(`- 99th Percentile: ${metrics.responseTimePercentiles.p99.toFixed(2)}ms`);
    
    console.log('\nScenario Performance:');
    for (const [scenarioName, scenarioMetric] of metrics.scenarioMetrics) {
      console.log(`- ${scenarioName}:`);
      console.log(`  * Requests: ${scenarioMetric.requests}`);
      console.log(`  * Avg Response Time: ${scenarioMetric.avgResponseTime.toFixed(2)}ms`);
      console.log(`  * Error Rate: ${(scenarioMetric.errorRate * 100).toFixed(2)}%`);
    }
    
    console.log('\nPerformance Assessment:');
    const passed = metrics.errorRate <= this.config.ERROR_THRESHOLD &&
                  metrics.responseTimePercentiles.p95 <= this.config.MAX_RESPONSE_TIME;
    
    console.log(`- Error Rate Threshold (${(this.config.ERROR_THRESHOLD * 100).toFixed(1)}%): ${metrics.errorRate <= this.config.ERROR_THRESHOLD ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`- 95th Percentile Response Time (${this.config.MAX_RESPONSE_TIME}ms): ${metrics.responseTimePercentiles.p95 <= this.config.MAX_RESPONSE_TIME ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`- Overall Assessment: ${passed ? '✅ PASS' : '❌ FAIL'}`);
    
    console.log('\n' + '='.repeat(60));
  }

  /**
   * Run the load test
   */
  async runLoadTest(): Promise<LoadTestMetrics> {
    console.log('Starting Load Test...');
    console.log(`Target: ${this.config.CONCURRENT_USERS} concurrent users for ${this.config.TEST_DURATION} seconds`);
    
    this.testStartTime = performance.now();
    const userPromises: Promise<void>[] = [];
    
    // Ramp up users gradually
    const usersPerSecond = this.config.CONCURRENT_USERS / this.config.RAMP_UP_TIME;
    
    for (let i = 0; i < this.config.CONCURRENT_USERS; i++) {
      const delay = (i / usersPerSecond) * 1000; // Spread users over ramp-up time
      
      const userPromise = setTimeout(delay).then(async () => {
        this.activeUsers++;
        const scenario = this.selectScenario();
        
        const endTime = this.testStartTime + (this.config.TEST_DURATION * 1000);
        
        // Keep running scenarios until test duration is reached
        while (performance.now() < endTime) {
          await this.simulateUserSession(scenario, i + 1);
          
          // Random delay between scenario repetitions
          await setTimeout(Math.random() * 5000 + 2000);
        }
        
        this.activeUsers--;
      });
      
      userPromises.push(userPromise);
    }
    
    // Wait for all users to complete
    await Promise.all(userPromises);
    
    this.testEndTime = performance.now();
    
    const metrics = this.calculateMetrics();
    this.printResults(metrics);
    
    return metrics;
  }
}

// Main execution
if (require.main === module) {
  const loadTester = new LoadTester(LOAD_TEST_CONFIG);
  
  loadTester.runLoadTest()
    .then((metrics) => {
      const passed = metrics.errorRate <= LOAD_TEST_CONFIG.ERROR_THRESHOLD &&
                    metrics.responseTimePercentiles.p95 <= LOAD_TEST_CONFIG.MAX_RESPONSE_TIME;
      
      process.exit(passed ? 0 : 1);
    })
    .catch((error) => {
      console.error('Load test failed:', error);
      process.exit(1);
    });
}

export { LoadTester, LOAD_TEST_CONFIG, USER_SCENARIOS };
export type { LoadTestMetrics, RequestResult };