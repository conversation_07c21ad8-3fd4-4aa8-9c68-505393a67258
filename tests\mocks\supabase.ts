import { vi } from 'vitest';

/**
 * Comprehensive Supabase client mock for testing
 * This mock supports all the chaining methods used throughout the application
 */

// Create a chainable mock object that supports all Supabase query methods
function createChainableMock(finalResult: any = { data: [], error: null }) {
  const chainable = {
    select: vi.fn().mockReturnThis(),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    delete: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    neq: vi.fn().mockReturnThis(),
    gt: vi.fn().mockReturnThis(),
    gte: vi.fn().mockReturnThis(),
    lt: vi.fn().mockReturnThis(),
    lte: vi.fn().mockReturnThis(),
    like: vi.fn().mockReturnThis(),
    ilike: vi.fn().mockReturnThis(),
    is: vi.fn().mockReturnThis(),
    in: vi.fn().mockReturnThis(),
    contains: vi.fn().mockReturnThis(),
    containedBy: vi.fn().mockReturnThis(),
    rangeGt: vi.fn().mockReturnThis(),
    rangeGte: vi.fn().mockReturnThis(),
    rangeLt: vi.fn().mockReturnThis(),
    rangeLte: vi.fn().mockReturnThis(),
    rangeAdjacent: vi.fn().mockReturnThis(),
    overlaps: vi.fn().mockReturnThis(),
    textSearch: vi.fn().mockReturnThis(),
    match: vi.fn().mockReturnThis(),
    not: vi.fn().mockReturnThis(),
    or: vi.fn().mockReturnThis(),
    filter: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    limit: vi.fn().mockReturnThis(),
    range: vi.fn().mockReturnThis(),
    single: vi.fn().mockReturnThis(),
    maybeSingle: vi.fn().mockReturnThis(),
    csv: vi.fn().mockReturnThis(),
    geojson: vi.fn().mockReturnThis(),
    explain: vi.fn().mockReturnThis(),
    rollback: vi.fn().mockReturnThis(),
    returns: vi.fn().mockReturnThis(),
  };

  // Make all methods return the chainable object for chaining
  Object.keys(chainable).forEach(key => {
    if (typeof chainable[key] === 'function') {
      chainable[key].mockReturnValue(chainable);
    }
  });

  // Override specific methods that should return promises
  chainable.single.mockResolvedValue(finalResult);
  chainable.range.mockResolvedValue(finalResult);
  
  // For methods that are typically the last in the chain
  const terminalMethods = ['single', 'range'];
  terminalMethods.forEach(method => {
    chainable[method].mockResolvedValue(finalResult);
  });

  return chainable;
}

/**
 * Create a mock Supabase client with all necessary methods
 */
export function createSupabaseMock(customResults: Record<string, any> = {}) {
  const defaultResults = {
    data: [],
    error: null,
    count: 0,
  };

  return {
    from: vi.fn((table: string) => {
      const result = customResults[table] || defaultResults;
      return createChainableMock(result);
    }),
    auth: {
      getUser: vi.fn().mockResolvedValue({
        data: { user: { id: 'test-user', email: '<EMAIL>' } },
        error: null,
      }),
      signInWithPassword: vi.fn().mockResolvedValue({
        data: { user: { id: 'test-user' }, session: { access_token: 'test-token' } },
        error: null,
      }),
      signOut: vi.fn().mockResolvedValue({ error: null }),
      onAuthStateChange: vi.fn().mockReturnValue({
        data: { subscription: { unsubscribe: vi.fn() } },
      }),
    },
    storage: {
      from: vi.fn((bucket: string) => ({
        createSignedUploadUrl: vi.fn().mockResolvedValue({
          data: { token: 'test-token', path: 'test-path' },
          error: null,
        }),
        createSignedUrl: vi.fn().mockResolvedValue({
          data: { signedUrl: 'https://test-signed-url.com' },
          error: null,
        }),
        getPublicUrl: vi.fn().mockReturnValue({
          data: { publicUrl: 'https://test-public-url.com' },
        }),
        upload: vi.fn().mockResolvedValue({
          data: { path: 'test-path' },
          error: null,
        }),
        remove: vi.fn().mockResolvedValue({
          data: null,
          error: null,
        }),
      })),
    },
    rpc: vi.fn().mockResolvedValue(defaultResults),
  };
}

/**
 * Default mock for getUserClient function
 */
export const mockGetUserClient = vi.fn((token: string) => {
  // Return admin client in test mode
  if (process.env.TEST_BYPASS_AUTH === '1' && token === 'test-token') {
    return createSupabaseMock();
  }
  return createSupabaseMock();
});

/**
 * Default mock for supabaseAdmin
 */
export const mockSupabaseAdmin = createSupabaseMock();

/**
 * Helper to create table-specific mocks
 */
export function createTableMock(tableName: string, mockData: any[] = []) {
  const mock = createSupabaseMock();
  
  // Override the from method for this specific table
  mock.from.mockImplementation((table: string) => {
    if (table === tableName) {
      return createChainableMock({ data: mockData, error: null });
    }
    return createChainableMock();
  });
  
  return mock;
}
