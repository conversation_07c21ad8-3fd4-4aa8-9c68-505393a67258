
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/DashboardHeader.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> DashboardHeader.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">70.2% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>139/198</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">47.05% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>8/17</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">11.76% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>2/17</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">70.2% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>139/198</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import React, { useState, useEffect, useRef } from 'react';
import { User, DashboardView, Notification, SearchResult, Theme } from '../types';
import { MenuIcon, BellIcon, SearchIcon, SettingsIcon, BillingIcon, UsersIcon, IntegrationsIcon, QuestionMarkCircleIcon, UserIcon, CloseIcon } from './Icons';
import Input from './ui/Input';
import NotificationsPanel from './NotificationsPanel';
import Dropdown from './ui/Dropdown';
import GlobalSearchResults from './GlobalSearchResults';
import { HELP_TOPICS } from '../constants';
&nbsp;
interface DashboardHeaderProps {
  onLogout: () =&gt; void;
  user: User;
  view: DashboardView;
  onToggleSidebar: () =&gt; void;
  onMarkAllNotificationsRead: () =&gt; void;
  onNotificationClick: (notification: Notification) =&gt; void;
  setView: (view: DashboardView) =&gt; void;
  onSearchResultClick: (result: SearchResult) =&gt; void;
  onUpdateUserSettings: (settings: { theme?: Theme }) =&gt; void;
}
&nbsp;
const viewTitles: Record&lt;DashboardView, string&gt; = {
  dashboard: 'Dashboard',
  generate: 'Generate',
  history: 'History',
  subscription: 'Subscription',
  settings: 'Settings',
  templates: 'Templates',
  documentDetail: 'Document Detail',
  lifecycle: 'Lifecycle',
  clauseLibrary: 'Clause Library',
  team: 'Team',
  integrations: 'Integrations',
  clientDetail: 'Client Detail',
  notifications: 'Notifications',
  analysis: 'Document Analysis',
  help: 'Help Center',
  obligations: 'Obligation Tracking',
  workflows: 'Workflow Builder',
  clients: 'Clients',
};
&nbsp;
const DashboardHeader: React.FC&lt;DashboardHeaderProps&gt; = (props) =&gt; {
    const { onLogout, user, view, onToggleSidebar, onMarkAllNotificationsRead, onNotificationClick, setView, onSearchResultClick } = props;
    const { onUpdateUserSettings } = props;
    const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
    const notificationsRef = useRef&lt;HTMLDivElement&gt;(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [searchResults, setSearchResults] = useState&lt;SearchResult[]&gt;([]);
    const [isSearchFocused, setIsSearchFocused] = useState(false);
    const searchRef = useRef&lt;HTMLDivElement&gt;(null);
    
    const hasUnread = user.notifications?.some(n =&gt; !n.isRead);
&nbsp;
    useEffect(() =&gt; {
        const handleClickOutside = <span class="fstat-no" title="function not covered" >(event: MouseEvent) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            if (notificationsRef.current &amp;&amp; !notificationsRef.current.contains(event.target as Node)) {</span>
<span class="cstat-no" title="statement not covered" >                setIsNotificationsOpen(false);</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >            if (searchRef.current &amp;&amp; !searchRef.current.contains(event.target as Node)) {</span>
<span class="cstat-no" title="statement not covered" >                setIsSearchFocused(false);</span>
<span class="cstat-no" title="statement not covered" >            }</span>
<span class="cstat-no" title="statement not covered" >        };</span>
        document.addEventListener("mousedown", handleClickOutside);
        return () =&gt; {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, []);
&nbsp;
    useEffect(() =&gt; {
        if (!searchTerm.trim()) {
            setSearchResults([]);
            return;
<span class="branch-0 cbranch-no" title="branch not covered" >        }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        const debounce = setTimeout(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            const lowercasedTerm = searchTerm.toLowerCase();</span>
<span class="cstat-no" title="statement not covered" >            const results: SearchResult[] = [];</span>
&nbsp;
            // Search Documents
<span class="cstat-no" title="statement not covered" >            user.documents.forEach(doc =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                if (doc.name.toLowerCase().includes(lowercasedTerm)) {</span>
<span class="cstat-no" title="statement not covered" >                    results.push({ id: doc.id, title: doc.name, type: 'document', context: `Updated ${new Date(doc.updatedAt).toLocaleDateString()}` });</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >            });</span>
&nbsp;
            // Search Folders
<span class="cstat-no" title="statement not covered" >            user.folders.forEach(folder =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                if (folder.name.toLowerCase().includes(lowercasedTerm)) {</span>
<span class="cstat-no" title="statement not covered" >                    results.push({ id: folder.id, title: folder.name, type: 'folder' });</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >            });</span>
&nbsp;
            // Search Clauses
<span class="cstat-no" title="statement not covered" >            (user.clauses || []).forEach(clause =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                if (clause.title.toLowerCase().includes(lowercasedTerm)) {</span>
<span class="cstat-no" title="statement not covered" >                    results.push({ id: clause.id, title: clause.title, type: 'clause' });</span>
<span class="cstat-no" title="statement not covered" >                }</span>
<span class="cstat-no" title="statement not covered" >            });</span>
            
            // Search Help Topics
<span class="cstat-no" title="statement not covered" >            HELP_TOPICS.forEach(category =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                category.topics.forEach(topic =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                    const contentString = Array.isArray(topic.content) ? topic.content.map(c =&gt; c.type === 'text' ? c.value : '').join(' ') : topic.content;</span>
<span class="cstat-no" title="statement not covered" >                    if (topic.title.toLowerCase().includes(lowercasedTerm) || contentString.toLowerCase().includes(lowercasedTerm)) {</span>
<span class="cstat-no" title="statement not covered" >                        results.push({ id: topic.id, title: topic.title, type: 'help', context: category.category });</span>
<span class="cstat-no" title="statement not covered" >                    }</span>
<span class="cstat-no" title="statement not covered" >                });</span>
<span class="cstat-no" title="statement not covered" >            });</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            setSearchResults(results);</span>
<span class="cstat-no" title="statement not covered" >        }, 300);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        return () =&gt; clearTimeout(debounce);</span>
&nbsp;
    }, [searchTerm, user]);
&nbsp;
    const handleResultClick = <span class="fstat-no" title="function not covered" >(result: SearchResult) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        onSearchResultClick(result);</span>
<span class="cstat-no" title="statement not covered" >        setSearchTerm('');</span>
<span class="cstat-no" title="statement not covered" >        setSearchResults([]);</span>
<span class="cstat-no" title="statement not covered" >        setIsSearchFocused(false);</span>
<span class="cstat-no" title="statement not covered" >    };</span>
&nbsp;
    return (
        &lt;header className="relative flex-shrink-0 h-16 bg-white dark:bg-zinc-950 border-b border-zinc-200 dark:border-zinc-800 flex items-center justify-between px-4 sm:px-6 lg:px-8"&gt;
            &lt;div className="flex items-center"&gt;
                &lt;button onClick={onToggleSidebar} className="lg:hidden mr-4 text-zinc-600 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-white" aria-label="Toggle sidebar"&gt;
                    &lt;MenuIcon className="w-6 h-6"/&gt;
                &lt;/button&gt;
                &lt;h1 className="text-xl font-semibold text-zinc-900 dark:text-white capitalize"&gt;{viewTitles[view]}&lt;/h1&gt;
            &lt;/div&gt;
            &lt;div className="flex items-center gap-4"&gt;
                 &lt;div className="relative" ref={searchRef}&gt;
                    &lt;Input
                      placeholder="Search workspace..."
                      value={searchTerm}
                      onChange={<span class="fstat-no" title="function not covered" >(e) =&gt; setSearchTerm(e.target.value)}</span>
                      onFocus={<span class="fstat-no" title="function not covered" >() =&gt; setIsSearchFocused(true)}</span>
                      leftIcon={&lt;SearchIcon className="h-5 w-5" /&gt;}
                      className="sm:w-64 bg-zinc-100 dark:bg-zinc-800 border-zinc-300 dark:border-zinc-700"
                    /&gt;
                    {(<span class="branch-0 cbranch-no" title="branch not covered" >isSearchFocused &amp;&amp; (searchResults.length &gt; 0 || <span class="branch-0 cbranch-no" title="branch not covered" >searchTerm)) &amp;&amp; (</span></span>
<span class="cstat-no" title="statement not covered" >                        &lt;GlobalSearchResults </span>
<span class="cstat-no" title="statement not covered" >                            results={searchResults}</span>
<span class="cstat-no" title="statement not covered" >                            onResultClick={handleResultClick}</span>
<span class="cstat-no" title="statement not covered" >                            searchTerm={searchTerm}</span>
<span class="cstat-no" title="statement not covered" >                        /&gt;</span>
                    )}
                 &lt;/div&gt;
                {/* Quota badge with tooltip and quick upgrade link */}
                {(() =&gt; {
                  const isPaid = user.planName === 'Premium' || user.planName === 'Enterprise';
                  const start = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
                  const end = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1);
                  const fmt = (d: Date) =&gt; d.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
                  const period = `${fmt(start)} – ${fmt(end)}`;
                  return (
                    &lt;div
                      className={`relative group hidden sm:flex items-center px-2 py-1 rounded-full text-xs bg-zinc-100 dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300 ${<span class="branch-0 cbranch-no" title="branch not covered" >isPaid ? '' : '</span>cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-700'}`}
                      onClick={<span class="fstat-no" title="function not covered" >() =&gt; { if (!isPaid) {setView('subscription');} }}</span>
                      aria-label={<span class="branch-0 cbranch-no" title="branch not covered" >isPaid ? 'Unlimited plan' : '</span>Monthly quota badge'}
                      title=""
                    &gt;
                      {<span class="branch-0 cbranch-no" title="branch not covered" >isPaid ? 'Unlimited' : `</span>MTD: ${user.quotaUsed || 0} of ${isFinite(user.quotaTotal as number) ? user.<span class="branch-0 cbranch-no" title="branch not covered" >quotaTotal : 5}</span>`}
                      &lt;div className="absolute top-full mt-2 left-1/2 -translate-x-1/2 w-max px-2 py-1 bg-zinc-800 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap"&gt;
                        Billing period: {period}
                        {!isPaid &amp;&amp; &lt;span className="ml-1 text-zinc-300"&gt;(Click to upgrade)&lt;/span&gt;}
                      &lt;/div&gt;
                    &lt;/div&gt;
                  );
                })()}
&nbsp;
                 &lt;div className="relative" ref={notificationsRef}&gt;
                    &lt;button onClick={<span class="fstat-no" title="function not covered" >() =&gt; setIsNotificationsOpen(p =&gt; !p)} c</span>lassName="relative p-2 rounded-full hover:bg-zinc-100 dark:hover:bg-zinc-800" aria-label="View notifications"&gt;
                        &lt;BellIcon className="w-6 h-6 text-zinc-600 dark:text-zinc-400"/&gt;
                        {hasUnread &amp;&amp; &lt;span className="absolute top-1.5 right-1.5 block h-2.5 w-2.5 rounded-full bg-red-500 ring-2 ring-white dark:ring-zinc-950" /&gt;}
                    &lt;/button&gt;
                    {<span class="branch-0 cbranch-no" title="branch not covered" >isNotificationsOpen &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                        &lt;NotificationsPanel</span>
<span class="cstat-no" title="statement not covered" >                            notifications={user.notifications || []}</span>
<span class="cstat-no" title="statement not covered" >                            onMarkAllRead={onMarkAllNotificationsRead}</span>
<span class="cstat-no" title="statement not covered" >                            onNotificationClick={<span class="fstat-no" title="function not covered" >(notif) =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >                                onNotificationClick(notif);</span>
<span class="cstat-no" title="statement not covered" >                                setIsNotificationsOpen(false);</span>
<span class="cstat-no" title="statement not covered" >                            }}</span>
<span class="cstat-no" title="statement not covered" >                            onClose={<span class="fstat-no" title="function not covered" >() =&gt; setIsNotificationsOpen(false)}</span></span>
<span class="cstat-no" title="statement not covered" >                            onViewAll={<span class="fstat-no" title="function not covered" >() =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >                                setView('notifications');</span>
<span class="cstat-no" title="statement not covered" >                                setIsNotificationsOpen(false);</span>
<span class="cstat-no" title="statement not covered" >                            }}</span>
<span class="cstat-no" title="statement not covered" >                        /&gt;</span>
                    )}
                &lt;/div&gt;
      &lt;Dropdown&gt;
        &lt;Dropdown.Trigger&gt;
          &lt;div className="flex items-center space-x-2 p-1 rounded-full hover:bg-zinc-100 dark:hover:bg-zinc-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500 dark:ring-offset-zinc-950"&gt;
            &lt;img src={user.avatarUrl} alt="User Avatar" className="w-9 h-9 rounded-full object-cover"/&gt;
            &lt;span className="hidden sm:inline text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;{user.name || 'Account'}&lt;/span&gt;
          &lt;/div&gt;
        &lt;/Dropdown.Trigger&gt;
        &lt;Dropdown.Content align="right"&gt;
          &lt;div className="px-4 py-3"&gt;
            &lt;div className="flex items-center gap-3"&gt;
              &lt;img src={user.avatarUrl} alt="User Avatar" className="w-10 h-10 rounded-full object-cover"/&gt;
              &lt;div&gt;
                &lt;div className="text-sm font-semibold text-zinc-900 dark:text-white"&gt;{user.name || 'Your Account'}&lt;/div&gt;
                &lt;div className="mt-1 inline-flex items-center px-2 py-0.5 rounded-full text-[11px] bg-zinc-100 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-200"&gt;{user.planName}&lt;/div&gt;
              &lt;/div&gt;
            &lt;/div&gt;
          &lt;/div&gt;
          &lt;Dropdown.Separator /&gt;
          &lt;Dropdown.Item onClick={<span class="fstat-no" title="function not covered" >() =&gt; setView('settings')} i</span>con={&lt;UserIcon className="w-4 h-4"/&gt;}&gt;Profile&lt;/Dropdown.Item&gt;
          &lt;Dropdown.Item onClick={<span class="fstat-no" title="function not covered" >() =&gt; setView('subscription')} i</span>con={&lt;BillingIcon className="w-4 h-4"/&gt;}&gt;Subscription&lt;/Dropdown.Item&gt;
          &lt;Dropdown.Item onClick={<span class="fstat-no" title="function not covered" >() =&gt; setView('team')} i</span>con={&lt;UsersIcon className="w-4 h-4"/&gt;}&gt;Team&lt;/Dropdown.Item&gt;
          &lt;Dropdown.Item onClick={<span class="fstat-no" title="function not covered" >() =&gt; setView('integrations')} i</span>con={&lt;IntegrationsIcon className="w-4 h-4"/&gt;}&gt;Integrations&lt;/Dropdown.Item&gt;
          &lt;Dropdown.Item onClick={<span class="fstat-no" title="function not covered" >() =&gt; setView('help')} i</span>con={&lt;QuestionMarkCircleIcon className="w-4 h-4"/&gt;}&gt;Help Center&lt;/Dropdown.Item&gt;
          &lt;Dropdown.Separator /&gt;
          &lt;div className="px-4 py-2"&gt;
            &lt;div className="text-[11px] uppercase tracking-wide text-zinc-500 dark:text-zinc-400 mb-2 flex items-center gap-2"&gt;&lt;SettingsIcon className="w-4 h-4"/&gt;Theme&lt;/div&gt;
            &lt;div className="grid grid-cols-3 gap-2"&gt;
              {(['light','dark','system'] as Theme[]).map(t =&gt; (
                &lt;button key={t} onClick={<span class="fstat-no" title="function not covered" >() =&gt; onUpdateUserSettings({ theme: t })} c</span>lassName={`px-2 py-1.5 rounded-md text-xs border ${user.theme === <span class="branch-0 cbranch-no" title="branch not covered" >t ? 'border-brand-500 text-brand-600' : '</span>border-zinc-300 dark:border-zinc-700 text-zinc-700 dark:text-zinc-300'}`}&gt;
                  {t.charAt(0).toUpperCase() + t.slice(1)}
                &lt;/button&gt;
              ))}
            &lt;/div&gt;
          &lt;/div&gt;
          &lt;Dropdown.Separator /&gt;
          &lt;Dropdown.Item onClick={onLogout} className="text-red-600 dark:text-red-400" icon={&lt;CloseIcon className="w-4 h-4"/&gt;}&gt;Log out&lt;/Dropdown.Item&gt;
        &lt;/Dropdown.Content&gt;
      &lt;/Dropdown&gt;
            &lt;/div&gt;
        &lt;/header&gt;
    )
}
&nbsp;
export default DashboardHeader;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    