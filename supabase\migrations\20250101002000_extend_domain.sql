-- Extend schema to cover remaining types in types.ts

-- Helpers
create extension if not exists pgcrypto;

-- Add missing columns to documents
do $$ begin
  alter table if exists public.documents add column if not exists client_id uuid null;
exception when undefined_table then null; end $$;

do $$ begin
  alter table if exists public.documents add column if not exists value numeric null;
exception when undefined_table then null; end $$;

do $$ begin
  alter table if exists public.documents add column if not exists tags text[] not null default '{}';
exception when undefined_table then null; end $$;

-- Clients
create table if not exists public.clients (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  name text not null,
  type text not null check (type in ('Company','Individual')),
  contact_person text,
  email text,
  phone text,
  address text,
  created_at timestamptz not null default now()
);
create index if not exists clients_user_id_idx on public.clients(user_id);
alter table public.clients enable row level security;
drop policy if exists clients_select_own on public.clients;
create policy clients_select_own on public.clients for select using (user_id = auth.uid());
drop policy if exists clients_mutate_own on public.clients;
create policy clients_mutate_own on public.clients for all using (user_id = auth.uid()) with check (user_id = auth.uid());

-- Link documents to clients
do $$ begin
  alter table public.documents
    add constraint documents_client_fk foreign key (client_id) references public.clients(id) on delete set null;
exception when duplicate_object then null; end $$;

-- Client assets
create table if not exists public.client_assets (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  client_id uuid not null references public.clients(id) on delete cascade,
  name text not null,
  url text not null,
  created_at timestamptz not null default now()
);
create index if not exists client_assets_user_idx on public.client_assets(user_id);
create index if not exists client_assets_client_idx on public.client_assets(client_id);
alter table public.client_assets enable row level security;
do $$ begin
  if exists (
    select 1 from pg_policies where schemaname = 'public' and tablename = 'client_assets' and policyname = 'client_assets_access'
  ) then
    drop policy client_assets_access on public.client_assets;
  end if;
end $$;
create policy client_assets_access on public.client_assets for all using (user_id = auth.uid()) with check (user_id = auth.uid());

-- Ensure storage bucket for client assets exists and allow auth uploads
-- Create bucket (public read)
insert into storage.buckets (id, name, public)
select 'contractgini', 'contractgini', false
where not exists (select 1 from storage.buckets where id = 'contractgini');

-- Enable RLS on objects and set bucket-scoped policies
alter table storage.objects enable row level security;
do $$ begin
  if exists (select 1 from pg_policies where schemaname = 'storage' and tablename = 'objects' and policyname = 'client_assets_read') then
    drop policy "client_assets_read" on storage.objects;
  end if;
  if exists (select 1 from pg_policies where schemaname = 'storage' and tablename = 'objects' and policyname = 'client_assets_insert') then
    drop policy "client_assets_insert" on storage.objects;
  end if;
  if exists (select 1 from pg_policies where schemaname = 'storage' and tablename = 'objects' and policyname = 'client_assets_update') then
    drop policy "client_assets_update" on storage.objects;
  end if;
  if exists (select 1 from pg_policies where schemaname = 'storage' and tablename = 'objects' and policyname = 'client_assets_delete') then
    drop policy "client_assets_delete" on storage.objects;
  end if;
end $$;
create policy "client_assets_read" on storage.objects for select using (
  bucket_id = 'contractgini' and auth.role() = 'authenticated'
);
create policy "client_assets_insert" on storage.objects for insert with check (
  bucket_id = 'contractgini' and auth.role() = 'authenticated'
);
create policy "client_assets_update" on storage.objects for update using (
  bucket_id = 'contractgini' and auth.role() = 'authenticated'
);
create policy "client_assets_delete" on storage.objects for delete using (
  bucket_id = 'contractgini' and auth.role() = 'authenticated'
);

-- Document collaborators
create table if not exists public.document_collaborators (
  id uuid primary key default gen_random_uuid(),
  document_id uuid not null references public.documents(id) on delete cascade,
  email text not null,
  permission text not null check (permission in ('view','edit')),
  avatar_url text
);
alter table public.document_collaborators enable row level security;
drop policy if exists doc_collab_access on public.document_collaborators;
create policy doc_collab_access on public.document_collaborators for all using (
  exists(select 1 from public.documents d where d.id = document_id and d.user_id = auth.uid())
) with check (
  exists(select 1 from public.documents d where d.id = document_id and d.user_id = auth.uid())
);

-- Comment threads and comments
create table if not exists public.comment_threads (
  id uuid primary key default gen_random_uuid(),
  document_id uuid not null references public.documents(id) on delete cascade,
  text_selection text not null,
  is_resolved boolean not null default false,
  created_at timestamptz not null default now()
);
alter table public.comment_threads enable row level security;
drop policy if exists comment_threads_access on public.comment_threads;
create policy comment_threads_access on public.comment_threads for all using (
  exists(select 1 from public.documents d where d.id = document_id and d.user_id = auth.uid())
) with check (
  exists(select 1 from public.documents d where d.id = document_id and d.user_id = auth.uid())
);

create table if not exists public.comments (
  id uuid primary key default gen_random_uuid(),
  thread_id uuid not null references public.comment_threads(id) on delete cascade,
  author_email text not null,
  content text not null,
  created_at timestamptz not null default now()
);
alter table public.comments enable row level security;
drop policy if exists comments_access on public.comments;
create policy comments_access on public.comments for all using (
  exists(
    select 1 from public.comment_threads t
    join public.documents d on d.id = t.document_id
    where t.id = comments.thread_id and d.user_id = auth.uid()
  )
) with check (
  exists(
    select 1 from public.comment_threads t
    join public.documents d on d.id = t.document_id
    where t.id = comments.thread_id and d.user_id = auth.uid()
  )
);

-- Notifications
create table if not exists public.notifications (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  type text not null check (type in ('comment','share','signature','team','marketing','approval')),
  message text not null,
  document_id uuid references public.documents(id) on delete set null,
  created_at timestamptz not null default now(),
  is_read boolean not null default false
);
alter table public.notifications enable row level security;
drop policy if exists notifications_access on public.notifications;
create policy notifications_access on public.notifications for all using (user_id = auth.uid()) with check (user_id = auth.uid());

-- Signatures
create table if not exists public.signatures (
  id uuid primary key default gen_random_uuid(),
  document_id uuid not null references public.documents(id) on delete cascade,
  email text not null,
  role text not null,
  status text not null check (status in ('pending','signed')),
  signed_at timestamptz,
  token text not null
);
alter table public.signatures enable row level security;
drop policy if exists signatures_access on public.signatures;
create policy signatures_access on public.signatures for all using (
  exists(select 1 from public.documents d where d.id = document_id and d.user_id = auth.uid())
) with check (
  exists(select 1 from public.documents d where d.id = document_id and d.user_id = auth.uid())
);

-- Key dates
create table if not exists public.document_key_dates (
  id uuid primary key default gen_random_uuid(),
  document_id uuid not null references public.documents(id) on delete cascade,
  event text not null,
  date date not null
);
alter table public.document_key_dates enable row level security;
drop policy if exists keydates_access on public.document_key_dates;
create policy keydates_access on public.document_key_dates for all using (
  exists(select 1 from public.documents d where d.id = document_id and d.user_id = auth.uid())
) with check (
  exists(select 1 from public.documents d where d.id = document_id and d.user_id = auth.uid())
);

-- Approvers
create table if not exists public.document_approvers (
  id uuid primary key default gen_random_uuid(),
  document_id uuid not null references public.documents(id) on delete cascade,
  email text not null,
  status text not null check (status in ('pending','approved','changes-requested')),
  comments text,
  responded_at timestamptz
);
alter table public.document_approvers enable row level security;
drop policy if exists approvers_access on public.document_approvers;
create policy approvers_access on public.document_approvers for all using (
  exists(select 1 from public.documents d where d.id = document_id and d.user_id = auth.uid())
) with check (
  exists(select 1 from public.documents d where d.id = document_id and d.user_id = auth.uid())
);

-- Obligations
create table if not exists public.obligations (
  id uuid primary key default gen_random_uuid(),
  document_id uuid not null references public.documents(id) on delete cascade,
  description text not null,
  due_date date not null,
  owner_email text,
  status text not null check (status in ('pending','completed','overdue'))
);
alter table public.obligations enable row level security;
drop policy if exists obligations_access on public.obligations;
create policy obligations_access on public.obligations for all using (
  exists(select 1 from public.documents d where d.id = document_id and d.user_id = auth.uid())
) with check (
  exists(select 1 from public.documents d where d.id = document_id and d.user_id = auth.uid())
);

-- Activity logs
create table if not exists public.activity_logs (
  id uuid primary key default gen_random_uuid(),
  document_id uuid references public.documents(id) on delete set null,
  user_email text not null,
  type text not null check (type in ('create','edit','share','comment','view','signature','revert','team','approval')),
  details text not null,
  timestamp timestamptz not null default now()
);
alter table public.activity_logs enable row level security;
drop policy if exists activity_logs_access on public.activity_logs;
create policy activity_logs_access on public.activity_logs for all using (
  document_id is null or exists(select 1 from public.documents d where d.id = document_id and d.user_id = auth.uid())
) with check (
  document_id is null or exists(select 1 from public.documents d where d.id = document_id and d.user_id = auth.uid())
);

-- Templates (public) and custom_templates (per user)
create table if not exists public.templates (
  id uuid primary key default gen_random_uuid(),
  title text not null,
  description text not null,
  category text not null,
  prompt text not null,
  required_plan text not null check (required_plan in ('Registered User','Premium'))
);
alter table public.templates enable row level security;
drop policy if exists templates_public_select on public.templates;
create policy templates_public_select on public.templates for select using (true);

create table if not exists public.custom_templates (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  name text not null,
  content text not null,
  created_at timestamptz not null default now()
);
alter table public.custom_templates enable row level security;
drop policy if exists custom_templates_access on public.custom_templates;
create policy custom_templates_access on public.custom_templates for all using (user_id = auth.uid()) with check (user_id = auth.uid());

-- Teams and membership
create table if not exists public.teams (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  owner_id uuid not null references auth.users(id) on delete cascade,
  status text not null check (status in ('active','suspended')),
  created_at timestamptz not null default now()
);
alter table public.teams enable row level security;
-- Defer creating teams_access policy until team_members exists

create table if not exists public.team_members (
  team_id uuid not null references public.teams(id) on delete cascade,
  user_id uuid not null references auth.users(id) on delete cascade,
  email text not null,
  role text not null check (role in ('Admin','Member')),
  avatar_url text,
  joined_at timestamptz not null default now(),
  primary key(team_id, user_id)
);
alter table public.team_members enable row level security;
drop policy if exists team_members_access on public.team_members;
create policy team_members_access on public.team_members for all using (
  exists(select 1 from public.team_members tm where tm.team_id = team_members.team_id and tm.user_id = auth.uid())
) with check (
  exists(select 1 from public.team_members tm where tm.team_id = team_members.team_id and tm.user_id = auth.uid())
);

-- Now create teams_access policy referencing team_members
drop policy if exists teams_access on public.teams;
create policy teams_access on public.teams for all using (
  owner_id = auth.uid() or exists(
    select 1 from public.team_members tm where tm.team_id = teams.id and tm.user_id = auth.uid()
  )
) with check (
  owner_id = auth.uid() or exists(
    select 1 from public.team_members tm where tm.team_id = teams.id and tm.user_id = auth.uid()
  )
);

create table if not exists public.team_api_keys (
  id uuid primary key default gen_random_uuid(),
  team_id uuid not null references public.teams(id) on delete cascade,
  name text not null,
  key text not null,
  created_at timestamptz not null default now()
);
alter table public.team_api_keys enable row level security;
do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'team_api_keys' and policyname = 'team_api_keys_access'
  ) then
    drop policy team_api_keys_access on public.team_api_keys;
  end if;
end $$;
create policy team_api_keys_access on public.team_api_keys for all using (
  exists(select 1 from public.team_members tm where tm.team_id = team_api_keys.team_id and tm.user_id = auth.uid())
) with check (
  exists(select 1 from public.team_members tm where tm.team_id = team_api_keys.team_id and tm.user_id = auth.uid())
);

create table if not exists public.team_sso_config (
  team_id uuid primary key references public.teams(id) on delete cascade,
  enabled boolean not null default false,
  idp_url text,
  entity_id text,
  certificate text
);
alter table public.team_sso_config enable row level security;
do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'team_sso_config' and policyname = 'team_sso_config_access'
  ) then
    drop policy team_sso_config_access on public.team_sso_config;
  end if;
end $$;
create policy team_sso_config_access on public.team_sso_config for all using (
  exists(select 1 from public.team_members tm where tm.team_id = team_sso_config.team_id and tm.user_id = auth.uid())
) with check (
  exists(select 1 from public.team_members tm where tm.team_id = team_sso_config.team_id and tm.user_id = auth.uid())
);

-- Profiles (user metadata not in auth.users)
create table if not exists public.profiles (
  id uuid primary key references auth.users(id) on delete cascade,
  name text,
  username text unique,
  avatar_url text,
  status text not null default 'active' check (status in ('active','suspended')),
  created_at timestamptz not null default now(),
  plan_expiry_date date,
  quota_used int not null default 0,
  quota_total int not null default 10,
  plan_name text not null default 'Registered User',
  team_id uuid references public.teams(id) on delete set null,
  theme text check (theme in ('light','dark','system')),
  job_title text,
  company text,
  bio text,
  website_url text,
  linkedin_url text,
  subscription_id text,
  customer_id text,
  subscription_status text
);
alter table public.profiles enable row level security;
do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'profiles' and policyname = 'profiles_access'
  ) then
    drop policy profiles_access on public.profiles;
  end if;
end $$;
create policy profiles_access on public.profiles for all using (id = auth.uid()) with check (id = auth.uid());

-- Workflows
create table if not exists public.workflow_templates (
  id uuid primary key default gen_random_uuid(),
  team_id uuid not null references public.teams(id) on delete cascade,
  name text not null,
  status text not null check (status in ('active','inactive'))
);
alter table public.workflow_templates enable row level security;
do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'workflow_templates' and policyname = 'workflow_templates_access'
  ) then
    drop policy workflow_templates_access on public.workflow_templates;
  end if;
end $$;
create policy workflow_templates_access on public.workflow_templates for all using (
  exists(select 1 from public.team_members tm where tm.team_id = workflow_templates.team_id and tm.user_id = auth.uid())
) with check (
  exists(select 1 from public.team_members tm where tm.team_id = workflow_templates.team_id and tm.user_id = auth.uid())
);

create table if not exists public.workflow_nodes (
  id uuid primary key default gen_random_uuid(),
  template_id uuid not null references public.workflow_templates(id) on delete cascade,
  type text not null,
  position_x numeric not null default 0,
  position_y numeric not null default 0,
  data jsonb not null default '{}'::jsonb
);
alter table public.workflow_nodes enable row level security;
do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'workflow_nodes' and policyname = 'workflow_nodes_access'
  ) then
    drop policy workflow_nodes_access on public.workflow_nodes;
  end if;
end $$;
create policy workflow_nodes_access on public.workflow_nodes for all using (
  exists(select 1 from public.workflow_templates wt join public.team_members tm on tm.team_id = wt.team_id where wt.id = workflow_nodes.template_id and tm.user_id = auth.uid())
) with check (
  exists(select 1 from public.workflow_templates wt join public.team_members tm on tm.team_id = wt.team_id where wt.id = workflow_nodes.template_id and tm.user_id = auth.uid())
);

create table if not exists public.workflow_edges (
  id uuid primary key default gen_random_uuid(),
  template_id uuid not null references public.workflow_templates(id) on delete cascade,
  source text not null,
  source_handle text,
  target text not null,
  target_handle text
);
alter table public.workflow_edges enable row level security;
do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'workflow_edges' and policyname = 'workflow_edges_access'
  ) then
    drop policy workflow_edges_access on public.workflow_edges;
  end if;
end $$;
create policy workflow_edges_access on public.workflow_edges for all using (
  exists(select 1 from public.workflow_templates wt join public.team_members tm on tm.team_id = wt.team_id where wt.id = workflow_edges.template_id and tm.user_id = auth.uid())
) with check (
  exists(select 1 from public.workflow_templates wt join public.team_members tm on tm.team_id = wt.team_id where wt.id = workflow_edges.template_id and tm.user_id = auth.uid())
);

create table if not exists public.workflow_instances (
  id uuid primary key default gen_random_uuid(),
  template_id uuid not null references public.workflow_templates(id) on delete cascade,
  document_id uuid not null references public.documents(id) on delete cascade,
  user_id uuid not null references auth.users(id) on delete cascade,
  status text not null check (status in ('running','completed','failed','waiting')),
  current_node_id uuid,
  created_at timestamptz not null default now(),
  resume_at timestamptz
);
alter table public.workflow_instances enable row level security;
do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'workflow_instances' and policyname = 'workflow_instances_access'
  ) then
    drop policy workflow_instances_access on public.workflow_instances;
  end if;
end $$;
create policy workflow_instances_access on public.workflow_instances for all using (
  user_id = auth.uid()
) with check (
  user_id = auth.uid()
);

-- Integrations & automation (connections, flows, runs)
create table if not exists public.user_connections (
  id uuid primary key default gen_random_uuid(),
  connector_id text not null,
  user_id uuid not null references auth.users(id) on delete cascade,
  credentials jsonb not null default '{}'::jsonb,
  created_at timestamptz not null default now()
);
alter table public.user_connections enable row level security;
do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'user_connections' and policyname = 'user_connections_access'
  ) then
    drop policy user_connections_access on public.user_connections;
  end if;
end $$;
create policy user_connections_access on public.user_connections for all using (user_id = auth.uid()) with check (user_id = auth.uid());

create table if not exists public.flows (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  user_id uuid not null references auth.users(id) on delete cascade,
  trigger_connection_id uuid not null references public.user_connections(id) on delete cascade,
  trigger_key text not null,
  action_connection_id uuid not null references public.user_connections(id) on delete cascade,
  action_key text not null,
  field_mapping jsonb not null default '{}'::jsonb,
  status text not null check (status in ('active','inactive')),
  created_at timestamptz not null default now()
);
alter table public.flows enable row level security;
do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'flows' and policyname = 'flows_access'
  ) then
    drop policy flows_access on public.flows;
  end if;
end $$;
create policy flows_access on public.flows for all using (user_id = auth.uid()) with check (user_id = auth.uid());

create table if not exists public.flow_runs (
  id uuid primary key default gen_random_uuid(),
  flow_id uuid not null references public.flows(id) on delete cascade,
  user_id uuid not null references auth.users(id) on delete cascade,
  status text not null check (status in ('success','failed','running')),
  started_at timestamptz not null default now(),
  finished_at timestamptz,
  trigger_data jsonb,
  action_data jsonb,
  error text
);
alter table public.flow_runs enable row level security;
do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'flow_runs' and policyname = 'flow_runs_access'
  ) then
    drop policy flow_runs_access on public.flow_runs;
  end if;
end $$;
create policy flow_runs_access on public.flow_runs for all using (user_id = auth.uid()) with check (user_id = auth.uid());

-- Optional: integrations table to store status/config per user
create table if not exists public.integrations (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  name text not null,
  description text not null,
  status text not null check (status in ('connected','disconnected')),
  sync_status text,
  config jsonb
);
alter table public.integrations enable row level security;
do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'integrations' and policyname = 'integrations_access'
  ) then
    drop policy integrations_access on public.integrations;
  end if;
end $$;
create policy integrations_access on public.integrations for all using (user_id = auth.uid()) with check (user_id = auth.uid());

-- Pricing plans (optional, for CMS)
create table if not exists public.pricing_plans (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  price text not null,
  price_detail text,
  features text[] not null default '{}',
  cta text not null,
  is_featured boolean not null default false,
  sort_order int not null default 0
);
alter table public.pricing_plans enable row level security;
do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'pricing_plans' and policyname = 'pricing_public_select'
  ) then
    drop policy pricing_public_select on public.pricing_plans;
  end if;
end $$;
create policy pricing_public_select on public.pricing_plans for select using (true);

-- Stripe config (single-row admin config; service role should manage)
create table if not exists public.stripe_config (
  id int primary key default 1,
  live_publishable_key text,
  live_secret_key text,
  test_publishable_key text,
  test_secret_key text,
  webhook_secret text,
  is_live_mode boolean not null default false
);
alter table public.stripe_config enable row level security;
do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'stripe_config' and policyname = 'stripe_config_admin_only'
  ) then
    drop policy stripe_config_admin_only on public.stripe_config;
  end if;
end $$;
create policy stripe_config_admin_only on public.stripe_config for all using (false) with check (false);
