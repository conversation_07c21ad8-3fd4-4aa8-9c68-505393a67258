import React, { useState, useMemo, useEffect } from 'react';
import { Template } from '../../types';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../ui/Card';
import { Button } from '../ui/Button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/Table';
import { PlusCircleIcon, EditIcon, TrashIcon, SearchIcon } from '../Icons';
import ConfirmationModal from './ConfirmationModal';
import TemplateEditorModal from './TemplateEditorModal';

interface CmsPageProps {
  publicTemplates: Template[];
  onCreateTemplate: (templateData: Omit<Template, 'id'>) => void;
  onUpdateTemplate: (template: Template) => void;
  onDeleteTemplate: (templateId: string) => void;
  initialSearchTerm: string | null;
  onClearSearchFilter: () => void;
}

const CmsPage: React.FC<CmsPageProps> = ({ publicTemplates, onCreateTemplate, onUpdateTemplate, onDeleteTemplate, initialSearchTerm, onClearSearchFilter }) => {
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [templateToEdit, setTemplateToEdit] = useState<Template | null>(null);
  const [templateToDelete, setTemplateToDelete] = useState<Template | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    if (initialSearchTerm) {
      setSearchTerm(initialSearchTerm);
      onClearSearchFilter();
    }
  }, [initialSearchTerm, onClearSearchFilter]);
  
  const filteredTemplates = useMemo(() => {
      if (!searchTerm) {return publicTemplates;}
      return publicTemplates.filter(t => t.title.toLowerCase().includes(searchTerm.toLowerCase()));
  }, [publicTemplates, searchTerm]);

  const handleCreate = () => {
    setTemplateToEdit(null);
    setIsEditorOpen(true);
  };
  
  const handleEdit = (template: Template) => {
    setTemplateToEdit(template);
    setIsEditorOpen(true);
  };

  const handleSave = (data: Omit<Template, 'id'> | Template) => {
    if ('id' in data) {
      onUpdateTemplate(data);
    } else {
      onCreateTemplate(data);
    }
    setIsEditorOpen(false);
  };

  const handleDeleteConfirm = () => {
    if (templateToDelete) {
      onDeleteTemplate(templateToDelete.id);
      setTemplateToDelete(null);
    }
  };

  return (
    <>
      <div className="p-4 sm:p-6 lg:p-8 space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
                <h1 className="text-3xl font-bold text-zinc-900 dark:text-white">Content Management</h1>
                <p className="text-zinc-600 dark:text-zinc-400 mt-1">Manage public-facing contract templates.</p>
            </div>
            <Button onClick={handleCreate}>
                <PlusCircleIcon className="w-5 h-5 mr-2" />
                Create New Template
            </Button>
        </div>

        <Card>
            <CardHeader>
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                    <div>
                        <CardTitle>Public Templates</CardTitle>
                        <CardDescription>This list of templates is visible to all users on the 'Templates' page.</CardDescription>
                    </div>
                    <div className="relative w-full sm:max-w-xs"><div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3"><SearchIcon className="h-5 w-5 text-zinc-400" /></div><input type="text" placeholder="Search templates..." value={searchTerm} onChange={e => setSearchTerm(e.target.value)} className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 p-2" /></div>
                </div>
            </CardHeader>
            <CardContent>
                <div className="overflow-x-auto">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Title</TableHead>
                                <TableHead>Category</TableHead>
                                <TableHead>Required Plan</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {filteredTemplates.map(template => (
                                <TableRow key={template.id}>
                                    <TableCell className="font-medium">{template.title}</TableCell>
                                    <TableCell>{template.category}</TableCell>
                                    <TableCell>{template.requiredPlan}</TableCell>
                                    <TableCell className="text-right">
                                        <Button variant="ghost" size="icon" onClick={() => handleEdit(template)} className="h-8 w-8">
                                            <EditIcon className="w-4 h-4" />
                                        </Button>
                                        <Button variant="ghost" size="icon" onClick={() => setTemplateToDelete(template)} className="h-8 w-8 text-red-600 hover:text-red-700">
                                            <TrashIcon className="w-4 h-4" />
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </div>
            </CardContent>
        </Card>
      </div>
      
      <TemplateEditorModal 
        isOpen={isEditorOpen}
        onClose={() => setIsEditorOpen(false)}
        onSave={handleSave}
        existingTemplate={templateToEdit}
      />

      {templateToDelete && (
        <ConfirmationModal
            isOpen={true}
            onClose={() => setTemplateToDelete(null)}
            onConfirm={handleDeleteConfirm}
            title="Delete Template"
            message={<>Are you sure you want to delete the template <strong>"{templateToDelete.title}"</strong>? This action cannot be undone.</>}
            confirmText="Delete Template"
        />
      )}
    </>
  );
};

export default CmsPage;