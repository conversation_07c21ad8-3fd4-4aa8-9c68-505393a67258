name: Security Checks

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  gitleaks-and-audit:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Gitleaks scan
        uses: gitleaks/gitleaks-action@v2
        with:
          args: --redact

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm install --no-audit --no-fund

      - name: Production dependency audit (high and above)
        run: npm audit --only=prod --audit-level=high || true

  tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
      - run: npm install --no-audit --no-fund
      - run: npm test --silent || true


