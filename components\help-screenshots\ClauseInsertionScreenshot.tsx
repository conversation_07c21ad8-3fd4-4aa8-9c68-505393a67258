
import React from 'react';
import { ArrowLeftIcon, CloseIcon, SaveIcon, LibraryIcon } from '../Icons';
import Indicator from './Indicator';

const ClauseInsertionScreenshot: React.FC = () => {
  return (
    <div className="relative my-6 p-4 border rounded-lg bg-zinc-50 dark:bg-zinc-900 overflow-hidden select-none">
      <Indicator number={1} position="top-12 right-[6.5rem]" arrow="down" />
      <div className="flex justify-between items-center gap-4">
        <div className="flex items-center gap-4">
          <div className="p-2 border rounded-md bg-white dark:bg-zinc-800"><ArrowLeftIcon className="h-5 w-5 text-zinc-600 dark:text-zinc-300" /></div>
          <div>
            <h1 className="text-lg font-bold text-zinc-800 dark:text-zinc-100">Sample NDA</h1>
            <p className="text-xs text-zinc-500 dark:text-zinc-400">Editing...</p>
          </div>
        </div>
        <div className="flex items-center gap-1.5">
          <div className="p-2 border rounded-md bg-white dark:bg-zinc-800"><LibraryIcon className="w-5 h-5 text-zinc-600 dark:text-zinc-300" /></div>
          <div className="p-2 border rounded-md bg-white dark:bg-zinc-800"><CloseIcon className="w-5 h-5 text-zinc-600 dark:text-zinc-300" /></div>
          <div className="p-2 border rounded-md bg-brand-600 text-white"><SaveIcon className="w-5 h-5" /></div>
        </div>
      </div>
    </div>
  );
};

export default ClauseInsertionScreenshot;
