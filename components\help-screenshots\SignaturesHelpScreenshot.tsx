
import React from 'react';
import { SignatureIcon, CheckCircleIcon, ClockIcon } from '../Icons';

const SignaturesHelpScreenshot = () => {
  return (
    <div className="relative my-6 p-4 border rounded-lg bg-zinc-50 dark:bg-zinc-900 overflow-hidden select-none text-xs">
        <div className="p-2">
            <h3 className="font-semibold text-zinc-800 dark:text-zinc-200 flex items-center gap-2">
                <SignatureIcon className="w-5 h-5"/>
                Signatures
            </h3>
            <div className="mt-2 space-y-2">
                <div className="flex items-center justify-between p-2 rounded-md bg-white dark:bg-zinc-800">
                    <p className="font-medium text-zinc-700 dark:text-zinc-300"><EMAIL></p>
                    <span className="flex items-center text-green-700 bg-green-100 dark:text-green-300 dark:bg-green-900/50 px-2 py-0.5 rounded-full"><CheckCircleIcon className="w-3 h-3 mr-1"/>Signed</span>
                </div>
                 <div className="flex items-center justify-between p-2 rounded-md bg-white dark:bg-zinc-800">
                    <p className="font-medium text-zinc-700 dark:text-zinc-300"><EMAIL></p>
                    <span className="flex items-center text-zinc-600 bg-zinc-100 dark:text-zinc-400 dark:bg-zinc-700 px-2 py-0.5 rounded-full"><ClockIcon className="w-3 h-3 mr-1"/>Pending</span>
                </div>
            </div>
        </div>
    </div>
  );
};
export default SignaturesHelpScreenshot;
