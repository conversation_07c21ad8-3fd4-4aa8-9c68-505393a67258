# Security Architecture & Runbook

This document outlines LexiGen’s security model, key management, and operational runbooks. It complements code comments and DB migrations under `supabase/migrations/`.

## Key Principles
- Server owns all secrets; clients never see service keys.
- Defense-in-depth: RLS + API validation + input sanitization + headers/CSP + rate limiting.
- Least privilege: storage and data access are scoped to the user.
- Secure defaults: reject unknown/unsupported input and sanitize HTML.

## Key & Secret Management
- Do not commit real secrets. Only `.env.example` stays in git.
- Store secrets in server env (e.g., `.env` on server), not in client `.env.local`.
- Rotate immediately if a secret is exposed (steps below).

### Secrets
- `SUPABASE_SERVICE_ROLE_KEY` (server only)
- `SUPABASE_ANON_KEY` (client + server)
- `SUPABASE_DB_PASSWORD` (CLI/DB)
- `SUPABASE_ACCESS_TOKEN` (CLI)
- `GEMINI_API_KEY` (server only)

### Rotation Runbook
1. Generate new keys in provider dashboards (Supabase → API; DB Password; Gemini).
2. Update server environment (not committed) and restart the server.
3. Invalidate old keys. If leaked in git, purge history (e.g., `git filter-repo`) and revoke access tokens.
4. Verify connectivity and API routes.

## Database & RLS
- All tables have RLS enabled and policies enforcing user ownership.
- Profiles protection: critical fields (`is_admin`, `plan_name`, `status`, `quota_*`, `subscription_*`, `team_id`, `plan_expiry_date`) are protected by a trigger that blocks updates unless `request.jwt.claim.role = 'service_role'`.
- Client assets table: RLS by `user_id = auth.uid()`.
- Storage: RLS policies require object path prefix `auth.uid()/...` to read/write/update/delete.

## Storage Security Model
- Bucket: `contractgini` (private recommended).
- Object key: `userId/clientId/timestamp_filename.ext`.
- Policies: `split_part(name, '/', 1) = auth.uid()` enforces ownership.
- Uploads: server issues a one-time signed upload URL after sanitizing file name and validating extension.
- Serving: server creates signed URLs per request; if the bucket is public, server can return a public URL as fallback.

## API & App Security
- Authentication: Supabase JWT checked in `requireAuth`; per-request RLS clients are created with anon key + Authorization header.
- Authorization: Admin endpoints require `requireAdmin`, which checks `profiles.is_admin` via RLS client.
- CORS: exact `CLIENT_ORIGIN`; credentials enabled only if needed.
- Rate limiting: generic limiter for `/api/*` and stricter limits for write-heavy routes.
- Error handling: server logs detailed errors; clients receive generic messages.

## XSS Mitigation
- Sanitization: `lib/sanitize.ts` uses DOMPurify. All `dangerouslySetInnerHTML` calls and any `innerHTML` assignments now sanitize first.
- CSP: Helmet sets a strict Content-Security-Policy (default-src 'self', with allowances for images/data/blob and connect-src to client origin/Supabase). Adjust as you add asset hosts.
- Rendering: markdown/html renderers, version comparison, static pages (terms/privacy), help articles, and editors sanitize on load.

## AI Key Handling
- No client AI keys. All AI calls proxy through server endpoints in `server/routes/ai.ts` using `GEMINI_API_KEY` (server only).
- Consider adding SSE/streaming on the server if user experience needs it; keep keys server-side.

## Incident Response
1. Identify breach (e.g., secret in git / suspicious access).
2. Rotate affected keys (Supabase, Gemini). Revoke tokens.
3. Purge git history if necessary; notify stakeholders.
4. Review access logs (Supabase storage/DB) and user activity.
5. Add targeted detections and improve rate limits or policies.

## Security Checklists

### Before Release
- [ ] No secrets in repo; `.env.example` only placeholders.
- [ ] RLS enabled for all tables; policies validated.
- [ ] Storage bucket private; policies scoped to `auth.uid()` prefix.
- [ ] Profile protected fields blocked by trigger; admin route guarded.
- [ ] CSP effective; sanitization applied to all HTML renderers/editors.
- [ ] Rate limits configured for write-heavy endpoints.
- [ ] AI calls proxy via server; no client AI keys.

### Ongoing
- [ ] CI secret scanning (gitleaks) passes.
- [ ] Dependency audits show no critical vulns.
- [ ] Logs monitored for spikes in 401/403/429 and storage deletes.

