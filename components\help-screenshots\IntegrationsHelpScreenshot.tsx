import React from 'react';
import { But<PERSON> } from '../ui/Button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/Card';
import { QuickBooksIcon } from '../Icons';
import Indicator from './Indicator';

const IntegrationsHelpScreenshot: React.FC = () => {
    return (
        <div className="relative my-6 p-4 border rounded-lg bg-zinc-50 dark:bg-zinc-900 overflow-hidden select-none text-xs">
            <Indicator number={1} position="bottom-12 left-1/4" arrow="up" />
            <h1 className="text-lg font-bold text-zinc-900 dark:text-zinc-100">Integrations</h1>
             <p className="text-zinc-600 dark:text-zinc-400 mt-1 mb-4">Connect LexiGen with your favorite tools.</p>
            <Card className="mt-4">
                <CardHeader>
                    <CardTitle className="text-sm">Available Apps</CardTitle>
                </CardHeader>
                <CardContent>
                    <Card className="flex flex-col">
                        <CardHeader className="flex-row items-center gap-4">
                            <QuickBooksIcon className="w-8 h-8"/>
                            <div>
                                <CardTitle className="text-sm">QuickBooks Online</CardTitle>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <p className="text-[10px] text-zinc-500">Automate invoicing and sync clients.</p>
                        </CardContent>
                        <div className="p-2 border-t dark:border-zinc-800">
                             <Button size="sm" className="w-full text-xs">Connect</Button>
                        </div>
                    </Card>
                </CardContent>
            </Card>
        </div>
    );
};

export default IntegrationsHelpScreenshot;
