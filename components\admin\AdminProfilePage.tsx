


import React, { useState, useRef } from 'react';
import { User, Theme } from '../../types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, TabsContent } from '../ui/Tabs';
import { cn } from '../../lib/utils';

interface AdminProfilePageProps {
  user: User;
  onUpdateProfile: (profile: Partial<Pick<User, 'name' | 'username' | 'avatarUrl'>>) => void;
  onChangePassword: (oldPassword: string, newPassword: string) => string | null;
  onUpdateUserSettings: (settings: { theme?: Theme }) => void;
}

const AdminProfilePage: React.FC<AdminProfilePageProps> = ({ user, onUpdateProfile, onChangePassword, onUpdateUserSettings }) => {
    const [activeTab, setActiveTab] = useState('profile');

    // Profile state
    const [name, setName] = useState(user.name || '');
    const [username, setUsername] = useState(user.username || '');
    const [avatarUrl, setAvatarUrl] = useState(user.avatarUrl || '');
    const [avatarPreview, setAvatarPreview] = useState(user.avatarUrl || '');
    const [profileSaved, setProfileSaved] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Password change state
    const [oldPassword, setOldPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [passwordError, setPasswordError] = useState<string | null>(null);
    const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);

    const handleProfileSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onUpdateProfile({ name, username, avatarUrl });
        setProfileSaved(true);
        setTimeout(() => setProfileSaved(false), 2000);
    };

    const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            const reader = new FileReader();
            reader.onloadend = () => {
                const result = reader.result as string;
                setAvatarPreview(result);
                setAvatarUrl(result);
            };
            reader.readAsDataURL(file);
        }
    };

    const handlePasswordChange = (e: React.FormEvent) => {
        e.preventDefault();
        setPasswordError(null);
        setPasswordSuccess(null);
        if (newPassword !== confirmPassword) {
            setPasswordError("New passwords do not match.");
            return;
        }
        if (newPassword.length < 8) {
            setPasswordError("New password must be at least 8 characters long.");
            return;
        }
        const error = onChangePassword(oldPassword, newPassword);
        if (error) {
            setPasswordError(error);
        } else {
            setPasswordSuccess("Password updated successfully!");
            setOldPassword('');
            setNewPassword('');
            setConfirmPassword('');
        }
    };

    const handleThemeChange = (theme: Theme) => onUpdateUserSettings({ theme });
    
    return (
        <div className="p-4 sm:p-6 lg:p-8">
            <Tabs>
                <TabsList className="grid w-full max-w-lg grid-cols-3">
                    <TabsTrigger onClick={() => setActiveTab('profile')} data-state={activeTab === 'profile' ? 'active' : 'inactive'}>Profile</TabsTrigger>
                    <TabsTrigger onClick={() => setActiveTab('appearance')} data-state={activeTab === 'appearance' ? 'active' : 'inactive'}>Appearance</TabsTrigger>
                    <TabsTrigger onClick={() => setActiveTab('security')} data-state={activeTab === 'security' ? 'active' : 'inactive'}>Security</TabsTrigger>
                </TabsList>
                <div className="mt-6">
                    {activeTab === 'profile' && (
                        <TabsContent>
                            <Card>
                                <CardHeader><CardTitle>Profile Information</CardTitle><CardDescription>Update your account's profile information.</CardDescription></CardHeader>
                                <CardContent>
                                    <form onSubmit={handleProfileSubmit} className="space-y-6 max-w-2xl">
                                        <div className="flex items-center gap-4">
                                            <img src={avatarPreview} alt="Avatar preview" className="w-20 h-20 rounded-full object-cover" />
                                            <div>
                                                <Button type="button" variant="outline" onClick={() => fileInputRef.current?.click()}>Change Avatar</Button>
                                                <input type="file" ref={fileInputRef} onChange={handleAvatarChange} accept="image/*" className="hidden"/>
                                                <p className="text-xs text-zinc-500 mt-2">JPG, GIF or PNG. 1MB max.</p>
                                            </div>
                                        </div>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div><label className="block text-sm font-medium dark:text-zinc-300">Full Name</label><input type="text" value={name} onChange={e => setName(e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" /></div>
                                            <div><label className="block text-sm font-medium dark:text-zinc-300">Username</label><input type="text" value={username} onChange={e => setUsername(e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" /></div>
                                        </div>
                                        <div className="flex items-center gap-4"><Button type="submit">Save Changes</Button>{profileSaved && <span className="text-sm text-green-600 dark:text-green-400">Profile saved!</span>}</div>
                                    </form>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    )}
                    {activeTab === 'appearance' && (
                        <TabsContent>
                            <Card>
                                <CardHeader><CardTitle>Appearance</CardTitle><CardDescription>Customize the look and feel of the admin portal.</CardDescription></CardHeader>
                                <CardContent className="max-w-2xl">
                                    <h4 className="font-medium text-zinc-800 dark:text-zinc-200">Theme</h4>
                                    <div className="mt-2 grid grid-cols-1 sm:grid-cols-3 gap-4">
                                        {(['light', 'dark', 'system'] as Theme[]).map(t => (
                                            <button key={t} onClick={() => handleThemeChange(t)} className={cn("p-4 rounded-lg border-2", user.theme === t ? 'border-brand-500' : 'border-zinc-200 dark:border-zinc-700')}>
                                                <div className={cn("h-16 w-full rounded-md", t === 'light' ? 'bg-white border' : t === 'dark' ? 'bg-zinc-800' : 'bg-gradient-to-br from-white from-50% to-zinc-800 to-50% border')}></div>
                                                <p className="mt-2 text-sm font-medium capitalize text-zinc-800 dark:text-zinc-200">{t}</p>
                                            </button>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    )}
                    {activeTab === 'security' && (
                        <TabsContent>
                            <Card>
                                <CardHeader><CardTitle>Change Password</CardTitle><CardDescription>Update your password for enhanced security.</CardDescription></CardHeader>
                                <CardContent>
                                    <form onSubmit={handlePasswordChange} className="space-y-4 max-w-sm">
                                        <div><label className="block text-sm font-medium dark:text-zinc-300">Current Password</label><input type="password" value={oldPassword} onChange={e => setOldPassword(e.target.value)} required className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2"/></div>
                                        <div><label className="block text-sm font-medium dark:text-zinc-300">New Password</label><input type="password" value={newPassword} onChange={e => setNewPassword(e.target.value)} required className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2"/></div>
                                        <div><label className="block text-sm font-medium dark:text-zinc-300">Confirm New Password</label><input type="password" value={confirmPassword} onChange={e => setConfirmPassword(e.target.value)} required className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2"/></div>
                                        {passwordError && <p className="text-sm text-red-500">{passwordError}</p>}
                                        {passwordSuccess && <p className="text-sm text-green-500 dark:text-green-400">{passwordSuccess}</p>}
                                        <div className="pt-2"><Button type="submit">Change Password</Button></div>
                                    </form>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    )}
                </div>
            </Tabs>
        </div>
    )
}

export default AdminProfilePage;