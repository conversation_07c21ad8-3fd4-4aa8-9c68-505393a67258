// @vitest-environment jsdom
import { describe, it, expect } from 'vitest';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import DashboardHeader from '../components/DashboardHeader';
import React from 'react';

describe('DashboardHeader', () => {
  it('renders DashboardHeader component with all props', () => {
    const user = {
      id: 'u1',
      email: '<EMAIL>',
      password: '',
      isVerified: true,
      status: 'active',
      documents: [],
      folders: [],
      notifications: [{ id: 'n1', type: 'comment', message: 'Test', createdAt: '', isRead: false }],
      quotaUsed: 0,
      quotaTotal: 0,
      planName: 'Free',
    };
    const noop = () => {};
    const { container } = render(
      <DashboardHeader
        onLogout={noop}
        user={user as any}
        view={'dashboard'}
        onToggleSidebar={noop}
        onMarkAllNotificationsRead={noop}
        onNotificationClick={noop}
        setView={noop}
        onSearchResultClick={noop}
        onUpdateUserSettings={noop}
      />
    );
    expect(container).toBeInTheDocument();
  });
});
