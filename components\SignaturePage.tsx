


import React, { useState, useMemo } from 'react';
import { Document as DocType } from '../types';
import { LegalIcon } from './Icons';
import DocumentRenderer from './DocumentRenderer';
import { Button } from './ui/Button';

interface SignaturePageProps {
  document: DocType | null;
  token: string;
  // FIX: Changed onSign to return a Promise<boolean> to match the async handleSignDocument function.
  onSign: (token: string, signerName: string) => Promise<boolean>;
}

const SignaturePage: React.FC<SignaturePageProps> = ({ document, token, onSign }) => {
    const [signerName, setSignerName] = useState('');
    const [isSigned, setIsSigned] = useState(false);
    const [error, setError] = useState('');

    const signerInfo = useMemo(() => {
        return document?.signatures.find(s => s.token === token);
    }, [document, token]);

    if (!document || !signerInfo) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-slate-100 text-center">
                <div>
                    <h2 className="text-2xl font-bold text-red-600">Invalid Link</h2>
                    <p className="text-slate-600 mt-2">This signature link is invalid or has expired.</p>
                </div>
            </div>
        );
    }
    
    if (signerInfo.status === 'signed') {
         return (
            <div className="min-h-screen flex items-center justify-center bg-slate-100 text-center">
                <div>
                    <h2 className="text-2xl font-bold text-green-600">Document Already Signed</h2>
                    <p className="text-slate-600 mt-2">You have already signed this document{signerInfo.signedAt ? ` on ${new Date(signerInfo.signedAt).toLocaleString()}` : ''}.</p>
                </div>
            </div>
        );
    }
    
    if (isSigned) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-slate-100 text-center">
                <div>
                    <h2 className="text-2xl font-bold text-green-600">Thank You!</h2>
                    <p className="text-slate-600 mt-2">Your signature has been successfully submitted.</p>
                </div>
            </div>
        );
    }

    // FIX: Made handleSign async to properly await the result of the onSign promise.
    const handleSign = async () => {
        setError('');
        if (!signerName.trim()) {
            setError('Please type your full name to sign.');
            return;
        }
        if (await onSign(token, signerName.trim())) {
            setIsSigned(true);
        } else {
            setError('An error occurred while signing. Please try again.');
        }
    };

    return (
        <div className="bg-slate-100">
            <header className="bg-white shadow-sm sticky top-0 z-10">
                <div className="max-w-5xl mx-auto py-4 px-4 flex justify-between items-center">
                     <div className="flex items-center group">
                        <LegalIcon className="h-8 w-8 text-blue-800" />
                        <span className="ml-3 text-2xl font-bold text-slate-900">LexiGen</span>
                     </div>
                     <p className="text-sm text-slate-500">Signing as {signerInfo.email}</p>
                </div>
            </header>
            
            <main className="py-8">
                <div className="page-container p-0 sm:p-4 md:p-8">
                    <DocumentRenderer 
                        content={document.content}
                        isEditing={false}
                        editorRef={{ current: null }}
                        onClick={() => {}}
                    />
                </div>

                <div className="max-w-5xl mx-auto px-4 mt-8">
                    <div className="bg-white p-6 rounded-lg shadow-md border">
                        <h3 className="font-bold text-lg">Signature Agreement</h3>
                        <p className="text-sm text-slate-600 mt-2">
                            By typing your name below and clicking "Sign and Agree", you are electronically signing this document and agree to be legally bound by its terms.
                        </p>
                        <div className="mt-4">
                            <label htmlFor="signerName" className="block text-sm font-medium text-slate-700">Type your full name to sign</label>
                            <input
                                type="text"
                                id="signerName"
                                value={signerName}
                                onChange={(e) => setSignerName(e.target.value)}
                                className="mt-1 block w-full sm:w-2/3 rounded-md border-slate-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-lg p-2"
                                placeholder="Your Full Name"
                            />
                        </div>
                        {signerName && (
                            <div className="mt-4 p-4 border-t">
                                <p className="text-sm text-slate-500">Your Signature:</p>
                                <p className="signature-font">{signerName}</p>
                            </div>
                        )}
                        {error && <p className="text-red-500 text-sm mt-2">{error}</p>}
                        <div className="mt-6">
                            <Button onClick={handleSign} disabled={!signerName.trim()}>
                                Sign and Agree
                            </Button>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    );
};

export default SignaturePage;