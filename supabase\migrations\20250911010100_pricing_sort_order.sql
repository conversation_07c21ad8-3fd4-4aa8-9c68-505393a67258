-- Add explicit display ordering for pricing plans
do $$ begin
  alter table public.pricing_plans add column if not exists sort_order int not null default 0;
exception when undefined_table then null; end $$;

-- Initialize known plan order
update public.pricing_plans set sort_order = 1 where name = 'Registered User' and sort_order = 0;
update public.pricing_plans set sort_order = 2 where name = 'Premium' and sort_order = 0;
update public.pricing_plans set sort_order = 3 where name = 'Enterprise' and sort_order = 0;

-- Optional index to improve ordered queries
do $$ begin
  create index if not exists pricing_plans_sort_order_idx on public.pricing_plans(sort_order asc);
exception when undefined_table then null; end $$;

