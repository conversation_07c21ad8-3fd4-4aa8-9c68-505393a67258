

import React from 'react';
import { sanitizeHtml } from '../lib/sanitize';
import { Button } from './ui/Button';
import { ArrowLeftIcon } from './Icons';

interface TermsPageProps {
    content: string;
}

const TermsPage: React.FC<TermsPageProps> = ({ content }) => {
  return (
    <div className="bg-white dark:bg-zinc-950 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-3xl mx-auto">
        <Button variant="ghost" onClick={() => window.history.back()} className="mb-6 text-zinc-600 dark:text-zinc-300">
            <ArrowLeftIcon className="w-4 h-4 mr-2"/>
            Back
        </Button>
        <h1 className="text-3xl font-extrabold text-zinc-900 dark:text-white">Terms and Conditions</h1>
        <p className="mt-2 text-zinc-600 dark:text-zinc-400">Last updated: {new Date().toLocaleDateString()}</p>
        
        <div 
          className="prose prose-zinc dark:prose-invert mt-8 max-w-none" 
          dangerouslySetInnerHTML={{ __html: sanitizeHtml(content) }}
        />
      </div>
       <style>{`
            .prose h2 {
                font-size: 1.25rem;
                font-weight: 600;
                margin-top: 2em;
                margin-bottom: 1em;
            }
            .prose p, .prose li {
                color: #3f3f46;
                line-height: 1.6;
            }
            .dark .prose p, .dark .prose li {
                color: #d4d4d8;
            }
            .prose ul {
                list-style-type: disc;
                padding-left: 1.5rem;
            }
       `}</style>
    </div>
  );
};

export default TermsPage;
