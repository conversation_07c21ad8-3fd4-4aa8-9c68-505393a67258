
import React from 'react';
import Indicator from './Indicator';
import { PlusCircleIcon } from '../Icons';

const ClauseLibraryHelpScreenshot: React.FC = () => {
    return (
        <div className="relative my-6 p-4 border rounded-lg bg-zinc-50 dark:bg-zinc-900 overflow-hidden select-none text-xs">
             <Indicator number={1} position="top-8 right-8" arrow="down" />
            <div className="flex justify-between items-center mb-4">
                <div>
                    <h1 className="text-lg font-bold text-zinc-900 dark:text-zinc-100">Clause Library</h1>
                    <p className="text-zinc-600 dark:text-zinc-400 mt-1">Manage your reusable clauses.</p>
                </div>
                <div className="bg-brand-600 text-white font-semibold px-3 py-1.5 rounded-lg shadow flex items-center">
                    <PlusCircleIcon className="w-4 h-4 mr-1"/> New Clause
                </div>
            </div>

            <div className="bg-white dark:bg-zinc-800 rounded-lg shadow border border-zinc-200 dark:border-zinc-700 p-3 h-32">
                 <div className="w-full max-w-xs h-8 bg-zinc-100 dark:bg-zinc-700 rounded-md border border-zinc-200 dark:border-zinc-600" />
            </div>
        </div>
    );
};

export default ClauseLibraryHelpScreenshot;
