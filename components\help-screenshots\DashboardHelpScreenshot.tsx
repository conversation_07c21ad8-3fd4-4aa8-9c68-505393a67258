
import React from 'react';
import Indicator from './Indicator';
import { DashboardIcon, HistoryIcon, PlusCircleIcon } from '../Icons';

const StatCard = ({ title, value }: { title: string, value: string }) => (
    <div className="bg-white dark:bg-zinc-800 rounded-md shadow p-3">
        <p className="text-[10px] text-zinc-500 dark:text-zinc-400">{title}</p>
        <p className="text-xl font-bold text-zinc-800 dark:text-zinc-100">{value}</p>
    </div>
);

const ChartBar = ({ height }: { height: string }) => (
    <div className="flex-1 h-full flex items-end">
        <div className="w-3/4 mx-auto rounded-t-sm bg-brand-300" style={{ height }}></div>
    </div>
);

const DashboardHelpScreenshot: React.FC = () => {
    return (
        <div className="relative my-6 p-4 border rounded-lg bg-zinc-100 dark:bg-zinc-900 overflow-hidden select-none text-[10px]">
            <Indicator number={1} position="top-8 left-1/4" arrow="down" />
            <Indicator number={2} position="top-1/2 left-1/2" arrow="up" />
            <Indicator number={3} position="top-1/4 -left-1" arrow="right" />
            
            <div className="flex gap-4">
                {/* Sidebar */}
                <div className="w-40 bg-zinc-800 text-white rounded-md p-2 space-y-2">
                    <div className="text-lg font-bold">LexiGen</div>
                    <div className="bg-zinc-700 p-2 rounded flex items-center"><DashboardIcon className="w-4 h-4 mr-2"/>Dashboard</div>
                    <div className="p-2 rounded flex items-center"><PlusCircleIcon className="w-4 h-4 mr-2"/>Generate Document</div>
                    <div className="p-2 rounded flex items-center"><HistoryIcon className="w-4 h-4 mr-2"/>Documents</div>
                </div>

                {/* Main Content */}
                <div className="flex-1">
                    <div className="grid grid-cols-4 gap-3 mb-4">
                        <StatCard title="Total Documents" value="4" />
                        <StatCard title="Folders Created" value="2" />
                        <StatCard title="Templates Used" value="0" />
                        <div className="bg-white dark:bg-zinc-800 rounded-md shadow p-3">
                            <p className="text-[10px] text-zinc-500 dark:text-zinc-400">Monthly Quota</p>
                            <div className="w-full bg-zinc-200 rounded-full h-1.5 mt-2">
                                <div className="bg-brand-600 h-1.5 rounded-full" style={{ width: '40%' }}></div>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white dark:bg-zinc-800 rounded-md shadow p-3">
                        <h2 className="text-sm font-semibold text-zinc-700 dark:text-zinc-200">Recent Activity</h2>
                        <div className="h-28 flex items-end gap-1 border-l border-b border-zinc-200 dark:border-zinc-700 p-1">
                            <ChartBar height="0%" /><ChartBar height="25%" /><ChartBar height="25%" />
                            <ChartBar height="0%" /><ChartBar height="60%" /><ChartBar height="0%" /><ChartBar height="0%" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DashboardHelpScreenshot;
