import React, { useMemo, memo } from 'react';
import { User, DashboardView, Template } from '../types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/Card';
import { Progress } from './ui/Progress';
import { FileTextIcon, FolderIcon, TemplateIcon } from './Icons';
// FIX: The exported member is INITIAL_PUBLIC_TEMPLATES, not CONTRACT_TEMPLATES. Aliasing it to fix the import error.
import { INITIAL_PUBLIC_TEMPLATES as CONTRACT_TEMPLATES } from '../constants';

interface DashboardAnalyticsProps {
  user: User;
  setView: (view: DashboardView) => void;
  publicTemplates?: Template[];
}

const StatCard: React.FC<{ title: string; value: number; icon: React.ElementType }> = memo(({ title, value, icon: Icon }) => (
    <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{title}</CardTitle>
            <Icon className="h-4 w-4 text-slate-500" />
        </CardHeader>
        <CardContent>
            <div className="text-2xl font-bold">{value}</div>
        </CardContent>
    </Card>
));


const DashboardAnalytics: React.FC<DashboardAnalyticsProps> = memo(({ user, setView, publicTemplates }) => {
    const isUnlimited = !isFinite(user.quotaTotal as number);
    const quotaPercentage = isUnlimited ? 0 : (user.quotaUsed / (user.quotaTotal || 1)) * 100;

    const templatesUsed = useMemo(() => {
        // Prefer explicit tag, fallback to name-based heuristic
        const byTag = user.documents.filter(d => (d.tags || []).includes('origin:template')).length;
        if (byTag > 0) {return byTag;}
        const publicTitles = (publicTemplates && publicTemplates.length > 0)
            ? new Set(publicTemplates.map(t => t.title))
            : new Set(CONTRACT_TEMPLATES.map(t => t.title));
        const customNames = new Set((user.customTemplates || []).map(ct => ct.name));
        return user.documents.filter(doc => publicTitles.has(doc.name) || customNames.has(doc.name)).length;
    }, [user.documents, publicTemplates, user.customTemplates]);

    return (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <StatCard title="Total Documents" value={user.documents.length} icon={FileTextIcon} />
            <StatCard title="Folders Created" value={user.folders.length} icon={FolderIcon} />
            <StatCard title="Templates Used" value={templatesUsed} icon={TemplateIcon} />
            
            <Card>
                <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Monthly Quota</CardTitle>
                    <CardDescription>
                        {isUnlimited ? 'Unlimited' : `${user.quotaUsed} of ${user.quotaTotal}`} generations used.
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    {!isUnlimited && <Progress value={quotaPercentage} className="h-2" />}
                    <p className="text-xs text-slate-500 mt-2">
                        {!isUnlimited && <a href="#" onClick={(e) => { e.preventDefault(); setView('subscription') }} className="text-blue-700 underline">Upgrade</a>} { !isUnlimited && 'for unlimited.' }
                    </p>
                </CardContent>
            </Card>
        </div>
    );
});

export default DashboardAnalytics;
