
import React, { useState, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/Card';
import { Button } from './ui/Button';
import { User, Theme, NotificationPreferences, Team, ApiKey, SsoConfig } from '../types';
import { AlertTriangleIcon } from './Icons';
import { cn } from '../lib/utils';
import { <PERSON>bs, TabsList, TabsTrigger, TabsContent } from './ui/Tabs';
import ApiSettings from './ApiSettings';
import SsoSettings from './SsoSettings';

interface SettingsPageProps {
  user: User;
  team: Team | undefined;
  onUpdateProfile: (profile: Partial<Pick<User, 'name' | 'username' | 'avatarUrl' | 'jobTitle' | 'company' | 'bio' | 'websiteUrl' | 'linkedinUrl'>>) => void;
  onDeleteAccount: () => void;
  onUpdateUserSettings: (settings: { theme?: Theme; notificationPreferences?: Partial<NotificationPreferences> }) => void;
  onChangePassword: (oldPassword: string, newPassword: string) => string | null;
  onGenerateApiKey: (name: string) => ApiKey;
  onRevokeApiKey: (keyId: string) => void;
  onUpdateSsoConfig: (config: SsoConfig) => void;
}

const ToggleSwitch: React.FC<{ checked: boolean; onChange: (checked: boolean) => void; label: string; description: string; }> = ({ checked, onChange, label, description }) => (
    <div className="flex items-center justify-between">
        <div>
            <label className="font-medium text-zinc-800 dark:text-zinc-200">{label}</label>
            <p className="text-sm text-zinc-500 dark:text-zinc-400">{description}</p>
        </div>
        <button
            type="button"
            role="switch"
            aria-checked={checked}
            onClick={() => onChange(!checked)}
            className={cn(
                'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 dark:ring-offset-zinc-900',
                checked ? 'bg-brand-600' : 'bg-zinc-200 dark:bg-zinc-700'
            )}
        >
            <span
                aria-hidden="true"
                className={cn(
                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                    checked ? 'translate-x-5' : 'translate-x-0'
                )}
            />
        </button>
    </div>
);


const SettingsPage: React.FC<SettingsPageProps> = (props) => {
    const { user, team, onUpdateProfile, onDeleteAccount, onUpdateUserSettings, onChangePassword, onGenerateApiKey, onRevokeApiKey, onUpdateSsoConfig } = props;
    const [activeTab, setActiveTab] = useState('profile');
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    
    // Profile state
    const [name, setName] = useState(user.name || '');
    const [username, setUsername] = useState(user.username || '');
    const [jobTitle, setJobTitle] = useState(user.jobTitle || '');
    const [company, setCompany] = useState(user.company || '');
    const [bio, setBio] = useState(user.bio || '');
    const [websiteUrl, setWebsiteUrl] = useState(user.websiteUrl || '');
    const [linkedinUrl, setLinkedinUrl] = useState(user.linkedinUrl || '');
    const [avatarUrl, setAvatarUrl] = useState(user.avatarUrl || '');
    const [avatarPreview, setAvatarPreview] = useState(user.avatarUrl || '');
    const [profileSaved, setProfileSaved] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    // Password change state
    const [oldPassword, setOldPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [passwordError, setPasswordError] = useState<string | null>(null);
    const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);
    
    const isEnterprise = user.planName === 'Enterprise';
    
    const handleProfileSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        onUpdateProfile({ name, username, avatarUrl, jobTitle, company, bio, websiteUrl, linkedinUrl });
        setProfileSaved(true);
        setTimeout(() => setProfileSaved(false), 2000);
    };
    
    const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            const reader = new FileReader();
            reader.onloadend = () => {
                const result = reader.result as string;
                setAvatarPreview(result);
                setAvatarUrl(result); // In a real app, this would be an upload URL
            };
            reader.readAsDataURL(file);
        }
    };

    const handlePasswordChange = (e: React.FormEvent) => {
        e.preventDefault();
        setPasswordError(null);
        setPasswordSuccess(null);
        if (newPassword !== confirmPassword) {
            setPasswordError("New passwords do not match.");
            return;
        }
        if (newPassword.length < 8) {
            setPasswordError("New password must be at least 8 characters long.");
            return;
        }
        const error = onChangePassword(oldPassword, newPassword);
        if (error) {
            setPasswordError(error);
        } else {
            setPasswordSuccess("Password updated successfully!");
            setOldPassword('');
            setNewPassword('');
            setConfirmPassword('');
        }
    };

    const handleThemeChange = (theme: Theme) => onUpdateUserSettings({ theme });
    const handleNotificationChange = (key: keyof NotificationPreferences, value: boolean) => {
        onUpdateUserSettings({ notificationPreferences: { ...user.notificationPreferences, [key]: value } });
    };
    
    const tabItems = [
        { id: 'profile', label: 'Profile' },
        { id: 'appearance', label: 'Appearance' },
        { id: 'notifications', label: 'Notifications' },
        { id: 'security', label: 'Security' },
    ];
    
    if (isEnterprise) {
        tabItems.push({ id: 'api', label: 'API & Integrations' });
        tabItems.push({ id: 'sso', label: 'Security (SSO)' });
    }

    return (
        <>
        <div className="p-4 sm:p-6 lg:p-8">
            <Tabs>
                <TabsList className="grid w-full max-w-2xl grid-cols-3 md:grid-cols-6">
                    {tabItems.map(tab => (
                        <TabsTrigger key={tab.id} onClick={() => setActiveTab(tab.id)} data-state={activeTab === tab.id ? 'active' : 'inactive'}>{tab.label}</TabsTrigger>
                    ))}
                </TabsList>
                
                <div className="mt-6">
                    {activeTab === 'profile' && (
                        <TabsContent>
                            <Card>
                                <CardHeader><CardTitle>Profile</CardTitle><CardDescription>Update your personal and professional information.</CardDescription></CardHeader>
                                <CardContent>
                                    <form onSubmit={handleProfileSubmit} className="space-y-6 max-w-2xl">
                                        <div className="flex items-center gap-4">
                                            <img src={avatarPreview} alt="Avatar preview" className="w-20 h-20 rounded-full object-cover" />
                                            <div>
                                                <Button type="button" variant="outline" onClick={() => fileInputRef.current?.click()}>Change Avatar</Button>
                                                <input type="file" ref={fileInputRef} onChange={handleAvatarChange} accept="image/*" className="hidden"/>
                                                <p className="text-xs text-zinc-500 mt-2">JPG, GIF or PNG. 1MB max.</p>
                                            </div>
                                        </div>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div><label className="block text-sm font-medium">Full Name</label><input type="text" value={name} onChange={e => setName(e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 p-2" /></div>
                                            <div><label className="block text-sm font-medium">Username</label><input type="text" value={username} onChange={e => setUsername(e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 p-2" /></div>
                                            <div><label className="block text-sm font-medium">Job Title</label><input type="text" value={jobTitle} onChange={e => setJobTitle(e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 p-2" /></div>
                                            <div><label className="block text-sm font-medium">Company</label><input type="text" value={company} onChange={e => setCompany(e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 p-2" /></div>
                                        </div>
                                        <div><label className="block text-sm font-medium">Bio</label><textarea value={bio} onChange={e => setBio(e.target.value)} rows={3} className="mt-1 block w-full rounded-md border-zinc-300 p-2"></textarea></div>
                                        <div><label className="block text-sm font-medium">Website URL</label><input type="url" value={websiteUrl} onChange={e => setWebsiteUrl(e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 p-2" /></div>
                                        <div><label className="block text-sm font-medium">LinkedIn URL</label><input type="url" value={linkedinUrl} onChange={e => setLinkedinUrl(e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 p-2" /></div>
                                        <div className="flex items-center gap-4"><Button type="submit">Save Changes</Button>{profileSaved && <span className="text-sm text-green-600">Profile saved!</span>}</div>
                                    </form>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    )}
                    {activeTab === 'appearance' && (
                         <TabsContent>
                            <Card><CardHeader><CardTitle>Appearance</CardTitle><CardDescription>Customize the look and feel of the application.</CardDescription></CardHeader>
                                <CardContent className="max-w-2xl">
                                    <h4 className="font-medium text-zinc-800">Theme</h4>
                                    <div className="mt-2 grid grid-cols-1 sm:grid-cols-3 gap-4">
                                        {(['light', 'dark', 'system'] as Theme[]).map(t => (
                                            <button key={t} onClick={() => handleThemeChange(t)} className={cn("p-4 rounded-lg border-2", user.theme === t ? 'border-brand-500' : 'border-zinc-200')}>
                                                <div className={cn("h-16 w-full rounded-md", t === 'light' ? 'bg-white border' : t === 'dark' ? 'bg-zinc-800' : 'bg-gradient-to-br from-white from-50% to-zinc-800 to-50% border')}></div>
                                                <p className="mt-2 text-sm font-medium capitalize">{t}</p>
                                            </button>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                         </TabsContent>
                    )}
                    {activeTab === 'notifications' && (
                        <TabsContent>
                            <Card><CardHeader><CardTitle>Notifications</CardTitle><CardDescription>Manage how you receive notifications.</CardDescription></CardHeader>
                                <CardContent className="space-y-4 max-w-2xl">
                                    <ToggleSwitch checked={user.notificationPreferences?.comments || false} onChange={v => handleNotificationChange('comments', v)} label="Comments & Mentions" description="Notify me about comments on my documents."/>
                                    <ToggleSwitch checked={user.notificationPreferences?.shares || false} onChange={v => handleNotificationChange('shares', v)} label="Document Shares" description="Notify me when someone shares a document."/>
                                    <ToggleSwitch checked={user.notificationPreferences?.signatures || false} onChange={v => handleNotificationChange('signatures', v)} label="Signature Events" description="Notify me about signature requests and completions."/>
                                    <ToggleSwitch checked={user.notificationPreferences?.team || false} onChange={v => handleNotificationChange('team', v)} label="Team Activity" description="Notify me about invites and changes in my team."/>
                                    <ToggleSwitch checked={user.notificationPreferences?.marketing || false} onChange={v => handleNotificationChange('marketing', v)} label="Marketing & Updates" description="Receive product updates and special offers."/>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    )}
                    {activeTab === 'security' && (
                        <TabsContent className="space-y-6">
                            <Card><CardHeader><CardTitle>Change Password</CardTitle><CardDescription>Update your password for enhanced security.</CardDescription></CardHeader>
                                <CardContent>
                                     <form onSubmit={handlePasswordChange} className="space-y-4 max-w-sm">
                                        <div><label className="block text-sm font-medium">Old Password</label><input type="password" value={oldPassword} onChange={e => setOldPassword(e.target.value)} required className="mt-1 block w-full rounded-md border-zinc-300 p-2"/></div>
                                        <div><label className="block text-sm font-medium">New Password</label><input type="password" value={newPassword} onChange={e => setNewPassword(e.target.value)} required className="mt-1 block w-full rounded-md border-zinc-300 p-2"/></div>
                                        <div><label className="block text-sm font-medium">Confirm New Password</label><input type="password" value={confirmPassword} onChange={e => setConfirmPassword(e.target.value)} required className="mt-1 block w-full rounded-md border-zinc-300 p-2"/></div>
                                        {passwordError && <p className="text-sm text-red-500">{passwordError}</p>}
                                        {passwordSuccess && <p className="text-sm text-green-500">{passwordSuccess}</p>}
                                        <div className="pt-2"><Button type="submit">Change Password</Button></div>
                                    </form>
                                </CardContent>
                            </Card>
                             <Card className="border-red-500"><CardHeader><CardTitle>Delete Account</CardTitle><CardDescription className="text-red-600">Permanently delete your account and all associated data.</CardDescription></CardHeader>
                                <CardContent>
                                    <p className="text-sm text-zinc-600 mb-4">Once your account is deleted, all of your data, including documents and personal settings, will be permanently removed. This action cannot be undone.</p>
                                    <Button variant="destructive" onClick={() => setIsDeleteModalOpen(true)}>Delete My Account</Button>
                                </CardContent>
                            </Card>
                        </TabsContent>
                    )}
                    {activeTab === 'api' && isEnterprise && (
                        <TabsContent>
                            <ApiSettings team={team} onGenerateApiKey={onGenerateApiKey} onRevokeApiKey={onRevokeApiKey} />
                        </TabsContent>
                    )}
                     {activeTab === 'sso' && isEnterprise && (
                        <TabsContent>
                           <SsoSettings ssoConfig={team?.ssoConfig} onUpdateSsoConfig={onUpdateSsoConfig} />
                        </TabsContent>
                    )}
                </div>
            </Tabs>
        </div>

        {isDeleteModalOpen && (
            <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
                <div className="bg-white rounded-2xl shadow-xl w-full max-w-md p-6">
                    <div className="flex items-start gap-4">
                        <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                            <AlertTriangleIcon className="h-6 w-6 text-red-600" />
                        </div>
                        <div className="mt-0 text-center sm:text-left">
                            <h3 className="text-lg leading-6 font-medium text-zinc-900">Delete Account</h3>
                            <p className="mt-2 text-sm text-zinc-500">Are you sure you want to delete your account? All of your data will be permanently removed. This action cannot be undone.</p>
                        </div>
                    </div>
                    <div className="mt-5 sm:mt-4 flex flex-col-reverse sm:flex-row-reverse gap-3">
                        <Button variant="destructive" onClick={() => { setIsDeleteModalOpen(false); onDeleteAccount(); }}>Delete</Button>
                        <Button variant="outline" onClick={() => setIsDeleteModalOpen(false)}>Cancel</Button>
                    </div>
                </div>
            </div>
        )}
        </>
    );
};

export default SettingsPage;
