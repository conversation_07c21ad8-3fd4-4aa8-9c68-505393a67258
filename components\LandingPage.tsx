

import React from 'react';
import Hero from './Hero';
import Pricing from './Pricing';
import Testimonials from './Testimonials';
import Footer from './Footer';
import { HOW_IT_WORKS_STEPS, FAQ_DATA } from '../constants';
import { ChevronDownIcon, SparklesIcon, EditIcon, SignatureIcon } from './Icons';
import { cn } from '../lib/utils';
import { PricingPlan, Testimonial } from '../types';
import SolutionsShowcase from './SolutionsShowcase';
import RoiCalculator from './RoiCalculator';

interface FAQItemProps {
  question: string;
  answer: string;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer }) => {
  const [isOpen, setIsOpen] = React.useState(false);
  return (
    <div className="border-b border-zinc-200 dark:border-zinc-800 py-6">
      <button
        className="w-full flex justify-between items-center text-left text-lg font-medium text-zinc-900 dark:text-white"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
      >
        <span>{question}</span>
        <ChevronDownIcon className={cn('w-6 h-6 transform transition-transform duration-300', isOpen ? 'rotate-180' : '')} />
      </button>
      <div
        className={cn(
          'overflow-hidden transition-all duration-300 ease-in-out',
          isOpen ? 'max-h-96 mt-4' : 'max-h-0'
        )}
      >
        <p className="text-zinc-600 dark:text-zinc-400">{answer}</p>
      </div>
    </div>
  );
};

const HowItWorks = () => {
    const icons = { SparklesIcon, EditIcon, SignatureIcon };
    return (
        <section id="how-it-works" className="py-20 sm:py-32">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="text-center">
                    <h2 className="text-3xl font-extrabold text-zinc-900 dark:text-white sm:text-4xl">
                        Get Started in 3 Simple Steps
                    </h2>
                </div>
                <div className="mt-16 grid md:grid-cols-3 gap-12 text-center">
                    {HOW_IT_WORKS_STEPS.map((step, i) => {
                        const Icon = icons[step.icon as keyof typeof icons];
                        return (
                        <div key={i}>
                            <div className="w-16 h-16 bg-brand-100 dark:bg-brand-900/50 text-brand-600 dark:text-brand-400 rounded-2xl flex items-center justify-center mx-auto">
                                <Icon className="w-8 h-8" />
                            </div>
                            <h3 className="mt-6 text-xl font-bold text-zinc-900 dark:text-white">{step.title}</h3>
                            <p className="mt-2 text-zinc-600 dark:text-zinc-400">{step.description}</p>
                        </div>
                        )
                    })}
                </div>
            </div>
        </section>
    )
};

const FAQSection = () => (
    <section id="faq" className="py-20 sm:py-32">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-extrabold text-zinc-900 dark:text-white sm:text-4xl">
            Frequently Asked Questions
          </h2>
        </div>
        <div className="mt-12">
            {FAQ_DATA.map((item, i) => (
                <FAQItem key={i} question={item.question} answer={item.answer} />
            ))}
        </div>
      </div>
    </section>
);

const FinalCTA = ({ setView }: { setView: (view: 'auth') => void }) => (
    <section className="bg-brand-600">
        <div className="max-w-4xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8">
            <h2 className="text-3xl font-extrabold text-white sm:text-4xl">
                <span className="block">Ready to simplify your legal work?</span>
            </h2>
            <p className="mt-4 text-lg leading-6 text-brand-100">
                Sign up today and start generating professional legal documents in minutes.
            </p>
            <button
                onClick={() => setView('auth')}
                className="mt-8 w-full inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-lg shadow-md text-base font-medium text-brand-600 bg-white hover:bg-brand-50 sm:w-auto"
            >
                Get Started for Free
            </button>
        </div>
    </section>
);


const LandingPage: React.FC<{ setView: (view: 'auth') => void, pricingPlans: PricingPlan[], testimonials: Testimonial[] }> = ({ setView, pricingPlans, testimonials }) => {
  return (
    <>
      <Hero />
      <HowItWorks />
      <SolutionsShowcase testimonials={testimonials} />
      <RoiCalculator />
      <Pricing setView={setView} pricingPlans={pricingPlans} />
      <Testimonials testimonials={testimonials} />
      <FAQSection />
      <FinalCTA setView={setView} />
      <Footer />
    </>
  );
};

export default LandingPage;