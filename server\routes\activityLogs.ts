import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient } from '../supabaseClient';

const router = Router();

const insertSchema = z.object({
  documentId: z.string().uuid().nullable().optional(),
  userEmail: z.string().email(),
  type: z.enum(['create','edit','share','comment','view','signature','revert','team','approval']),
  details: z.string().min(1),
  timestamp: z.string().optional(),
});

router.get('/', requireAuth, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('activity_logs').select('*').order('timestamp', { ascending: false });
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ logs: data });
});

router.post('/', requireAuth, async (req: AuthRequest, res) => {
  const parsed = insertSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa
    .from('activity_logs')
    .insert({
      document_id: parsed.data.documentId ?? null,
      user_email: parsed.data.userEmail,
      type: parsed.data.type,
      details: parsed.data.details,
      timestamp: parsed.data.timestamp ?? new Date().toISOString(),
    })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ log: data });
});

export default router;

