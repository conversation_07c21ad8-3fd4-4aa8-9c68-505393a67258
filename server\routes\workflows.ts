import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { requirePremium } from '../middleware/plan';
import { getUserClient } from '../supabaseClient';

const router = Router();

// Workflow Templates
const templateInsert = z.object({ teamId: z.string().uuid(), name: z.string().min(1), status: z.enum(['active','inactive']).default('inactive') });
const templateUpdate = templateInsert.partial();

router.get('/templates', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('workflow_templates').select('*').order('id');
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ templates: data });
});

router.post('/templates', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const parsed = templateInsert.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa
    .from('workflow_templates')
    .insert({ team_id: parsed.data.teamId, name: parsed.data.name, status: parsed.data.status })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ template: data });
});

router.put('/templates/:id', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = templateUpdate.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{ name: string; status: string; team_id: string }> = { name: parsed.data.name, status: parsed.data.status };
  if (parsed.data.teamId) {patch.team_id = parsed.data.teamId;}
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('workflow_templates').update(patch).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ template: data });
});

router.delete('/templates/:id', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('workflow_templates').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

// Nodes
const nodeInsert = z.object({ templateId: z.string().uuid(), type: z.string().min(1), position: z.object({ x: z.number(), y: z.number() }), data: z.record(z.any()).default({}) });
const nodeUpdate = z.object({ type: z.string().optional(), position: z.object({ x: z.number(), y: z.number() }).optional(), data: z.record(z.any()).optional() });

router.get('/templates/:templateId/nodes', requireAuth, requirePremium, async (req: AuthRequest<{ templateId: string }>, res) => {
  const { templateId } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('workflow_nodes').select('*').eq('template_id', templateId).order('id');
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ nodes: data });
});

router.post('/nodes', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const parsed = nodeInsert.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa
    .from('workflow_nodes')
    .insert({ template_id: parsed.data.templateId, type: parsed.data.type, position_x: parsed.data.position.x, position_y: parsed.data.position.y, data: parsed.data.data })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ node: data });
});

router.put('/nodes/:id', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = nodeUpdate.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{ type: string; position_x: number; position_y: number; data: Record<string, unknown> }> = {};
  if (parsed.data.type) {patch.type = parsed.data.type;}
  if (parsed.data.position) { patch.position_x = parsed.data.position.x; patch.position_y = parsed.data.position.y; }
  if (parsed.data.data) {patch.data = parsed.data.data;}
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('workflow_nodes').update(patch).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ node: data });
});

router.delete('/nodes/:id', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('workflow_nodes').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

// Edges
const edgeInsert = z.object({ templateId: z.string().uuid(), source: z.string().min(1), sourceHandle: z.string().optional(), target: z.string().min(1), targetHandle: z.string().optional() });
const edgeUpdate = edgeInsert.partial();

router.get('/templates/:templateId/edges', requireAuth, requirePremium, async (req: AuthRequest<{ templateId: string }>, res) => {
  const { templateId } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('workflow_edges').select('*').eq('template_id', templateId).order('id');
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ edges: data });
});

router.post('/edges', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const parsed = edgeInsert.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa
    .from('workflow_edges')
    .insert({ template_id: parsed.data.templateId, source: parsed.data.source, source_handle: parsed.data.sourceHandle, target: parsed.data.target, target_handle: parsed.data.targetHandle })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ edge: data });
});

router.put('/edges/:id', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = edgeUpdate.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{ source: string; source_handle: string; target: string; target_handle: string; template_id: string }> = {
    source: parsed.data.source,
    source_handle: parsed.data.sourceHandle,
    target: parsed.data.target,
    target_handle: parsed.data.targetHandle,
  };
  if (parsed.data.templateId) {patch.template_id = parsed.data.templateId;}
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('workflow_edges').update(patch).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ edge: data });
});

router.delete('/edges/:id', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('workflow_edges').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

// Instances
const instanceInsert = z.object({ templateId: z.string().uuid(), documentId: z.string().uuid(), status: z.enum(['running','completed','failed','waiting']).default('running'), currentNodeId: z.string().uuid().optional(), resumeAt: z.string().optional() });
const instanceUpdate = instanceInsert.partial();

router.get('/instances', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('workflow_instances').select('*').order('created_at', { ascending: false });
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ instances: data });
});

router.post('/instances', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const parsed = instanceInsert.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data: auth } = await supa.auth.getUser();
  const userId = auth.user?.id;
  const { data, error } = await supa
    .from('workflow_instances')
    .insert({
      template_id: parsed.data.templateId,
      document_id: parsed.data.documentId,
      user_id: userId,
      status: parsed.data.status,
      current_node_id: parsed.data.currentNodeId ?? null,
      resume_at: parsed.data.resumeAt ?? null,
    })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ instance: data });
});

router.put('/instances/:id', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = instanceUpdate.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{ status: string; current_node_id: string; resume_at: string; template_id: string; document_id: string }> = {
    status: parsed.data.status,
    current_node_id: parsed.data.currentNodeId,
    resume_at: parsed.data.resumeAt,
  };
  if (parsed.data.templateId) {patch.template_id = parsed.data.templateId;}
  if (parsed.data.documentId) {patch.document_id = parsed.data.documentId;}
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('workflow_instances').update(patch).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ instance: data });
});

router.delete('/instances/:id', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('workflow_instances').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

export default router;
