
import React, { useState } from 'react';
import { Team, PricingPlan } from '../../types';
import { Button } from '../ui/Button';
import { CloseIcon, BillingIcon } from '../Icons';

interface ManageSubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (teamId: string, planName: string) => void;
  team: Team;
  plans: PricingPlan[];
  currentPlanName: string;
}

const ManageSubscriptionModal: React.FC<ManageSubscriptionModalProps> = ({ isOpen, onClose, onSave, team, plans, currentPlanName }) => {
  const [selectedPlan, setSelectedPlan] = useState(currentPlanName);

  if (!isOpen) {return null;}

  const handleSave = () => {
    onSave(team.id, selectedPlan);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-md">
        <header className="flex items-center justify-between p-4 border-b dark:border-zinc-800">
          <h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200 flex items-center">
            <BillingIcon className="w-5 h-5 mr-2 text-brand-700 dark:text-brand-400" />
            Manage Subscription
          </h2>
          <button onClick={onClose} className="p-2 text-zinc-500 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800 rounded-lg">
            <CloseIcon className="w-6 h-6" />
          </button>
        </header>
        <main className="p-6">
          <p className="text-sm text-zinc-600 dark:text-zinc-400 mb-4">
            Change the subscription plan for <span className="font-semibold text-zinc-800 dark:text-zinc-200">{team.name}</span>.
          </p>
          <select
            value={selectedPlan}
            onChange={(e) => setSelectedPlan(e.target.value)}
            className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 shadow-sm p-2"
          >
            {plans.map(plan => (
              <option key={plan.id} value={plan.name}>{plan.name} ({plan.price})</option>
            ))}
          </select>
        </main>
        <footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t dark:border-zinc-800">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={selectedPlan === currentPlanName}>
            Save Changes
          </Button>
        </footer>
      </div>
    </div>
  );
};

export default ManageSubscriptionModal;