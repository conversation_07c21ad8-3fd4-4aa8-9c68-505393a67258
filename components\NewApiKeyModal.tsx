
import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON> } from '../types';
import { Button } from './ui/Button';
import { CloseIcon, ClipboardCopyIcon, AlertTriangleIcon } from './Icons';

interface NewApiKeyModalProps {
  apiKey: ApiKey;
  onClose: () => void;
}

const NewApiKeyModal: React.FC<NewApiKeyModalProps> = ({ apiKey, onClose }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    navigator.clipboard.writeText(apiKey.key);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-lg">
        <header className="flex items-center justify-between p-4 border-b dark:border-zinc-800">
          <h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200">API Key Generated</h2>
          <button onClick={onClose} className="p-2 text-zinc-500 hover:bg-zinc-100 rounded-lg">
            <CloseIcon className="w-6 h-6" />
          </button>
        </header>
        <main className="p-6 space-y-4">
            <div className="p-3 bg-amber-50 border-l-4 border-amber-400 text-amber-800 rounded-r-lg flex items-start gap-3">
                <AlertTriangleIcon className="w-8 h-8 flex-shrink-0 text-amber-500" />
                <div>
                    <h4 className="font-bold">Important: Copy Your Key</h4>
                    <p className="text-sm">This is the only time you will see this key. For your security, we do not store it. Please copy it and save it in a safe place.</p>
                </div>
            </div>
            <div className="flex items-center gap-2">
                <input
                    type="text"
                    readOnly
                    value={apiKey.key}
                    className="flex-1 block w-full rounded-md border-zinc-300 bg-zinc-100 p-2 font-mono text-sm dark:bg-zinc-800 dark:text-zinc-300"
                />
                <Button variant="outline" onClick={handleCopy}>
                    <ClipboardCopyIcon className="w-4 h-4 mr-2" />
                    {copied ? 'Copied!' : 'Copy'}
                </Button>
            </div>
        </main>
        <footer className="p-4 bg-zinc-50 dark:bg-zinc-950/50 flex justify-end rounded-b-2xl border-t dark:border-zinc-800">
          <Button onClick={onClose}>Close</Button>
        </footer>
      </div>
    </div>
  );
};

export default NewApiKeyModal;
