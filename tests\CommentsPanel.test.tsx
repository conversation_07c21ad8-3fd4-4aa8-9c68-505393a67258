// @vitest-environment jsdom
import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import CommentsPanel from '../components/CommentsPanel';
import React from 'react';

describe('CommentsPanel', () => {
  it('renders CommentsPanel component', () => {
    const document = { 
      commentThreads: [], 
      id: 'd1', 
      name: 'Doc', 
      content: '', 
      createdAt: '', 
      updatedAt: '',
      versions: [],
      collaborators: [],
      status: 'draft' as const,
      signatures: []
    };
    const mockUser = {
      id: 'user-1',
      name: 'Test User',
      email: '<EMAIL>',
      password: '',
      isVerified: true,
      status: 'active' as const,
      documents: [],
      folders: [],
      quotaUsed: 0,
      quotaTotal: 100,
      planName: 'Premium'
    };
    const mockOnAddReply = vi.fn();
    const mockOnResolveThread = vi.fn();
    const mockOnDeleteComment = vi.fn();
    render(
      <CommentsPanel 
        document={document} 
        currentUser={mockUser}
        allUsers={[mockUser]}
        onAddReply={mockOnAddReply}
        onResolveThread={mockOnResolveThread}
        onDeleteComment={mockOnDeleteComment}
      />
    );
    expect(screen.getByText(/Comments/i)).toBeInTheDocument();
  });
});
