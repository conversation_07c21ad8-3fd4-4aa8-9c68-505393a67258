

import React, { useState, useRef, useCallback } from 'react';
import { WorkflowTemplate, WorkflowNode, WorkflowEdge, WorkflowNodeType, WorkflowTriggerType, User } from '../types';
import { Button } from './ui/Button';
// FIX: Added missing icons and removed unused ones from the import statement.
import { ArrowLeftIcon, SaveIcon, PlayIcon, GitBranchIcon, UserCheckIcon, MailIcon, TimerIcon, EditIcon, SignatureIcon, UserPlusIcon, FolderMoveIcon, TagsIcon, LayoutGridIcon } from './Icons';
import * as Nodes from './ui/WorkflowNodes';
import { cn } from '../lib/utils';
// FIX: Changed the import for PropertiesPanel to correct a module resolution issue where the default export was not being found due to a syntax error in the source file.
import PropertiesPanel from './PropertiesPanel';

interface WorkflowCanvasProps {
  user: User;
  template: WorkflowTemplate;
  onSave: (template: WorkflowTemplate) => void;
  onBack: () => void;
}

const nodeComponents = {
  trigger: Nodes.TriggerNode,
  condition: Nodes.ConditionNode,
  approval: Nodes.ApprovalNode,
  notification: Nodes.NotificationNode,
  // FIX: Added missing DelayNode to the components map.
  delay: Nodes.DelayNode,
  // FIX: Added missing UpdateFieldNode to the components map.
  'update-field': Nodes.UpdateFieldNode,
  // FIX: Added missing WaitForSignatureNode to the components map.
  'wait-for-signature': Nodes.WaitForSignatureNode,
  'add-collaborator': Nodes.AddCollaboratorNode,
  'move-to-folder': Nodes.MoveToFolderNode,
  'add-tag': Nodes.AddTagNode,
};

const paletteItems: { type: WorkflowNodeType, label: string, icon: React.FC<{className?:string}>, iconClass: string }[] = [
    { type: 'condition', label: 'Condition', icon: GitBranchIcon, iconClass: 'text-amber-500' },
    { type: 'delay', label: 'Delay', icon: TimerIcon, iconClass: 'text-orange-500' },
    { type: 'approval', label: 'Approval', icon: UserCheckIcon, iconClass: 'text-sky-500' },
    { type: 'update-field', label: 'Update Field', icon: EditIcon, iconClass: 'text-purple-500' },
    { type: 'notification', label: 'Send Email', icon: MailIcon, iconClass: 'text-teal-500' },
    { type: 'wait-for-signature', label: 'e-Signature', icon: SignatureIcon, iconClass: 'text-indigo-500' },
    { type: 'add-collaborator', label: 'Add Collaborator', icon: UserPlusIcon, iconClass: 'text-cyan-500' },
    { type: 'move-to-folder', label: 'Move to Folder', icon: FolderMoveIcon, iconClass: 'text-lime-500' },
    { type: 'add-tag', label: 'Add Tag', icon: TagsIcon, iconClass: 'text-pink-500' },
];

const availableNodesMap: Record<WorkflowTriggerType, WorkflowNodeType[]> = {
    'document-created': ['condition', 'delay', 'approval', 'update-field', 'notification', 'wait-for-signature', 'add-collaborator', 'move-to-folder', 'add-tag'],
    'approval-requested': ['condition', 'delay', 'approval', 'update-field', 'notification'],
    'status-changed': ['condition', 'delay', 'notification', 'update-field', 'move-to-folder', 'add-tag'],
};


const WorkflowCanvas: React.FC<WorkflowCanvasProps> = ({ user, template, onSave, onBack }) => {
    const [nodes, setNodes] = useState<WorkflowNode[]>(template.nodes);
    const [edges, setEdges] = useState<WorkflowEdge[]>(template.edges);
    const [name, setName] = useState(template.name);
    const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
    const [selectedEdgeId, setSelectedEdgeId] = useState<string | null>(null);
    const [isConnecting, setIsConnecting] = useState<{ sourceId: string; sourceHandle?: string; x: number; y: number } | null>(null);

    // Scrollable workspace canvas; inner workspace grows to fit nodes
    const canvasRef = useRef<HTMLDivElement>(null);
    const dragInfoRef = useRef<{ nodeId: string; startPos: { x: number; y: number }; startMouse: { x: number; y: number }; isDragging: boolean; } | null>(null);
    const connectionInfoRef = useRef<{ sourceId: string; sourceHandle?: string; } | null>(null);

    const handleMouseMove = useCallback((e: MouseEvent) => {
        if (!dragInfoRef.current) {return;}

        const { startMouse, startPos, nodeId } = dragInfoRef.current;
        const dx = e.clientX - startMouse.x;
        const dy = e.clientY - startMouse.y;
        
        if (!dragInfoRef.current.isDragging && Math.sqrt(dx*dx + dy*dy) > 5) {
            dragInfoRef.current.isDragging = true;
        }

        if (dragInfoRef.current.isDragging) {
            let newX = startPos.x + dx;
            let newY = startPos.y + dy;

            if (canvasRef.current) {
                const canvasRect = canvasRef.current.getBoundingClientRect();
                const nodeWidth = 220;
                const nodeHeight = 80;
                newX = Math.max(0, Math.min(newX, canvasRect.width - nodeWidth));
                newY = Math.max(0, Math.min(newY, canvasRect.height - nodeHeight));
            }
            setNodes(prevNodes => prevNodes.map(n => (n.id === nodeId ? { ...n, position: { x: newX, y: newY } } : n)));
        }
    }, []);

    const handleMouseUp = useCallback(() => {
        if (dragInfoRef.current && !dragInfoRef.current.isDragging) {
            setSelectedNodeId(dragInfoRef.current.nodeId);
            setSelectedEdgeId(null);
        }
        dragInfoRef.current = null;
        window.removeEventListener('mousemove', handleMouseMove);
        window.removeEventListener('mouseup', handleMouseUp);
    }, [handleMouseMove]);
    
    const handleNodeMouseDown = (e: React.MouseEvent, node: WorkflowNode) => {
        if (node.type === 'trigger') { // Trigger node is not draggable, but selectable
            setSelectedNodeId(node.id);
            setSelectedEdgeId(null);
            return;
        }
        e.preventDefault();
        e.stopPropagation();
        dragInfoRef.current = { nodeId: node.id, startPos: node.position, startMouse: { x: e.clientX, y: e.clientY }, isDragging: false };
        window.addEventListener('mousemove', handleMouseMove);
        window.addEventListener('mouseup', handleMouseUp);
    };
    
    const handleConnectionMouseMove = useCallback((e: MouseEvent) => {
        if (!connectionInfoRef.current || !canvasRef.current) {return;}
        const canvasRect = canvasRef.current.getBoundingClientRect();
        setIsConnecting({
            sourceId: connectionInfoRef.current.sourceId,
            sourceHandle: connectionInfoRef.current.sourceHandle,
            x: e.clientX - canvasRect.left,
            y: e.clientY - canvasRect.top,
        });
    }, []);

    const handleConnectionMouseUp = useCallback((e: MouseEvent) => {
        const target = e.target as HTMLElement;
        const handleEl = target.closest('[data-handle-type="target"]');

        if (handleEl && connectionInfoRef.current) {
            const targetNodeEl = handleEl.closest('[data-node-id]');
            if (targetNodeEl) {
                const targetId = targetNodeEl.getAttribute('data-node-id');
                if (!targetId) { return; }
                const targetHandle = handleEl.getAttribute('data-handle-id') || undefined;
                const { sourceId, sourceHandle } = connectionInfoRef.current;
                
                 if (sourceId !== targetId) {
                    const newEdge: WorkflowEdge = {
                        id: `edge-${sourceId}-${sourceHandle || 'o'}-${targetId}-${targetHandle || 'i'}`,
                        source: sourceId,
                        sourceHandle: sourceHandle,
                        target: targetId,
                        targetHandle,
                    };
                    if (!edges.some(edge => edge.id === newEdge.id)) {
                        setEdges(eds => [...eds, newEdge]);
                    }
                }
            }
        }
        
        setIsConnecting(null);
        connectionInfoRef.current = null;
        window.removeEventListener('mousemove', handleConnectionMouseMove);
        window.removeEventListener('mouseup', handleConnectionMouseUp);
    }, [edges, handleConnectionMouseMove]);

    const handleStartConnection = (e: React.MouseEvent, sourceId: string, sourceHandle?: string) => {
        e.stopPropagation();
        connectionInfoRef.current = { sourceId, sourceHandle };
        window.addEventListener('mousemove', handleConnectionMouseMove);
        window.addEventListener('mouseup', handleConnectionMouseUp);
    };

    const _handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        const canvasBounds = canvasRef.current?.getBoundingClientRect();
        if (!canvasBounds) {return;}

        const type = e.dataTransfer.getData('application/reactflow') as WorkflowNodeType;
        if (type) {
            const position = { x: e.clientX - canvasBounds.left - 110, y: e.clientY - canvasBounds.top - 40 };
            const newNode: WorkflowNode = {
                id: `${type}-${crypto.randomUUID()}`,
                type,
                position,
                data: { label: `New ${type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' ')}` }
            };
            setNodes(nds => [...nds, newNode]);
        }
    };
    
    const getHandlePosition = (nodeId: string, handleType: 'source' | 'target', handleId?: string) => {
        const node = nodes.find(n => n.id === nodeId);
        if (!node) {return { x: 0, y: 0 };}
        
        let xOffset = 110;
        if (handleType === 'source' && node.type === 'condition') {
            xOffset = handleId === 'true' ? 65 : 155;
        }
        const yOffset = handleType === 'source' ? 80 : 0;
        
        return { x: node.position.x + xOffset, y: node.position.y + yOffset };
    };

    const handleAutoArrange = () => {
        if (nodes.length <= 1) {return;}
    
        const nodeWidth = 220; const nodeHeight = 80;
        const hSpacing = 80; const vSpacing = 100;
    
        const adj: Record<string, string[]> = {};
        const inDegree: Record<string, number> = {};
        nodes.forEach(node => { adj[node.id] = []; inDegree[node.id] = 0; });
        edges.forEach(edge => { adj[edge.source].push(edge.target); inDegree[edge.target]++; });
    
        const root = nodes.find(n => inDegree[n.id] === 0);
        // eslint-disable-next-line no-console
        if (!root) { console.error("No root node found for auto-layout"); return; }
    
        const levels: Record<string, number> = {};
        const queue: string[] = [root.id];
        levels[root.id] = 0;
        const visited = new Set<string>();
        let head = 0;
    
        while (head < queue.length) {
            const u = queue[head++];
            visited.add(u);
            (adj[u] || []).forEach(v => {
                if (!visited.has(v)) {
                    levels[v] = (levels[u] || 0) + 1;
                    queue.push(v);
                }
            });
        }
    
        const nodesByLevel: Record<number, string[]> = {};
        let maxLevel = 0;
        nodes.forEach(node => {
            const level = levels[node.id] ?? -1;
            if (level === -1) {return;} // Skip disconnected nodes
            if (!nodesByLevel[level]) {nodesByLevel[level] = [];}
            nodesByLevel[level].push(node.id);
            maxLevel = Math.max(maxLevel, level);
        });
    
        const canvasWidth = canvasRef.current?.clientWidth || 1000;
        const newNodes = nodes.map(node => {
            const level = levels[node.id];
            if (level === undefined) {return node;}
    
            const levelNodes = nodesByLevel[level];
            const nodeIndex = levelNodes.indexOf(node.id);
            const levelWidth = levelNodes.length * nodeWidth + (levelNodes.length - 1) * hSpacing;
            const startX = (canvasWidth - levelWidth) / 2;
    
            return {
                ...node,
                position: {
                    x: startX + nodeIndex * (nodeWidth + hSpacing),
                    y: level * (nodeHeight + vSpacing) + 50,
                },
            };
        });
        setNodes(newNodes);
    };

    const handleSave = () => onSave({ ...template, name, nodes, edges });
    const selectedNode = nodes.find(n => n.id === selectedNodeId);
    
    const triggerNode = nodes.find(n => n.type === 'trigger');
    const triggerType = triggerNode?.data.triggerType;
    const availablePaletteItems = triggerType ? paletteItems.filter(item => availableNodesMap[triggerType]?.includes(item.type)) : paletteItems;


    return (
        <div className="h-full flex flex-col bg-zinc-50 dark:bg-zinc-900">
            <header className="p-4 border-b border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-950 flex items-center justify-between">
                <div className="flex items-center gap-2"><Button variant="ghost" size="icon" onClick={onBack}><ArrowLeftIcon className="w-5 h-5"/></Button><input type="text" value={name} onChange={e => setName(e.target.value)} className="text-xl font-bold bg-transparent focus:outline-none focus:ring-2 ring-brand-500 rounded px-2" /></div>
                <div className="flex items-center gap-2">
                    <Button variant="outline" onClick={handleAutoArrange}><LayoutGridIcon className="w-5 h-5 mr-2"/>Auto-arrange</Button>
                    <Button onClick={handleSave}><SaveIcon className="w-5 h-5 mr-2" /> Save Workflow</Button>
                </div>
            </header>
            <div className="flex-1 flex overflow-hidden">
                <aside className="w-60 p-4 border-r border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-950">
                    <h3 className="font-semibold mb-2">Triggers</h3>
                    <div className="p-3 border rounded-lg flex items-center gap-2 bg-zinc-100 dark:bg-zinc-800 cursor-not-allowed text-zinc-500"><PlayIcon className="w-5 h-5 text-green-500"/>On Event</div>
                    <h3 className="font-semibold mb-2 mt-4">Logic & Actions</h3>
                    <div className="space-y-2">{availablePaletteItems.map(item => (<div key={item.type} onDragStart={(e) => e.dataTransfer.setData('application/reactflow', item.type)} draggable className={`p-3 border rounded-lg flex items-center gap-2 cursor-grab bg-white dark:bg-zinc-900 hover:bg-zinc-50 dark:hover:bg-zinc-800`}><item.icon className={cn("w-5 h-5", item.iconClass)}/>{item.label}</div>))}</div>
                </aside>
                {/* Scroll container for the canvas */}
                <main className="flex-1 relative overflow-auto">
                    {/* Workspace grows with content; scrollbars appear only when needed */}
                    {(() => {
                        const nodeWidth = 220;
                        const nodeHeight = 80;
                        const pad = 200;
                        const maxPos = nodes.reduce(
                          (acc, n) => ({
                            x: Math.max(acc.x, n.position.x || 0),
                            y: Math.max(acc.y, n.position.y || 0),
                          }),
                          { x: 0, y: 0 }
                        );
                        const workspaceWidth = Math.max(0, maxPos.x + nodeWidth + pad);
                        const workspaceHeight = Math.max(0, maxPos.y + nodeHeight + pad);
                        return (
                          <div
                            ref={canvasRef}
                            className="relative"
                            style={{ minWidth: '100%', minHeight: '100%', width: workspaceWidth || '100%', height: workspaceHeight || '100%' }}
                          >
                        {/* Base background color */}
                        <div className="absolute inset-0 bg-white dark:bg-zinc-950"></div>
                        {/* Dotted grid using currentColor for easy theming */}
                        <div className="absolute inset-0 pointer-events-none text-zinc-200 dark:text-zinc-700" style={{ backgroundImage: 'radial-gradient(currentColor 1px, transparent 1px)', backgroundSize: '20px 20px' }} />
                        <svg className="absolute w-full h-full pointer-events-none">
                        {edges.map(edge => {
                            const sourcePos = getHandlePosition(edge.source, 'source', edge.sourceHandle);
                            const targetPos = getHandlePosition(edge.target, 'target', edge.targetHandle);
                            if (!sourcePos || !targetPos) {return null;}
                            const { x: x1, y: y1 } = sourcePos; const { x: x2, y: y2 } = targetPos;
                            const isSelected = selectedEdgeId === edge.id;
                            return (<g key={edge.id} className="cursor-pointer" onClick={(e) => { e.stopPropagation(); setSelectedEdgeId(edge.id); setSelectedNodeId(null); }}><path d={`M ${x1},${y1} C ${x1},${y1+50} ${x2},${y2-50} ${x2},${y2}`} stroke={isSelected ? '#6366f1' : "#a1a1aa"} strokeWidth="2" fill="none" className="pointer-events-auto" /><path d={`M ${x1},${y1} C ${x1},${y1+50} ${x2},${y2-50} ${x2},${y2}`} stroke="transparent" strokeWidth="12" fill="none" className="pointer-events-auto" /></g>);
                        })}
                        {isConnecting && (() => {
                            const sourcePos = getHandlePosition(isConnecting.sourceId, 'source', isConnecting.sourceHandle);
                            if (!sourcePos) {return null;}
                            const { x: x1, y: y1 } = sourcePos; const { x: x2, y: y2 } = isConnecting;
                            return <path d={`M ${x1},${y1} C ${x1},${y1+50} ${x2},${y2-50} ${x2},${y2}`} stroke="#6366f1" strokeWidth="2" fill="none" />;
                        })()}
                        </svg>
                        {nodes.map(node => {
                         const NodeComponent = nodeComponents[node.type];
                         return (<div 
                            key={node.id} 
                            data-node-id={node.id}
                            onMouseDown={e => handleNodeMouseDown(e, node)} 
                            onClick={(e) => e.stopPropagation() } // Stop propagation to prevent canvas click
                            className={cn("absolute cursor-grab transition-all duration-150", selectedNodeId === node.id ? "ring-2 ring-brand-500 rounded-lg shadow-2xl z-10" : "z-0")} 
                            style={{ transform: `translate(${node.position.x}px, ${node.position.y}px)` }}
                         >
                            <NodeComponent 
                                data={node.data} 
                                onStartConnection={(e, handle) => handleStartConnection(e, node.id, handle)} 
                                user={user}
                             />
                         </div>);
                        })}
                          </div>
                        );
                    })()}
                </main>
                <PropertiesPanel 
                    user={user}
                    selectedNode={selectedNode} 
                    selectedEdgeId={selectedEdgeId} 
                    onUpdateNode={(data) => selectedNodeId && setNodes(nds => nds.map(n => n.id === selectedNodeId ? {...n, data: {...n.data, ...data}} : n))} 
                    onDeleteNode={() => { setNodes(nds => nds.filter(n => n.id !== selectedNodeId)); setEdges(eds => eds.filter(e => e.source !== selectedNodeId && e.target !== selectedNodeId)); setSelectedNodeId(null);}} 
                    onDeleteEdge={() => { setEdges(eds => eds.filter(e => e.id !== selectedEdgeId)); setSelectedEdgeId(null); }}
                />
            </div>
            {/* Removed old utility classes for grid background in favor of inline radial-gradient above */}
        </div>
    );
};

export default WorkflowCanvas;
