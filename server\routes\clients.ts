import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient, supabaseAdmin } from '../supabaseClient';

const router = Router();

const insertSchema = z.object({
  name: z.string().min(1),
  type: z.enum(['Company', 'Individual']),
  contactPerson: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  address: z.string().optional(),
});

const updateSchema = insertSchema.partial();

router.get('/', requireAuth, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('clients').select('*').order('created_at', { ascending: false });
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ clients: data });
});

router.post('/', requireAuth, async (req: AuthRequest, res) => {
  const parsed = insertSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data: profile } = await supa.auth.getUser();
  const userId = profile.user?.id;
  const { data, error } = await supa
    .from('clients')
    .insert({
      user_id: userId,
      name: parsed.data.name,
      type: parsed.data.type,
      contact_person: parsed.data.contactPerson,
      email: parsed.data.email,
      phone: parsed.data.phone,
      address: parsed.data.address,
    })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ client: data });
});

router.put('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = updateSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const patch: Record<string, unknown> = req.body;
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const { data, error } = await supa.from('clients').update(patch).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ client: data });
});

router.delete('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  // Cleanup client assets (storage + rows) using a single batch list/remove
  try {
    const userId = req.user?.id;
    if (!userId) {return res.status(401).json({ error: 'User not authenticated' });}
    const basePath = `${userId}/${id}`;
    const toDelete: string[] = [];
    let offset = 0;
    while (true) {
      const { data, error } = await supabaseAdmin.storage.from('contractgini').list(basePath, { limit: 1000, offset });
      if (error) {break;}
      if (!data || data.length === 0) {break;}
      for (const obj of data) {
        if (obj.name) {toDelete.push(`${basePath}/${obj.name}`);}
      }
      if (data.length < 1000) {break;}
      offset += 1000;
    }
    if (toDelete.length > 0) {
      await supabaseAdmin.storage.from('contractgini').remove(toDelete);
    }
    await supa.from('client_assets').delete().eq('client_id', id);
  } catch {
    // Ignore cleanup errors - best effort only
  }
  const { error } = await supa.from('clients').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

export default router;
