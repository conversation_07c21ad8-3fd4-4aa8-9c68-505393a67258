import { describe, it, expect, beforeAll, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

vi.mock('../server/supabaseClient', () => {
  const getUserClient = (_token: string) => ({
    from: (_table: string) => ({
      select: () => ({
        order: () => ({ data: [{ id: 't1', name: 'Template 1', content: 'Content', created_at: new Date().toISOString() }], error: null })
      }),
      insert: (row: { name: string; content: string }) => ({
        select: () => ({ single: () => Promise.resolve({ data: { id: 't2', ...row, created_at: new Date().toISOString() }, error: null }) })
      })
    }),
    auth: {
      getUser: () => Promise.resolve({ data: { user: { id: 'u1' } } })
    }
  });
  return { getUserClient };
});

beforeAll(() => { process.env.TEST_BYPASS_AUTH = '1'; });

describe('Custom Templates routes', () => {
  it('GET /api/custom-templates returns templates', async () => {
    const res = await request(app).get('/api/custom-templates');
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('templates');
    expect(Array.isArray(res.body.templates)).toBe(true);
  });

  it('POST /api/custom-templates creates template', async () => {
    const res = await request(app)
      .post('/api/custom-templates')
      .send({
        name: 'Test Template',
        content: 'Some content'
      })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(201);
    expect(res.body).toHaveProperty('template');
  });
});
