import React from 'react';

const AnalysisScreenshot = () => {
    return (
        <div className="w-full h-full bg-zinc-50 p-6 flex gap-6 overflow-hidden select-none">
            <div className="flex-1">
                <h1 className="text-2xl font-bold text-zinc-900">Analyze a Document</h1>
                <p className="text-sm text-zinc-600 mt-1 mb-4">Paste your document text below or upload a file to get an AI-powered analysis.</p>

                <div className="bg-white rounded-lg shadow border border-zinc-200 h-64 flex flex-col">
                    <div className="flex-1 p-3 text-sm text-zinc-400">
                        Paste your legal document text here...
                    </div>
                    <div className="p-2 border-t border-zinc-200 bg-zinc-50 flex justify-between items-center">
                        <div className="border border-zinc-300 bg-white text-zinc-600 text-xs font-semibold px-3 py-1.5 rounded-md">Upload File</div>
                        <div className="bg-indigo-600 text-white text-xs font-semibold px-3 py-1.5 rounded-md">Analyze Document</div>
                    </div>
                </div>
            </div>
            <div className="flex-1">
                 <div className="h-full w-full border-2 border-dashed border-zinc-300 rounded-lg flex items-center justify-center">
                    <p className="text-zinc-400 text-sm">Your analysis results will appear here.</p>
                 </div>
            </div>
        </div>
    );
};

export default AnalysisScreenshot;