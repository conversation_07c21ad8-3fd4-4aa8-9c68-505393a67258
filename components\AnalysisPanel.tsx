import React, { useState } from 'react';
import { Button } from './ui/Button';
import { ShieldCheckIcon } from './Icons';
import { apiFetch } from '../lib/api';
import { AnalysisResult } from '../types';
import { cn } from '../lib/utils';

interface AnalysisPanelProps {
  documentContent: string;
}

const AnalysisPanel: React.FC<AnalysisPanelProps> = ({ documentContent }) => {
  const [analysis, setAnalysis] = useState<AnalysisResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFetchAnalysis = async () => {
    setIsLoading(true);
    setError(null);
    setAnalysis(null);
    try {
      const resp = await apiFetch<{ analysis: AnalysisResult }>(`/api/ai/analyze`, { method: 'POST', body: JSON.stringify({ content: documentContent }) });
      setAnalysis(resp.analysis);
    } catch {
      setError('Failed to fetch analysis from AI.');
      // Error logged for debugging
    } finally {
      setIsLoading(false);
    }
  };

  const getSeverityClass = (severity: string) => {
    switch(severity) {
        case 'High Priority': return 'border-red-500 bg-red-50';
        case 'Suggestion': return 'border-amber-500 bg-amber-50';
        default: return 'border-blue-500 bg-blue-50';
    }
  };

  return (
    <>
      <div className="p-4 border-b border-slate-200">
        <Button className="w-full" onClick={handleFetchAnalysis} disabled={isLoading}>
          {isLoading ? 'Analyzing...' : 'Analyze Document'}
        </Button>
        <p className="text-xs text-slate-500 mt-2 text-center">AI will review for potential issues, unclear language, and missing clauses.</p>
      </div>
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {isLoading && (
            <div className="text-center text-slate-500">
                <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                <p>Analyzing document...</p>
            </div>
        )}
        {error && <p className="text-sm text-red-500">{error}</p>}
        {analysis && (
            <div className="space-y-4">
                <div>
                    <h4 className="font-semibold text-slate-800">Summary</h4>
                    <p className="text-sm text-slate-600 mt-1">{analysis.summary}</p>
                </div>
                 <div>
                    <h4 className="font-semibold text-slate-800">Findings</h4>
                    <div className="space-y-3 mt-2">
                        {analysis.findings.map((finding, index) => (
                            <div key={index} className={cn("border-l-4 rounded-r-lg p-3", getSeverityClass(finding.severity))}>
                                <h5 className="font-semibold text-sm text-slate-800">{finding.severity}</h5>
                                <p className="text-sm text-slate-600">{finding.description}</p>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        )}
        {!isLoading && !analysis && !error && (
            <div className="text-center text-sm text-slate-400 pt-8">
                <ShieldCheckIcon className="w-10 h-10 mx-auto text-slate-300 mb-2"/>
                Click "Analyze Document" to get an AI-powered review.
            </div>
        )}
      </div>
    </>
  );
};

export default AnalysisPanel;
