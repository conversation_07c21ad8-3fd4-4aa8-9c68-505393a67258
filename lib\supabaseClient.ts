import { createClient } from '@supabase/supabase-js';

const ensureEnvValue = (value: string | undefined, errorMessage: string) => {
  const normalized = typeof value === 'string' ? value.trim() : undefined;

  if (!normalized || normalized === 'undefined') {
    throw new Error(errorMessage);
  }

  return normalized;
};

const url = ensureEnvValue(
  import.meta.env.VITE_SUPABASE_URL as string | undefined,
  'Missing Supabase URL. Set the VITE_SUPABASE_URL environment variable to your project URL.'
);

const anonKey = ensureEnvValue(
  import.meta.env.VITE_SUPABASE_ANON_KEY as string | undefined,
  'Missing Supabase anon key. Set the VITE_SUPABASE_ANON_KEY environment variable to your project anon key.'
);

export const supabase = createClient(url, anonKey);

