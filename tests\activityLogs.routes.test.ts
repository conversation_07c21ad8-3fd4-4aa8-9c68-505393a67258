import { describe, it, expect, beforeAll, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

vi.mock('../server/supabaseClient', () => {
  const getUserClient = (_token: string) => ({
    from: (_table: string) => ({
      select: () => ({
        order: () => ({ data: [{ id: 'l1', type: 'create', details: 'Created doc', user_email: '<EMAIL>', timestamp: new Date().toISOString() }], error: null })
      }),
      insert: () => ({
        select: () => ({ single: () => Promise.resolve({ data: { id: 'l2', type: 'edit', details: 'Edited doc', user_email: '<EMAIL>', timestamp: new Date().toISOString() }, error: null }) })
      })
    })
  });
  return { getUserClient };
});

beforeAll(() => { process.env.TEST_BYPASS_AUTH = '1'; });

describe('Activity Logs routes', () => {
  it('GET /api/activity-logs returns logs', async () => {
    const res = await request(app).get('/api/activity-logs');
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('logs');
    expect(Array.isArray(res.body.logs)).toBe(true);
  });

  it('POST /api/activity-logs creates log', async () => {
    const res = await request(app)
      .post('/api/activity-logs')
      .send({
        userEmail: '<EMAIL>',
        type: 'create',
        details: 'Created doc',
      })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(201);
    expect(res.body).toHaveProperty('log');
  });
});
