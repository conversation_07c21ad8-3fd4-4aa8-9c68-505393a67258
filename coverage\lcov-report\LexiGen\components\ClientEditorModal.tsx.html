
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/ClientEditorModal.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> ClientEditorModal.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">76.36% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>42/55</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">40% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>2/5</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">11.11% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/9</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">76.36% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>42/55</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">2x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import React, { useState, useEffect } from 'react';
import { Client } from '../types';
import { Button } from './ui/Button';
import { CloseIcon, UsersIcon } from './Icons';
&nbsp;
interface ClientEditorModalProps {
  isOpen: boolean;
  onClose: () =&gt; void;
  onSave: (data: Omit&lt;Client, 'id' | 'createdAt'&gt;) =&gt; void;
  existingClient?: Client | null;
}
&nbsp;
const ClientEditorModal: React.FC&lt;ClientEditorModalProps&gt; = ({ isOpen, onClose, onSave, existingClient }) =&gt; {
    const [client, setClient] = useState&lt;Omit&lt;Client, 'id' | 'createdAt'&gt;&gt;({
        name: '',
        type: 'Company',
        contactPerson: '',
        email: '',
        phone: '',
        address: '',
    });
&nbsp;
    useEffect(() =&gt; {
        if (existingClient) <span class="branch-0 cbranch-no" title="branch not covered" >{</span>
<span class="cstat-no" title="statement not covered" >            setClient(existingClient);</span>
        } else {
            setClient({ name: '', type: 'Company', contactPerson: '', email: '', phone: '', address: '' });
        }
    }, [existingClient, isOpen]);
    
    if (!isOpen) {return null;<span class="branch-0 cbranch-no" title="branch not covered" >}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const handleInputChange = <span class="fstat-no" title="function not covered" >(field: keyof Omit&lt;Client, 'id' | 'createdAt'&gt;, value: string) =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >        setClient(prev =&gt; ({...prev, [field]: value }));</span>
<span class="cstat-no" title="statement not covered" >    };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const handleSave = <span class="fstat-no" title="function not covered" >() =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >        if(client.name.trim()) {</span>
<span class="cstat-no" title="statement not covered" >            onSave(client);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >    };</span>
    
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;header className="flex items-center justify-between p-4 border-b dark:border-zinc-800"&gt;</span>
                    &lt;h2 className="text-lg font-semibold flex items-center gap-2"&gt;&lt;UsersIcon className="w-5 h-5 text-brand-600"/&gt;{existingClient ? <span class="branch-0 cbranch-no" title="branch not covered" >'Edit Client' : 'Create New Client'}&lt;</span>/h2&gt;
                    &lt;Button variant="ghost" size="icon" onClick={onClose}&gt;&lt;CloseIcon className="w-5 h-5"/&gt;&lt;/Button&gt;
                &lt;/header&gt;
                &lt;main className="p-6 space-y-4 max-h-[60vh] overflow-y-auto"&gt;
                    &lt;div className="grid grid-cols-1 sm:grid-cols-2 gap-4"&gt;
                        &lt;div&gt;&lt;label className="block text-sm font-medium dark:text-zinc-300"&gt;Client Name*&lt;/label&gt;&lt;input type="text" value={client.name} onChange={<span class="fstat-no" title="function not covered" >e =&gt; handleInputChange('name', e.target.value)} c</span>lassName="mt-1 w-full p-2 border rounded-md dark:bg-zinc-800 dark:border-zinc-700" required /&gt;&lt;/div&gt;
                        &lt;div&gt;&lt;label className="block text-sm font-medium dark:text-zinc-300"&gt;Type&lt;/label&gt;&lt;select value={client.type} onChange={<span class="fstat-no" title="function not covered" >e =&gt; handleInputChange('type', e.target.value)} c</span>lassName="mt-1 w-full p-2 border rounded-md dark:bg-zinc-800 dark:border-zinc-700"&gt;&lt;option&gt;Company&lt;/option&gt;&lt;option&gt;Individual&lt;/option&gt;&lt;/select&gt;&lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div className="grid grid-cols-1 sm:grid-cols-2 gap-4"&gt;
                        &lt;div&gt;&lt;label className="block text-sm font-medium dark:text-zinc-300"&gt;Contact Person&lt;/label&gt;&lt;input type="text" value={client.contactPerson} onChange={<span class="fstat-no" title="function not covered" >e =&gt; handleInputChange('contactPerson', e.target.value)} c</span>lassName="mt-1 w-full p-2 border rounded-md dark:bg-zinc-800 dark:border-zinc-700" /&gt;&lt;/div&gt;
                        &lt;div&gt;&lt;label className="block text-sm font-medium dark:text-zinc-300"&gt;Email&lt;/label&gt;&lt;input type="email" value={client.email} onChange={<span class="fstat-no" title="function not covered" >e =&gt; handleInputChange('email', e.target.value)} c</span>lassName="mt-1 w-full p-2 border rounded-md dark:bg-zinc-800 dark:border-zinc-700" /&gt;&lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div&gt;&lt;label className="block text-sm font-medium dark:text-zinc-300"&gt;Phone&lt;/label&gt;&lt;input type="tel" value={client.phone} onChange={<span class="fstat-no" title="function not covered" >e =&gt; handleInputChange('phone', e.target.value)} c</span>lassName="mt-1 w-full p-2 border rounded-md dark:bg-zinc-800 dark:border-zinc-700" /&gt;&lt;/div&gt;
                    &lt;div&gt;&lt;label className="block text-sm font-medium dark:text-zinc-300"&gt;Address&lt;/label&gt;&lt;textarea value={client.address} onChange={<span class="fstat-no" title="function not covered" >e =&gt; handleInputChange('address', e.target.value)} r</span>ows={3} className="mt-1 w-full p-2 border rounded-md dark:bg-zinc-800 dark:border-zinc-700" /&gt;&lt;/div&gt;
                &lt;/main&gt;
                &lt;footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t dark:border-zinc-800"&gt;
                    &lt;Button variant="outline" onClick={onClose}&gt;Cancel&lt;/Button&gt;
                    &lt;Button onClick={handleSave} disabled={!client.name.trim()}&gt;Save Client&lt;/Button&gt;
                &lt;/footer&gt;
            &lt;/div&gt;
        &lt;/div&gt;
    );
};
&nbsp;
export default ClientEditorModal;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    