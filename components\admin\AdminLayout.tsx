
import React, { useEffect, useRef, useState } from 'react';
import { AdminSearchResult, AdminView, NotificationPreferences, PricingPlan, StripeConfig, Team, Template, Theme, User } from '../../types';
import AdminHeader from './AdminHeader';
import AdminProfilePage from './AdminProfilePage';
import AdminSettingsPage from './AdminSettingsPage';
import AdminSidebar from './AdminSidebar';
import AnalyticsDashboardPage from './AnalyticsDashboardPage';
import BillingPage from './BillingPage';
import CmsPage from './CmsPage';
import PlanManagementPage from './PlanManagementPage';

// Navigation utility for path-based routing
const navigateTo = (path: string, replace: boolean = false) => {
  if (replace) {
    window.history.replaceState(null, '', path);
  } else {
    window.history.pushState(null, '', path);
  }
  // Trigger a popstate event to update the UI
  window.dispatchEvent(new PopStateEvent('popstate'));
};

// URL mapping utilities for admin routing
const adminViewToPath = (adminView: AdminView): string => {
  switch (adminView) {
    case 'analytics': return '/admin';
    case 'plans': return '/admin/plans';
    case 'cms': return '/admin/cms';
    case 'settings': return '/admin/settings';
    case 'profile': return '/admin/profile';
    case 'billing': return '/admin/billing';
    default: return '/admin';
  }
};

const pathToAdminView = (path: string): AdminView | null => {
  switch (path) {
    case '/admin': return 'analytics';
    case '/admin/plans': return 'plans';
    case '/admin/cms': return 'cms';
    case '/admin/settings': return 'settings';
    case '/admin/profile': return 'profile';
    case '/admin/billing': return 'billing';
    default: return null;
  }
};

interface AdminLayoutProps {
  user: User;
  onLogout: () => void;
  allUsers: User[];
  allTeams: Team[];
  onUpdateSubscription: (teamId: string, planName: string) => void;
  onToggleUserStatus: (userId: string) => void;
  onManualVerifyUser: (userId: string) => void;
  onTriggerPasswordReset: (email: string) => void;
  pricingPlans: PricingPlan[];
  onCreatePlan: (planData: Omit<PricingPlan, 'id'>) => void;
  onUpdatePlan: (plan: PricingPlan) => void;
  onDeletePlan: (planId: string) => void;
  publicTemplates: Template[];
  onCreatePublicTemplate: (templateData: Omit<Template, 'id'>) => void;
  onUpdatePublicTemplate: (template: Template) => void;
  onDeletePublicTemplate: (templateId: string) => void;
  termsContent: string;
  privacyContent: string;
  onUpdateStaticContent: (type: 'terms' | 'privacy', content: string) => void;
  onUpdateProfile: (profile: Partial<Pick<User, 'name' | 'username' | 'avatarUrl'>>) => void;
  onChangePassword: (oldPassword: string, newPassword: string) => string | null;
  onUpdateUserSettings: (settings: { theme?: Theme; notificationPreferences?: Partial<NotificationPreferences> }) => void;
  onCancelSubscription: (userId: string) => void;
  stripeConfig: StripeConfig;
  onUpdateStripeConfig: (config: StripeConfig) => void;
}

const AdminLayout: React.FC<AdminLayoutProps> = (props) => {
  const {
    user, onLogout, allUsers, allTeams,
    onUpdateSubscription: _onUpdateSubscription, onToggleUserStatus,
    onManualVerifyUser, onTriggerPasswordReset,
    pricingPlans, onCreatePlan, onUpdatePlan, onDeletePlan,
    publicTemplates, onCreatePublicTemplate, onUpdatePublicTemplate, onDeletePublicTemplate,
    termsContent, privacyContent, onUpdateStaticContent,
    onUpdateProfile, onChangePassword, onUpdateUserSettings, onCancelSubscription,
    stripeConfig, onUpdateStripeConfig
  } = props;
  const [adminView, setAdminView] = useState<AdminView>(() => {
    // Initialize admin view from URL path
    const path = window.location.pathname;
    const viewFromPath = pathToAdminView(path);
    return viewFromPath || 'analytics';
  });

  // Search State
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<AdminSearchResult[]>([]);
  const [isSearchVisible, setIsSearchVisible] = useState(false);
  const [initialSearchTerm, setInitialSearchTerm] = useState<{ view: AdminView, term: string } | null>(null);
  const searchRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }

    const debounce = setTimeout(() => {
      const lowercasedTerm = searchTerm.toLowerCase();
      const results: AdminSearchResult[] = [];
      const adminPages = ['Analytics', 'Customers', 'Plan Management', 'CMS', 'Platform Settings'];
      const pageMap: Record<string, AdminView> = {
        analytics: 'analytics',
        customers: 'billing',
        'plan management': 'plans',
        cms: 'cms',
        'platform settings': 'settings',
      };

      allUsers.forEach(u => {
        if (!u.isSuperAdmin && (u.name?.toLowerCase().includes(lowercasedTerm) || u.email.toLowerCase().includes(lowercasedTerm))) {
          results.push({ id: u.id, title: u.name || u.email, type: 'user', context: u.email });
        }
      });

      pricingPlans.forEach(p => {
        if (p.name.toLowerCase().includes(lowercasedTerm)) {
          results.push({ id: p.id, title: p.name, type: 'plan' });
        }
      });
      publicTemplates.forEach(t => {
        if (t.title.toLowerCase().includes(lowercasedTerm)) {
          results.push({ id: t.id, title: t.title, type: 'template' });
        }
      });
      adminPages.forEach(p => {
        const pageKey = p.toLowerCase();
        if (pageKey.includes(lowercasedTerm) && pageMap[pageKey]) {
          results.push({ id: pageMap[pageKey], title: p, type: 'page', context: 'Admin Page' });
        }
      });

      setSearchResults(results);
    }, 300);

    return () => clearTimeout(debounce);
  }, [searchTerm, allUsers, allTeams, pricingPlans, publicTemplates]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsSearchVisible(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Sync admin view with URL path
  useEffect(() => {
    const handleRouteChange = () => {
      const path = window.location.pathname;
      const viewFromPath = pathToAdminView(path);
      if (viewFromPath && viewFromPath !== adminView) {
        setAdminView(viewFromPath);
      }
    };

    window.addEventListener('popstate', handleRouteChange);
    return () => window.removeEventListener('popstate', handleRouteChange);
  }, [adminView]);

  // Update URL path when admin view changes
  useEffect(() => {
    const expectedPath = adminViewToPath(adminView);
    if (window.location.pathname !== expectedPath) {
      navigateTo(expectedPath, true);
    }
  }, [adminView]);

  const handleSearchResultClick = (result: AdminSearchResult) => {
    let targetView: AdminView;
    switch (result.type) {
      case 'user':
        targetView = 'billing';
        break;
      case 'plan':
        targetView = 'plans';
        break;
      case 'template':
        targetView = 'cms';
        break;
      case 'page':
        targetView = result.id as AdminView;
        break;
      default:
        targetView = 'analytics'; // Fallback
    }

    setAdminView(targetView);
    setInitialSearchTerm({ view: targetView, term: result.title });

    setSearchTerm('');
    setSearchResults([]);
    setIsSearchVisible(false);
  };

  const onClearSearchFilter = () => setInitialSearchTerm(null);

  const renderContent = () => {
    const filterTerm = initialSearchTerm?.view === adminView ? initialSearchTerm.term : null;
    switch (adminView) {
      case 'analytics':
        return <AnalyticsDashboardPage allUsers={allUsers} allTeams={allTeams} pricingPlans={pricingPlans} />;
      case 'plans':
        return (
          <PlanManagementPage
            plans={pricingPlans}
            onCreatePlan={onCreatePlan}
            onUpdatePlan={onUpdatePlan}
            onDeletePlan={onDeletePlan}
            initialSearchTerm={filterTerm}
            onClearSearchFilter={onClearSearchFilter}
          />
        );
      case 'cms':
        return <CmsPage
          publicTemplates={publicTemplates}
          onCreateTemplate={onCreatePublicTemplate}
          onUpdateTemplate={onUpdatePublicTemplate}
          onDeleteTemplate={onDeletePublicTemplate}
          initialSearchTerm={filterTerm}
          onClearSearchFilter={onClearSearchFilter}
        />;
      case 'settings':
        return <AdminSettingsPage
          termsContent={termsContent}
          privacyContent={privacyContent}
          onUpdateStaticContent={onUpdateStaticContent}
          stripeConfig={stripeConfig}
          onUpdateStripeConfig={onUpdateStripeConfig}
        />;
      case 'profile':
        return <AdminProfilePage user={user} onUpdateProfile={onUpdateProfile} onChangePassword={onChangePassword} onUpdateUserSettings={onUpdateUserSettings} />;
      case 'billing':
        return <BillingPage
          allUsers={allUsers}
          allTeams={allTeams}
          onCancelSubscription={onCancelSubscription}
          pricingPlans={pricingPlans}
          onToggleUserStatus={onToggleUserStatus}
          onManualVerifyUser={onManualVerifyUser}
          onTriggerPasswordReset={onTriggerPasswordReset}
          initialSearchTerm={filterTerm}
          onClearSearchFilter={onClearSearchFilter}
        />;
      default:
        return <AnalyticsDashboardPage allUsers={allUsers} allTeams={allTeams} pricingPlans={pricingPlans} />;
    }
  };

  return (
    <div className="flex h-screen bg-zinc-100 dark:bg-zinc-900">
      <AdminSidebar currentView={adminView} setView={setAdminView} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <AdminHeader
          user={user}
          onLogout={onLogout}
          currentView={adminView}
          setView={setAdminView}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          onSearchFocus={() => setIsSearchVisible(true)}
          isSearchVisible={isSearchVisible && (searchResults.length > 0 || !!searchTerm)}
          searchResults={searchResults}
          onSearchResultClick={handleSearchResultClick}
          searchRef={searchRef}
        />
        <main className="flex-1 overflow-x-hidden overflow-y-auto">
          {renderContent()}
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
