declare module 'redis' {
  export interface RedisClientType {
    connect(): Promise<void>;
    on(event: string, listener: (...args: unknown[]) => void): void;
    get(key: string): Promise<string | null>;
    setEx(key: string, ttl: number, value: string): Promise<void>;
    del(key: string | string[]): Promise<void>;
    keys(pattern: string): Promise<string[]>;
    publish(channel: string, message: string): Promise<number>;
    subscribe(channel: string, listener: (message: string) => void): Promise<void>;
    quit(): Promise<void>;
  }
  export function createClient(options: unknown): RedisClientType;
}
