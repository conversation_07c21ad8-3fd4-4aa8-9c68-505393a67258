import React, { useState, useEffect, useRef } from 'react';
import { sanitizeHtml } from '../lib/sanitize';
import { Button } from './ui/Button';
import { CloseIcon, LibraryIcon, TagsIcon } from './Icons';
import { Clause } from '../types';

interface ClauseEditorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: Omit<Clause, 'id' | 'createdAt'>) => void;
  existingClause?: Clause | null;
  initialContent?: string | null;
}

const ClauseEditorModal: React.FC<ClauseEditorModalProps> = ({ isOpen, onClose, onSave, existingClause, initialContent }) => {
  const [title, setTitle] = useState('');
  const [tags, setTags] = useState('');
  const contentRef = useRef<HTMLDivElement>(null);
  
  const modalTitle = existingClause ? 'Edit Clause' : 'Create New Clause';

  useEffect(() => {
    if (isOpen) {
      if (existingClause) {
        setTitle(existingClause.title);
        setTags(existingClause.tags.join(', '));
        if (contentRef.current) {
          contentRef.current.innerHTML = sanitizeHtml(existingClause.content || '');
        }
      } else {
        setTitle('');
        setTags('');
        if (contentRef.current) {
          contentRef.current.innerHTML = sanitizeHtml(initialContent || '');
        }
      }
    }
  }, [isOpen, existingClause, initialContent]);

  if (!isOpen) {return null;}

  const handleSaveClick = () => {
    const content = contentRef.current?.innerHTML || '';
    if (title.trim() && content.trim()) {
      onSave({
        title: title.trim(),
        content: content.trim(),
        tags: tags.split(',').map(t => t.trim()).filter(Boolean),
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-2xl flex flex-col h-[80vh]">
        <header className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold text-slate-800 flex items-center">
            <LibraryIcon className="w-5 h-5 mr-2 text-blue-700" />
            {modalTitle}
          </h2>
          <button onClick={onClose} className="p-2 text-slate-500 hover:bg-slate-100 rounded-lg">
            <CloseIcon className="w-6 h-6" />
          </button>
        </header>
        <main className="p-6 flex-1 overflow-y-auto space-y-4">
          <div>
            <label htmlFor="clauseTitle" className="block text-sm font-medium text-slate-700 mb-1">
              Clause Title
            </label>
            <input
              type="text"
              id="clauseTitle"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="e.g., Confidentiality"
              className="block w-full rounded-md border-slate-300 shadow-sm p-2"
            />
          </div>
          <div>
            <label htmlFor="clauseTags" className="block text-sm font-medium text-slate-700 mb-1">
              Tags (comma-separated)
            </label>
            <div className="relative">
                 <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <TagsIcon className="h-5 w-5 text-slate-400" />
                </div>
                <input
                    type="text"
                    id="clauseTags"
                    value={tags}
                    onChange={(e) => setTags(e.target.value)}
                    placeholder="e.g., NDA, Standard, IP"
                    className="block w-full rounded-md border-slate-300 shadow-sm pl-10 p-2"
                />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-1">
              Clause Content
            </label>
            <div
              ref={contentRef}
              contentEditable
              className="w-full h-48 p-2 border border-slate-300 rounded-md overflow-y-auto focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              suppressContentEditableWarning
            />
          </div>
        </main>
        <footer className="p-4 bg-slate-50 flex justify-end gap-3 rounded-b-2xl border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSaveClick} disabled={!title.trim()}>
            Save Clause
          </Button>
        </footer>
      </div>
    </div>
  );
};

export default ClauseEditorModal;
