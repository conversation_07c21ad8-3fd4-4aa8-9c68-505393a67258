
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/NotificationsPanel.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> NotificationsPanel.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">8.19% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>5/61</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/2</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">8.19% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>5/61</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span></td><td class="text"><pre class="prettyprint lang-js">import React from 'react';
import { Notification } from '../types';
import { Button } from './ui/Button';
import { BellIcon } from './Icons';
import { cn } from '../lib/utils';
&nbsp;
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >function timeAgo(dateString: string): string {</span></span>
<span class="cstat-no" title="statement not covered" >  const date = new Date(dateString);</span>
<span class="cstat-no" title="statement not covered" >  const now = new Date();</span>
<span class="cstat-no" title="statement not covered" >  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  let interval = seconds / 31536000;</span>
<span class="cstat-no" title="statement not covered" >  if (interval &gt; 1) {return Math.floor(interval) + " years ago";}</span>
<span class="cstat-no" title="statement not covered" >  interval = seconds / 2592000;</span>
<span class="cstat-no" title="statement not covered" >  if (interval &gt; 1) {return Math.floor(interval) + " months ago";}</span>
<span class="cstat-no" title="statement not covered" >  interval = seconds / 86400;</span>
<span class="cstat-no" title="statement not covered" >  if (interval &gt; 1) {return Math.floor(interval) + " days ago";}</span>
<span class="cstat-no" title="statement not covered" >  interval = seconds / 3600;</span>
<span class="cstat-no" title="statement not covered" >  if (interval &gt; 1) {return Math.floor(interval) + " hours ago";}</span>
<span class="cstat-no" title="statement not covered" >  interval = seconds / 60;</span>
<span class="cstat-no" title="statement not covered" >  if (interval &gt; 1) {return Math.floor(interval) + " minutes ago";}</span>
<span class="cstat-no" title="statement not covered" >  return Math.floor(seconds) + " seconds ago";</span>
<span class="cstat-no" title="statement not covered" >}</span>
&nbsp;
&nbsp;
interface NotificationsPanelProps {
  notifications: Notification[];
  onMarkAllRead: () =&gt; void;
  onNotificationClick: (notification: Notification) =&gt; void;
  onClose: () =&gt; void;
  onViewAll: () =&gt; void;
}
&nbsp;
const NotificationsPanel: React.FC&lt;NotificationsPanelProps&gt; = <span class="fstat-no" title="function not covered" >({ notifications, onMarkAllRead, onNotificationClick, onClose: _onClose, onViewAll }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const sortedNotifications = [...notifications].sort((a,b) =&gt; new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div </span>
<span class="cstat-no" title="statement not covered" >            className="origin-top-right absolute right-0 mt-2 w-80 rounded-lg shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10 flex flex-col"</span>
<span class="cstat-no" title="statement not covered" >            style={{ maxHeight: 'calc(100vh - 80px)' }}</span>
        &gt;
<span class="cstat-no" title="statement not covered" >            &lt;header className="p-4 border-b flex items-center justify-between"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;h3 className="font-semibold text-slate-900"&gt;Notifications&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;Button variant="link" className="text-sm p-0 h-auto" onClick={onMarkAllRead}&gt;Mark all as read&lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/header&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex-1 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >                {sortedNotifications.length &gt; 0 ? (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;ul className="divide-y divide-slate-200"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        {sortedNotifications.slice(0, 10).map(notif =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                            &lt;li key={notif.id}&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;button</span>
<span class="cstat-no" title="statement not covered" >                                    onClick={() =&gt; onNotificationClick(notif)}</span>
<span class="cstat-no" title="statement not covered" >                                    className="w-full text-left p-4 hover:bg-slate-50 flex items-start gap-3"</span>
                                &gt;
<span class="cstat-no" title="statement not covered" >                                    {!notif.isRead &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                                        &lt;div className="w-2.5 h-2.5 rounded-full bg-blue-500 mt-1.5 flex-shrink-0"&gt;&lt;/div&gt;</span>
                                    )}
<span class="cstat-no" title="statement not covered" >                                    &lt;div className={cn("flex-1", notif.isRead ? 'pl-5' : '')}&gt;</span>
<span class="cstat-no" title="statement not covered" >                                        &lt;p className={cn("text-sm text-slate-800", !notif.isRead &amp;&amp; "font-semibold")}&gt;</span>
<span class="cstat-no" title="statement not covered" >                                            {notif.message}</span>
<span class="cstat-no" title="statement not covered" >                                        &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                                        &lt;p className="text-xs text-slate-500 mt-1"&gt;{timeAgo(notif.createdAt)}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >                        ))}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/ul&gt;</span>
                ) : (
<span class="cstat-no" title="statement not covered" >                    &lt;div className="p-12 text-center text-sm text-slate-500"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;BellIcon className="w-10 h-10 mx-auto text-slate-300 mb-2"/&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p&gt;You're all caught up!&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                )}
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;footer className="p-2 border-t bg-slate-50 rounded-b-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;Button variant="link" className="w-full text-sm text-brand-600" onClick={onViewAll}&gt;</span>
                    View all notifications
<span class="cstat-no" title="statement not covered" >                &lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/footer&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
    );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
export default NotificationsPanel;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    