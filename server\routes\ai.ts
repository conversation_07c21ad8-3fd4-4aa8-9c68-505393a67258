import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, maybeAuth, AuthRequest } from '../middleware/auth';
import { GoogleGenAI, Type } from '@google/genai';
import { createChatSession, analyzeExternalDocument } from '../../services/geminiService';

const router = Router();


function getAI() {
  const GEMINI_API_KEY = (process.env.GEMINI_API_KEY || '').trim();
  if (!GEMINI_API_KEY || GEMINI_API_KEY === '' || GEMINI_API_KEY === 'undefined') {
    return null;
  }
  return new GoogleGenAI({ apiKey: GEMINI_API_KEY });
}

// Basic one-shot chat with system prompt for conversational behavior
const chatSchema = z.object({ prompt: z.string().min(1) });
// Optional auth for doc generation to keep local/dev smooth
router.post('/chat', maybeAuth, async (req: AuthRequest, res) => {
  const parsed = chatSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  try {
    // Use createChatSession to get proper system prompt behavior
    const chat = createChatSession();
    const result = await chat.sendMessage({ message: parsed.data.prompt });
    return res.json({ text: (result.text || '').trim() });
  } catch (e: unknown) {
    const errorMsg = e instanceof Error ? e.message : 'AI request failed';
    return res.status(500).json({ error: errorMsg });
  }
});

// SSE streaming chat
router.get('/chat/stream', maybeAuth, async (req: AuthRequest, res) => {
  const prompt = (req.query.prompt as string) || '';
  if (!prompt.trim()) {return res.status(400).end();}
  const ai = getAI();
  if (!ai) {return res.status(500).end();}
  res.setHeader('Content-Type', 'text/event-stream');
  // Prevent proxy buffering and transformations so tokens flush immediately
  res.setHeader('Cache-Control', 'no-cache, no-transform');
  res.setHeader('Connection', 'keep-alive');
  // Nginx accel-buffering hint (ignored by some proxies, harmless otherwise)
  res.setHeader('X-Accel-Buffering', 'no');
  res.flushHeaders?.();
  // Prime the stream to defeat certain proxy buffers
  try { res.write(':' + ' '.repeat(2048) + '\n\n'); } catch { /* Ignore write errors for stream priming */ }
  let ended = false;
  res.on('close', () => {
    ended = true;
    try { res.end(); } catch { /* Ignore errors when ending response stream */ }
  });
  try {
    const chat = ai.chats.create({ model: 'gemini-2.5-flash' });
    const result = await chat.sendMessageStream({ message: prompt });
    for await (const chunk of result as AsyncIterable<{ text?: string }>) {
      if (ended) {break;}
      const txt = (chunk?.text || '').toString();
      if (txt) {
        res.write(`data: ${JSON.stringify({ text: txt })}\n\n`);
      }
    }
    res.write(`event: done\n`);
    res.write(`data: {}\n\n`);
    res.end();
  } catch (e: unknown) {
    if (e instanceof Error) {
      res.status(500).json({ error: e.message });
    } else {
      res.status(500).json({ error: 'Unknown error' });
    }
    try { res.write(`event: error\n`); res.write(`data: ${JSON.stringify({ error: 'stream-failed' })}\n\n`); } catch { /* Ignore write errors during error handling */ }
    res.end();
  }
});

// Compare versions -> HTML
const compareSchema = z.object({ contentA: z.string(), contentB: z.string() });
router.post('/compare-versions', requireAuth, async (req: AuthRequest, res) => {
  const ai = getAI();
  if (!ai) {return res.status(500).json({ error: 'AI not configured' });}
  const parsed = compareSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  try {
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: `You are a document comparison tool. Compare the two versions of the document provided below. Your goal is to produce a single HTML document that shows the changes from "Version A" to "Version B". Use standard HTML <ins> tags for additions and <del> tags for deletions. Do not add any explanation or commentary outside of the HTML document itself.
\n--- VERSION A ---\n${parsed.data.contentA}\n--- END VERSION A ---\n\n--- VERSION B ---\n${parsed.data.contentB}\n--- END VERSION B ---`,
    });
      // Try to parse the response as JSON and check for required properties
      let html = null;
      try {
        html = typeof response.text === 'string' ? response.text.trim() : null;
      } catch { /* Ignore JSON parsing errors */ }
      if (!html) {
        return res.status(500).json({ error: 'AI request failed' });
      }
      return res.json({ html });
  } catch (e: unknown) {
    return res.status(500).json({ error: e instanceof Error ? e.message : 'AI request failed' });
  }
});

// Analyze document
const analyzeSchema = z.object({ content: z.string() });
router.post('/analyze', requireAuth, async (req: AuthRequest, res) => {
  const ai = getAI();
  if (!ai) {return res.status(500).json({ error: 'AI not configured' });}
  const parsed = analyzeSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  try {
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: `Analyze the following legal document. Provide a brief overall summary and a list of findings. Findings should include potential issues, unclear language, or missing clauses. For each finding, provide a severity ('High Priority', 'Suggestion', or 'Information') and a description of the issue. Document content:\n\n${parsed.data.content}`,
      config: {
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            summary: { type: Type.STRING },
            findings: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  severity: { type: Type.STRING },
                  description: { type: Type.STRING },
                },
              },
            },
          },
        },
      },
    });
    // Try to parse the response as JSON and check for required properties
    let parsedJson: { summary?: string; findings?: unknown[] } | null = null;
    try {
      parsedJson = typeof response.text === 'string' ? JSON.parse(response.text) : null;
    } catch { /* Ignore JSON parsing errors */ }
    if (!parsedJson || typeof parsedJson.summary !== 'string' || !Array.isArray(parsedJson.findings)) {
      return res.status(500).json({ error: 'AI request failed' });
    }
    return res.json({ summary: parsedJson.summary, findings: parsedJson.findings });
  } catch (e: unknown) {
    return res.status(500).json({ error: e instanceof Error ? e.message : 'AI request failed' });
  }

});

router.post('/external-analyze', requireAuth, async (req: AuthRequest, res) => {
  const key = (process.env.GEMINI_API_KEY || process.env.VITE_GEMINI_API_KEY || '').trim();
  if (!key || key === 'undefined') {return res.status(500).json({ error: 'AI not configured' });}
  const parsed = analyzeSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  try {
    const result = await analyzeExternalDocument(parsed.data.content);
    return res.json({ result });
  } catch (e: unknown) {
    const errorMsg = e instanceof Error ? e.message : 'AI request failed';
    if (errorMsg.toLowerCase().includes('not configured')) {
      return res.status(500).json({ error: 'AI not configured' });
    }
    return res.status(500).json({ error: errorMsg || 'AI request failed' });
  }
});

// Suggest clauses
router.post('/suggest-clauses', requireAuth, async (req: AuthRequest, res) => {
  const ai = getAI();
  if (!ai) {return res.status(500).json({ error: 'AI not configured' });}
  const parsed = analyzeSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  try {
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: `Based on the following legal document, suggest 3-5 relevant clauses that could be added to improve it. Provide a title, a brief description of what the clause does, and the full legal text for the clause. Here is the document content: \n\n${parsed.data.content}`,
      config: {
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: {
            suggestions: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  title: { type: Type.STRING },
                  description: { type: Type.STRING },
                  text: { type: Type.STRING },
                },
              },
            },
          },
        },
      },
    });
    const jsonStr = (response.text || '').trim();
    const parsedJson = JSON.parse(jsonStr);
    return res.json({ suggestions: parsedJson.suggestions || [] });
  } catch (e: unknown) {
    return res.status(500).json({ error: e instanceof Error ? e.message : 'AI request failed' });
  }
});

// Extract key dates
router.post('/extract-key-dates', requireAuth, async (req: AuthRequest, res) => {
  const ai = getAI();
  if (!ai) {return res.status(500).json({ error: 'AI not configured' });}
  const parsed = analyzeSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  try {
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: `From the following document, extract any key dates such as 'Effective Date', 'End Date', 'Termination Date', or 'Renewal Notice Date'. Return in JSON with an array 'keyDates' of objects: { event: string, date: string (YYYY-MM-DD) }. Content: ${parsed.data.content}`,
      config: {
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: { keyDates: { type: Type.ARRAY, items: { type: Type.OBJECT, properties: { event: { type: Type.STRING }, date: { type: Type.STRING } } } } },
        },
      },
    });
    const jsonStr = (response.text || '').trim();
    const parsedJson = JSON.parse(jsonStr);
    return res.json({ keyDates: parsedJson.keyDates || [] });
  } catch (e: unknown) {
    return res.status(500).json({ error: e instanceof Error ? e.message : 'AI request failed' });
  }
});

// Extract obligations
router.post('/extract-obligations', requireAuth, async (req: AuthRequest, res) => {
  const ai = getAI();
  if (!ai) {return res.status(500).json({ error: 'AI not configured' });}
  const parsed = analyzeSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  try {
    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: `From the following document, extract obligations as { description, dueDate (YYYY-MM-DD) } in JSON array 'obligations'. Only include obligations with a parseable due date. Content: ${parsed.data.content}`,
      config: {
        responseMimeType: 'application/json',
        responseSchema: {
          type: Type.OBJECT,
          properties: { obligations: { type: Type.ARRAY, items: { type: Type.OBJECT, properties: { description: { type: Type.STRING }, dueDate: { type: Type.STRING } } } } },
        },
      },
    });
    const jsonStr = (response.text || '').trim();
    const parsedJson = JSON.parse(jsonStr);
    return res.json({ obligations: parsedJson.obligations || [] });
  } catch (e: unknown) {
    return res.status(500).json({ error: e instanceof Error ? e.message : 'AI request failed' });
  }
});

export default router;
