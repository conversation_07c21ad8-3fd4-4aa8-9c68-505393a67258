import React, { useState } from 'react';
import { Button } from './ui/Button';
import { CloseIcon } from './Icons';
import { Connector } from '../types';

interface ConnectionAuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  connector: Connector | null;
  onConnect: (credentials: Record<string, string>) => void;
}

const ConnectionAuthModal: React.FC<ConnectionAuthModalProps> = ({ isOpen, onClose, connector, onConnect }) => {
    const [credentials, setCredentials] = useState<Record<string, string>>({});
    const [error, setError] = useState('');

    if (!isOpen || !connector) {return null;}

    const { name, auth } = connector;
    
    const handleInputChange = (key: string, value: string) => {
        setCredentials(prev => ({...prev, [key]: value }));
    };

    const handleConnect = () => {
        setError('');
        if (auth.type === 'apiKey' && auth.fields) {
            for (const field of auth.fields) {
                if (!credentials[field.key]) {
                    setError(`${field.label} is required.`);
                    return;
                }
            }
        }
        // In a real app, you would validate credentials here before saving
        onConnect(credentials);
    };

    return (
        <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-md">
                <header className="flex items-center justify-between p-4 border-b dark:border-zinc-800">
                    <h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200">Connect to {name}</h2>
                    <Button variant="ghost" size="icon" onClick={onClose}><CloseIcon className="w-5 h-5"/></Button>
                </header>
                <main className="p-6 space-y-4">
                    {auth.type === 'apiKey' && auth.fields?.map(field => (
                        <div key={field.key}>
                            <label htmlFor={field.key} className="block text-sm font-medium text-zinc-700 dark:text-zinc-300">{field.label}</label>
                            <input
                                type={field.type}
                                id={field.key}
                                value={credentials[field.key] || ''}
                                onChange={e => handleInputChange(field.key, e.target.value)}
                                className="mt-1 block w-full p-2 border rounded-md dark:bg-zinc-800 dark:border-zinc-700"
                            />
                            {field.helpText && <p className="mt-1 text-xs text-zinc-500 dark:text-zinc-400">{field.helpText}</p>}
                        </div>
                    ))}
                    {auth.type === 'oauth2' && (
                        <p className="text-center">You will be redirected to {name} to authorize the connection.</p>
                    )}
                     {error && <p className="text-sm text-red-500">{error}</p>}
                </main>
                <footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t dark:border-zinc-800">
                    <Button variant="outline" onClick={onClose}>Cancel</Button>
                    <Button onClick={handleConnect}>
                        {auth.type === 'oauth2' ? 'Continue' : 'Connect'}
                    </Button>
                </footer>
            </div>
        </div>
    );
};

export default ConnectionAuthModal;
