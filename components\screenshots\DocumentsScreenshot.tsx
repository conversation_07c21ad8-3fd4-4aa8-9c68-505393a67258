import React from 'react';
import { FolderIcon } from '../Icons';

const FolderItem = ({ name, active }: { name: string, active?: boolean }) => (
    <div className={`flex items-center p-2 rounded-md text-sm font-medium ${active ? 'bg-indigo-50 text-indigo-700' : 'text-zinc-700'}`}>
        <FolderIcon className="w-5 h-5 mr-3"/> {name}
    </div>
);

const DocumentRow = ({ name, status, date }: { name: string, status: string, date: string }) => (
    <tr className="border-b border-zinc-200">
        <td className="p-3 font-medium text-zinc-800">{name}</td>
        <td className="p-3"><span className="bg-zinc-100 text-zinc-700 text-xs font-semibold px-2 py-0.5 rounded-full">{status}</span></td>
        <td className="p-3 text-zinc-500">{date}</td>
        <td className="p-3 text-right font-bold text-zinc-500">...</td>
    </tr>
);

const DocumentsScreenshot = () => {
    return (
        <div className="w-full h-full bg-zinc-50 p-6 flex gap-6 overflow-hidden select-none">
            <aside className="w-48 flex-shrink-0 bg-white p-3 rounded-xl border border-zinc-200">
                <div className="bg-indigo-600 text-white text-sm font-semibold px-4 py-2.5 rounded-lg shadow text-center mb-3">
                    New Folder
                </div>
                <nav className="space-y-1">
                    <FolderItem name="All Documents" active />
                    <FolderItem name="Uncategorized" />
                    <FolderItem name="Client Agreements" />
                    <FolderItem name="Personal" />
                    <FolderItem name="Archived" />
                </nav>
            </aside>
            <main className="flex-1 bg-white p-4 rounded-xl border border-zinc-200 flex flex-col">
                <div className="flex justify-between items-center mb-3">
                    <div>
                        <h2 className="text-lg font-bold text-zinc-900">Your Documents</h2>
                        <p className="text-xs text-zinc-500">Documents / All Documents</p>
                    </div>
                    <div className="w-48 h-8 bg-zinc-100 rounded-md border border-zinc-200 flex items-center px-2 text-sm text-zinc-400">
                        Search documents...
                    </div>
                </div>
                <div className="flex-1">
                    <table className="w-full text-left text-sm">
                        <thead>
                            <tr className="border-b border-zinc-200">
                                <th className="p-3 font-semibold text-zinc-600">Name</th>
                                <th className="p-3 font-semibold text-zinc-600">Status</th>
                                <th className="p-3 font-semibold text-zinc-600">Date Updated</th>
                                <th className="p-3 font-semibold text-zinc-600 text-right">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <DocumentRow name="Sample NDA" status="Draft" date="September 8, 2025" />
                            <DocumentRow name="Freelance Contract" status="Draft" date="September 8, 2025" />
                            <DocumentRow name="Last Will" status="Draft" date="September 6, 2025" />
                            <DocumentRow name="Uncategorized Doc" status="Draft" date="September 5, 2025" />
                        </tbody>
                    </table>
                </div>
            </main>
        </div>
    );
};

export default DocumentsScreenshot;