
import React from 'react';
import Indicator from './Indicator';
import { BotIcon, UserIcon, SendIcon } from '../Icons';

const GenerateHelpScreenshot: React.FC = () => {
    return (
        <div className="relative my-6 p-4 border rounded-lg bg-white dark:bg-zinc-900 overflow-hidden select-none text-xs">
             <Indicator number={1} position="bottom-12 left-1/2" arrow="up" />

            <div className="flex flex-col h-96">
                <div className="flex-1 p-4 space-y-4">
                    {/* Bot Message */}
                    <div className="flex items-start gap-3">
                        <div className="w-8 h-8 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center">
                            <BotIcon className="w-5 h-5 text-brand-700 dark:text-brand-400" />
                        </div>
                        <div className="px-4 py-2 rounded-2xl bg-zinc-100 dark:bg-zinc-800">
                            <p>Hello! How can I help you today?</p>
                        </div>
                    </div>
                    {/* User Message */}
                    <div className="flex items-start gap-3 justify-end">
                        <div className="px-4 py-2 rounded-2xl bg-brand-600 text-white">
                            <p>Draft a simple NDA</p>
                        </div>
                        <div className="w-8 h-8 rounded-full bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center">
                            <UserIcon className="w-5 h-5 text-zinc-600 dark:text-zinc-300" />
                        </div>
                    </div>
                </div>
                {/* Input Area */}
                <div className="p-3 border-t border-zinc-200 dark:border-zinc-800">
                    <div className="flex items-center gap-2">
                        <div className="flex-1 w-full px-3 py-2 bg-zinc-100 dark:bg-zinc-800 rounded-lg text-zinc-500">
                            Describe the contract you need...
                        </div>
                        <div className="w-10 h-10 bg-brand-600 text-white rounded-lg flex items-center justify-center">
                            <SendIcon className="w-5 h-5" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default GenerateHelpScreenshot;
