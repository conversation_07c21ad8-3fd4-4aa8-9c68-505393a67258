// @vitest-environment jsdom
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import AnalysisPanel from '../components/AnalysisPanel';
import React from 'react';

describe('AnalysisPanel', () => {
  it('renders AnalysisPanel component', () => {
    render(<AnalysisPanel documentContent="Test content" />);
    expect(screen.getByRole('button', { name: /Analyze Document/i })).toBeInTheDocument();
    expect(screen.getByText(/AI will review for potential issues/i)).toBeInTheDocument();
    expect(screen.getByText(/Click "Analyze Document" to get an AI-powered review/i)).toBeInTheDocument();
  });
});
