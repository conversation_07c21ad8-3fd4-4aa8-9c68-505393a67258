import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { requirePremium } from '../middleware/plan';
import { getUserClient } from '../supabaseClient';

const router = Router();

const insertSchema = z.object({
  name: z.string().min(1),
  trigger: z.object({ connectionId: z.string().uuid(), triggerKey: z.string().min(1) }),
  action: z.object({ connectionId: z.string().uuid(), actionKey: z.string().min(1) }),
  fieldMapping: z.record(z.string()).default({}),
  status: z.enum(['active','inactive']).default('inactive'),
});
const updateSchema = insertSchema.partial();

router.get('/', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('flows').select('*').order('created_at', { ascending: false });
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ flows: data });
});

router.post('/', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const parsed = insertSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data: auth } = await supa.auth.getUser();
  const userId = auth.user?.id;
  const { data, error } = await supa
    .from('flows')
    .insert({
      name: parsed.data.name,
      user_id: userId,
      trigger_connection_id: parsed.data.trigger.connectionId,
      trigger_key: parsed.data.trigger.triggerKey,
      action_connection_id: parsed.data.action.connectionId,
      action_key: parsed.data.action.actionKey,
      field_mapping: parsed.data.fieldMapping,
      status: parsed.data.status,
    })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ flow: data });
});

router.put('/:id', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = updateSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{
    name: string;
    status: string;
    field_mapping: Record<string, string>;
    trigger_connection_id: string;
    trigger_key: string;
    action_connection_id: string;
    action_key: string;
  }> = {
    name: parsed.data.name,
    status: parsed.data.status,
    field_mapping: parsed.data.fieldMapping,
  };
  if (parsed.data.trigger) {
    patch.trigger_connection_id = parsed.data.trigger.connectionId;
    patch.trigger_key = parsed.data.trigger.triggerKey;
  }
  if (parsed.data.action) {
    patch.action_connection_id = parsed.data.action.connectionId;
    patch.action_key = parsed.data.action.actionKey;
  }
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('flows').update(patch).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ flow: data });
});

router.delete('/:id', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('flows').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

export default router;
