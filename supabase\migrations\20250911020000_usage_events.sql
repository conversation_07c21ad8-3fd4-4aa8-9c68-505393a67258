-- Track monthly generation usage events per user
create table if not exists public.usage_events (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  event text not null check (event in ('generation')),
  created_at timestamptz not null default now()
);

alter table public.usage_events enable row level security;

-- Users can insert/select their own usage events
drop policy if exists usage_events_self_select on public.usage_events;
create policy usage_events_self_select on public.usage_events for select using (user_id = auth.uid());

drop policy if exists usage_events_self_insert on public.usage_events;
create policy usage_events_self_insert on public.usage_events for insert with check (user_id = auth.uid());

create index if not exists usage_events_user_id_created_at_idx on public.usage_events(user_id, created_at desc);
