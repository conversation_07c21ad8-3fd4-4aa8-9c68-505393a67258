// @vitest-environment jsdom
import { describe, it, expect, vi } from 'vitest';
import { render, fireEvent, screen } from '@testing-library/react';
import CreateFolderModal from '../components/CreateFolderModal';
import '@testing-library/jest-dom';
import React from 'react';

describe('CreateFolderModal', () => {
  it('renders and interacts with CreateFolderModal', () => {
    const onClose = vi.fn();
    const onSave = vi.fn();
    render(
      <CreateFolderModal isOpen={true} onClose={onClose} onSave={onSave} />
    );
    // Modal should render input and buttons
    expect(screen.getByLabelText(/Folder Name/i)).toBeInTheDocument();
    fireEvent.change(screen.getByLabelText(/Folder Name/i), { target: { value: 'Test Folder' } });
    fireEvent.click(screen.getByText(/Create Folder/i));
    expect(onSave).toHaveBeenCalledWith('Test Folder');
    fireEvent.click(screen.getByText(/Cancel/i));
    expect(onClose).toHaveBeenCalled();
  });

  it('does not render when isOpen is false', () => {
    const { container } = render(
      <CreateFolderModal isOpen={false} onClose={() => {}} onSave={() => {}} />
    );
    expect(container).toBeEmptyDOMElement();
  });
});
