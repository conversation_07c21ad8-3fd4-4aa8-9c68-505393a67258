
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/SolutionsShowcase.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> SolutionsShowcase.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/81</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/81</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">&nbsp;
import React from 'react';
<span class="cstat-no" title="statement not covered" >import { LegalIcon, BriefcaseIcon, ShoppingCartIcon, CheckIcon } from './Icons';</span>
<span class="cstat-no" title="statement not covered" >import { TESTIMONIALS } from '../constants';</span>
<span class="cstat-no" title="statement not covered" >import ClauseLibraryScreenshot from './screenshots/ClauseLibraryScreenshot';</span>
<span class="cstat-no" title="statement not covered" >import TemplatesScreenshot from './screenshots/TemplatesScreenshot';</span>
<span class="cstat-no" title="statement not covered" >import LifecycleScreenshot from './screenshots/LifecycleScreenshot';</span>
<span class="cstat-no" title="statement not covered" >import { cn } from '../lib/utils';</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const PersonaFeature: React.FC&lt;{ icon: React.ElementType, title: string, description: string, screenshot: React.ReactNode, testimonial: typeof TESTIMONIALS[0], reverse?: boolean }&gt; = (props) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const { icon: Icon, title, description, screenshot, testimonial, reverse = false } = props;</span>
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="grid lg:grid-cols-2 gap-12 lg:gap-20 items-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className={cn("order-2", reverse ? "lg:order-2" : "lg:order-1")}&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="inline-flex items-center gap-3 bg-brand-50 dark:bg-brand-900/30 px-4 py-2 rounded-full border border-brand-200 dark:border-brand-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;Icon className="w-6 h-6 text-brand-600 dark:text-brand-400" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;h3 className="text-2xl font-bold text-zinc-900 dark:text-white"&gt;{title}&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="mt-4 text-lg text-zinc-600 dark:text-zinc-400"&gt;{description}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;ul className="mt-6 space-y-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;li className="flex items-center gap-3"&gt;&lt;CheckIcon className="w-5 h-5 text-brand-500"/&gt;&lt;span className="text-zinc-700 dark:text-zinc-300"&gt;Centralized Clause Library&lt;/span&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;li className="flex items-center gap-3"&gt;&lt;CheckIcon className="w-5 h-5 text-brand-500"/&gt;&lt;span className="text-zinc-700 dark:text-zinc-300"&gt;AI-Powered Risk Analysis&lt;/span&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;li className="flex items-center gap-3"&gt;&lt;CheckIcon className="w-5 h-5 text-brand-500"/&gt;&lt;span className="text-zinc-700 dark:text-zinc-300"&gt;Customizable Approval Workflows&lt;/span&gt;&lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/ul&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;blockquote className="mt-8 p-6 bg-zinc-50 dark:bg-zinc-900 rounded-xl border border-zinc-200 dark:border-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;p className="text-zinc-700 dark:text-zinc-300 italic"&gt;“{testimonial.quote}”&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;footer className="mt-4 flex items-center gap-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;img className="w-12 h-12 rounded-full" src={testimonial.avatar} alt={testimonial.name} /&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;div className="font-semibold text-zinc-900 dark:text-white"&gt;{testimonial.name}&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;div className="text-zinc-600 dark:text-zinc-400"&gt;{testimonial.title}&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/footer&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/blockquote&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className={cn("order-1", reverse ? "lg:order-1" : "lg:order-2")}&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="p-4 bg-zinc-100 dark:bg-zinc-900 rounded-2xl shadow-xl"&gt;{screenshot}&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
    );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
&nbsp;
<span class="cstat-no" title="statement not covered" >const SolutionsShowcase = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;section id="solutions" className="py-20 sm:py-32 bg-white dark:bg-zinc-950"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="text-center"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;h2 className="text-3xl font-extrabold text-zinc-900 dark:text-white sm:text-4xl lg:text-5xl"&gt;</span>
            A Solution for Every Team
<span class="cstat-no" title="statement not covered" >          &lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;p className="mt-4 text-lg text-zinc-600 dark:text-zinc-400 max-w-3xl mx-auto"&gt;</span>
            LexiGen empowers Legal, Sales, and Procurement teams to manage the entire contract lifecycle with speed and confidence.
<span class="cstat-no" title="statement not covered" >          &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        &lt;div className="mt-20 space-y-24"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div id="solutions-legal"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;PersonaFeature</span>
<span class="cstat-no" title="statement not covered" >              icon={LegalIcon}</span>
<span class="cstat-no" title="statement not covered" >              title="For Legal Teams"</span>
<span class="cstat-no" title="statement not covered" >              description="Mitigate risk and maintain control with a single source of truth for all contracts. Standardize language with a pre-approved clause library and accelerate reviews with powerful AI analysis."</span>
<span class="cstat-no" title="statement not covered" >              screenshot={&lt;ClauseLibraryScreenshot /&gt;}</span>
<span class="cstat-no" title="statement not covered" >              testimonial={TESTIMONIALS[2]} // Emily White, Paralegal</span>
<span class="cstat-no" title="statement not covered" >            /&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >           &lt;div id="solutions-sales"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;PersonaFeature</span>
<span class="cstat-no" title="statement not covered" >                icon={BriefcaseIcon}</span>
<span class="cstat-no" title="statement not covered" >                title="For Sales Teams"</span>
<span class="cstat-no" title="statement not covered" >                description="Close deals faster by eliminating contract bottlenecks. Empower your sales reps to self-serve approved templates, reducing legal's workload and accelerating the sales cycle from proposal to signature."</span>
<span class="cstat-no" title="statement not covered" >                screenshot={&lt;TemplatesScreenshot /&gt;}</span>
<span class="cstat-no" title="statement not covered" >                testimonial={TESTIMONIALS[1]} // John Smith, Freelancer</span>
<span class="cstat-no" title="statement not covered" >                reverse</span>
<span class="cstat-no" title="statement not covered" >            /&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >           &lt;div id="solutions-procurement"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;PersonaFeature</span>
<span class="cstat-no" title="statement not covered" >                icon={ShoppingCartIcon}</span>
<span class="cstat-no" title="statement not covered" >                title="For Procurement"</span>
<span class="cstat-no" title="statement not covered" >                description="Streamline vendor onboarding and manage obligations effectively. Gain complete visibility into your supplier agreements, track key dates with an AI-powered lifecycle dashboard, and never miss a renewal."</span>
<span class="cstat-no" title="statement not covered" >                screenshot={&lt;LifecycleScreenshot /&gt;}</span>
<span class="cstat-no" title="statement not covered" >                testimonial={TESTIMONIALS[3]} // David Chen, Procurement</span>
<span class="cstat-no" title="statement not covered" >            /&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/section&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default SolutionsShowcase;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    