
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/PropertiesPanel.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> PropertiesPanel.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">25.43% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>29/114</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">8.33% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/12</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">4.34% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/23</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">25.43% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>29/114</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span></td><td class="text"><pre class="prettyprint lang-js">&nbsp;
&nbsp;
import React from 'react';
import { WorkflowNode, DocumentStatus, WorkflowTriggerType, User, Permission } from '../types';
import { Button } from './ui/Button';
import { TrashIcon } from './Icons';
&nbsp;
interface PropertiesPanelProps {
  user: User;
  selectedNode: WorkflowNode | null;
  selectedEdgeId: string | null;
  onUpdateNode: (data: Partial&lt;WorkflowNode&gt;) =&gt; void;
  onDeleteNode: () =&gt; void;
  onDeleteEdge: () =&gt; void;
}
&nbsp;
const LabelInput: React.FC&lt;{ value: string, onChange: (val: string) =&gt; void }&gt; = <span class="fstat-no" title="function not covered" >({ value, onChange }) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;label className="text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Node Label&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;input type="text" value={value} onChange={e =&gt; onChange(e.target.value)} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
);
&nbsp;
const allStatuses: DocumentStatus[] = ['draft', 'in-review', 'approved', 'out-for-signature', 'completed', 'archived'];
const valueOperators = ['&gt;', '&lt;', '=='];
const nameOperators = ['contains', 'starts with', 'ends with'];
const dateOperators = ['before', 'after'];
const stringOperators = ['is', 'is not'];
&nbsp;
&nbsp;
const PropertiesPanel: React.FC&lt;PropertiesPanelProps&gt; = ({ user, selectedNode, selectedEdgeId, onUpdateNode, onDeleteNode, onDeleteEdge }) =&gt; {
    if (!selectedNode &amp;&amp; !selectedEdgeId) {
        return (
            &lt;aside className="w-72 p-4 border-l border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-950"&gt;
                &lt;h3 className="font-semibold text-zinc-800 dark:text-zinc-200"&gt;Properties&lt;/h3&gt;
                &lt;p className="text-sm text-zinc-500 dark:text-zinc-400 mt-4"&gt;Select a node or an edge to view its properties.&lt;/p&gt;
            &lt;/aside&gt;
        );
<span class="branch-0 cbranch-no" title="branch not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (selectedEdgeId) {</span>
<span class="cstat-no" title="statement not covered" >        return (</span>
<span class="cstat-no" title="statement not covered" >             &lt;aside className="w-72 p-4 border-l border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-950"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;h3 className="font-semibold text-zinc-800 dark:text-zinc-200"&gt;Connection&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="mt-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;Button variant="destructive" size="sm" onClick={onDeleteEdge} className="w-full"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;TrashIcon className="w-4 h-4 mr-2"/&gt;Delete Connection</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/aside&gt;</span>
        )
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!selectedNode) {return null;}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const handleConditionFieldChange = <span class="fstat-no" title="function not covered" >(field: string) =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >        let op: string = 'is';</span>
<span class="cstat-no" title="statement not covered" >        if (field === 'value') {op = '&gt;';}</span>
<span class="cstat-no" title="statement not covered" >        if (field === 'name') {op = 'contains';}</span>
<span class="cstat-no" title="statement not covered" >        if (field === 'createdAt') {op = 'after';}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        onUpdateNode({</span>
<span class="cstat-no" title="statement not covered" >            conditionField: field,</span>
<span class="cstat-no" title="statement not covered" >            conditionOperator: op,</span>
<span class="cstat-no" title="statement not covered" >            conditionValue: null</span>
<span class="cstat-no" title="statement not covered" >        });</span>
<span class="cstat-no" title="statement not covered" >    }</span>
    
<span class="cstat-no" title="statement not covered" >    const getConditionOperators = <span class="fstat-no" title="function not covered" >() =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >        switch (selectedNode?.data.conditionField) {</span>
<span class="cstat-no" title="statement not covered" >            case 'value': return valueOperators;</span>
<span class="cstat-no" title="statement not covered" >            case 'name': return nameOperators;</span>
<span class="cstat-no" title="statement not covered" >            case 'createdAt': return dateOperators;</span>
<span class="cstat-no" title="statement not covered" >            default: return stringOperators;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >        &lt;aside className="w-72 p-4 border-l border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-950 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;h3 className="font-semibold text-zinc-800 dark:text-zinc-200 capitalize"&gt;{selectedNode.type.replace('-', ' ')} Node&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="mt-4 space-y-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                {selectedNode.type === 'trigger' ? (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;label className="text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Trigger Event&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;select</span>
<span class="cstat-no" title="statement not covered" >                            value={selectedNode.data.triggerType || ''}</span>
<span class="cstat-no" title="statement not covered" >                            onChange={<span class="fstat-no" title="function not covered" >e =&gt; {</span></span>
<span class="cstat-no" title="statement not covered" >                                const selectedOption = e.target.options[e.target.selectedIndex];</span>
<span class="cstat-no" title="statement not covered" >                                onUpdateNode({ triggerType: e.target.value as WorkflowTriggerType, label: selectedOption.text });</span>
<span class="cstat-no" title="statement not covered" >                            }}</span>
<span class="cstat-no" title="statement not covered" >                            className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"</span>
                        &gt;
<span class="cstat-no" title="statement not covered" >                            &lt;option value="document-created"&gt;On Document Creation&lt;/option&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;option value="approval-requested"&gt;On Approval Request&lt;/option&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;option value="status-changed"&gt;On Status Change&lt;/option&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/select&gt;</span>
<span class="cstat-no" title="statement not covered" ><span class="branch-0 cbranch-no" title="branch not covered" >                    &lt;/div&gt;</span></span>
                ) : (
<span class="cstat-no" title="statement not covered" >                    &lt;LabelInput value={selectedNode.data.label} onChange={<span class="fstat-no" title="function not covered" >val =&gt; onUpdateNode({ label: val })} /</span>&gt;</span>
                )}
&nbsp;
                {selectedNode.type === <span class="branch-0 cbranch-no" title="branch not covered" >'condition' &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;&lt;label className="text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Field&lt;/label&gt;&lt;select value={selectedNode.data.conditionField || 'value'} onChange={<span class="fstat-no" title="function not covered" >e =&gt; handleConditionFieldChange(e.target.value)} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"&gt;&lt;option value="value"&gt;Value&lt;/option&gt;&lt;option value="status"&gt;Status&lt;/option&gt;&lt;option value="folderId"&gt;Folder&lt;/option&gt;&lt;option value="clientId"&gt;Client&lt;/option&gt;&lt;option value="name"&gt;Name&lt;/option&gt;&lt;option value="createdAt"&gt;Creation Date&lt;/option&gt;&lt;/select&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;&lt;label className="text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Operator&lt;/label&gt;&lt;select value={selectedNode.data.conditionOperator || '&gt;'} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ conditionOperator: e.target.value })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"&gt;{getConditionOperators().map(op =&gt; &lt;option key={op} value={op}&gt;{op}&lt;/option&gt;)}&lt;/select&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;&lt;label className="text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Value&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                        {selectedNode.data.conditionField === 'value' &amp;&amp; &lt;input type="number" value={selectedNode.data.conditionValue || 0} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ conditionValue: Number(e.target.value) })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/&gt;}</span>
<span class="cstat-no" title="statement not covered" >                        {selectedNode.data.conditionField === 'status' &amp;&amp; &lt;select value={selectedNode.data.conditionValue || 'draft'} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ conditionValue: e.target.value })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"&gt;{allStatuses.map(s =&gt; &lt;option key={s} value={s}&gt;{s}&lt;/option&gt;)}&lt;/select&gt;}</span>
<span class="cstat-no" title="statement not covered" >                        {selectedNode.data.conditionField === 'folderId' &amp;&amp; &lt;select value={selectedNode.data.conditionValue || ''} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ conditionValue: e.target.value })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"&gt;&lt;option value=""&gt;Select Folder&lt;/option&gt;{user.folders.map(f =&gt; &lt;option key={f.id} value={f.id}&gt;{f.name}&lt;/option&gt;)}&lt;/select&gt;}</span>
<span class="cstat-no" title="statement not covered" >                        {selectedNode.data.conditionField === 'clientId' &amp;&amp; &lt;select value={selectedNode.data.conditionValue || ''} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ conditionValue: e.target.value })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"&gt;&lt;option value=""&gt;Select Client&lt;/option&gt;{(user.clients || []).map(c =&gt; &lt;option key={c.id} value={c.id}&gt;{c.name}&lt;/option&gt;)}&lt;/select&gt;}</span>
<span class="cstat-no" title="statement not covered" >                        {selectedNode.data.conditionField === 'name' &amp;&amp; &lt;input type="text" value={selectedNode.data.conditionValue || ''} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ conditionValue: e.target.value })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/&gt;}</span>
<span class="cstat-no" title="statement not covered" >                        {selectedNode.data.conditionField === 'createdAt' &amp;&amp; &lt;input type="date" value={selectedNode.data.conditionValue ? (selectedNode.data.conditionValue as string).split('T')[0] : ''} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ conditionValue: e.target.value })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/&gt;}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/&gt;</span>
                )}
&nbsp;
                {selectedNode.type === <span class="branch-0 cbranch-no" title="branch not covered" >'approval' &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;&lt;label className="text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Approver Email&lt;/label&gt;&lt;input type="email" value={selectedNode.data.approverEmail || ''} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ approverEmail: e.target.value })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/&gt;&lt;/div&gt;</span>
                )}
&nbsp;
                {selectedNode.type === <span class="branch-0 cbranch-no" title="branch not covered" >'notification' &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;&lt;label className="text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Recipient Email&lt;/label&gt;&lt;input type="email" value={selectedNode.data.notificationRecipient || ''} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ notificationRecipient: e.target.value })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/&gt;&lt;/div&gt;</span>
                )}
&nbsp;
                {selectedNode.type === <span class="branch-0 cbranch-no" title="branch not covered" >'delay' &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;&lt;label className="text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Delay (days)&lt;/label&gt;&lt;input type="number" value={selectedNode.data.delayDays || 0} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ delayDays: Number(e.target.value) })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/&gt;&lt;/div&gt;</span>
                )}
&nbsp;
                {selectedNode.type === <span class="branch-0 cbranch-no" title="branch not covered" >'update-field' &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;&lt;label className="text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Field to Update&lt;/label&gt;&lt;select value={selectedNode.data.updateField || 'status'} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ updateField: e.target.value })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"&gt;&lt;option value="status"&gt;Status&lt;/option&gt;&lt;/select&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;&lt;label className="text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;New Value&lt;/label&gt;&lt;select value={selectedNode.data.updateValue || 'draft'} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ updateValue: e.target.value })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"&gt;{allStatuses.map(s =&gt; &lt;option key={s} value={s}&gt;{s}&lt;/option&gt;)}&lt;/select&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/&gt;</span>
                )}
&nbsp;
                {selectedNode.type === <span class="branch-0 cbranch-no" title="branch not covered" >'add-collaborator' &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;&lt;label className="text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Collaborator Email&lt;/label&gt;&lt;input type="email" value={selectedNode.data.collaboratorEmail || ''} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ collaboratorEmail: e.target.value })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;&lt;label className="text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Permission&lt;/label&gt;&lt;select value={selectedNode.data.collaboratorPermission || 'view'} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ collaboratorPermission: e.target.value as Permission })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"&gt;&lt;option value="view"&gt;View&lt;/option&gt;&lt;option value="edit"&gt;Edit&lt;/option&gt;&lt;/select&gt;&lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/&gt;</span>
                )}
&nbsp;
                {selectedNode.type === <span class="branch-0 cbranch-no" title="branch not covered" >'move-to-folder' &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;&lt;label className="text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Target Folder&lt;/label&gt;&lt;select value={selectedNode.data.targetFolderId || ''} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ targetFolderId: e.target.value })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"&gt;&lt;option value=""&gt;Select Folder&lt;/option&gt;{user.folders.map(f =&gt; &lt;option key={f.id} value={f.id}&gt;{f.name}&lt;/option&gt;)}&lt;/select&gt;&lt;/div&gt;</span>
                )}
&nbsp;
                {selectedNode.type === <span class="branch-0 cbranch-no" title="branch not covered" >'add-tag' &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;&lt;label className="text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Tag Name&lt;/label&gt;&lt;input type="text" value={selectedNode.data.tagName || ''} onChange={<span class="fstat-no" title="function not covered" >e =&gt; onUpdateNode({ tagName: e.target.value })} c</span>lassName="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/&gt;&lt;/div&gt;</span>
                )}
                
                {selectedNode.type !== <span class="branch-0 cbranch-no" title="branch not covered" >'trigger' &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div className="mt-6 pt-4 border-t border-zinc-200 dark:border-zinc-700"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;Button variant="destructive" size="sm" onClick={onDeleteNode} className="w-full"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;TrashIcon className="w-4 h-4 mr-2"/&gt;Delete Node</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                )}
            &lt;/div&gt;
        &lt;/aside&gt;
    );
};
&nbsp;
export default PropertiesPanel;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    