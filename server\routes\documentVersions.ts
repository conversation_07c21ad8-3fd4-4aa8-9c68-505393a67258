import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient } from '../supabaseClient';

const router = Router();

const insertSchema = z.object({ documentId: z.string().uuid(), content: z.string().min(1) });

router.get('/:documentId/versions', requireAuth, async (req: AuthRequest<{ documentId: string }>, res) => {
  const { documentId } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('document_versions').select('*').eq('document_id', documentId).order('saved_at', { ascending: true });
  if (error) {
    return res.status(500).json({ error: error.message });
  }
  
  // Transform database fields to match frontend interface
  const transformedVersions = data?.map(version => ({
    versionId: version.id,
    content: version.content,
    savedAt: version.saved_at
  })) || [];
  
  res.json({ versions: transformedVersions });
});

router.post('/versions', requireAuth, async (req: AuthRequest, res) => {
  const parsed = insertSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const { documentId, content } = parsed.data;
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('document_versions').insert({
    document_id: documentId,
    content,
    version_type: 'auto'
  }).select().single();
  
  if (error || !data) {
    return res.status(500).json({ error: error?.message || 'Unknown error' });
  }
  
  // Transform database fields to match frontend interface
  const transformedVersion = {
    versionId: data.id,
    content: data.content,
    savedAt: data.saved_at
  };
  
  res.status(201).json({ version: transformedVersion });
});

router.post('/manual', requireAuth, async (req: AuthRequest, res) => {
  const parsed = insertSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const { documentId, content } = parsed.data;
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('document_versions').insert({
    document_id: documentId,
    content,
    version_type: 'manual'
  }).select().single();
  
  if (error || !data) {
    return res.status(500).json({ error: error?.message || 'Unknown error' });
  }
  
  // Transform database fields to match frontend interface
  const transformedVersion = {
    versionId: data.id,
    content: data.content,
    savedAt: data.saved_at
  };
  
  res.status(201).json({ version: transformedVersion });
});

router.delete('/versions/:id', requireAuth, async (req: AuthRequest<{ id: string }>, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  
  // First check if the version exists
  const { data: existingVersion, error: checkError } = await supa
    .from('document_versions')
    .select('id')
    .eq('id', id)
    .single();
  
  if (checkError || !existingVersion) {
    return res.status(404).json({ error: 'Document version not found' });
  }
  
  const { error } = await supa.from('document_versions').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

router.put('/versions/:versionId/restore', requireAuth, async (req: AuthRequest<{ versionId: string }>, res) => {
  const { versionId } = req.params;
  const supa = getUserClient(getAccessToken(req));
  
  // Get the version to restore
  const { data: version, error: versionError } = await supa
    .from('document_versions')
    .select('document_id, content')
    .eq('id', versionId)
    .single();

  if (versionError || !version) {
    return res.status(404).json({ error: 'Document version not found' });
  }

  // Get the current document
  const { data: document, error: docError } = await supa
    .from('documents')
    .select('id, content')
    .eq('id', version.document_id)
    .single();

  if (docError || !document) {
    return res.status(404).json({ error: 'Document not found' });
  }

  // Create a new version with current content before restoring
  const { error: createVersionError } = await supa
    .from('document_versions')
    .insert({
      document_id: document.id,
      content: document.content
    });

  if (createVersionError) {
    return res.status(500).json({ error: 'Failed to create backup version' });
  }

  // Update the document with the restored content
  const { error: updateError } = await supa
    .from('documents')
    .update({
      content: version.content,
      updated_at: new Date().toISOString()
    })
    .eq('id', document.id);

  if (updateError) {
    return res.status(500).json({ error: 'Failed to restore document version' });
  }

  res.json({ 
    success: true, 
    document: {
      id: document.id,
      content: version.content,
      updated_at: new Date().toISOString()
    }
  });
});

export default router;
