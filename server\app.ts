import cors from 'cors';
import dotenv from 'dotenv';
import express, { type Application } from 'express';
import helmet from 'helmet';

import rateLimit from 'express-rate-limit';
import RedisStore, { type RedisReply } from 'rate-limit-redis';
import { initMonitoring } from './lib/monitoring';
import { getRedisClient, initRedis } from './lib/redis';
import { errorHandler } from './middleware/errorHandler';
import { requestLogger } from './middleware/logging';
import activityLogsRouter from './routes/activityLogs';
import adminProfilesRouter from './routes/adminProfiles';
import aiRouter from './routes/ai';
import approversRouter from './routes/approvers';
import clausesRouter from './routes/clauses';
import clientAssetsRouter from './routes/clientAssets';
import clientsRouter from './routes/clients';
import collaboratorsRouter from './routes/collaborators';
import commentsRouter from './routes/comments';
import connectionsRouter from './routes/connections';
import customTemplatesRouter from './routes/customTemplates';
import documentsRouter from './routes/documents';
import documentVersionsRouter from './routes/documentVersions';
import flowRunsRouter from './routes/flowRuns';
import flowsRouter from './routes/flows';
import foldersRouter from './routes/folders';
import integrationsRouter from './routes/integrations';
import keydatesRouter from './routes/keydates';
import notificationsRouter from './routes/notifications';
import obligationsRouter from './routes/obligations';
import pricingRouter from './routes/pricing';
import profilesRouter from './routes/profiles';
import quotaRouter from './routes/quota';
import refundsRouter from './routes/refunds';
import signaturesRouter from './routes/signatures';
import stripeCheckoutRouter from './routes/stripeCheckout';
import stripeConfigRouter from './routes/stripeConfig';
import teamsRouter from './routes/teams';
import templatesRouter from './routes/templates';
import workflowsRouter from './routes/workflows';

dotenv.config({ path: process.env.BACKEND_ENV_PATH || '.env' });

initMonitoring();

const clientOrigin = process.env.CLIENT_ORIGIN || 'http://localhost:5173';

export const app = express();

app.use(cors({ origin: clientOrigin, credentials: true }));
app.use(helmet({
  contentSecurityPolicy: {
    useDefaults: true,
    directives: {
      defaultSrc: ["'self'"],
      baseUri: ["'self'"],
      objectSrc: ["'none'"],
      imgSrc: ["'self'", 'data:', 'blob:', 'https:'],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      frameAncestors: ["'self'"],
      frameSrc: ["'self'", 'https:'],
      connectSrc: ["'self'", clientOrigin, 'https:'],
    },
  },
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
}));
app.use(express.json({ limit: '2mb' }));
app.use(requestLogger);

await initRedis();
configureRateLimiting(app);

app.get('/', (_req, res) => {
  res.json({ ok: true, service: 'LexiGen API', health: '/health', routes: ['/api/documents', '/api/clauses'] });
});
app.get('/health', (_req, res) => { res.json({ ok: true }); });

app.use('/api/documents', documentsRouter);
app.use('/api/clauses', clausesRouter);
app.use('/api/clients', clientsRouter);
app.use('/api/clients', clientAssetsRouter);
app.use('/api/folders', foldersRouter);
app.use('/api/documents', collaboratorsRouter);
app.use('/api', commentsRouter);
app.use('/api/notifications', notificationsRouter);
app.use('/api/signatures', signaturesRouter);
app.use('/api/key-dates', keydatesRouter);
app.use('/api/approvers', approversRouter);
app.use('/api/obligations', obligationsRouter);
app.use('/api/activity-logs', activityLogsRouter);
app.use('/api/custom-templates', customTemplatesRouter);
app.use('/api/profile', profilesRouter);
app.use('/api/admin/profiles', adminProfilesRouter);
app.use('/api/teams', teamsRouter);
app.use('/api/integrations', integrationsRouter);
app.use('/api/connections', connectionsRouter);
app.use('/api/flows', flowsRouter);
app.use('/api/flow-runs', flowRunsRouter);
app.use('/api/workflows', workflowsRouter);
app.use('/api/templates', templatesRouter);
app.use('/api/pricing-plans', pricingRouter);
app.use('/api/documents', documentVersionsRouter);
app.use('/api/stripe', stripeCheckoutRouter);
app.use('/api/stripe-config', stripeConfigRouter);
app.use('/api/refunds', refundsRouter);
app.use('/api/quota', quotaRouter);
app.use('/api/ai', aiRouter);
app.use(errorHandler);

function configureRateLimiting(targetApp: Application): void {
  const apiLimiter = buildRateLimiter(120, 'rl:api:');
  const writeLimiter = buildRateLimiter(60, 'rl:writes:');

  targetApp.use('/api/', apiLimiter);
  targetApp.use(['/api/documents', '/api/clients', '/api/comments', '/api/signatures'], writeLimiter);
}

function buildRateLimiter(max: number, prefix: string) {
  const store = createRedisStore(prefix);
  const options = {
    windowMs: 60 * 1000,
    max,
    standardHeaders: true,
    legacyHeaders: false,
  };

  return rateLimit(store ? { ...options, store } : options);
}

function createRedisStore(prefix: string): RedisStore | undefined {
  const redisClient = getRedisClient();
  if (!redisClient) {
    return undefined;
  }

  return new RedisStore({
    prefix,
    sendCommand: async (...args: string[]): Promise<RedisReply> =>
      redisClient.sendCommand(args) as Promise<RedisReply>,
  });
}

export default app;
