import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { requirePremium } from '../middleware/plan';
import { getUserClient } from '../supabaseClient';

const router = Router();

const insertSchema = z.object({
  documentId: z.string().uuid(),
  description: z.string().min(1),
  dueDate: z.string().min(1),
  ownerEmail: z.string().email().optional(),
  status: z.enum(['pending','completed','overdue']).default('pending'),
});
const updateSchema = z.object({
  description: z.string().min(1).optional(),
  dueDate: z.string().min(1).optional(),
  ownerEmail: z.string().email().optional(),
  status: z.enum(['pending','completed','overdue']).optional(),
});

router.get('/', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('obligations').select('*').order('due_date');
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ obligations: data });
});

router.post('/', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const parsed = insertSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa
    .from('obligations')
    .insert({
      document_id: parsed.data.documentId,
      description: parsed.data.description,
      due_date: parsed.data.dueDate,
      owner_email: parsed.data.ownerEmail,
      status: parsed.data.status,
    })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ obligation: data });
});

router.put('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parsed = updateSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{
    description: string;
    due_date: string;
    owner_email: string;
    status: string;
  }> = {
    description: parsed.data.description,
    due_date: parsed.data.dueDate,
    owner_email: parsed.data.ownerEmail,
    status: parsed.data.status,
  };
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('obligations').update(patch).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ obligation: data });
});

router.delete('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('obligations').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

export default router;
