

import React, { useState } from 'react';
import { supabase } from '../lib/supabaseClient';
import { GoogleIcon, LockIcon, UserIcon } from './Icons';

interface LoginProps {
  onSwitchToRegister: () => void;
  onSwitchToForgotPassword: () => void;
  handleLogin: (email: string, password: string) => void;
  handleOAuthLogin: () => void;
  authError: string | null;
}

const Login: React.FC<LoginProps> = ({ onSwitchToRegister, onSwitchToForgotPassword, handleLogin, handleOAuthLogin, authError }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleLogin(email, password);
  };

  const handleGoogleSignIn = async () => {
    handleOAuthLogin(); // Trigger splash screen
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: { redirectTo: window.location.origin },
      });
      if (error) {
        // Google sign-in failed - error handled by UI state
      }
    } catch {
      // Google sign-in exception - error handled by UI state
    }
  };

  return (
    <div className="w-full max-w-md">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-zinc-900 dark:text-white">Welcome Back</h2>
        <p className="text-zinc-500 dark:text-zinc-400 mt-2">Log in to access your documents.</p>
      </div>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="email-login" className="block text-sm font-medium text-zinc-700 dark:text-zinc-300">Email Address</label>
          <div className="mt-1 relative rounded-md shadow-sm">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <UserIcon className="h-5 w-5 text-zinc-400" />
            </div>
            <input
              type="email"
              id="email-login"
              name="email"
              autoComplete="email"
              required
              className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 focus:border-brand-500 focus:ring-brand-500 sm:text-sm p-3"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>
        </div>
        <div>
          <label htmlFor="password-login" className="block text-sm font-medium text-zinc-700 dark:text-zinc-300">Password</label>
          <div className="mt-1 relative rounded-md shadow-sm">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <LockIcon className="h-5 w-5 text-zinc-400" />
            </div>
            <input
              type="password"
              id="password-login"
              name="password"
              autoComplete="current-password"
              required
              className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 focus:border-brand-500 focus:ring-brand-500 sm:text-sm p-3"
              placeholder="••••••••"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
          </div>
        </div>
        {authError && <p className="text-sm text-red-600 text-center">{authError}</p>}
        <div className="flex items-center justify-end">
          <div className="text-sm">
            <button type="button" onClick={onSwitchToForgotPassword} className="font-medium text-brand-600 hover:text-brand-700">Forgot your password?</button>
          </div>
        </div>
        <div>
          <button type="submit" className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-brand-600 hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500">
            Log In
          </button>
        </div>
      </form>

      <div className="my-6">
        <div className="relative">
          <div className="absolute inset-0 flex items-center" aria-hidden="true">
            <div className="w-full border-t border-zinc-300 dark:border-zinc-700" />
          </div>
          <div className="relative flex justify-center text-sm">
            <span className="bg-white dark:bg-zinc-950 px-2 text-zinc-500 dark:text-zinc-400">OR</span>
          </div>
        </div>
      </div>

      <div>
        <button
          type="button"
          onClick={handleGoogleSignIn}
          className="w-full inline-flex justify-center items-center py-3 px-4 border border-zinc-300 dark:border-zinc-700 rounded-lg shadow-sm bg-white dark:bg-zinc-800 text-sm font-medium text-zinc-700 dark:text-zinc-300 hover:bg-zinc-50 dark:hover:bg-zinc-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500"
        >
          <GoogleIcon className="w-5 h-5 mr-3" />
          Sign in with Google
        </button>
      </div>

      <p className="mt-6 text-center text-sm text-zinc-600 dark:text-zinc-400">
        Don't have an account?{' '}
        <button onClick={onSwitchToRegister} className="font-medium text-brand-600 hover:text-brand-700 focus:outline-none">
          Sign up
        </button>
      </p>
    </div>
  );
};
export default Login;
