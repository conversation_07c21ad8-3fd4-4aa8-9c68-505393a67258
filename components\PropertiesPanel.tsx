

import React from 'react';
import { WorkflowNode, DocumentStatus, WorkflowTriggerType, User, Permission } from '../types';
import { Button } from './ui/Button';
import { TrashIcon } from './Icons';

interface PropertiesPanelProps {
  user: User;
  selectedNode: WorkflowNode | null;
  selectedEdgeId: string | null;
  onUpdateNode: (data: Partial<WorkflowNode>) => void;
  onDeleteNode: () => void;
  onDeleteEdge: () => void;
}

const LabelInput: React.FC<{ value: string, onChange: (val: string) => void }> = ({ value, onChange }) => (
    <div>
        <label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">Node Label</label>
        <input type="text" value={value} onChange={e => onChange(e.target.value)} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/>
    </div>
);

const allStatuses: DocumentStatus[] = ['draft', 'in-review', 'approved', 'out-for-signature', 'completed', 'archived'];
const valueOperators = ['>', '<', '=='];
const nameOperators = ['contains', 'starts with', 'ends with'];
const dateOperators = ['before', 'after'];
const stringOperators = ['is', 'is not'];


const PropertiesPanel: React.FC<PropertiesPanelProps> = ({ user, selectedNode, selectedEdgeId, onUpdateNode, onDeleteNode, onDeleteEdge }) => {
    if (!selectedNode && !selectedEdgeId) {
        return (
            <aside className="w-72 p-4 border-l border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-950">
                <h3 className="font-semibold text-zinc-800 dark:text-zinc-200">Properties</h3>
                <p className="text-sm text-zinc-500 dark:text-zinc-400 mt-4">Select a node or an edge to view its properties.</p>
            </aside>
        );
    }

    if (selectedEdgeId) {
        return (
             <aside className="w-72 p-4 border-l border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-950">
                <h3 className="font-semibold text-zinc-800 dark:text-zinc-200">Connection</h3>
                <div className="mt-4">
                    <Button variant="destructive" size="sm" onClick={onDeleteEdge} className="w-full">
                        <TrashIcon className="w-4 h-4 mr-2"/>Delete Connection
                    </Button>
                </div>
            </aside>
        )
    }

    if (!selectedNode) {return null;}

    const handleConditionFieldChange = (field: string) => {
        let op: string = 'is';
        if (field === 'value') {op = '>';}
        if (field === 'name') {op = 'contains';}
        if (field === 'createdAt') {op = 'after';}

        onUpdateNode({
            data: {
                ...selectedNode.data,
                conditionField: field as 'value' | 'status' | 'folderId' | 'name' | 'createdAt' | 'clientId',
                conditionOperator: op as '>' | '<' | '==' | 'is' | 'is not' | 'contains' | 'starts with' | 'ends with' | 'before' | 'after',
                conditionValue: null
            }
        });
    }
    
    const getConditionOperators = () => {
        switch (selectedNode?.data.conditionField) {
            case 'value': return valueOperators;
            case 'name': return nameOperators;
            case 'createdAt': return dateOperators;
            default: return stringOperators;
        }
    }

    return (
        <aside className="w-72 p-4 border-l border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-950 overflow-y-auto">
            <h3 className="font-semibold text-zinc-800 dark:text-zinc-200 capitalize">{selectedNode.type.replace('-', ' ')} Node</h3>
            <div className="mt-4 space-y-4">
                {selectedNode.type === 'trigger' ? (
                    <div>
                        <label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">Trigger Event</label>
                        <select
                            value={selectedNode.data.triggerType || ''}
                            onChange={e => {
                                const selectedOption = e.target.options[e.target.selectedIndex];
                                onUpdateNode({ 
                                    data: {
                                        ...selectedNode.data,
                                        triggerType: e.target.value as WorkflowTriggerType, 
                                        label: selectedOption.text 
                                    }
                                });
                            }}
                            className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"
                        >
                            <option value="document-created">On Document Creation</option>
                            <option value="approval-requested">On Approval Request</option>
                            <option value="status-changed">On Status Change</option>
                        </select>
                    </div>
                ) : (
                    <LabelInput value={selectedNode.data.label} onChange={val => onUpdateNode({ data: { ...selectedNode.data, label: val } })} />
                )}

                {selectedNode.type === 'condition' && (
                    <>
                    <div><label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">Field</label><select value={selectedNode.data.conditionField || 'value'} onChange={e => handleConditionFieldChange(e.target.value)} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"><option value="value">Value</option><option value="status">Status</option><option value="folderId">Folder</option><option value="clientId">Client</option><option value="name">Name</option><option value="createdAt">Creation Date</option></select></div>
                    <div><label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">Operator</label><select value={selectedNode.data.conditionOperator || '>'} onChange={e => onUpdateNode({ data: { ...selectedNode.data, conditionOperator: e.target.value as '>' | '<' | '==' | 'is' | 'is not' | 'contains' | 'starts with' | 'ends with' | 'before' | 'after' } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800">{getConditionOperators().map(op => <option key={op} value={op}>{op}</option>)}</select></div>
                    <div><label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">Value</label>
                        {selectedNode.data.conditionField === 'value' && <input type="number" value={selectedNode.data.conditionValue || 0} onChange={e => onUpdateNode({ data: { ...selectedNode.data, conditionValue: Number(e.target.value) } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/>}
                        {selectedNode.data.conditionField === 'status' && <select value={selectedNode.data.conditionValue || 'draft'} onChange={e => onUpdateNode({ data: { ...selectedNode.data, conditionValue: e.target.value } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800">{allStatuses.map(s => <option key={s} value={s}>{s}</option>)}</select>}
                        {selectedNode.data.conditionField === 'folderId' && <select value={selectedNode.data.conditionValue || ''} onChange={e => onUpdateNode({ data: { ...selectedNode.data, conditionValue: e.target.value } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"><option value="">Select Folder</option>{user.folders.map(f => <option key={f.id} value={f.id}>{f.name}</option>)}</select>}
                        {selectedNode.data.conditionField === 'clientId' && <select value={selectedNode.data.conditionValue || ''} onChange={e => onUpdateNode({ data: { ...selectedNode.data, conditionValue: e.target.value } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"><option value="">Select Client</option>{(user.clients || []).map(c => <option key={c.id} value={c.id}>{c.name}</option>)}</select>}
                        {selectedNode.data.conditionField === 'name' && <input type="text" value={selectedNode.data.conditionValue || ''} onChange={e => onUpdateNode({ data: { ...selectedNode.data, conditionValue: e.target.value } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/>}
                        {selectedNode.data.conditionField === 'createdAt' && <input type="date" value={selectedNode.data.conditionValue ? (selectedNode.data.conditionValue as string).split('T')[0] : ''} onChange={e => onUpdateNode({ data: { ...selectedNode.data, conditionValue: e.target.value } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/>}
                    </div>
                    </>
                )}

                {selectedNode.type === 'approval' && (
                    <div><label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">Approver Email</label><input type="email" value={selectedNode.data.approverEmail || ''} onChange={e => onUpdateNode({ data: { ...selectedNode.data, approverEmail: e.target.value } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/></div>
                )}

                {selectedNode.type === 'notification' && (
                    <div><label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">Recipient Email</label><input type="email" value={selectedNode.data.notificationRecipient || ''} onChange={e => onUpdateNode({ data: { ...selectedNode.data, notificationRecipient: e.target.value } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/></div>
                )}

                {selectedNode.type === 'delay' && (
                    <div><label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">Delay (days)</label><input type="number" value={selectedNode.data.delayDays || 0} onChange={e => onUpdateNode({ data: { ...selectedNode.data, delayDays: Number(e.target.value) } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/></div>
                )}

                {selectedNode.type === 'update-field' && (
                    <>
                    <div><label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">Field to Update</label><select value={selectedNode.data.updateField || 'status'} onChange={e => onUpdateNode({ data: { ...selectedNode.data, updateField: e.target.value as 'status' } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"><option value="status">Status</option></select></div>
                    <div><label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">New Value</label><select value={selectedNode.data.updateValue || 'draft'} onChange={e => onUpdateNode({ data: { ...selectedNode.data, updateValue: e.target.value as DocumentStatus } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800">{allStatuses.map(s => <option key={s} value={s}>{s}</option>)}</select></div>
                    </>
                )}

                {selectedNode.type === 'add-collaborator' && (
                    <>
                    <div><label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">Collaborator Email</label><input type="email" value={selectedNode.data.collaboratorEmail || ''} onChange={e => onUpdateNode({ data: { ...selectedNode.data, collaboratorEmail: e.target.value } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/></div>
                    <div><label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">Permission</label><select value={selectedNode.data.collaboratorPermission || 'view'} onChange={e => onUpdateNode({ data: { ...selectedNode.data, collaboratorPermission: e.target.value as Permission } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"><option value="view">View</option><option value="edit">Edit</option></select></div>
                    </>
                )}

                {selectedNode.type === 'move-to-folder' && (
                    <div><label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">Target Folder</label><select value={selectedNode.data.targetFolderId || ''} onChange={e => onUpdateNode({ data: { ...selectedNode.data, targetFolderId: e.target.value } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"><option value="">Select Folder</option>{user.folders.map(f => <option key={f.id} value={f.id}>{f.name}</option>)}</select></div>
                )}

                {selectedNode.type === 'add-tag' && (
                    <div><label className="text-sm font-medium text-zinc-700 dark:text-zinc-300">Tag Name</label><input type="text" value={selectedNode.data.tagName || ''} onChange={e => onUpdateNode({ data: { ...selectedNode.data, tagName: e.target.value } })} className="mt-1 w-full p-2 border border-zinc-300 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800"/></div>
                )}
                
                {selectedNode.type !== 'trigger' && (
                    <div className="mt-6 pt-4 border-t border-zinc-200 dark:border-zinc-700">
                        <Button variant="destructive" size="sm" onClick={onDeleteNode} className="w-full">
                            <TrashIcon className="w-4 h-4 mr-2"/>Delete Node
                        </Button>
                    </div>
                )}
            </div>
        </aside>
    );
};

export default PropertiesPanel;