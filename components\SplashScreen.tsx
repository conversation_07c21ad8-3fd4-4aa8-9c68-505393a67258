import React from 'react';
import { LegalIcon } from './Icons';

type SplashScreenProps = {
  message?: string;
};

const SplashScreen: React.FC<SplashScreenProps> = ({ message }) => {
  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-brand-50 to-white dark:from-zinc-950 dark:to-zinc-900 flex items-center justify-center p-6">
      <div className="w-full max-w-md">
        <div className="flex flex-col items-center text-center bg-white/70 dark:bg-zinc-900/60 backdrop-blur rounded-2xl shadow-xl border border-zinc-100 dark:border-zinc-800 p-10">
          <div className="h-16 w-16 rounded-2xl bg-brand-100 dark:bg-brand-900/40 flex items-center justify-center">
            <LegalIcon className="h-9 w-9 text-brand-600" />
          </div>
          <h1 className="mt-6 text-2xl font-bold tracking-tight text-zinc-900 dark:text-white">
            LexiGen
          </h1>
          <p className="mt-2 text-sm text-zinc-600 dark:text-zinc-300">
            {message || 'Preparing your workspace...'}
          </p>
          <div className="mt-6 h-10 w-10 rounded-full border-2 border-brand-200 dark:border-brand-800 border-t-brand-600 animate-spin" aria-label="Loading" />
        </div>
      </div>
    </div>
  );
};

export default SplashScreen;

