

import React from 'react';
import { WorkflowNodeData, User } from '../../types';
import { PlayIcon, GitBranchIcon, UserCheckIcon, MailIcon, TimerIcon, EditIcon, SignatureIcon, UserPlusIcon, FolderMoveIcon, TagsIcon } from '../Icons';
import { cn } from '../../lib/utils';

interface NodeProps {
  data: WorkflowNodeData;
  user: User; // Add user for context
  onStartConnection: (e: React.MouseEvent, handle?: string) => void;
}

const Handle: React.FC<{
    position: 'top' | 'bottom';
    type: 'source' | 'target';
    id?: string;
    onMouseDown?: (e: React.MouseEvent) => void;
    className?: string;
}> = ({ position, type, id, onMouseDown, className }) => (
    <div
        data-handle-id={id}
        data-handle-type={type}
        onMouseDown={onMouseDown}
        className={cn(
            "absolute w-3 h-3 bg-zinc-300 dark:bg-zinc-600 rounded-full border-2 border-white dark:border-zinc-900 hover:bg-brand-500 hover:border-brand-300",
            position === 'top' ? "-top-1.5" : "-bottom-1.5",
            className
        )}
    />
);


const NodeWrapper: React.FC<{ children: React.ReactNode, className?: string }> = ({ children, className }) => (
    <div className={cn("w-[220px] bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-700 rounded-lg shadow-md", className)}>
        {children}
    </div>
);

const NodeHeader: React.FC<{ icon: React.FC<{className?: string}>, title: string, iconClass?: string }> = ({ icon: Icon, title, iconClass }) => (
    <div className="p-2 border-b border-zinc-200 dark:border-zinc-700 flex items-center gap-2">
        <Icon className={cn("w-4 h-4", iconClass)} />
        <span className="text-sm font-semibold text-zinc-800 dark:text-zinc-200">{title}</span>
    </div>
);

export const TriggerNode: React.FC<Pick<NodeProps, 'data' | 'onStartConnection'>> = ({ data, onStartConnection }) => (
    <NodeWrapper className="border-t-4 border-t-green-500">
        <NodeHeader icon={PlayIcon} title="Trigger" iconClass="text-green-500"/>
        <div className="p-3 text-sm text-zinc-600 dark:text-zinc-300">{data.label}</div>
        <Handle position="bottom" type="source" onMouseDown={(e) => onStartConnection(e)} className="left-1/2 -translate-x-1/2" />
    </NodeWrapper>
);

export const ConditionNode: React.FC<NodeProps> = ({ data, onStartConnection, user }) => {
    
    const renderConditionText = () => {
        const field = data.conditionField || '...';
        const operator = data.conditionOperator || '...';
        let valueText = '...';

        if(data.conditionValue) {
            switch(field) {
                case 'value':
                    valueText = `$${Number(data.conditionValue).toLocaleString()}`;
                    break;
                case 'status':
                case 'name':
                    valueText = `'${data.conditionValue}'`;
                    break;
                case 'createdAt':
                    valueText = new Date(data.conditionValue as string).toLocaleDateString();
                    break;
                case 'folderId': {
                    const folder = user.folders.find(f => f.id === data.conditionValue);
                    valueText = folder ? `'${folder.name}'` : 'a folder';
                    break;
                }
                case 'clientId': {
                    const client = user.clients?.find(c => c.id === data.conditionValue);
                    valueText = client ? `'${client.name}'` : 'a client';
                    break;
                }
                default:
                    valueText = '...';
            }
        }
        
        return (
            <p>If Doc <span className="font-semibold capitalize">{field.replace('Id','')}</span> {operator} <span className="font-mono bg-zinc-100 dark:bg-zinc-800 p-1 rounded text-brand-600 dark:text-brand-400">{valueText}</span></p>
        );
    };

    return (
        <NodeWrapper className="border-t-4 border-t-amber-500">
            <Handle position="top" type="target" className="left-1/2 -translate-x-1/2" />
            <NodeHeader icon={GitBranchIcon} title="Condition" iconClass="text-amber-500"/>
            <div className="p-3 text-sm text-zinc-700 dark:text-zinc-300">
                {renderConditionText()}
            </div>
            <div className="flex justify-around items-center h-4">
                <span className="text-xs text-zinc-500 dark:text-zinc-400">True</span>
                <span className="text-xs text-zinc-500 dark:text-zinc-400">False</span>
            </div>
            <Handle position="bottom" type="source" id="true" onMouseDown={(e) => onStartConnection(e, 'true')} className="left-1/4" />
            <Handle position="bottom" type="source" id="false" onMouseDown={(e) => onStartConnection(e, 'false')} className="right-1/4" />
        </NodeWrapper>
    );
};

const BaseActionNode: React.FC<NodeProps & { icon: React.FC<{className?: string}>; title: string; borderColorClass: string; iconClass: string; children: React.ReactNode; }> =
({ data: _data, onStartConnection, icon, title, borderColorClass, iconClass, children }) => (
    <NodeWrapper className={cn("border-t-4", borderColorClass)}>
        <Handle position="top" type="target" className="left-1/2 -translate-x-1/2" />
        <NodeHeader icon={icon} title={title} iconClass={iconClass} />
        <div className="p-3 text-sm text-zinc-600 dark:text-zinc-300">
            {children}
        </div>
        <Handle position="bottom" type="source" onMouseDown={(e) => onStartConnection(e)} className="left-1/2 -translate-x-1/2" />
    </NodeWrapper>
);

export const ApprovalNode: React.FC<NodeProps> = (props) => (
    <BaseActionNode {...props} icon={UserCheckIcon} title="Approval" borderColorClass="border-t-sky-500" iconClass="text-sky-500">
        <p>Request approval from:</p>
        <p className="font-medium truncate text-zinc-800 dark:text-zinc-200">{props.data.approverEmail || 'Not set'}</p>
    </BaseActionNode>
);

export const NotificationNode: React.FC<NodeProps> = (props) => (
    <BaseActionNode {...props} icon={MailIcon} title="Notification" borderColorClass="border-t-teal-500" iconClass="text-teal-500">
        <p>Send email to:</p>
        <p className="font-medium truncate text-zinc-800 dark:text-zinc-200">{props.data.notificationRecipient || 'Not set'}</p>
    </BaseActionNode>
);

// FIX: Added missing DelayNode component for workflows.
export const DelayNode: React.FC<NodeProps> = (props) => (
    <BaseActionNode {...props} icon={TimerIcon} title="Delay" borderColorClass="border-t-orange-500" iconClass="text-orange-500">
        <p>Wait for</p>
        <p className="font-medium text-zinc-800 dark:text-zinc-200">{props.data.delayDays || 0} days</p>
    </BaseActionNode>
);

// FIX: Added missing UpdateFieldNode component for workflows.
export const UpdateFieldNode: React.FC<NodeProps> = (props) => (
    <BaseActionNode {...props} icon={EditIcon} title="Update Field" borderColorClass="border-t-purple-500" iconClass="text-purple-500">
        <p>Set <span className="font-semibold capitalize">{props.data.updateField || '...'}</span> to</p>
        <p className="font-medium text-zinc-800 dark:text-zinc-200">{props.data.updateValue || '...'}</p>
    </BaseActionNode>
);

// FIX: Added missing WaitForSignatureNode component for workflows.
export const WaitForSignatureNode: React.FC<NodeProps> = (props) => (
    <BaseActionNode {...props} icon={SignatureIcon} title="Wait for e-Signature" borderColorClass="border-t-indigo-500" iconClass="text-indigo-500">
        <p>Pauses workflow until document is fully signed.</p>
    </BaseActionNode>
);

export const AddCollaboratorNode: React.FC<NodeProps> = (props) => (
    <BaseActionNode {...props} icon={UserPlusIcon} title="Add Collaborator" borderColorClass="border-t-cyan-500" iconClass="text-cyan-500">
        <p>Add <span className="font-medium">{props.data.collaboratorEmail || '...'}</span></p>
        <p>with <span className="font-semibold capitalize">{props.data.collaboratorPermission || '...'}</span> access.</p>
    </BaseActionNode>
);

export const MoveToFolderNode: React.FC<NodeProps> = (props) => {
    const folder = props.user.folders.find(f => f.id === props.data.targetFolderId);
    return (
        <BaseActionNode {...props} icon={FolderMoveIcon} title="Move to Folder" borderColorClass="border-t-lime-500" iconClass="text-lime-500">
            <p>Move document to:</p>
            <p className="font-medium">{folder?.name || '...'}</p>
        </BaseActionNode>
    );
};

export const AddTagNode: React.FC<NodeProps> = (props) => (
    <BaseActionNode {...props} icon={TagsIcon} title="Add Tag" borderColorClass="border-t-pink-500" iconClass="text-pink-500">
        <p>Add tag:</p>
        <p className="font-medium">{props.data.tagName || '...'}</p>
    </BaseActionNode>
);