import { describe, it, expect, beforeAll, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

vi.mock('../server/supabaseClient', () => {
  const getUserClient = (_token: string) => ({
    auth: { getUser: async () => ({ data: { user: { id: 'test-user' } }, error: null }) },
    from: (_table: string) => ({
      select: (_cols: string) => ({ order: async () => ({ data: [], error: null }) }),
      insert: (row: Record<string, unknown>) => ({ select: () => ({ single: async () => ({ data: { id: 'cli-1', ...row }, error: null }) }) }),
      update: (patch: Record<string, unknown>) => ({ eq: (_col: string, id: string) => ({ select: () => ({ single: async () => ({ data: { id, ...patch }, error: null }) }) }) }),
      delete: () => ({ eq: async () => ({ error: null }) }),
    }),
  });
  const supabaseAdmin = {
    storage: { from: () => ({ list: async () => ({ data: [], error: null }), remove: async () => ({ data: null, error: null }) }) },
  };
  return { getUserClient, supabaseAdmin };
});

beforeAll(() => { process.env.TEST_BYPASS_AUTH = '1'; });

describe('Clients routes', () => {
  it('POST /api/clients creates client', async () => {
    const res = await request(app)
      .post('/api/clients')
      .send({ name: 'Acme', type: 'Company' })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(201);
    expect(res.body).toHaveProperty('client');
    expect(res.body.client).toHaveProperty('name', 'Acme');
  });

  it('PUT /api/clients/:id updates client', async () => {
    const res = await request(app)
      .put('/api/clients/cli-1')
      .send({ name: 'Acme Updated' })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(200);
    expect(res.body.client).toHaveProperty('name', 'Acme Updated');
  });

  it('DELETE /api/clients/:id removes client', async () => {
    const res = await request(app)
      .delete('/api/clients/cli-1');
    expect(res.status).toBe(204);
  });
});
