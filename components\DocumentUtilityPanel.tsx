import React, { useCallback, useEffect, useState } from 'react';
import { apiFetch } from '../lib/api';
import { cn } from '../lib/utils';
import { ActivityLog, DashboardView, Document as DocType, Obligation, User } from '../types';
import AnalysisPanel from './AnalysisPanel';
import ApprovalsPanel from './ApprovalsPanel';
import ClauseSuggestionsPanel from './ClauseSuggestionsPanel';
import CommentsPanel from './CommentsPanel';
import { ActivityIcon, ApprovalIcon, CheckCircleIcon, ClockIcon, CloseIcon, EditIcon, EyeIcon, LockSolidIcon, MessageSquarePlusIcon, RevertIcon, ShareIcon, ShieldCheckIcon, SignatureIcon, TeamIcon, UsersIcon, WandIcon } from './Icons';
import ObligationsPanel from './ObligationsPanel';
import SignaturePanel from './SignaturePanel';
import { Button } from './ui/Button';
import VersionHistoryPanel from './VersionHistoryPanel';

type UtilityTab = 'suggestions' | 'history' | 'comments' | 'analysis' | 'signatures' | 'activity' | 'approvals' | 'obligations';

interface DocumentUtilityPanelProps {
  document: DocType;
  isEditing: boolean;
  onInsertClause: (clauseContent: string) => void;
  onRevert: (documentId: string, versionId: string) => void;
  user: User;
  allUsers: User[];
  setView: (view: DashboardView) => void;
  documentContent: string;
  activeTool: UtilityTab | null;
  setActiveTool: (tab: UtilityTab | null) => void;
  onAddReply: (documentId: string, threadId: string, content: string) => void;
  onResolveThread: (documentId: string, threadId: string) => void;
  onDeleteComment: (documentId: string, threadId: string, commentId: string) => void;
  activityLogs: ActivityLog[];
  isOpen: boolean;
  onClose: () => void;
  onUpdateObligationStatus: (docId: string, obligationId: string, status: Obligation['status']) => void;
}

const tabConfig: { id: UtilityTab; icon: React.FC<{ className?: string }>; label: string; premium?: boolean; }[] = [
  { id: 'activity', icon: ActivityIcon, label: 'Activity' },
  { id: 'comments', icon: MessageSquarePlusIcon, label: 'Comments' },
  { id: 'history', icon: ClockIcon, label: 'Versions' },
  { id: 'signatures', icon: SignatureIcon, label: 'Signatures' },
  { id: 'approvals', icon: UsersIcon, label: 'Approvals', premium: true },
  { id: 'obligations', icon: CheckCircleIcon, label: 'Obligations', premium: true },
  { id: 'suggestions', icon: WandIcon, label: 'AI Suggestions', premium: true },
  { id: 'analysis', icon: ShieldCheckIcon, label: 'AI Analysis', premium: true },
];

// Enhanced ActivityPanel with loading states, real-time updates, and improved visual design
interface ActivityPanelProps {
  logs: ActivityLog[];
  allUsers: User[];
  documentId: string;
}

const ActivityPanel: React.FC<ActivityPanelProps> = ({ logs: initialLogs, allUsers, documentId }) => {
  const [logs, setLogs] = useState<ActivityLog[]>(initialLogs);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Function to fetch fresh activity logs
  const fetchActivityLogs = useCallback(async () => {
    if (!documentId) return;

    setIsLoading(true);
    setError(null);

    try {
      const response = await apiFetch<{ documents: Array<{ activityLogs: ActivityLog[] }> }>('/api/documents/batch', {
        method: 'POST',
        body: JSON.stringify({
          documentIds: [documentId],
          includeActivityLogs: true,
          includeComments: false,
          includeMetadata: false,
          includeVersions: false,
          includeCollaborators: false,
          includeSignatures: false
        })
      });

      if (response.documents && response.documents.length > 0) {
        const freshLogs = response.documents[0].activityLogs || [];
        // Only update state if logs have actually changed to prevent unnecessary re-renders
        setLogs(prevLogs => {
          if (JSON.stringify(prevLogs) !== JSON.stringify(freshLogs)) {
            return freshLogs;
          }
          return prevLogs;
        });
      }
    } catch (err) {
      console.error('Failed to fetch activity logs:', err);
      setError('Failed to load activity logs');
    } finally {
      setIsLoading(false);
    }
  }, [documentId]);

  // Update logs when initialLogs change (e.g., when document changes)
  useEffect(() => {
    setLogs(initialLogs);
  }, [initialLogs]);

  // Auto-refresh activity logs every 2 minutes for real-time updates (less aggressive)
  useEffect(() => {
    const interval = setInterval(fetchActivityLogs, 120000); // 2 minutes instead of 30 seconds
    return () => clearInterval(interval);
  }, [fetchActivityLogs]);

  // Helper function to get user info
  const getUser = useCallback((email: string) => {
    return allUsers.find(u => u.email === email);
  }, [allUsers]);

  // Helper function to get activity icon
  const getActivityIcon = (type: ActivityLog['type']) => {
    const iconClass = "w-4 h-4";
    switch (type) {
      case 'create':
        return <ActivityIcon className={iconClass} />;
      case 'edit':
        return <EditIcon className={iconClass} />;
      case 'share':
        return <ShareIcon className={iconClass} />;
      case 'comment':
        return <MessageSquarePlusIcon className={iconClass} />;
      case 'view':
        return <EyeIcon className={iconClass} />;
      case 'signature':
        return <SignatureIcon className={iconClass} />;
      case 'revert':
        return <RevertIcon className={iconClass} />;
      case 'team':
        return <TeamIcon className={iconClass} />;
      case 'approval':
        return <ApprovalIcon className={iconClass} />;
      default:
        return <ActivityIcon className={iconClass} />;
    }
  };

  // Helper function to get activity color
  const getActivityColor = (type: ActivityLog['type']) => {
    switch (type) {
      case 'create':
        return 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/20';
      case 'edit':
        return 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/20';
      case 'share':
        return 'text-purple-600 bg-purple-50 dark:text-purple-400 dark:bg-purple-900/20';
      case 'comment':
        return 'text-orange-600 bg-orange-50 dark:text-orange-400 dark:bg-orange-900/20';
      case 'view':
        return 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/20';
      case 'signature':
        return 'text-indigo-600 bg-indigo-50 dark:text-indigo-400 dark:bg-indigo-900/20';
      case 'revert':
        return 'text-red-600 bg-red-50 dark:text-red-400 dark:bg-red-900/20';
      case 'team':
        return 'text-teal-600 bg-teal-50 dark:text-teal-400 dark:bg-teal-900/20';
      case 'approval':
        return 'text-emerald-600 bg-emerald-50 dark:text-emerald-400 dark:bg-emerald-900/20';
      default:
        return 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/20';
    }
  };

  // Helper function to format timestamp
  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
    } else if (diffInMinutes < 1440) { // Less than 24 hours
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} hour${hours === 1 ? '' : 's'} ago`;
    } else if (diffInMinutes < 10080) { // Less than 7 days
      const days = Math.floor(diffInMinutes / 1440);
      return `${days} day${days === 1 ? '' : 's'} ago`;
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  if (error) {
    return (
      <div className="flex-1 flex flex-col items-center justify-center p-8 text-center">
        <div className="text-red-500 mb-4">
          <ActivityIcon className="w-12 h-12 mx-auto mb-2" />
          <p className="text-sm font-medium">Failed to load activity logs</p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{error}</p>
        </div>
        <Button
          onClick={fetchActivityLogs}
          className="text-xs px-3 py-1"
          disabled={isLoading}
        >
          {isLoading ? 'Retrying...' : 'Try Again'}
        </Button>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Header with refresh button */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">Activity Log</h3>
        <button
          onClick={fetchActivityLogs}
          disabled={isLoading}
          className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 disabled:opacity-50"
          title="Refresh activity logs"
        >
          <ActivityIcon className={cn("w-4 h-4", isLoading && "animate-spin")} />
        </button>
      </div>

      {/* Activity list */}
      <div className="flex-1 overflow-y-auto">
        {isLoading && logs.length === 0 ? (
          // Loading skeleton
          <div className="p-4 space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-start gap-3 animate-pulse">
                <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : logs.length > 0 ? (
          <div className="p-4 space-y-4">
            {logs.map((log, index) => {
              const user = getUser(log.userEmail);
              const isFirstOfDay = index === 0 ||
                new Date(log.timestamp).toDateString() !== new Date(logs[index - 1].timestamp).toDateString();

              return (
                <div key={log.id}>
                  {/* Date separator */}
                  {isFirstOfDay && (
                    <div className="flex items-center gap-3 mb-4">
                      <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700"></div>
                      <span className="text-xs text-gray-500 dark:text-gray-400 px-2">
                        {new Date(log.timestamp).toDateString() === new Date().toDateString()
                          ? 'Today'
                          : new Date(log.timestamp).toLocaleDateString('en-US', {
                            weekday: 'long',
                            month: 'short',
                            day: 'numeric'
                          })
                        }
                      </span>
                      <div className="flex-1 h-px bg-gray-200 dark:bg-gray-700"></div>
                    </div>
                  )}

                  {/* Activity entry */}
                  <div className="flex items-start gap-3 group hover:bg-gray-50 dark:hover:bg-gray-800/50 rounded-lg p-2 -m-2 transition-colors">
                    {/* User avatar */}
                    <div className="relative">
                      {user?.avatarUrl ? (
                        <img
                          src={user.avatarUrl}
                          alt={user.name || log.userEmail}
                          className="w-8 h-8 rounded-full"
                        />
                      ) : (
                        <div className="w-8 h-8 rounded-full bg-gray-300 dark:bg-gray-600 flex items-center justify-center">
                          <span className="text-xs font-medium text-gray-600 dark:text-gray-300">
                            {(user?.name || log.userEmail).charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}

                      {/* Activity type icon */}
                      <div className={cn(
                        "absolute -bottom-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center border-2 border-white dark:border-gray-800",
                        getActivityColor(log.type)
                      )}>
                        {getActivityIcon(log.type)}
                      </div>
                    </div>

                    {/* Activity content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between gap-2">
                        <p className="text-sm text-gray-900 dark:text-gray-100 leading-relaxed">
                          <span className="font-semibold">{user?.name || log.userEmail}</span>
                          <span className="ml-1">{log.details}</span>
                        </p>
                      </div>
                      <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {formatTimestamp(log.timestamp)}
                      </p>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          // Empty state
          <div className="flex-1 flex flex-col items-center justify-center p-8 text-center">
            <ActivityIcon className="w-12 h-12 text-gray-400 mb-4" />
            <p className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">No activity yet</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Activity will appear here as you and others interact with this document
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

const TooltipIconButton: React.FC<{
  title: string;
  isActive: boolean;
  isDisabled: boolean;
  onClick: () => void;
  children: React.ReactNode;
}> = ({ title, isActive, isDisabled, onClick, children }) => {
  return (
    <div className="relative group flex justify-center">
      <button
        onClick={onClick}
        disabled={isDisabled}
        className={cn(
          'w-12 h-12 rounded-lg flex items-center justify-center relative transition-colors',
          isActive
            ? 'bg-brand-50 text-brand-600 dark:bg-zinc-700 dark:text-brand-400'
            : 'text-zinc-500 hover:bg-zinc-100 hover:text-zinc-700 dark:hover:bg-zinc-800 dark:text-zinc-400 dark:hover:text-zinc-100',
          isDisabled && 'text-zinc-400 dark:text-zinc-600 cursor-not-allowed hover:bg-transparent dark:hover:bg-transparent'
        )}
        aria-label={title}
      >
        {children}
        {isActive && <div className="absolute left-0 top-1/4 h-1/2 w-1 bg-brand-600 dark:bg-brand-400 rounded-r-full"></div>}
        {isDisabled && <LockSolidIcon className="w-3 h-3 absolute bottom-1 right-1 text-amber-500" />}
      </button>
      <div className="absolute right-full top-1/2 -translate-y-1/2 mr-4 w-max px-2 py-1 bg-zinc-800 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-20">
        {isDisabled ? 'Upgrade to Premium' : title}
      </div>
    </div>
  );
};

const DocumentUtilityPanel: React.FC<DocumentUtilityPanelProps> = ({
  document,
  onInsertClause,
  onRevert,
  user,
  allUsers,
  setView,
  documentContent,
  activeTool,
  setActiveTool,
  onAddReply,
  onResolveThread,
  onDeleteComment,
  activityLogs,
  isOpen,
  onClose,
  onUpdateObligationStatus,
}) => {
  const isPremium = user.planName === 'Premium' || user.planName === 'Enterprise';

  const renderTabContent = (tool: UtilityTab) => {
    switch (tool) {
      case 'activity':
        return <ActivityPanel logs={activityLogs} allUsers={allUsers} documentId={document.id} />;
      case 'suggestions':
        return <ClauseSuggestionsPanel documentContent={documentContent} onInsertClause={onInsertClause} user={user} setView={setView} />;
      case 'history':
        return <VersionHistoryPanel document={document} onRevert={onRevert} />;
      case 'comments':
        return <CommentsPanel document={document} currentUser={user} allUsers={allUsers} onAddReply={onAddReply} onResolveThread={onResolveThread} onDeleteComment={onDeleteComment} />;
      case 'analysis':
        return <AnalysisPanel documentContent={documentContent} />;
      case 'signatures':
        return <SignaturePanel document={document} allUsers={allUsers} />;
      case 'approvals':
        return <ApprovalsPanel document={document} allUsers={allUsers} />;
      case 'obligations':
        return <ObligationsPanel document={document} onUpdateStatus={onUpdateObligationStatus} />;
      default:
        return null;
    }
  };

  const activeTabConfig = tabConfig.find(t => t.id === activeTool);

  return (
    <>
      {isOpen && <div onClick={onClose} className="fixed inset-0 bg-black/50 z-20 md:hidden" />}
      <div className={cn(
        "w-[400px] max-w-[90vw] flex-shrink-0 border-l border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900 flex h-full",
        "fixed md:relative inset-y-0 right-0 z-30 transition-transform transform",
        isOpen ? "translate-x-0" : "translate-x-full",
        "md:translate-x-0 md:w-[320px] xl:w-[400px]"
      )}>
        <div className="flex-1 flex flex-col bg-white dark:bg-zinc-950 overflow-hidden">
          {activeTool ? (
            <div className="flex-1 flex flex-col h-full">
              <header className="p-4 border-b border-zinc-200 dark:border-zinc-800 flex items-center justify-between flex-shrink-0">
                <h3 className="font-semibold text-zinc-800 dark:text-zinc-200 flex items-center gap-2">
                  {activeTabConfig && <activeTabConfig.icon className="w-5 h-5 text-zinc-500" />}
                  {activeTabConfig?.label}
                </h3>
                <Button variant="ghost" size="icon" onClick={() => setActiveTool(null)} className="h-8 w-8">
                  <CloseIcon className="w-5 h-5" />
                </Button>
              </header>
              {renderTabContent(activeTool)}
            </div>
          ) : (
            <div className="flex-1 flex flex-col items-center justify-center text-center p-6">
              <div className="w-16 h-16 bg-zinc-100 dark:bg-zinc-800 rounded-full flex items-center justify-center mb-4">
                <WandIcon className="w-8 h-8 text-zinc-400 dark:text-zinc-600" />
              </div>
              <h3 className="font-semibold text-zinc-800 dark:text-zinc-200">Document Tools</h3>
              <p className="text-sm text-zinc-500 dark:text-zinc-400 mt-1">Select a tool from the sidebar to get started.</p>
            </div>
          )}
        </div>

        <div className="w-16 flex-shrink-0 border-l border-zinc-200 dark:border-zinc-800 flex flex-col items-center py-2 space-y-1">
          {tabConfig.map(tab => {
            const isDisabled = !!tab.premium && !isPremium;
            return (
              <TooltipIconButton
                key={tab.id}
                title={tab.label}
                isActive={activeTool === tab.id}
                isDisabled={isDisabled}
                onClick={() => {
                  if (isDisabled) { return; }
                  setActiveTool(activeTool === tab.id ? null : tab.id);
                }}
              >
                <tab.icon className="w-6 h-6" />
              </TooltipIconButton>
            );
          })}
        </div>
      </div>
    </>
  );
};

export default DocumentUtilityPanel;
