// @vitest-environment jsdom
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import CookieConsent from '../components/CookieConsent';
import React from 'react';

describe('CookieConsent', () => {
  it('renders CookieConsent component', () => {
    render(<CookieConsent />);
    // There are multiple cookie-related texts, so check for heading
    expect(screen.getByRole('heading', { name: /Cookie Preferences/i })).toBeInTheDocument();
  });
});
