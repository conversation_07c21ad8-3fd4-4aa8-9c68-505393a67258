# Repository Guidelines

## Project Structure & Module Organization
- Frontend: `App.tsx`, `components/`, `lib/` (API + Supabase helpers), `services/` (AI/Gemini helpers), `index.tsx`, `index.html`.
- Backend API: `server/` (`index.ts` entry, `routes/`, `middleware/`, `supabaseClient.ts`).
- Config: `.env*`, `tsconfig.json`, `vite.config.ts`, `constants.ts`, `types.ts`.
- Data/Infra: `supabase/` (CLI config, migrations, schema).

## Build, Test, and Development Commands
- `npm run dev` — runs client (Vite) and server (Express) together.
- `npm run dev:client` — starts Vite dev server.
- `npm run dev:server` — starts Express with `tsx` watcher.
- `npm run build` — builds the frontend for production.
- `npm run preview` — serves the built frontend locally.
- `npm run db:init` / `db:link` / `db:push` — Supabase project setup and migration push.

## Coding Style & Naming Conventions
- Language: TypeScript (frontend and backend). Indent 2 spaces; prefer single quotes; end lines with semicolons.
- React: Components in `components/` use PascalCase filenames and exports (e.g., `DashboardLayout.tsx`).
- Modules: Use named exports; colocate types in `types.ts` when shared; keep API calls in `lib/`.
- Imports: Use relative paths within this repo; avoid deep nesting by extracting helpers.

## Testing Guidelines
- Current status: no automated tests configured. When adding tests, use Vitest + React Testing Library for UI and supertest for Express.
- Naming: `*.test.ts` / `*.test.tsx` next to the code or under `__tests__/`.
- Targets: unit-test pure utils; smoke-test critical routes under `server/routes/`.

## Commit & Pull Request Guidelines
- Commits: follow Conventional Commits (e.g., `feat:`, `fix:`, `chore:`). History shows `feat:` usage; keep scope clear and messages concise.
- PRs must include: summary, rationale, before/after notes; link related issues; screenshots or cURL examples for API/UI changes; checklist for env or migration steps.
- Keep PRs focused; avoid unrelated refactors.

## Security & Configuration Tips
- Secrets: never commit real credentials. Use `.env.local` (git-ignored) and keep `.env.example` in sync.
- CORS: set `CLIENT_ORIGIN` to the exact client URL (no `*`). Server enables `credentials`; verify cookies/headers as needed.
- API keys: keep AI and service keys server-side only. Prefer proxying AI calls via `server/` instead of calling from the client.
- Supabase: client uses anon key only; service role key is server-only. Enable RLS and write explicit policies for all tables.
- Payloads & logging: keep `express.json({ limit: '2mb' })`; avoid logging secrets; return generic errors in production.
- Production: set `NODE_ENV=production`; if behind a proxy, set `app.set('trust proxy', 1)` and enforce HTTPS at the edge.
- `TEST_BYPASS_AUTH` is for local testing only. Never enable it in staging or production because it disables authentication.

### Configuration Reference
- Server env: `PORT`, `CLIENT_ORIGIN`, `BACKEND_ENV_PATH`, `SUPABASE_URL`, `SUPABASE_SERVICE_ROLE_KEY`, `SUPABASE_ANON_KEY`, `GEMINI_API_KEY`.
- Client env (Vite): must be prefixed with `VITE_`, e.g., `VITE_SUPABASE_URL`, `VITE_SUPABASE_ANON_KEY`. Do not expose sensitive keys.
- Supabase CLI: configure in `supabase/config.toml`; use `npm run db:link` and `npm run db:push` to manage schema.

## Agent-Specific Instructions
- Limit scope to requested changes; match existing patterns. Do not add dependencies without need. Update docs when altering API routes or env vars.
 - Admin redirect rule: When a user logs in and their profile indicates `is_admin = true`, the app must redirect them to the admin portal (set `view` to `admin` and `window.location.hash = '#/admin'`). Ensure the `User` object sets `isSuperAdmin = true` based on `profile.is_admin` so hash-based navigation continues to work. Do not remove or regress this behavior in future edits.
