import React from 'react';
import { Document as DocType, User } from '../types';
import { CheckCircleIcon, ClockIcon } from './Icons';
import { cn } from '../lib/utils';

interface SignaturePanelProps {
  document: DocType;
  allUsers: User[];
}

const SignaturePanel: React.FC<SignaturePanelProps> = ({ document, allUsers }) => {
    
    const getSignerInfo = (email: string) => {
        return allUsers.find(u => u.email === email) || { name: email, avatarUrl: '' };
    }

    if (document.status === 'draft' || document.signatures.length === 0) {
        return (
            <div className="flex-1 flex items-center justify-center p-6 text-center">
                <p className="text-sm text-slate-500">
                    No signature requests have been sent for this document.
                </p>
            </div>
        );
    }
    
    return (
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
            <h4 className="font-semibold text-slate-800 px-2">Signature Status</h4>
            {document.signatures.map(signer => {
                const signerInfo = getSignerInfo(signer.email);
                const isSigned = signer.status === 'signed';
                return (
                    <div key={signer.id} className="flex items-center gap-3 p-2 rounded-lg">
                        <img src={signerInfo.avatarUrl} alt={signerInfo.name} className="w-9 h-9 rounded-full"/>
                        <div className="flex-1">
                            <p className="text-sm font-medium text-slate-800">{signerInfo.name}</p>
                            <p className="text-xs text-slate-500">{signer.role}</p>
                        </div>
                        <div className={cn(
                            "flex items-center text-xs font-medium px-2 py-1 rounded-full",
                            isSigned ? "bg-green-100 text-green-800" : "bg-slate-100 text-slate-600"
                        )}>
                            {isSigned ? <CheckCircleIcon className="w-4 h-4 mr-1"/> : <ClockIcon className="w-4 h-4 mr-1"/>}
                            {isSigned ? 'Signed' : 'Pending'}
                        </div>
                    </div>
                );
            })}
             {document.status === 'completed' && (
                <div className="p-3 text-center bg-green-50 border border-green-200 rounded-lg">
                    <p className="text-sm font-semibold text-green-800">Document Completed</p>
                    <p className="text-xs text-green-600">All parties have signed.</p>
                </div>
             )}
        </div>
    );
};

export default SignaturePanel;
