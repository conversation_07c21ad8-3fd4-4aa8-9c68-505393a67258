// @vitest-environment jsdom
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import HelpCenterPage from '../components/HelpCenterPage';
import React from 'react';

describe('HelpCenterPage', () => {
  it('renders HelpCenterPage component', () => {
    render(<HelpCenterPage />);
    // The main heading is "How can we help?"
    expect(screen.getByRole('heading', { name: /How can we help/i })).toBeInTheDocument();
  });
});
