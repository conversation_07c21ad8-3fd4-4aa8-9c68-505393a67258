// @vitest-environment jsdom
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import ClientsPage from '../components/ClientsPage';

describe('ClientsPage', () => {
  it('renders ClientsPage component', () => {
    const user = { id: 'u1', name: 'Test User', email: '<EMAIL>', clients: [
      { id: 'c1', name: 'Client 1', email: '<EMAIL>', createdAt: '', updatedAt: '' }
    ], documents: [], folders: [] };
    render(
      <ClientsPage
        user={user as any}
        onCreateClient={() => {}}
        onUpdateClient={() => {}}
        onDeleteClient={() => {}}
      />
    );
    expect(screen.getByText('Client 1')).toBeInTheDocument();
  });
});
