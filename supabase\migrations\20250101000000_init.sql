-- Initial schema for LexiGen
-- Mirrors supabase/schema.sql so CLI-managed migrations can be pushed

create extension if not exists pgcrypto;

-- Documents table
create table if not exists public.documents (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  name text not null,
  content text not null default '',
  folder_id uuid null,
  status text not null default 'draft',
  created_at timestamptz not null default now(),
  updated_at timestamptz not null default now()
);

create index if not exists documents_user_id_idx on public.documents(user_id);
create index if not exists documents_updated_at_idx on public.documents(updated_at desc);

alter table public.documents enable row level security;

do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'documents' and policyname = 'docs_select_own'
  ) then
    drop policy "docs_select_own" on public.documents;
  end if;
end $$;
create policy "docs_select_own" on public.documents for select using (
  user_id = auth.uid()
);

do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'documents' and policyname = 'docs_insert_own'
  ) then
    drop policy "docs_insert_own" on public.documents;
  end if;
end $$;
create policy "docs_insert_own" on public.documents for insert with check (
  user_id = auth.uid()
);

do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'documents' and policyname = 'docs_update_own'
  ) then
    drop policy "docs_update_own" on public.documents;
  end if;
end $$;
create policy "docs_update_own" on public.documents for update using (
  user_id = auth.uid()
);

do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'documents' and policyname = 'docs_delete_own'
  ) then
    drop policy "docs_delete_own" on public.documents;
  end if;
end $$;
create policy "docs_delete_own" on public.documents for delete using (
  user_id = auth.uid()
);

-- Document versions
create table if not exists public.document_versions (
  id uuid primary key default gen_random_uuid(),
  document_id uuid not null references public.documents(id) on delete cascade,
  content text not null,
  saved_at timestamptz not null default now()
);

create index if not exists document_versions_doc_id_idx on public.document_versions(document_id);

alter table public.document_versions enable row level security;

do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'document_versions' and policyname = 'docvers_select_own'
  ) then
    drop policy "docvers_select_own" on public.document_versions;
  end if;
end $$;
create policy "docvers_select_own" on public.document_versions for select using (
  exists(
    select 1 from public.documents d
    where d.id = document_versions.document_id
      and d.user_id = auth.uid()
  )
);

do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'document_versions' and policyname = 'docvers_insert_own'
  ) then
    drop policy "docvers_insert_own" on public.document_versions;
  end if;
end $$;
create policy "docvers_insert_own" on public.document_versions for insert with check (
  exists(
    select 1 from public.documents d
    where d.id = document_versions.document_id
      and d.user_id = auth.uid()
  )
);

-- Clauses table
create table if not exists public.clauses (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  title text not null,
  content text not null,
  tags text[] not null default '{}',
  created_at timestamptz not null default now()
);

create index if not exists clauses_user_id_idx on public.clauses(user_id);

alter table public.clauses enable row level security;

do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'clauses' and policyname = 'clauses_select_own'
  ) then
    drop policy "clauses_select_own" on public.clauses;
  end if;
end $$;
create policy "clauses_select_own" on public.clauses for select using (
  user_id = auth.uid()
);

do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'clauses' and policyname = 'clauses_mutate_own'
  ) then
    drop policy "clauses_mutate_own" on public.clauses;
  end if;
end $$;
create policy "clauses_mutate_own" on public.clauses for all using (
  user_id = auth.uid()
) with check (
  user_id = auth.uid()
);

-- Folders table
create table if not exists public.folders (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  name text not null,
  created_at timestamptz not null default now()
);

create index if not exists folders_user_id_idx on public.folders(user_id);

alter table public.folders enable row level security;

do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'folders' and policyname = 'folders_select_own'
  ) then
    drop policy "folders_select_own" on public.folders;
  end if;
end $$;
create policy "folders_select_own" on public.folders for select using (
  user_id = auth.uid()
);

do $$ begin
  if exists (
    select 1 from pg_policies
    where schemaname = 'public' and tablename = 'folders' and policyname = 'folders_mutate_own'
  ) then
    drop policy "folders_mutate_own" on public.folders;
  end if;
end $$;
create policy "folders_mutate_own" on public.folders for all using (
  user_id = auth.uid()
 ) with check (
  user_id = auth.uid()
 );
