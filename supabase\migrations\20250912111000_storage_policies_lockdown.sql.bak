-- Lock down storage policies to user-scoped prefixes
alter table if exists storage.objects enable row level security;

do $$ begin
  if exists (select 1 from pg_policies where schemaname = 'storage' and tablename = 'objects' and policyname = 'client_assets_read') then
    drop policy "client_assets_read" on storage.objects;
  end if;
  if exists (select 1 from pg_policies where schemaname = 'storage' and tablename = 'objects' and policyname = 'client_assets_insert') then
    drop policy "client_assets_insert" on storage.objects;
  end if;
  if exists (select 1 from pg_policies where schemaname = 'storage' and tablename = 'objects' and policyname = 'client_assets_update') then
    drop policy "client_assets_update" on storage.objects;
  end if;
  if exists (select 1 from pg_policies where schemaname = 'storage' and tablename = 'objects' and policyname = 'client_assets_delete') then
    drop policy "client_assets_delete" on storage.objects;
  end if;
end $$;

create policy "client_assets_read" on storage.objects for select using (
  bucket_id = 'contractgini' and split_part(name, '/', 1) = auth.uid()::text
);
create policy "client_assets_insert" on storage.objects for insert with check (
  bucket_id = 'contractgini' and split_part(name, '/', 1) = auth.uid()::text
);
create policy "client_assets_update" on storage.objects for update using (
  bucket_id = 'contractgini' and split_part(name, '/', 1) = auth.uid()::text
);
create policy "client_assets_delete" on storage.objects for delete using (
  bucket_id = 'contractgini' and split_part(name, '/', 1) = auth.uid()::text
);

-- Reload PostgREST schema
select pg_notify('pgrst', 'reload schema');