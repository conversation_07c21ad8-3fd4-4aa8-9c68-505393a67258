// FIX: Correctly import useState and useEffect from React to make them available in the component.
import React, { useState, useEffect } from 'react';
import { SparklesIcon } from './Icons';
import DashboardScreenshot from './screenshots/DashboardScreenshot';
import DocumentsScreenshot from './screenshots/DocumentsScreenshot';
import LifecycleScreenshot from './screenshots/LifecycleScreenshot';

const UI_SCREENS = [
  { name: 'Dashboard', component: <DashboardScreenshot /> },
  { name: 'Documents', component: <DocumentsScreenshot /> },
  { name: 'Lifecycle', component: <LifecycleScreenshot /> },
];

const AnimatedUiPreview = () => {
  const [activeIndex, setActiveIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveIndex((prevIndex) => (prevIndex + 1) % UI_SCREENS.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);
  
  const activeScreen = UI_SCREENS[activeIndex];

  return (
    <div className="relative mt-20 w-full max-w-4xl animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
      <div className="absolute -inset-2 bg-gradient-to-r from-brand-500 to-brand-700 rounded-2xl blur-lg opacity-60 animate-float"></div>
      <div className="relative bg-zinc-800/60 backdrop-blur-md rounded-2xl shadow-2xl border border-zinc-700 overflow-hidden">
        <div className="h-10 bg-zinc-900/50 flex items-center px-4 gap-2">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <div className="flex-1 text-center">
            <div className="bg-zinc-700 text-sm text-zinc-300 rounded-md px-4 py-1 max-w-xs mx-auto truncate">
              lexigen.ai/{activeScreen.name.toLowerCase()}
            </div>
          </div>
        </div>
        <div className="relative h-72 bg-zinc-100 dark:bg-zinc-900 overflow-hidden">
          {UI_SCREENS.map((screen, index) => (
             <div
              key={screen.name}
              className={`absolute inset-0 w-full h-full transition-opacity duration-1000 ${activeIndex === index ? 'opacity-100' : 'opacity-0'}`}
            >
              {screen.component}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};


const Hero: React.FC = () => {
  return (
    <div className="relative w-full pt-32 pb-20 lg:pt-40 lg:pb-28 overflow-hidden bg-zinc-950">
      <div className="absolute top-0 left-0 w-full h-full bg-grid-zinc-800/[0.2] [mask-image:linear-gradient(to_bottom,white_5%,transparent_50%)]"></div>
      
      {/* Background Glows */}
      <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
        <div className="absolute -translate-x-[50%] -translate-y-[50%] w-[800px] h-[800px] bg-brand-900/30 rounded-full blur-3xl"></div>
        <div className="absolute translate-x-[50%] translate-y-[20%] w-[600px] h-[600px] bg-brand-600/20 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 flex flex-col items-center text-center">
        
        <h1 className="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl lg:text-7xl animate-fade-in-up" style={{ animationDelay: '0.1s' }}>
          <span>Draft, Analyze, and Manage</span>
          <span className="block bg-gradient-to-r from-brand-400 to-brand-500 text-transparent bg-clip-text mt-2">Legal Documents with AI</span>
        </h1>

        <p className="mt-6 max-w-3xl text-lg text-zinc-300/90 md:text-xl animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
          Stop paying exorbitant legal fees. LexiGen is the all-in-one platform to generate, review, and track contracts in minutes, not days.
        </p>

        <div className="mt-8 flex flex-wrap justify-center gap-4 animate-fade-in-up" style={{ animationDelay: '0.3s' }}>
            <a href="#solutions" className="inline-flex items-center justify-center px-8 py-3.5 text-base font-semibold text-white bg-brand-600 rounded-lg shadow-lg hover:bg-brand-700">
                See Solutions
            </a>
            <a href="#pricing" className="inline-flex items-center justify-center px-8 py-3.5 text-base font-semibold text-white bg-zinc-800/50 rounded-lg shadow-lg hover:bg-zinc-800/80 backdrop-blur-sm border border-zinc-700">
                <SparklesIcon className="w-5 h-5 mr-2 text-amber-300"/>
                View Pricing
            </a>
        </div>

        <AnimatedUiPreview />

      </div>
      <style>{`
        .bg-grid-zinc-800\\[\\[0\\.2\\]] {
          background-image: linear-gradient(to right, rgb(63 63 70 / 0.2) 1px, transparent 1px), linear-gradient(to bottom, rgb(63 63 70 / 0.2) 1px, transparent 1px);
        }
      `}</style>
    </div>
  );
};

export default Hero;