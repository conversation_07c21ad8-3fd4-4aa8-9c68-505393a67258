

import React, { useState } from 'react';
import { Document as DocType, DocumentVersion } from '../types';
import { Button } from './ui/Button';
// FIX: Replaced missing RestoreIcon with existing UndoIcon.
import { UndoIcon, AlertTriangleIcon, RepeatIcon } from './Icons';
import VersionComparisonModal from './VersionComparisonModal';

interface VersionHistoryPanelProps {
  document: DocType;
  onRevert: (documentId: string, versionId: string) => void;
}

const VersionHistoryPanel: React.FC<VersionHistoryPanelProps> = ({ document, onRevert }) => {
  const [versionToRevert, setVersionToRevert] = useState<DocumentVersion | null>(null);
  const [versionToCompare, setVersionToCompare] = useState<DocumentVersion | null>(null);

  const handleRevertConfirm = () => {
    if (versionToRevert) {
      onRevert(document.id, versionToRevert.versionId);
      setVersionToRevert(null);
    }
  };
  
  const sortedVersions = [...document.versions].sort((a, b) => b.version - a.version);

  return (
    <>
      <div className="flex-1 overflow-y-auto p-2">
            {sortedVersions.map((version) => (
                <div key={version.versionId} className="p-3 rounded-lg hover:bg-zinc-50 dark:hover:bg-zinc-800/50">
                    <div className="flex justify-between items-center">
                         <div>
                            <p className="text-sm font-medium text-zinc-800 dark:text-zinc-200">
                                {`Version ${version.version}`}
                            </p>
                            <p className="text-xs text-zinc-500 dark:text-zinc-400">
                                Saved on {new Date(version.savedAt).toLocaleString()}
                            </p>
                         </div>
                         <div className="flex items-center gap-1">
                                <Button variant="outline" size="sm" onClick={() => setVersionToCompare(version)}>
                                    <RepeatIcon className="w-4 h-4 mr-1.5" />
                                    Compare
                                </Button>
                                <Button variant="outline" size="sm" onClick={() => setVersionToRevert(version)}>
                                    <UndoIcon className="w-4 h-4 mr-1.5" />
                                    Restore
                                </Button>
                         </div>
                    </div>
                </div>
            ))}
      </div>

      {versionToRevert && (
        <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
          <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-md p-6">
            <div className="flex items-start gap-4">
              <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-amber-100 dark:bg-amber-900/30 sm:mx-0 sm:h-10 sm:w-10">
                <AlertTriangleIcon className="h-6 w-6 text-amber-600 dark:text-amber-400" />
              </div>
              <div className="mt-0 text-center sm:text-left">
                <h3 className="text-lg leading-6 font-medium text-zinc-900 dark:text-zinc-50">Restore Version</h3>
                <p className="mt-2 text-sm text-zinc-500 dark:text-zinc-400">
                  Are you sure you want to restore this version? The current content will be replaced with the content from{' '}
                  <span className="font-semibold">{new Date(versionToRevert.savedAt).toLocaleString()}</span>.
                  This action will create a new version and cannot be undone.
                </p>
              </div>
            </div>
            <div className="mt-5 sm:mt-4 flex flex-col-reverse sm:flex-row-reverse gap-3">
              <Button onClick={handleRevertConfirm} className="bg-amber-500 hover:bg-amber-600 text-white">Restore</Button>
              <Button variant="outline" onClick={() => setVersionToRevert(null)}>Cancel</Button>
            </div>
          </div>
        </div>
      )}
      
      {versionToCompare && (
        <VersionComparisonModal 
            isOpen={!!versionToCompare}
            onClose={() => setVersionToCompare(null)}
            oldVersion={versionToCompare}
            currentContent={document.content}
        />
      )}
    </>
  );
};

export default VersionHistoryPanel;
