const rawApiBase = import.meta.env.VITE_API_BASE || 'http://localhost:3001';
export const API_BASE = rawApiBase.replace(/\/$/, '');

export async function apiFetch<T>(path: string, opts: globalThis.RequestInit = {}): Promise<T> {
  const token = (await getAccessToken?.()) || undefined;
  const headers = new Headers(opts.headers);
  headers.set('Content-Type', 'application/json');
  if (token) {headers.set('Authorization', `Bearer ${token}`);}
  const res = await fetch(`${API_BASE}${path}`, { ...opts, headers });
  const responseText = await res.text();
  if (!res.ok) {throw new Error(responseText || res.statusText);}

  if (!responseText) {
    return undefined as T;
  }

  const contentType = res.headers.get('content-type') ?? '';
  if (!contentType.includes('application/json')) {
    return responseText as unknown as T;
  }

  try {
    return JSON.parse(responseText) as T;
  } catch (err) {
    const message = err instanceof Error ? err.message : 'Unexpected response format';
    throw new Error(`Failed to parse JSON response: ${message}`);
  }
}

// Provide a hook point for auth integration; replace with Supabase session
let getAccessToken: null | (() => Promise<string | null>) = null;
export function setAccessTokenGetter(fn: () => Promise<string | null>) {
  getAccessToken = fn;
}

