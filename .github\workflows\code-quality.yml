name: Code Quality

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
      - run: npm install --no-audit --no-fund
      - name: Run ESLint
        run: npm run lint:check
      - name: Check TypeScript
        run: npx tsc --noEmit