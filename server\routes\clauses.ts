import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient } from '../supabaseClient';

const router = Router();

const clauseInsert = z.object({
  title: z.string().min(1),
  content: z.string().min(1),
  tags: z.array(z.string()).default([]),
});

const clauseUpdate = z.object({
  title: z.string().min(1).optional(),
  content: z.string().min(1).optional(),
  tags: z.array(z.string()).optional(),
});

router.get('/', requireAuth, async (req: AuthRequest, res) => {
  const userClient = getUserClient(getAccessToken(req));
  const { data, error } = await userClient
    .from('clauses')
    .select('*')
    .order('created_at', { ascending: false });
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ clauses: data });
});

router.post('/', requireAuth, async (req: AuthRequest, res) => {
  const parse = clauseInsert.safeParse(req.body);
  if (!parse.success) {return res.status(400).json({ error: parse.error.message });}
  const userId = req.user?.id;
  if (!userId) {return res.status(401).json({ error: 'User not authenticated' });}
  const userClient = getUserClient(getAccessToken(req));
  const { data, error } = await userClient
    .from('clauses')
    .insert({ user_id: userId, ...parse.data })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ clause: data });
});

router.put('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const parse = clauseUpdate.safeParse(req.body);
  if (!parse.success) {return res.status(400).json({ error: parse.error.message });}
  const userClient = getUserClient(getAccessToken(req));
  const { data, error } = await userClient
    .from('clauses')
    .update(parse.data)
    .eq('id', id)
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ clause: data });
});

router.delete('/:id', requireAuth, async (req: AuthRequest, res) => {
  const { id } = req.params;
  const userClient = getUserClient(getAccessToken(req));
  const { error } = await userClient
    .from('clauses')
    .delete()
    .eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

export default router;
