
import React from 'react';
import Indicator from './Indicator';

const KanbanCard = ({ title }: { title: string }) => (
    <div className="bg-white dark:bg-zinc-800 p-2 rounded-md shadow border border-zinc-200 dark:border-zinc-700">
        <p className="font-medium text-zinc-800 dark:text-zinc-200 truncate">{title}</p>
    </div>
);

const KanbanColumn = ({ title, children }: { title: string, children?: React.ReactNode }) => (
    <div className="bg-zinc-100 dark:bg-zinc-900 rounded-lg p-2 flex-1">
        <h3 className="font-semibold text-zinc-600 dark:text-zinc-300 text-[10px] text-center capitalize mb-2 p-1 bg-zinc-200 dark:bg-zinc-800 rounded-md">
            {title}
        </h3>
        <div className="space-y-2 min-h-[50px]">
            {children}
        </div>
    </div>
);

const LifecycleHelpScreenshot: React.FC = () => {
    return (
        <div className="relative my-6 p-4 border rounded-lg bg-zinc-50 dark:bg-zinc-950 overflow-hidden select-none text-xs">
            <div
                className="absolute top-[45%]"
                style={{
                    left: 'calc(25% + 1rem)',
                    width: 'calc(25% - 2rem)',
                    height: '2px',
                    backgroundImage: 'linear-gradient(to right, #4f46e5 50%, transparent 50%)',
                    backgroundSize: '8px 2px',
                    backgroundRepeat: 'repeat-x',
                }}
            />
            <div className="absolute top-[45%] text-brand-600 font-bold" style={{ left: 'calc(50% - 0.5rem)'}}>&rarr;</div>
            <Indicator number={1} position="top-1/3 left-1/4" arrow="right" />
            
            <h2 className="text-lg font-bold text-zinc-900 dark:text-zinc-100 mb-3">Contract Pipeline</h2>
            <div className="flex gap-3">
                <KanbanColumn title="Draft">
                    <KanbanCard title="Freelance Contract" />
                </KanbanColumn>
                <KanbanColumn title="Out For-Signature" />
                <KanbanColumn title="Completed" />
                <KanbanColumn title="Archived" />
            </div>
        </div>
    );
};

export default LifecycleHelpScreenshot;
