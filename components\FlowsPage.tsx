import React, { useState } from 'react';
import { User, Flow } from '../types';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from './ui/Card';
import { Button } from './ui/Button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/Table';
import { PlusCircleIcon, TrashIcon, GitBranchIcon, MoreVerticalIcon, EditIcon } from './Icons';
import { cn } from '../lib/utils';
import { ALL_CONNECTORS } from './connectors';
import Dropdown from './ui/Dropdown';
import FlowEditorModal from './FlowEditorModal';

interface FlowsPageProps {
  user: User;
  onCreateFlow: (flowData: Omit<Flow, 'id' | 'userId' | 'createdAt'>) => void;
  onUpdateFlow: (flowId: string, updates: Partial<Flow>) => void;
  onDeleteFlow: (flowId: string) => void;
}

const ToggleSwitch: React.FC<{ checked: boolean; onChange: (checked: boolean) => void; }> = ({ checked, onChange }) => (
    <button
        type="button"
        role="switch"
        aria-checked={checked}
        onClick={(e) => { e.stopPropagation(); onChange(!checked); }}
        className={cn(
            'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 dark:ring-offset-zinc-950',
            checked ? 'bg-brand-600' : 'bg-zinc-200 dark:bg-zinc-700'
        )}
    >
        <span
            aria-hidden="true"
            className={cn(
                'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                checked ? 'translate-x-5' : 'translate-x-0'
            )}
        />
    </button>
);


const FlowsPage: React.FC<FlowsPageProps> = ({ user, onCreateFlow, onUpdateFlow, onDeleteFlow }) => {
    const flows = user.flows || [];
    const [isFlowEditorOpen, setIsFlowEditorOpen] = useState(false);
    const [editingFlow, setEditingFlow] = useState<Flow | null>(null);

    const getConnector = (connectionId: string) => {
        const connection = user.connections?.find(c => c.id === connectionId);
        return ALL_CONNECTORS.find(c => c.id === connection?.connectorId);
    };

    const handleNewFlow = () => {
        setEditingFlow(null);
        setIsFlowEditorOpen(true);
    };

    const handleEditFlow = (flow: Flow) => {
        setEditingFlow(flow);
        setIsFlowEditorOpen(true);
    };

    const handleSaveFlow = (flowData: Omit<Flow, 'id' | 'userId' | 'createdAt'>, flowId?: string) => {
        if (flowId) {
            onUpdateFlow(flowId, flowData);
        } else {
            onCreateFlow(flowData);
        }
        setIsFlowEditorOpen(false);
    };
    
    return (
        <>
        <div className="p-4 sm:p-6 lg:p-8 space-y-6">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 className="text-3xl font-bold text-zinc-900 dark:text-white">Flows</h1>
                    <p className="text-zinc-600 dark:text-zinc-400 mt-1">Manage your automated workflows between applications.</p>
                </div>
                <Button onClick={handleNewFlow}>
                    <PlusCircleIcon className="w-5 h-5 mr-2" /> New Flow
                </Button>
            </div>

             <Card>
                <CardHeader>
                    <CardTitle>Your Flows</CardTitle>
                    <CardDescription>All your active and inactive automations.</CardDescription>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Flow</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Created</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {flows.length > 0 ? flows.map(flow => {
                                const triggerConnector = getConnector(flow.trigger.connectionId);
                                const actionConnector = getConnector(flow.action.connectionId);
                                const TriggerIcon = triggerConnector?.icon;
                                const ActionIcon = actionConnector?.icon;
                                return(
                                <TableRow key={flow.id}>
                                    <TableCell>
                                        <div className="flex items-center gap-2">
                                            {TriggerIcon && <TriggerIcon className="w-6 h-6"/>}
                                            <span className="font-bold">&rarr;</span>
                                            {ActionIcon && <ActionIcon className="w-6 h-6"/>}
                                            <span className="font-medium">{flow.name}</span>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <ToggleSwitch checked={flow.status === 'active'} onChange={(isChecked) => onUpdateFlow(flow.id, { status: isChecked ? 'active' : 'inactive' })} />
                                    </TableCell>
                                    <TableCell>{new Date(flow.createdAt).toLocaleDateString()}</TableCell>
                                    <TableCell className="text-right">
                                        <Dropdown>
                                            <Dropdown.Trigger>
                                                <Button variant="ghost" size="icon"><MoreVerticalIcon className="w-4 h-4" /></Button>
                                            </Dropdown.Trigger>
                                            <Dropdown.Content align="right">
                                                <Dropdown.Item onClick={() => handleEditFlow(flow)} icon={<EditIcon className="w-4 h-4"/>}>Edit</Dropdown.Item>
                                                <Dropdown.Item onClick={() => onDeleteFlow(flow.id)} className="text-red-600" icon={<TrashIcon className="w-4 h-4"/>}>Delete</Dropdown.Item>
                                            </Dropdown.Content>
                                        </Dropdown>
                                    </TableCell>
                                </TableRow>
                                );
                            }) : (
                                 <TableRow>
                                    <TableCell colSpan={4} className="h-48 text-center">
                                        <GitBranchIcon className="mx-auto h-12 w-12 text-zinc-300 dark:text-zinc-700" />
                                        <h3 className="mt-2 font-semibold">No Flows Created</h3>
                                        <p className="text-sm text-zinc-500">Create a new flow to start automating.</p>
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </CardContent>
             </Card>
        </div>
        <FlowEditorModal
            isOpen={isFlowEditorOpen}
            onClose={() => setIsFlowEditorOpen(false)}
            user={user}
            onSave={handleSaveFlow}
            initialFlow={editingFlow}
        />
        </>
    );
};

export default FlowsPage;
