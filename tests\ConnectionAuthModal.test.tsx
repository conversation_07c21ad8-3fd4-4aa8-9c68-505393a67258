// @vitest-environment jsdom
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import ConnectionAuthModal from '../components/ConnectionAuthModal';
import React from 'react';

describe('ConnectionAuthModal', () => {
  it('renders ConnectionAuthModal component', () => {
    const mockConnector = {
      id: 'test',
      name: 'Test Connector',
      description: '',
      icon: () => null,
      auth: { type: 'apiKey', fields: [{ key: 'apiKey', label: 'API Key', type: 'text' }] },
    };
    render(
      <ConnectionAuthModal
        isOpen={true}
        onClose={() => {}}
        connector={mockConnector as any}
        onConnect={() => {}}
      />
    );
    // <PERSON><PERSON> should render a label for the API Key field
    expect(screen.getByLabelText(/API Key/i)).toBeInTheDocument();
  });
});
