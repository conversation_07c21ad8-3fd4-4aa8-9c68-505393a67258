// Test setup file for vitest

// Mock ResizeObserver for testing compatibility
global.ResizeObserver = class ResizeObserver {
  constructor(_callback: (entries: ResizeObserverEntry[], observer: ResizeObserver) => void) {
    // Mock implementation
  }
  
  observe(_target: Element, _options?: { box?: string }): void {
    // Mock implementation
  }
  
  unobserve(_target: Element): void {
    // Mock implementation
  }
  
  disconnect(): void {
    // Mock implementation
  }
};

// Mock IntersectionObserver if needed
if (!global.IntersectionObserver) {
  global.IntersectionObserver = class IntersectionObserver {
    constructor(_callback: (entries: IntersectionObserverEntry[], observer: IntersectionObserver) => void, _options?: { root?: Element | null; rootMargin?: string; threshold?: number | number[] }) {
      // Mock implementation
    }
    
    observe(_target: Element): void {
      // Mock implementation
    }
    
    unobserve(_target: Element): void {
      // Mock implementation
    }
    
    disconnect(): void {
      // Mock implementation
    }
  } as typeof IntersectionObserver;
}

// Mock canvas methods used in tests
(HTMLCanvasElement.prototype.getContext as unknown as () => CanvasRenderingContext2D | null) = () => ({
  drawImage: () => null,
  fillRect: () => null,
  clearRect: () => null,
  getImageData: () => ({ data: new Uint8ClampedArray() }),
  putImageData: () => null,
  createImageData: () => ({ data: new Uint8ClampedArray() }),
  setTransform: () => null,
  beginPath: () => null,
  closePath: () => null,
  moveTo: () => null,
  lineTo: () => null,
  arc: () => null,
  fill: () => null,
  stroke: () => null,
  save: () => null,
  restore: () => null,
  measureText: () => ({ width: 0 }),
  translate: () => null,
  scale: () => null,
  rotate: () => null,
}) as unknown as CanvasRenderingContext2D;

HTMLCanvasElement.prototype.toDataURL = () => 'data:image/png;base64,';
