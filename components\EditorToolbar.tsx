

import React, { useState, useRef, useEffect } from 'react';
import { Button } from './ui/Button';
import { 
    BoldIcon, ItalicIcon, UnderlineIcon, StrikethroughIcon,
    H1Icon, H2Icon, ListIcon,
    UndoIcon, RedoIcon,
    TextColorIcon, HighlightIcon,
    AlignLeftIcon, AlignCenterIcon, AlignRightIcon, AlignJustifyIcon
} from './Icons';

interface EditorToolbarProps {
  editorRef: React.RefObject<HTMLDivElement>;
}

const TEXT_COLORS = ['#000000', '#e03131', '#c2255c', '#9c36b5', '#6741d9', '#3b5bdb', '#1c7ed6', '#0b7285', '#087f5b', '#2f9e44', '#f08c00', '#787c82'];
const HIGHLIGHT_COLORS = ['#ffff00', '#ffc9c9', '#fcc2d7', '#eebefa', '#d0bfff', '#bac8ff', '#a5d8ff', '#99e9f2', '#63e6be', '#8ce99a', '#ffd43b', '#d0d4d9', 'transparent'];

const ColorPalette: React.FC<{
    colors: string[];
    onSelect: (color: string) => void;
    onClose: () => void;
}> = ({ colors, onSelect, onClose }) => {
    const paletteRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (paletteRef.current && !paletteRef.current.contains(event.target as Node)) {
                onClose();
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, [onClose]);

    return (
        <div ref={paletteRef} className="absolute top-full mt-2 z-20 bg-white dark:bg-zinc-800 rounded-md shadow-lg border border-zinc-200 dark:border-zinc-700 p-2 grid grid-cols-6 gap-1">
            {colors.map(color => (
                <button
                    key={color}
                    onClick={() => onSelect(color)}
                    className="w-6 h-6 rounded-sm border border-zinc-200 dark:border-zinc-600"
                    style={{ backgroundColor: color === 'transparent' ? undefined : color }}
                    title={color === 'transparent' ? 'No Color' : color}
                >
                  {color === 'transparent' && <div className="w-full h-full bg-no-repeat bg-center" style={{backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M-10 10l20-20M80 110l20-20M-10 90l100-100' stroke='%23dc2626' stroke-width='8'/%3E%3C/svg%3E")`}}></div>}
                </button>
            ))}
        </div>
    );
};


const EditorToolbar: React.FC<EditorToolbarProps> = ({ editorRef }) => {
  const [showTextColorPicker, setShowTextColorPicker] = useState(false);
  const [showHighlightColorPicker, setShowHighlightColorPicker] = useState(false);

  const applyFormat = (command: string, value?: string) => {
    if (editorRef.current) {
      editorRef.current.focus();
      document.execCommand(command, false, value);
    }
  };

  const applyColor = (command: 'foreColor' | 'hiliteColor', color: string) => {
    applyFormat(command, color);
    setShowTextColorPicker(false);
    setShowHighlightColorPicker(false);
  };
  
  const ToolbarButton: React.FC<{onClick: () => void; title: string; children: React.ReactNode}> = ({onClick, title, children}) => (
      <Button variant="ghost" size="icon" onClick={onClick} title={title} className="h-9 w-9 dark:text-zinc-300 dark:hover:bg-zinc-700">
          {children}
      </Button>
  );

  const Divider = () => <div className="w-px h-6 bg-zinc-200 dark:bg-zinc-700 mx-1"></div>;

  return (
    <div className="sticky top-2 z-10 mb-4 bg-white/90 dark:bg-zinc-800/90 backdrop-blur-md p-1 rounded-lg shadow-lg border border-zinc-200 dark:border-zinc-700 flex items-center justify-center flex-wrap gap-0.5 max-w-2xl mx-auto">
      <ToolbarButton onClick={() => applyFormat('undo')} title="Undo"><UndoIcon className="w-5 h-5" /></ToolbarButton>
      <ToolbarButton onClick={() => applyFormat('redo')} title="Redo"><RedoIcon className="w-5 h-5" /></ToolbarButton>
      <Divider />
      <ToolbarButton onClick={() => applyFormat('bold')} title="Bold"><BoldIcon className="w-5 h-5" /></ToolbarButton>
      <ToolbarButton onClick={() => applyFormat('italic')} title="Italic"><ItalicIcon className="w-5 h-5" /></ToolbarButton>
      <ToolbarButton onClick={() => applyFormat('underline')} title="Underline"><UnderlineIcon className="w-5 h-5" /></ToolbarButton>
      <ToolbarButton onClick={() => applyFormat('strikeThrough')} title="Strikethrough"><StrikethroughIcon className="w-5 h-5" /></ToolbarButton>
      <Divider />
      <div className="relative">
          <ToolbarButton onClick={() => { setShowHighlightColorPicker(false); setShowTextColorPicker(p => !p); }} title="Text Color"><TextColorIcon className="w-5 h-5" /></ToolbarButton>
          {showTextColorPicker && <ColorPalette colors={TEXT_COLORS} onSelect={(color) => applyColor('foreColor', color)} onClose={() => setShowTextColorPicker(false)}/>}
      </div>
      <div className="relative">
          <ToolbarButton onClick={() => { setShowTextColorPicker(false); setShowHighlightColorPicker(p => !p); }} title="Highlight Color"><HighlightIcon className="w-5 h-5" /></ToolbarButton>
          {showHighlightColorPicker && <ColorPalette colors={HIGHLIGHT_COLORS} onSelect={(color) => applyColor('hiliteColor', color)} onClose={() => setShowHighlightColorPicker(false)} />}
      </div>
      <Divider />
      <ToolbarButton onClick={() => applyFormat('justifyLeft')} title="Align Left"><AlignLeftIcon className="w-5 h-5" /></ToolbarButton>
      <ToolbarButton onClick={() => applyFormat('justifyCenter')} title="Align Center"><AlignCenterIcon className="w-5 h-5" /></ToolbarButton>
      <ToolbarButton onClick={() => applyFormat('justifyRight')} title="Align Right"><AlignRightIcon className="w-5 h-5" /></ToolbarButton>
      <ToolbarButton onClick={() => applyFormat('justifyFull')} title="Justify"><AlignJustifyIcon className="w-5 h-5" /></ToolbarButton>
      <Divider />
      <ToolbarButton onClick={() => applyFormat('formatBlock', '<h1>')} title="Heading 1"><H1Icon className="w-5 h-5" /></ToolbarButton>
      <ToolbarButton onClick={() => applyFormat('formatBlock', '<h2>')} title="Heading 2"><H2Icon className="w-5 h-5" /></ToolbarButton>
      <ToolbarButton onClick={() => applyFormat('insertUnorderedList')} title="Bulleted List"><ListIcon className="w-5 h-5" /></ToolbarButton>
    </div>
  );
};

export default EditorToolbar;