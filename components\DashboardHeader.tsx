import React, { useEffect, useRef, useState } from 'react';
import { HELP_TOPICS } from '../constants';
import { DashboardView, Notification, SearchResult, Theme, User } from '../types';
import GlobalSearchResults from './GlobalSearchResults';
import { BellIcon, BillingIcon, CloseIcon, IntegrationsIcon, MenuIcon, QuestionMarkCircleIcon, SearchIcon, SettingsIcon, UserIcon, UsersIcon } from './Icons';
import NotificationsPanel from './NotificationsPanel';
import Dropdown from './ui/Dropdown';
import Input from './ui/Input';

interface DashboardHeaderProps {
  onLogout: () => void;
  user: User;
  view: DashboardView;
  onToggleSidebar: () => void;
  onMarkAllNotificationsRead: () => void;
  onNotificationClick: (notification: Notification) => void;
  setView: (view: DashboardView) => void;
  onSearchResultClick: (result: SearchResult) => void;
  onUpdateUserSettings: (settings: { theme?: Theme }) => void;
}

const viewTitles: Record<DashboardView, string> = {
  dashboard: 'Dashboard',
  generate: 'AI Document Generator',
  history: 'Document Library',
  subscription: 'Subscription & Billing',
  settings: 'Account Settings',
  templates: 'Document Templates',
  documentDetail: 'Document Detail',
  lifecycle: 'Contract Lifecycle',
  clauseLibrary: 'Clause Library',
  team: 'Team Management',
  integrations: 'Integrations',
  clientDetail: 'Client Detail',
  notifications: 'Notifications',
  analysis: 'Document Analysis',
  help: 'Help Center',
  obligations: 'Obligation Tracking',
  workflows: 'Workflow Automation',
  clients: 'Client Management',
};

const DashboardHeader: React.FC<DashboardHeaderProps> = (props) => {
  const { onLogout, user, view, onToggleSidebar, onMarkAllNotificationsRead, onNotificationClick, setView, onSearchResultClick } = props;
  const { onUpdateUserSettings } = props;
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const notificationsRef = useRef<HTMLDivElement>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);

  const hasUnread = user.notifications?.some(n => !n.isRead);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setIsNotificationsOpen(false);
      }
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsSearchFocused(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }

    const debounce = setTimeout(() => {
      const lowercasedTerm = searchTerm.toLowerCase();
      const results: SearchResult[] = [];

      // Search Documents
      user.documents.forEach(doc => {
        if (doc.name.toLowerCase().includes(lowercasedTerm)) {
          results.push({ id: doc.id, title: doc.name, type: 'document', context: `Updated ${new Date(doc.updatedAt).toLocaleDateString()}` });
        }
      });

      // Search Folders
      user.folders.forEach(folder => {
        if (folder.name.toLowerCase().includes(lowercasedTerm)) {
          results.push({ id: folder.id, title: folder.name, type: 'folder' });
        }
      });

      // Search Clauses
      (user.clauses || []).forEach(clause => {
        if (clause.title.toLowerCase().includes(lowercasedTerm)) {
          results.push({ id: clause.id, title: clause.title, type: 'clause' });
        }
      });

      // Search Help Topics
      HELP_TOPICS.forEach(category => {
        category.topics.forEach(topic => {
          const contentString = Array.isArray(topic.content) ? topic.content.map(c => c.type === 'text' ? c.value : '').join(' ') : topic.content;
          if (topic.title.toLowerCase().includes(lowercasedTerm) || contentString.toLowerCase().includes(lowercasedTerm)) {
            results.push({ id: topic.id, title: topic.title, type: 'help', context: category.category });
          }
        });
      });

      setSearchResults(results);
    }, 300);

    return () => clearTimeout(debounce);

  }, [searchTerm, user]);

  const handleResultClick = (result: SearchResult) => {
    onSearchResultClick(result);
    setSearchTerm('');
    setSearchResults([]);
    setIsSearchFocused(false);
  };

  return (
    <header className="relative flex-shrink-0 h-16 bg-white dark:bg-zinc-950 border-b border-zinc-200 dark:border-zinc-800 flex items-center justify-between px-4 sm:px-6 lg:px-8">
      <div className="flex items-center">
        <button onClick={onToggleSidebar} className="lg:hidden mr-4 text-zinc-600 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-white" aria-label="Toggle sidebar">
          <MenuIcon className="w-6 h-6" />
        </button>
        <h1 className="text-xl font-semibold text-zinc-900 dark:text-white capitalize">{viewTitles[view]}</h1>
      </div>
      <div className="flex items-center gap-4">
        <div className="relative" ref={searchRef}>
          <Input
            placeholder="Search workspace..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onFocus={() => setIsSearchFocused(true)}
            leftIcon={<SearchIcon className="h-5 w-5" />}
            className="sm:w-64 bg-zinc-100 dark:bg-zinc-800 border-zinc-300 dark:border-zinc-700"
          />
          {(isSearchFocused && (searchResults.length > 0 || searchTerm)) && (
            <GlobalSearchResults
              results={searchResults}
              onResultClick={handleResultClick}
              searchTerm={searchTerm}
            />
          )}
        </div>
        {/* Quota badge with tooltip and quick upgrade link */}
        {(() => {
          const isPaid = user.planName === 'Premium' || user.planName === 'Enterprise';
          const start = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
          const end = new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1);
          const fmt = (d: Date) => d.toLocaleDateString(undefined, { month: 'short', day: 'numeric' });
          const period = `${fmt(start)} – ${fmt(end)}`;
          return (
            <div
              className={`relative group hidden sm:flex items-center px-2 py-1 rounded-full text-xs bg-zinc-100 dark:bg-zinc-800 text-zinc-700 dark:text-zinc-300 ${isPaid ? '' : 'cursor-pointer hover:bg-zinc-200 dark:hover:bg-zinc-700'}`}
              onClick={() => { if (!isPaid) { setView('subscription'); } }}
              aria-label={isPaid ? 'Unlimited plan' : 'Monthly quota badge'}
              title=""
            >
              {isPaid ? 'Unlimited' : `MTD: ${user.quotaUsed || 0} of ${isFinite(user.quotaTotal as number) ? user.quotaTotal : 5}`}
              <div className="absolute top-full mt-2 left-1/2 -translate-x-1/2 w-max px-2 py-1 bg-zinc-800 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap">
                Billing period: {period}
                {!isPaid && <span className="ml-1 text-zinc-300">(Click to upgrade)</span>}
              </div>
            </div>
          );
        })()}

        <div className="relative" ref={notificationsRef}>
          <button onClick={() => setIsNotificationsOpen(p => !p)} className="relative p-2 rounded-full hover:bg-zinc-100 dark:hover:bg-zinc-800" aria-label="View notifications">
            <BellIcon className="w-6 h-6 text-zinc-600 dark:text-zinc-400" />
            {hasUnread && <span className="absolute top-1.5 right-1.5 block h-2.5 w-2.5 rounded-full bg-red-500 ring-2 ring-white dark:ring-zinc-950" />}
          </button>
          {isNotificationsOpen && (
            <NotificationsPanel
              notifications={user.notifications || []}
              onMarkAllRead={onMarkAllNotificationsRead}
              onNotificationClick={(notif) => {
                onNotificationClick(notif);
                setIsNotificationsOpen(false);
              }}
              onClose={() => setIsNotificationsOpen(false)}
              onViewAll={() => {
                setView('notifications');
                setIsNotificationsOpen(false);
              }}
            />
          )}
        </div>
        <Dropdown>
          <Dropdown.Trigger>
            <div className="flex items-center space-x-2 p-1 rounded-full hover:bg-zinc-100 dark:hover:bg-zinc-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500 dark:ring-offset-zinc-950">
              <img src={user.avatarUrl} alt="User Avatar" className="w-9 h-9 rounded-full object-cover" />
              <span className="hidden sm:inline text-sm font-medium text-zinc-700 dark:text-zinc-300">{user.name || 'Account'}</span>
            </div>
          </Dropdown.Trigger>
          <Dropdown.Content align="right">
            <div className="px-4 py-3">
              <div className="flex items-center gap-3">
                <img src={user.avatarUrl} alt="User Avatar" className="w-10 h-10 rounded-full object-cover" />
                <div>
                  <div className="text-sm font-semibold text-zinc-900 dark:text-white">{user.name || 'Your Account'}</div>
                  <div className="mt-1 inline-flex items-center px-2 py-0.5 rounded-full text-[11px] bg-zinc-100 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-200">{user.planName}</div>
                </div>
              </div>
            </div>
            <Dropdown.Separator />
            <Dropdown.Item onClick={() => setView('settings')} icon={<UserIcon className="w-4 h-4" />}>Profile</Dropdown.Item>
            <Dropdown.Item onClick={() => setView('subscription')} icon={<BillingIcon className="w-4 h-4" />}>Subscription</Dropdown.Item>
            <Dropdown.Item onClick={() => setView('team')} icon={<UsersIcon className="w-4 h-4" />}>Team</Dropdown.Item>
            <Dropdown.Item onClick={() => setView('integrations')} icon={<IntegrationsIcon className="w-4 h-4" />}>Integrations</Dropdown.Item>
            <Dropdown.Item onClick={() => setView('help')} icon={<QuestionMarkCircleIcon className="w-4 h-4" />}>Help Center</Dropdown.Item>
            <Dropdown.Separator />
            <div className="px-4 py-2">
              <div className="text-[11px] uppercase tracking-wide text-zinc-500 dark:text-zinc-400 mb-2 flex items-center gap-2"><SettingsIcon className="w-4 h-4" />Theme</div>
              <div className="grid grid-cols-3 gap-2">
                {(['light', 'dark', 'system'] as Theme[]).map(t => (
                  <button key={t} onClick={() => onUpdateUserSettings({ theme: t })} className={`px-2 py-1.5 rounded-md text-xs border ${user.theme === t ? 'border-brand-500 text-brand-600' : 'border-zinc-300 dark:border-zinc-700 text-zinc-700 dark:text-zinc-300'}`}>
                    {t.charAt(0).toUpperCase() + t.slice(1)}
                  </button>
                ))}
              </div>
            </div>
            <Dropdown.Separator />
            <Dropdown.Item onClick={onLogout} className="text-red-600 dark:text-red-400" icon={<CloseIcon className="w-4 h-4" />}>Log out</Dropdown.Item>
          </Dropdown.Content>
        </Dropdown>
      </div>
    </header>
  )
}

export default DashboardHeader;
