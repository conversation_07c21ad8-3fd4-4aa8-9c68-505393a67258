import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient, supabaseAdmin } from '../supabaseClient';

const router = Router();

const urlSchema = z.string().refine(v => {
  if (!v) {return false;}
  if (v.startsWith('storage://contractgini/')) {return true;}
  try { new URL(v); return true; } catch { return false; }
}, 'Invalid URL');
const insertSchema = z.object({ name: z.string().min(1), url: urlSchema });
const updateSchema = z.object({ name: z.string().min(1).optional(), url: urlSchema.optional() });
const uploadUrlSchema = z.object({ fileName: z.string().min(1) });

function sanitizeFileName(name: string): string {
  // Strip directory separators and limit to safe chars
  const base = name.replace(/[\\/]+/g, '_');
  return base.replace(/[^A-Za-z0-9._\-\s]/g, '_').slice(0, 200);
}

function isAllowedExtension(name: string): boolean {
  const m = name.toLowerCase().match(/\.([a-z0-9]+)$/);
  if (!m) {return false;}
  const ext = m[1];
  const allowed = new Set(['png','jpg','jpeg','gif','webp','svg','pdf','doc','docx']);
  return allowed.has(ext);
}

// Create a signed upload URL for a new asset (one-time use)
router.post('/:clientId/assets/upload-url', requireAuth, async (req: AuthRequest, res) => {
  const { clientId } = req.params as { clientId: string };
  const parsed = uploadUrlSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const userId = req.user?.id;
  if (!userId) {return res.status(401).json({ error: 'User not authenticated' });}
  const safeName = sanitizeFileName(parsed.data.fileName);
  if (!isAllowedExtension(safeName)) {return res.status(400).json({ error: 'Unsupported file type' });}
  const path = `${userId}/${clientId}/${Date.now()}_${safeName}`;
  const { data, error } = await supabaseAdmin.storage.from('contractgini').createSignedUploadUrl(path);
  if (error || !data?.token) {return res.status(500).json({ error: error?.message || 'Failed to create signed upload URL' });}
  res.json({ path, token: data.token });
});

// List assets for a client
router.get('/:clientId/assets', requireAuth, async (req: AuthRequest, res) => {
  const { clientId } = req.params as { clientId: string };
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa
    .from('client_assets')
    .select('*')
    .eq('client_id', clientId)
    .order('created_at', { ascending: false });
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ assets: data });
});

// Create asset for a client
router.post('/:clientId/assets', requireAuth, async (req: AuthRequest, res) => {
  const { clientId } = req.params as { clientId: string };
  const parsed = insertSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data: profile } = await supa.auth.getUser();
  const userId = profile.user?.id;
  const { data, error } = await supa
    .from('client_assets')
    .insert({ user_id: userId, client_id: clientId, name: parsed.data.name, url: parsed.data.url })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ asset: data });
});

// Update asset
router.put('/:clientId/assets/:assetId', requireAuth, async (req: AuthRequest, res) => {
  const { assetId } = req.params as { assetId: string };
  const parsed = updateSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  // Fetch previous asset to allow cleanup if URL changed
  const { data: prev } = await supa.from('client_assets').select('*').eq('id', assetId).single();
  const patch: Partial<{ name: string; url: string }> = { name: parsed.data.name, url: parsed.data.url };
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const { data, error } = await supa
    .from('client_assets')
    .update(patch)
    .eq('id', assetId)
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  try {
    // If URL changed and the previous asset referenced our storage, remove old file
    const oldUrl: string | undefined = prev?.url;
    const newUrl: string | undefined = data?.url;
    if (oldUrl && newUrl && oldUrl !== newUrl) {
      let path: string | null = null;
      if (oldUrl.startsWith('storage://contractgini/')) {
        path = decodeURIComponent(oldUrl.substring('storage://contractgini/'.length));
      } else {
        const markerPriv = '/storage/v1/object/private/contractgini/';
        const markerPub = '/storage/v1/object/public/contractgini/';
        if (oldUrl.includes(markerPriv)) {
          path = decodeURIComponent(oldUrl.substring(oldUrl.indexOf(markerPriv) + markerPriv.length));
        } else if (oldUrl.includes(markerPub)) {
          path = decodeURIComponent(oldUrl.substring(oldUrl.indexOf(markerPub) + markerPub.length));
        }
      }
      if (path) {
        await supabaseAdmin.storage.from('contractgini').remove([path]);
      }
    }
  } catch {
    // Ignore storage cleanup errors - best effort only
  }
  res.json({ asset: data });
});

// Delete asset
router.delete('/:clientId/assets/:assetId', requireAuth, async (req: AuthRequest, res) => {
  const { assetId } = req.params as { assetId: string };
  const supa = getUserClient(getAccessToken(req));
  // Fetch asset to determine storage path
  const { data: asset, error: fetchErr } = await supa.from('client_assets').select('*').eq('id', assetId).single();
  if (fetchErr) {return res.status(500).json({ error: fetchErr.message });}
  // Attempt to remove the storage object when URL matches our bucket
  try {
    const url: string | undefined = asset?.url;
    if (url) {
      let path: string | null = null;
      if (url.startsWith('storage://contractgini/')) {
        path = decodeURIComponent(url.substring('storage://contractgini/'.length));
      } else {
        const markerPriv = '/storage/v1/object/private/contractgini/';
        const markerPub = '/storage/v1/object/public/contractgini/';
        if (url.includes(markerPriv)) {
          path = decodeURIComponent(url.substring(url.indexOf(markerPriv) + markerPriv.length));
        } else if (url.includes(markerPub)) {
          path = decodeURIComponent(url.substring(url.indexOf(markerPub) + markerPub.length));
        }
      }
      if (path) {
        await supabaseAdmin.storage.from('contractgini').remove([path]);
      }
    }
  } catch {
    // best-effort cleanup; ignore failures
  }
  const { error } = await supa.from('client_assets').delete().eq('id', assetId);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

// Get a signed URL for viewing an asset (server-side signing)
router.get('/:clientId/assets/:assetId/signed-url', requireAuth, async (req: AuthRequest, res) => {
  const { clientId, assetId } = req.params as { clientId: string; assetId: string };
  const ttlMs = parseInt((req.query.ttl as string) || '3600', 10);
  const ttl = Number.isFinite(ttlMs) && ttlMs > 0 ? Math.min(ttlMs, 60 * 60 * 24) : 3600; // cap at 24h
  try {
    const supa = getUserClient(getAccessToken(req));
    // Verify ownership via RLS client
    const { data: asset, error } = await supa
      .from('client_assets')
      .select('*')
      .eq('id', assetId)
      .eq('client_id', clientId)
      .single();
    if (error || !asset) {return res.status(404).json({ error: 'Asset not found' });}
    const url: string | undefined = asset?.url;
    if (!url) {return res.status(400).json({ error: 'Asset has no URL' });}
    if (!url.startsWith('storage://contractgini/')) {return res.json({ url });}
    const path = decodeURIComponent(url.substring('storage://contractgini/'.length));
    const { data, error: signErr } = await supabaseAdmin.storage.from('contractgini').createSignedUrl(path, ttl);
    if (!signErr && data?.signedUrl) {return res.json({ url: data.signedUrl });}
    // Fallback: if bucket is public, return public URL instead of signed
    const pub = supabaseAdmin.storage.from('contractgini').getPublicUrl(path);
    if (pub?.data?.publicUrl) {return res.json({ url: pub.data.publicUrl });}
    return res.status(500).json({ error: signErr?.message || 'Failed to sign URL' });
  } catch (e: unknown) {
    return res.status(500).json({ error: e instanceof Error ? e.message : 'Failed to get signed URL' });
  }
});

export default router;
