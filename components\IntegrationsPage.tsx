import React, { useState } from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from './ui/Card';
import { Button } from './ui/Button';
import { LockSolidIcon, PlusCircleIcon, TrashIcon, GitBranchIcon, MoreVerticalIcon, EditIcon, ActivityIcon } from './Icons';
import { User, Connector, UserConnection, Flow, FlowRun } from '../types';
import { ALL_CONNECTORS } from './connectors';
import ConnectionAuthModal from './ConnectionAuthModal';
import { Tabs, TabsList, TabsTrigger, TabsContent } from './ui/Tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/Table';
import { cn } from '../lib/utils';
import Dropdown from './ui/Dropdown';
import FlowEditorModal from './FlowEditorModal';
import FlowRunDetailsModal from './FlowRunDetailsModal';


interface IntegrationsPageProps {
    user: User;
    onCreateConnection: (connectorId: string, credentials: Record<string, string | number | boolean>) => void;
    onDeleteConnection: (connectionId: string) => void;
    onCreateFlow: (flowData: Omit<Flow, 'id' | 'userId' | 'createdAt'>) => void;
    onUpdateFlow: (flowId: string, updates: Partial<Flow>) => void;
    onDeleteFlow: (flowId: string) => void;
}

const ToggleSwitch: React.FC<{ checked: boolean; onChange: (checked: boolean) => void; }> = ({ checked, onChange }) => (
    <button
        type="button"
        role="switch"
        aria-checked={checked}
        onClick={(e) => { e.stopPropagation(); onChange(!checked); }}
        className={cn(
            'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 dark:ring-offset-zinc-950',
            checked ? 'bg-brand-600' : 'bg-zinc-200 dark:bg-zinc-700'
        )}
    >
        <span
            aria-hidden="true"
            className={cn(
                'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                checked ? 'translate-x-5' : 'translate-x-0'
            )}
        />
    </button>
);


const IntegrationsPage: React.FC<IntegrationsPageProps> = ({ user, onCreateConnection, onDeleteConnection, onCreateFlow, onUpdateFlow, onDeleteFlow }) => {
    const [activeTab, setActiveTab] = useState('connections');
    const [authModalConnector, setAuthModalConnector] = useState<Connector | null>(null);
    const [isFlowEditorOpen, setIsFlowEditorOpen] = useState(false);
    const [editingFlow, setEditingFlow] = useState<Flow | null>(null);
    const [selectedRun, setSelectedRun] = useState<FlowRun | null>(null);
    const isEnterprise = user.planName === 'Enterprise';

    const userConnections = user.connections || [];

    const findConnection = (connectorId: string): UserConnection | undefined => {
        return userConnections.find(c => c.connectorId === connectorId);
    }
    
    const handleNewFlow = () => {
        setEditingFlow(null);
        setIsFlowEditorOpen(true);
    };

    const handleEditFlow = (flow: Flow) => {
        setEditingFlow(flow);
        setIsFlowEditorOpen(true);
    };

    const handleSaveFlow = (flowData: Omit<Flow, 'id' | 'userId' | 'createdAt'>, flowId?: string) => {
        if (flowId) {
            onUpdateFlow(flowId, flowData);
        } else {
            onCreateFlow(flowData);
        }
        setIsFlowEditorOpen(false);
    };

    const flows = user.flows || [];
    
    const getConnector = (connectionId: string) => {
        const connection = user.connections?.find(c => c.id === connectionId);
        return ALL_CONNECTORS.find(c => c.id === connection?.connectorId);
    };
    
    const ConnectionsView = () => (
        <Card className="mt-4">
            <CardHeader>
                <CardTitle>App Connections</CardTitle>
                <CardDescription>Select an application to connect and configure.</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {ALL_CONNECTORS.map(connector => {
                    const Icon = connector.icon;
                    const connection = findConnection(connector.id);

                    return (
                        <Card key={connector.id} className="flex flex-col">
                            <CardHeader className="flex-row items-center gap-4">
                                <Icon className="w-10 h-10 flex-shrink-0" />
                                <div>
                                    <CardTitle>{connector.name}</CardTitle>
                                </div>
                            </CardHeader>
                            <CardContent className="flex-1 flex flex-col justify-between">
                                <p className="text-sm text-zinc-500 dark:text-zinc-400">{connector.description}</p>
                            </CardContent>
                            <div className="p-4 mt-auto border-t dark:border-zinc-800">
                                {connection ? (
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm font-medium text-green-600">Connected</span>
                                        <Button variant="outline" size="sm" onClick={() => onDeleteConnection(connection.id)}>Disconnect</Button>
                                    </div>
                                ) : (
                                    <Button className="w-full" onClick={() => setAuthModalConnector(connector)}>
                                        Connect
                                    </Button>
                                )}
                            </div>
                        </Card>
                    );
                })}
            </CardContent>
        </Card>
    );
    
    const FlowsView = () => (
        <Card className="mt-4">
            <CardHeader>
                <CardTitle>Your Flows</CardTitle>
                <CardDescription>All your active and inactive automations.</CardDescription>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Flow</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Created</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {flows.length > 0 ? flows.map(flow => {
                            const triggerConnector = getConnector(flow.trigger.connectionId);
                            const actionConnector = getConnector(flow.action.connectionId);
                            const TriggerIcon = triggerConnector?.icon;
                            const ActionIcon = actionConnector?.icon;
                            return(
                            <TableRow key={flow.id}>
                                <TableCell>
                                    <div className="flex items-center gap-2">
                                        {TriggerIcon && <TriggerIcon className="w-6 h-6"/>}
                                        <span className="font-bold">&rarr;</span>
                                        {ActionIcon && <ActionIcon className="w-6 h-6"/>}
                                        <span className="font-medium">{flow.name}</span>
                                    </div>
                                </TableCell>
                                <TableCell>
                                    <ToggleSwitch checked={flow.status === 'active'} onChange={(isChecked) => onUpdateFlow(flow.id, { status: isChecked ? 'active' : 'inactive' })} />
                                </TableCell>
                                <TableCell>{new Date(flow.createdAt).toLocaleDateString()}</TableCell>
                                <TableCell className="text-right">
                                    <Dropdown>
                                        <Dropdown.Trigger>
                                            <Button variant="ghost" size="icon"><MoreVerticalIcon className="w-4 h-4" /></Button>
                                        </Dropdown.Trigger>
                                        <Dropdown.Content align="right">
                                            <Dropdown.Item onClick={() => handleEditFlow(flow)} icon={<EditIcon className="w-4 h-4"/>}>Edit</Dropdown.Item>
                                            <Dropdown.Item onClick={() => onDeleteFlow(flow.id)} className="text-red-600 dark:text-red-500 hover:bg-red-50 dark:hover:bg-red-500/10" icon={<TrashIcon className="w-4 h-4"/>}>Delete</Dropdown.Item>
                                        </Dropdown.Content>
                                    </Dropdown>
                                </TableCell>
                            </TableRow>
                            );
                        }) : (
                             <TableRow>
                                <TableCell colSpan={4} className="h-48 text-center">
                                    <GitBranchIcon className="mx-auto h-12 w-12 text-zinc-300 dark:text-zinc-700" />
                                    <h3 className="mt-2 font-semibold">No Flows Created</h3>
                                    <p className="text-sm text-zinc-500">Create a new flow to start automating.</p>
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>
         </Card>
    );

    const FlowsHistoryView = () => {
        const runs = (user.flowRuns || []).sort((a, b) => new Date(b.startedAt).getTime() - new Date(a.startedAt).getTime());
    
        const getFlowById = (flowId: string) => (user.flows || []).find(f => f.id === flowId);
    
        const StatusBadge = ({ status }: { status: FlowRun['status'] }) => {
            const styles = {
                success: 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300',
                failed: 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300',
                running: 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300 animate-pulse',
            };
            return <span className={cn('inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize', styles[status])}>{status}</span>;
        };
    
        return (
            <Card className="mt-4">
                <CardHeader>
                    <CardTitle>Flow Run History</CardTitle>
                    <CardDescription>A log of all your automation executions.</CardDescription>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Flow</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead>Ran At</TableHead>
                                <TableHead className="text-right">Details</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {runs.length > 0 ? runs.map(run => {
                                const flow = getFlowById(run.flowId);
                                if (!flow) {return null;}
                                const triggerConnector = getConnector(flow.trigger.connectionId);
                                const actionConnector = getConnector(flow.action.connectionId);
                                const TriggerIcon = triggerConnector?.icon;
                                const ActionIcon = actionConnector?.icon;
                                return(
                                <TableRow key={run.id}>
                                    <TableCell>
                                        <div className="flex items-center gap-2">
                                            {TriggerIcon && <TriggerIcon className="w-6 h-6"/>}
                                            <span className="font-bold">&rarr;</span>
                                            {ActionIcon && <ActionIcon className="w-6 h-6"/>}
                                            <span className="font-medium">{flow.name}</span>
                                        </div>
                                    </TableCell>
                                    <TableCell><StatusBadge status={run.status} /></TableCell>
                                    <TableCell>{new Date(run.startedAt).toLocaleString()}</TableCell>
                                    <TableCell className="text-right">
                                        <Button variant="outline" size="sm" onClick={() => setSelectedRun(run)}>View</Button>
                                    </TableCell>
                                </TableRow>
                                );
                            }) : (
                                 <TableRow>
                                    <TableCell colSpan={4} className="h-48 text-center">
                                        <ActivityIcon className="mx-auto h-12 w-12 text-zinc-300 dark:text-zinc-700" />
                                        <h3 className="mt-2 font-semibold">No Run History</h3>
                                        <p className="text-sm text-zinc-500">Activate a flow and perform the trigger action to see runs here.</p>
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        );
    };

    if (!isEnterprise) {
        return (
             <div className="p-4 sm:p-6 lg:p-8 h-full flex items-center justify-center">
                <div className="text-center">
                    <LockSolidIcon className="w-12 h-12 mx-auto text-zinc-300 dark:text-zinc-700 mb-4" />
                    <h3 className="text-xl font-semibold text-zinc-800 dark:text-zinc-200">Unlock Integrations</h3>
                    <p className="text-zinc-500 dark:text-zinc-400 mt-2 mb-4 max-w-md">
                        Upgrade to the Enterprise plan to connect LexiGen with third-party applications and automate your workflows.
                    </p>
                    <Button>Upgrade to Enterprise</Button>
                </div>
            </div>
        )
    }

    return (
        <>
            <div className="p-4 sm:p-6 lg:p-8 space-y-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-zinc-900 dark:text-white">Integrations</h1>
                        <p className="text-zinc-600 dark:text-zinc-400 mt-1">Connect apps and automate your workflows.</p>
                    </div>
                     {activeTab === 'flows' && (
                        <Button onClick={handleNewFlow}>
                            <PlusCircleIcon className="w-5 h-5 mr-2" /> New Flow
                        </Button>
                    )}
                </div>
                <Tabs>
                    <TabsList>
                        <TabsTrigger onClick={() => setActiveTab('connections')} data-state={activeTab === 'connections' ? 'active' : 'inactive'}>Connections</TabsTrigger>
                        <TabsTrigger onClick={() => setActiveTab('flows')} data-state={activeTab === 'flows' ? 'active' : 'inactive'}>Flows</TabsTrigger>
                        <TabsTrigger onClick={() => setActiveTab('runHistory')} data-state={activeTab === 'runHistory' ? 'active' : 'inactive'}>Run History</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent className={activeTab === 'connections' ? 'block' : 'hidden'}>
                        <ConnectionsView />
                    </TabsContent>
                    
                    <TabsContent className={activeTab === 'flows' ? 'block' : 'hidden'}>
                        <FlowsView />
                    </TabsContent>

                    <TabsContent className={activeTab === 'runHistory' ? 'block' : 'hidden'}>
                        <FlowsHistoryView />
                    </TabsContent>
                </Tabs>
            </div>
            <ConnectionAuthModal
                isOpen={!!authModalConnector}
                onClose={() => setAuthModalConnector(null)}
                connector={authModalConnector}
                onConnect={(credentials) => {
                    if(authModalConnector) {
                        onCreateConnection(authModalConnector.id, credentials);
                        setAuthModalConnector(null);
                    }
                }}
            />
            <FlowEditorModal
                isOpen={isFlowEditorOpen}
                onClose={() => setIsFlowEditorOpen(false)}
                user={user}
                onSave={handleSaveFlow}
                initialFlow={editingFlow}
            />
            <FlowRunDetailsModal
                isOpen={!!selectedRun}
                onClose={() => setSelectedRun(null)}
                run={selectedRun}
                user={user}
            />
        </>
    );
};

export default IntegrationsPage;
