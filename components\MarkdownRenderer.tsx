import React, { forwardRef } from 'react';
import { sanitizeHtml } from '../lib/sanitize';

interface MarkdownRendererProps {
  content: string;
}

const MarkdownRenderer = forwardRef<HTMLDivElement, MarkdownRendererProps>(({ content }, ref) => {
  // Simple check to see if the content is likely HTML
  const isHtml = /<[a-z][\s\S]*>/i.test(content || "");

  if (isHtml) {
    // If it's HTML, render it directly. This is for documents saved in the library.
    return <div ref={ref} className="text-zinc-800 leading-relaxed document-editor-content" dangerouslySetInnerHTML={{ __html: sanitizeHtml(content) }} />;
  }

  // If it's not HTML, parse it as Markdown. This is for newly generated documents.
  const lines = (content || "").split('\n');
  const elements: React.ReactElement[] = [];
  let listItems: string[] = [];

  const renderList = () => {
    if (listItems.length > 0) {
      elements.push(
        <ul key={`ul-${elements.length}`} className="list-disc pl-5 space-y-1 my-4">
          {listItems.map((item, i) => (
            <li key={i} dangerouslySetInnerHTML={{ __html: sanitizeHtml(item.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')) }} />
          ))}
        </ul>
      );
      listItems = [];
    }
  };

  lines.forEach((line, index) => {
    if (line.startsWith('### ')) {
      renderList();
      elements.push(<h3 key={index} className="text-xl font-semibold mt-4 mb-2" dangerouslySetInnerHTML={{ __html: sanitizeHtml(line.substring(4).replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')) }} />);
    } else if (line.startsWith('## ')) {
      renderList();
      elements.push(<h2 key={index} className="text-2xl font-bold mt-6 mb-3" dangerouslySetInnerHTML={{ __html: sanitizeHtml(line.substring(3).replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')) }} />);
    } else if (line.startsWith('# ')) {
      renderList();
      elements.push(<h1 key={index} className="text-3xl font-extrabold mt-8 mb-4" dangerouslySetInnerHTML={{ __html: sanitizeHtml(line.substring(2).replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')) }} />);
    } else if (line.startsWith('* ') || line.startsWith('- ')) {
      listItems.push(line.substring(2));
    } else if (line.trim() === '') {
       renderList();
       // Add a visual space for paragraph breaks, but not multiple in a row
       if (elements.length > 0 && elements[elements.length-1].type !== 'br' ) {
            elements.push(<br key={`br-${index}`} />);
       }
    } else {
      renderList();
      elements.push(<p key={index} className="my-2" dangerouslySetInnerHTML={{ __html: sanitizeHtml(line.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')) }} />);
    }
  });

  renderList();

  return <div ref={ref} className="text-zinc-800 leading-relaxed document-editor-content">{elements}</div>;
});

export default MarkdownRenderer;
