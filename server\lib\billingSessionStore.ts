import { randomUUID } from 'crypto';
import { logger } from './logger';

export type BillingSessionStatus = 'pending' | 'confirmed' | 'canceled';

export interface BillingSession {
  id: string;
  userId: string;
  planName: string;
  providerSessionId: string | null;
  status: BillingSessionStatus;
  createdAt: number;
}

const sessions = new Map<string, BillingSession>();
const SESSION_TTL_MS = 15 * 60 * 1000; // 15 minutes

interface CreateBillingSessionOptions {
  providerSessionId?: string | null;
  id?: string;
}

function purgeExpiredSessions(now = Date.now()): void {
  for (const [sessionId, session] of sessions.entries()) {
    if (session.status !== 'pending' || now - session.createdAt > SESSION_TTL_MS) {
      sessions.delete(sessionId);
    }
  }
}

export function createBillingSession(
  userId: string,
  planName: string,
  options: CreateBillingSessionOptions = {}
): BillingSession {
  purgeExpiredSessions();
  const session: BillingSession = {
    id: options.id ?? randomUUID(),
    userId,
    planName,
    providerSessionId: options.providerSessionId ?? null,
    status: 'pending',
    createdAt: Date.now(),
  };
  sessions.set(session.id, session);
  logger.info({ sessionId: session.id, userId, planName }, 'Created billing session');
  return session;
}

export function getBillingSession(sessionId: string): BillingSession | undefined {
  purgeExpiredSessions();
  return sessions.get(sessionId);
}

export function getBillingSessionForUser(sessionId: string, userId: string): BillingSession | undefined {
  const session = getBillingSession(sessionId);
  if (!session || session.userId !== userId) {
    return undefined;
  }
  return session;
}

export function setBillingSessionStatus(sessionId: string, status: BillingSessionStatus): BillingSession | undefined {
  const session = sessions.get(sessionId);
  if (!session) {
    return undefined;
  }
  session.status = status;
  sessions.set(sessionId, session);
  logger.info({ sessionId, status, userId: session.userId, planName: session.planName }, 'Updated billing session status');
  return session;
}

export function deleteBillingSession(sessionId: string): void {
  sessions.delete(sessionId);
}

export function resetBillingSessions(): void {
  sessions.clear();
}
