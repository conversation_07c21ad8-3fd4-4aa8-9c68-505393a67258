
import React from 'react';
import { SearchIcon } from '../Icons';

const SearchHelpScreenshot: React.FC = () => {
    return (
        <div className="relative my-6 p-4 border rounded-lg bg-zinc-50 dark:bg-zinc-900 overflow-hidden select-none text-xs h-40">
             <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-2">
                    <SearchIcon className="h-4 w-4 text-zinc-400" />
                </div>
                <input 
                    type="text"
                    placeholder="Search workspace..."
                    defaultValue="NDA"
                    readOnly
                    className="block w-full sm:w-64 rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-8 p-1.5"
                />
            </div>
             <div className="absolute top-16 w-64 rounded-md shadow-lg bg-white dark:bg-zinc-800 ring-1 ring-black dark:ring-zinc-700 ring-opacity-5 z-10">
                 <div className="p-1">
                     <p className="px-2 py-1 text-[10px] font-semibold text-zinc-500">Documents</p>
                     <ul>
                         <li>
                             <div className="px-2 py-1 rounded hover:bg-zinc-100 dark:hover:bg-zinc-700">
                                 <p className="text-zinc-800 dark:text-zinc-100">Sample NDA</p>
                             </div>
                         </li>
                     </ul>
                 </div>
             </div>
        </div>
    );
};

export default SearchHelpScreenshot;
