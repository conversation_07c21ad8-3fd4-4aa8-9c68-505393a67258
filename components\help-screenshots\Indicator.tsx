
import React from 'react';
import { cn } from '../../lib/utils';

interface IndicatorProps {
  number: number;
  position: string; // Tailwind classes for top/left/right/bottom
  arrow?: 'up' | 'down' | 'left' | 'right';
}

const Indicator: React.FC<IndicatorProps> = ({ number, position, arrow }) => {
  const arrowClasses = {
    up: 'border-b-8 border-b-brand-600 -top-2 left-1/2 -translate-x-1/2',
    down: 'border-t-8 border-t-brand-600 -bottom-2 left-1/2 -translate-x-1/2',
    left: 'border-r-8 border-r-brand-600 -left-2 top-1/2 -translate-y-1/2',
    right: 'border-l-8 border-l-brand-600 -right-2 top-1/2 -translate-y-1/2',
  };

  const arrowBaseClasses = 'w-0 h-0 border-transparent absolute';

  return (
    <div className={cn('absolute z-10 flex items-center justify-center', position)}>
      <div className="relative">
        <div className="w-6 h-6 bg-brand-600 rounded-full flex items-center justify-center text-white font-bold text-sm shadow-lg border-2 border-white">
          {number}
        </div>
        {arrow && (
          <div className={cn(arrowBaseClasses, arrowClasses[arrow])} />
        )}
      </div>
    </div>
  );
};

export default Indicator;
