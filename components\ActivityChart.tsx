import React, { useEffect, useRef, useMemo, memo } from 'react';
import { Document as DocType } from '../types';

declare global {
    interface Window {
        Chart: typeof import('chart.js').Chart;
    }
}

interface ActivityChartProps {
    documents: DocType[];
}

const ActivityChart: React.FC<ActivityChartProps> = memo(({ documents }) => {
    const chartRef = useRef<HTMLCanvasElement>(null);
    const chartInstanceRef = useRef<InstanceType<typeof import('chart.js').Chart> | null>(null);

    // Memoize chart data preparation to avoid recalculation on every render
    const chartData = useMemo(() => {
        const labels: string[] = [];
        const data: number[] = [];
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        for (let i = 6; i >= 0; i--) {
            const date = new Date(today);
            date.setDate(today.getDate() - i);
            labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
            
            const count = documents.filter(doc => {
                const docDate = new Date(doc.createdAt);
                docDate.setHours(0, 0, 0, 0);
                return docDate.getTime() === date.getTime();
            }).length;
            data.push(count);
        }

        return { labels, data };
    }, [documents]);

    useEffect(() => {
        if (!chartRef.current || !window.Chart) {return;}

        const ctx = chartRef.current.getContext('2d');
        if (!ctx) {return;}

        // Destroy previous chart instance if it exists
        if (chartInstanceRef.current) {
            chartInstanceRef.current.destroy();
        }

        chartInstanceRef.current = new window.Chart(ctx, {
            type: 'bar',
            data: {
                labels: chartData.labels,
                datasets: [{
                    label: 'Documents Created',
                    data: chartData.data,
                    backgroundColor: 'rgba(59, 130, 246, 0.5)',
                    borderColor: 'rgba(59, 130, 246, 1)',
                    borderWidth: 1,
                    borderRadius: 4,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        
        // Cleanup function
        return () => {
            if (chartInstanceRef.current) {
                chartInstanceRef.current.destroy();
            }
        };

    }, [documents]);

    return (
        <div className="h-80">
            <canvas ref={chartRef}></canvas>
        </div>
    );
});

export default ActivityChart;
