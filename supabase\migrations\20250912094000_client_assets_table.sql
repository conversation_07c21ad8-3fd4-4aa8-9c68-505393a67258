-- Dedicated migration for public.client_assets and its RLS policies
-- Also ensures public.clients exists (required FK) with RLS aligned to repo usage

create extension if not exists pgcrypto;

-- Ensure public.clients exists (idempotent) with RLS policies
create table if not exists public.clients (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  name text not null,
  type text not null check (type in ('Company','Individual')),
  contact_person text,
  email text,
  phone text,
  address text,
  created_at timestamptz not null default now()
);
create index if not exists clients_user_id_idx on public.clients(user_id);
alter table public.clients enable row level security;
drop policy if exists clients_select_own on public.clients;
create policy clients_select_own on public.clients for select using (user_id = auth.uid());
drop policy if exists clients_mutate_own on public.clients;
create policy clients_mutate_own on public.clients for all using (user_id = auth.uid()) with check (user_id = auth.uid());

-- Create public.client_assets table with RLS and policy
create table if not exists public.client_assets (
  id uuid primary key default gen_random_uuid(),
  user_id uuid not null references auth.users(id) on delete cascade,
  client_id uuid not null references public.clients(id) on delete cascade,
  name text not null,
  url text not null,
  created_at timestamptz not null default now()
);
create index if not exists client_assets_user_idx on public.client_assets(user_id);
create index if not exists client_assets_client_idx on public.client_assets(client_id);
alter table public.client_assets enable row level security;
drop policy if exists client_assets_access on public.client_assets;
create policy client_assets_access on public.client_assets for all using (user_id = auth.uid()) with check (user_id = auth.uid());

-- Optional: ensure the storage bucket referenced by server/routes/clientAssets.ts exists
insert into storage.buckets (id, name, public)
select 'contractgini', 'contractgini', false
where not exists (select 1 from storage.buckets where id = 'contractgini');

-- Nudge PostgREST to refresh its schema cache so the API can see new tables immediately
select pg_notify('pgrst', 'reload schema');
