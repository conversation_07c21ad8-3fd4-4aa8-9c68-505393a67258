import React from 'react';
import { Button } from './ui/Button';
import { CloseIcon, CheckCircleIcon, XCircleIcon } from './Icons';
import { FlowRun, User } from '../types';
import { ALL_CONNECTORS } from './connectors';
import { cn } from '../lib/utils';

interface FlowRunDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  run: FlowRun | null;
  user: User;
}

const DataViewer: React.FC<{ title: string; data: unknown }> = ({ title, data }) => (
    <div>
        <h4 className="font-semibold text-zinc-800 dark:text-zinc-200">{title}</h4>
        <pre className="mt-1 p-3 bg-zinc-100 dark:bg-zinc-800 rounded-md text-xs text-zinc-700 dark:text-zinc-300 overflow-x-auto">
            <code>{JSON.stringify(data, null, 2)}</code>
        </pre>
    </div>
);


const FlowRunDetailsModal: React.FC<FlowRunDetailsModalProps> = ({ isOpen, onClose, run, user }) => {
    if (!isOpen || !run) {return null;}

    const flow = user.flows?.find(f => f.id === run.flowId);
    if (!flow) {return null;}

    const getConnector = (connectionId: string) => {
        const connection = user.connections?.find(c => c.id === connectionId);
        return ALL_CONNECTORS.find(c => c.id === connection?.connectorId);
    };

    const triggerConnector = getConnector(flow.trigger.connectionId);
    const actionConnector = getConnector(flow.action.connectionId);
    const TriggerIcon = triggerConnector?.icon;
    const ActionIcon = actionConnector?.icon;
    
    const StatusDisplay = () => {
        const isSuccess = run.status === 'success';
        const isFailed = run.status === 'failed';
        const Icon = isSuccess ? CheckCircleIcon : isFailed ? XCircleIcon : null;
        
        const wrapperClasses = isSuccess 
            ? 'bg-green-50 dark:bg-green-900/30 border border-green-200 dark:border-green-800' 
            : 'bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800';
        
        const iconClasses = isSuccess ? 'text-green-500' : 'text-red-500';
        const titleClasses = isSuccess ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200';
        const textClasses = isSuccess ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300';
    
        if (run.status === 'running') {
            return null;
        }
    
        return (
            <div className={cn('p-4 rounded-lg', wrapperClasses)}>
                <div className="flex items-start gap-3">
                    {Icon && <Icon className={cn('w-6 h-6 flex-shrink-0', iconClasses)} />}
                    <div>
                        <h3 className={cn('font-semibold capitalize', titleClasses)}>
                            Run {run.status}
                        </h3>
                        {isFailed && run.error && (
                            <p className={cn('mt-1 text-sm', textClasses)}>{run.error}</p>
                        )}
                         {isSuccess && (
                            <p className={cn('mt-1 text-sm', textClasses)}>The flow ran successfully.</p>
                        )}
                    </div>
                </div>
            </div>
        );
    };

    return (
        <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-2xl h-[80vh] flex flex-col">
                <header className="flex items-center justify-between p-4 border-b dark:border-zinc-800">
                    <div className="flex items-center gap-2">
                         {TriggerIcon && <TriggerIcon className="w-6 h-6"/>}
                         <span className="font-bold">&rarr;</span>
                         {ActionIcon && <ActionIcon className="w-6 h-6"/>}
                         <h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200">{flow.name}</h2>
                    </div>
                    <Button variant="ghost" size="icon" onClick={onClose}><CloseIcon className="w-5 h-5"/></Button>
                </header>
                <main className="p-6 flex-1 overflow-y-auto space-y-6">
                    <div className="text-sm text-zinc-500 dark:text-zinc-400">
                        <p><strong>Run ID:</strong> {run.id}</p>
                        <p><strong>Started:</strong> {new Date(run.startedAt).toLocaleString()}</p>
                    </div>

                    <StatusDisplay />
                    
                    {run.triggerData && <DataViewer title="Data In (from Trigger)" data={run.triggerData} />}
                    {run.actionData && <DataViewer title="Data Out (to Action)" data={run.actionData} />}

                </main>
                <footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t dark:border-zinc-800">
                    <Button variant="outline" onClick={onClose}>Close</Button>
                </footer>
            </div>
        </div>
    );
};

export default FlowRunDetailsModal;