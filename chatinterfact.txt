

import React, { useState, useRef, useEffect, useCallback } from 'react';
import type { Chat } from '@google/ai/generativelanguage';
import { ChatMessage, MessageRole, User } from '../types';
import { INITIAL_MESSAGE, ANONYMOUS_USER_QUOTA } from '../constants';
import { createChatSession, sendMessageToChatStream } from '../services/geminiService';
import { UserIcon, BotIcon, SendIcon, DocumentIcon } from './Icons';
import MarkdownRenderer from './MarkdownRenderer';
import DocumentViewerModal from './DocumentViewerModal';
import { cn } from '../lib/utils';

const SUGGESTED_PROMPTS = [
    "Draft a simple Non-Disclosure Agreement (NDA)",
    "Create a freelance contract for a web developer",
    "Write a basic rental lease agreement clause",
];

interface ChatInterfaceProps {
    setView?: (view: 'home' | 'auth' | 'dashboard') => void;
    user?: User | null;
    onSaveDocument?: (content: string, name: string, folderId: string | null, clientId: string | null) => void;
    initialPrompt?: string | null;
    onInitialPromptHandled?: () => void;
    isDemo?: boolean;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ setView, user, onSaveDocument, initialPrompt, onInitialPromptHandled, isDemo = false }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([INITIAL_MESSAGE]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [anonymousQuota, setAnonymousQuota] = useState(ANONYMOUS_USER_QUOTA);
  const [reviewModalContent, setReviewModalContent] = useState<string | null>(null);

  const chatRef = useRef<Chat | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const isAnonymous = !user;
  const quota = isAnonymous ? anonymousQuota : (user?.quotaTotal || 0) - (user?.quotaUsed || 0);
  const atQuotaLimit = quota <= 0;

  useEffect(() => {
    chatRef.current = createChatSession();
  }, []);
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(scrollToBottom, [messages]);

  const handleSendMessage = useCallback(async (prompt: string) => {
    if (!prompt.trim() || isLoading || !chatRef.current) return;
    
    if (atQuotaLimit) {
      setError(isAnonymous ? "You have reached your free generation limit for this session." : "You have reached your monthly generation limit.");
      return;
    }

    const userMessage: ChatMessage = { id: Date.now().toString(), role: MessageRole.USER, content: prompt };
    const modelPlaceholder: ChatMessage = { id: `${Date.now()}-streaming`, role: MessageRole.MODEL, content: '' };
    
    setMessages(prev => prev.length === 1 && prev[0].id === 'initial-message' ? [userMessage, modelPlaceholder] : [...prev, userMessage, modelPlaceholder]);
    setInput('');
    setIsLoading(true);
    setError(null);
    
    let fullResponse = "";
    await sendMessageToChatStream(chatRef.current, prompt, (chunk) => {
        fullResponse += chunk;
        setMessages(prev => {
            const newMessages = [...prev];
            const lastMsg = newMessages[newMessages.length - 1];
            if (lastMsg && lastMsg.role === MessageRole.MODEL) {
                lastMsg.content = fullResponse;
            }
            return newMessages;
        });
    });
    
    const contractStartMarker = '---CONTRACT START---';
    const contractEndMarker = '---CONTRACT END---';
    let isContract = false;
    let contractText = '';

    setMessages(prev => {
        const newMessages = [...prev];
        const lastMsg = newMessages.pop();
        if(!lastMsg) return prev;

        if (fullResponse.includes(contractStartMarker) && fullResponse.includes(contractEndMarker)) {
            isContract = true;
            const preContractText = fullResponse.substring(0, fullResponse.indexOf(contractStartMarker)).trim();
            contractText = fullResponse.substring(fullResponse.indexOf(contractStartMarker) + contractStartMarker.length, fullResponse.indexOf(contractEndMarker)).trim();
            
            if (preContractText) {
                newMessages.push({ id: `${lastMsg.id}-pre`, role: MessageRole.MODEL, content: preContractText });
            }
            newMessages.push({ id: `${lastMsg.id}-contract`, role: MessageRole.MODEL, content: contractText, isContract: true });
        } else {
            lastMsg.content = fullResponse;
            newMessages.push(lastMsg);
        }
        return newMessages;
    });
    
    // Auto-open modal if a contract was generated
    if (isContract) {
        setReviewModalContent(contractText);
    }
    
    setIsLoading(false);

  }, [isLoading, atQuotaLimit, isAnonymous]);

  useEffect(() => {
      if (initialPrompt && onInitialPromptHandled) {
          handleSendMessage(initialPrompt);
          onInitialPromptHandled();
      }
  }, [initialPrompt, onInitialPromptHandled, handleSendMessage]);


  const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      handleSendMessage(input);
  }
  
  const handleSaveFromModal = (documentName: string, folderId: string | null, htmlContent: string, clientId: string | null) => {
      if (isAnonymous) {
        setAnonymousQuota(prev => prev - 1);
        console.log(`Anonymous user "saved" document: ${documentName}. Quota remaining: ${anonymousQuota - 1}`);
      } else if (onSaveDocument) {
        onSaveDocument(htmlContent, documentName, folderId, clientId);
      }
      
      setReviewModalContent(null);
    };

  if (isDemo) {
    const demoMessages: ChatMessage[] = [
      { id: 'demo1', role: MessageRole.USER, content: "Draft a simple Non-Disclosure Agreement for me." },
      { id: 'demo2', role: MessageRole.MODEL, content: "Of course, I can help with that. Generating the NDA now..." },
      { id: 'demo3', role: MessageRole.MODEL, content: `<h1>Mutual Non-Disclosure Agreement</h1><p>This Agreement is made between Party A and Party B...</p>`, isContract: true },
    ];
    return (
      <div className="flex flex-col h-[500px] bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-2xl shadow-2xl shadow-zinc-200/50 dark:shadow-black/20">
        <div className="flex-1 overflow-y-auto p-6 space-y-6 pointer-events-none">
          {demoMessages.map((msg) => (
            <div key={msg.id} className={cn('flex items-start gap-4', msg.role === MessageRole.USER ? 'justify-end' : '')}>
              {msg.role === MessageRole.MODEL && (
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
                  <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
                </div>
              )}
              <div className={cn('w-full max-w-xl', msg.role === MessageRole.USER ? 'order-2' : '')}>
                {msg.isContract ? (
                  <div className="bg-zinc-50 dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-4">
                    <div className="flex items-center text-zinc-700 dark:text-zinc-200 font-semibold mb-3">
                      <DocumentIcon className="w-5 h-5 mr-2" />
                      <span>Generated Legal Document</span>
                    </div>
                    <div className="max-h-48 overflow-y-auto p-2 bg-white dark:bg-zinc-900 rounded border border-zinc-200 dark:border-zinc-700 text-sm text-zinc-800 dark:text-zinc-300">
                      <MarkdownRenderer content={msg.content.split('\n').slice(0, 10).join('\n') + "\n..."} />
                    </div>
                    <div className="mt-3">
                      <button className="w-full text-center px-4 py-2 text-sm font-semibold text-white bg-brand-600 rounded-lg shadow-sm">
                        Review & Save
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className={cn('px-5 py-3 rounded-2xl', msg.role === MessageRole.USER ? 'bg-brand-600 text-white' : 'bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200')}>
                    <MarkdownRenderer content={msg.content} />
                  </div>
                )}
              </div>
              {msg.role === MessageRole.USER && (
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center order-1">
                  <UserIcon className="w-6 h-6 text-zinc-600 dark:text-zinc-300" />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  }


  return (
    <>
    <div className="flex flex-col h-[70vh] max-h-[700px] bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-2xl shadow-2xl shadow-zinc-200/50 dark:shadow-black/20">
      <div className="flex-1 overflow-y-auto p-6 space-y-6">
        {messages.map((msg, index) => (
          <div key={msg.id} className={`flex items-start gap-4 ${msg.role === MessageRole.USER ? 'justify-end' : ''}`}>
            {msg.role === MessageRole.MODEL && (
              <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
                <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
              </div>
            )}
             <div className={`w-full max-w-xl ${msg.role === MessageRole.USER ? 'order-2' : ''}`}>
                {msg.isContract ? (
                    <div className="bg-zinc-50 dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-4">
                        <div className="flex items-center text-zinc-700 dark:text-zinc-200 font-semibold mb-3">
                            <DocumentIcon className="w-5 h-5 mr-2" />
                            <span>Generated Legal Document</span>
                        </div>
                        <div className="max-h-48 overflow-y-auto p-2 bg-white dark:bg-zinc-900 rounded border border-zinc-200 dark:border-zinc-700 text-sm text-zinc-800 dark:text-zinc-300">
                            <MarkdownRenderer content={msg.content.split('\n').slice(0, 10).join('\n') + "\n..."} />
                        </div>
                        <div className="mt-3">
                            <button 
                                onClick={() => setReviewModalContent(msg.content)}
                                className="w-full text-center px-4 py-2 text-sm font-semibold text-white bg-brand-600 hover:bg-brand-700 rounded-lg shadow-sm"
                            >
                                Review & Save
                            </button>
                        </div>
                    </div>
                ) : (
                    <div className={`px-5 py-3 rounded-2xl ${msg.role === MessageRole.USER ? 'bg-brand-600 text-white' : 'bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200'}`}>
                         <MarkdownRenderer content={msg.content} />
                         {isLoading && index === messages.length -1 && <span className="inline-block w-2 h-4 bg-zinc-600 dark:bg-zinc-400 animate-pulse ml-1" />}
                    </div>
                )}
            </div>
            {msg.role === MessageRole.USER && (
              <div className="flex-shrink-0 w-10 h-10 rounded-full bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center order-1">
                <UserIcon className="w-6 h-6 text-zinc-600 dark:text-zinc-300" />
              </div>
            )}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>
      <div className="p-4 border-t border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900 rounded-b-2xl">
        <div className="mb-3 text-center">
            { !isLoading && !atQuotaLimit && (
                <div className="flex flex-wrap justify-center gap-2">
                    {SUGGESTED_PROMPTS.map(prompt => (
                        <button key={prompt} onClick={() => handleSendMessage(prompt)} className="px-3 py-1.5 bg-zinc-100 dark:bg-zinc-800 hover:bg-zinc-200 dark:hover:bg-zinc-700 text-zinc-700 dark:text-zinc-300 text-sm rounded-full transition">
                            {prompt}
                        </button>
                    ))}
                </div>
            )}
        </div>
        <div className="mb-2 text-center text-sm text-zinc-500 dark:text-zinc-400">
            {atQuotaLimit ? (
                isAnonymous ? (
                    <span>
                        Quota limit reached. {setView && <button onClick={() => setView('auth')} className="font-medium text-brand-600 hover:text-brand-700">Sign up</button>} for more generations.
                    </span>
                ) : (
                    <span>Monthly quota reached. Upgrade to Premium for unlimited generations.</span>
                )
             ) : (
                <span>You have {quota} generation{quota !== 1 ? 's' : ''} remaining.</span>
            )}
        </div>
        <form onSubmit={handleSubmit} className="flex items-center gap-2">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={atQuotaLimit ? (isAnonymous ? "Sign up to continue." : "Upgrade to continue.") : "Describe the contract you need..."}
            className="flex-1 w-full px-4 py-3 bg-zinc-100 dark:bg-zinc-800 rounded-xl focus:outline-none focus:ring-2 focus:ring-brand-500 transition disabled:opacity-50"
            disabled={isLoading || atQuotaLimit}
          />
          <button
            type="submit"
            className="flex-shrink-0 w-12 h-12 bg-brand-600 text-white rounded-xl flex items-center justify-center hover:bg-brand-700 disabled:bg-zinc-300 dark:disabled:bg-zinc-700 transition-colors shadow-sm"
            disabled={isLoading || !input.trim() || atQuotaLimit}
          >
            <SendIcon className="w-6 h-6" />
          </button>
        </form>
         {error && <p className="text-red-500 text-sm text-center mt-2">{error}</p>}
      </div>
    </div>
    <p className="text-xs text-zinc-500 dark:text-zinc-400 text-center mt-4 max-w-2xl mx-auto">
        Disclaimer: This document was generated by an AI and is not a substitute for professional legal advice. Please consult with a qualified attorney before using this contract.
    </p>
    <DocumentViewerModal 
        isOpen={!!reviewModalContent}
        onClose={() => setReviewModalContent(null)}
        contractContent={reviewModalContent || ''}
        onSave={handleSaveFromModal}
        user={user}
    />
    </>
  );
};

export default ChatInterface;