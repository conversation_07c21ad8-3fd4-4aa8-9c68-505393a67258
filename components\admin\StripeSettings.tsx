
import React, { useState, useEffect } from 'react';
import { StripeConfig } from '../../types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { cn } from '../../lib/utils';

interface StripeSettingsProps {
  stripeConfig: StripeConfig;
  onUpdateStripeConfig: (config: StripeConfig) => void;
}

const StripeSettings: React.FC<StripeSettingsProps> = ({ stripeConfig, onUpdateStripeConfig }) => {
  const [config, setConfig] = useState<StripeConfig>(stripeConfig);
  const [isSaved, setIsSaved] = useState(false);

  useEffect(() => {
    setConfig(stripeConfig);
  }, [stripeConfig]);

  const handleInputChange = (field: keyof StripeConfig, value: string | boolean) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onUpdateStripeConfig(config);
    setIsSaved(true);
    setTimeout(() => setIsSaved(false), 2000);
  };

  return (
    <Card className="mt-4">
      <CardHeader>
        <CardTitle>Stripe Configuration</CardTitle>
        <CardDescription>Manage your Stripe API keys and webhook settings for payment processing.</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6 max-w-2xl">
          <div className="flex items-center justify-between p-3 bg-zinc-50 rounded-lg border dark:bg-zinc-800/50 dark:border-zinc-800">
            <div>
              <label className="font-medium text-zinc-800 dark:text-zinc-200">Stripe Live Mode</label>
              <p className="text-sm text-zinc-500 dark:text-zinc-400">
                {config.isLiveMode ? 'Live transactions are being processed.' : 'Currently in Test Mode.'}
              </p>
            </div>
            <button
              type="button"
              role="switch"
              aria-checked={config.isLiveMode}
              onClick={() => handleInputChange('isLiveMode', !config.isLiveMode)}
              className={cn('relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors', config.isLiveMode ? 'bg-brand-600' : 'bg-zinc-200 dark:bg-zinc-700')}
            >
              <span className={cn('pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition', config.isLiveMode ? 'translate-x-5' : 'translate-x-0')}/>
            </button>
          </div>
          <div className="space-y-4">
            <h4 className="font-medium text-zinc-800 dark:text-zinc-200">Live Keys</h4>
            <div><label className="block text-sm font-medium">Live Publishable Key</label><input type="password" value={config.livePublishableKey} onChange={e => handleInputChange('livePublishableKey', e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" /></div>
            <div><label className="block text-sm font-medium">Live Secret Key</label><input type="password" value={config.liveSecretKey} onChange={e => handleInputChange('liveSecretKey', e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" /></div>
          </div>
          <div className="space-y-4">
            <h4 className="font-medium text-zinc-800 dark:text-zinc-200">Test Keys</h4>
            <div><label className="block text-sm font-medium">Test Publishable Key</label><input type="password" value={config.testPublishableKey} onChange={e => handleInputChange('testPublishableKey', e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" /></div>
            <div><label className="block text-sm font-medium">Test Secret Key</label><input type="password" value={config.testSecretKey} onChange={e => handleInputChange('testSecretKey', e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" /></div>
          </div>
          <div>
            <label className="block text-sm font-medium">Webhook Signing Secret</label>
            <input type="password" value={config.webhookSecret} onChange={e => handleInputChange('webhookSecret', e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" />
            <p className="text-xs text-zinc-500 mt-1">Found in your Stripe dashboard, used to verify webhook events.</p>
          </div>
          <div className="flex items-center gap-4 pt-4 border-t dark:border-zinc-800">
            <Button type="submit">Save Stripe Settings</Button>
            {isSaved && <span className="text-sm font-medium text-green-600 dark:text-green-400">Settings saved!</span>}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default StripeSettings;
