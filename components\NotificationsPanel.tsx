import React from 'react';
import { Notification } from '../types';
import { Button } from './ui/Button';
import { BellIcon } from './Icons';
import { cn } from '../lib/utils';

function timeAgo(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  let interval = seconds / 31536000;
  if (interval > 1) {return Math.floor(interval) + " years ago";}
  interval = seconds / 2592000;
  if (interval > 1) {return Math.floor(interval) + " months ago";}
  interval = seconds / 86400;
  if (interval > 1) {return Math.floor(interval) + " days ago";}
  interval = seconds / 3600;
  if (interval > 1) {return Math.floor(interval) + " hours ago";}
  interval = seconds / 60;
  if (interval > 1) {return Math.floor(interval) + " minutes ago";}
  return Math.floor(seconds) + " seconds ago";
}


interface NotificationsPanelProps {
  notifications: Notification[];
  onMarkAllRead: () => void;
  onNotificationClick: (notification: Notification) => void;
  onClose: () => void;
  onViewAll: () => void;
}

const NotificationsPanel: React.FC<NotificationsPanelProps> = ({ notifications, onMarkAllRead, onNotificationClick, onClose: _onClose, onViewAll }) => {
    const sortedNotifications = [...notifications].sort((a,b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return (
        <div 
            className="origin-top-right absolute right-0 mt-2 w-80 rounded-lg shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-10 flex flex-col"
            style={{ maxHeight: 'calc(100vh - 80px)' }}
        >
            <header className="p-4 border-b flex items-center justify-between">
                <h3 className="font-semibold text-slate-900">Notifications</h3>
                <Button variant="link" className="text-sm p-0 h-auto" onClick={onMarkAllRead}>Mark all as read</Button>
            </header>
            <div className="flex-1 overflow-y-auto">
                {sortedNotifications.length > 0 ? (
                    <ul className="divide-y divide-slate-200">
                        {sortedNotifications.slice(0, 10).map(notif => (
                            <li key={notif.id}>
                                <button
                                    onClick={() => onNotificationClick(notif)}
                                    className="w-full text-left p-4 hover:bg-slate-50 flex items-start gap-3"
                                >
                                    {!notif.isRead && (
                                        <div className="w-2.5 h-2.5 rounded-full bg-blue-500 mt-1.5 flex-shrink-0"></div>
                                    )}
                                    <div className={cn("flex-1", notif.isRead ? 'pl-5' : '')}>
                                        <p className={cn("text-sm text-slate-800", !notif.isRead && "font-semibold")}>
                                            {notif.message}
                                        </p>
                                        <p className="text-xs text-slate-500 mt-1">{timeAgo(notif.createdAt)}</p>
                                    </div>
                                </button>
                            </li>
                        ))}
                    </ul>
                ) : (
                    <div className="p-12 text-center text-sm text-slate-500">
                        <BellIcon className="w-10 h-10 mx-auto text-slate-300 mb-2"/>
                        <p>You're all caught up!</p>
                    </div>
                )}
            </div>
            <footer className="p-2 border-t bg-slate-50 rounded-b-lg">
                <Button variant="link" className="w-full text-sm text-brand-600" onClick={onViewAll}>
                    View all notifications
                </Button>
            </footer>
        </div>
    );
};

export default NotificationsPanel;