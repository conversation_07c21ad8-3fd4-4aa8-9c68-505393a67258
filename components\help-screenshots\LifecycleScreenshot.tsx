


import React from 'react';

const StatCard = ({ title, value }: { title: string, value: string }) => (
    <div className="bg-white rounded-lg shadow p-3">
        <p className="text-xs text-zinc-500">{title}</p>
        <p className="text-xl font-bold text-zinc-800">{value}</p>
    </div>
);

const KanbanCard = ({ title, date }: { title: string, date: string }) => (
    <div className="bg-white p-2.5 rounded-md shadow border border-zinc-200">
        <p className="font-medium text-xs text-zinc-800 truncate">{title}</p>
        <p className="text-xs text-zinc-500 mt-0.5">Updated: {date}</p>
    </div>
);

// FIX: Made the `children` prop optional to allow for empty columns, resolving the TypeScript error.
const KanbanColumn = ({ title, children }: { title: string, children?: React.ReactNode }) => (
    <div className="bg-zinc-100 rounded-lg p-2 flex-1">
        <h3 className="font-semibold text-zinc-600 text-xs text-center capitalize mb-2 p-1.5 bg-zinc-200 rounded-md">
            {title}
        </h3>
        <div className="space-y-2">
            {children}
        </div>
    </div>
);

const LifecycleScreenshot = () => {
    return (
        <div className="w-full h-full bg-zinc-50 p-6 overflow-hidden select-none">
             <div className="grid grid-cols-4 gap-4 mb-6">
                <StatCard title="Active Contracts" value="0" />
                <StatCard title="Pending Signatures" value="1" />
                <StatCard title="Archived Contracts" value="0" />
                <StatCard title="Expiring Soon (90d)" value="0" />
            </div>

            <h2 className="text-lg font-bold text-zinc-900 mb-3">Contract Pipeline</h2>

            <div className="flex gap-4">
                <KanbanColumn title="Draft">
                    <KanbanCard title="Freelance Contract" date="9/8/2025" />
                    <KanbanCard title="Last Will" date="9/6/2025" />
                    <KanbanCard title="Uncategorized Doc" date="9/5/2025" />
                </KanbanColumn>
                <KanbanColumn title="Out For-Signature">
                    <KanbanCard title="Sample NDA" date="9/10/2025" />
                </KanbanColumn>
                <KanbanColumn title="Completed" />
                <KanbanColumn title="Archived" />
            </div>
        </div>
    );
};

export default LifecycleScreenshot;