import React, { useMemo } from 'react';
import { User, DashboardView, Document as DocType, Obligation } from '../types';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from './ui/Card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/Table';
import { Button } from './ui/Button';
import { LockSolidIcon } from './Icons';
import { cn } from '../lib/utils';

interface ObligationsPageProps {
  user: User;
  setView: (view: DashboardView) => void;
  onViewDocument: (doc: DocType) => void;
  onUpdateObligationStatus: (docId: string, obligationId: string, status: Obligation['status']) => void;
}

const ObligationsPage: React.FC<ObligationsPageProps> = ({ user, setView, onViewDocument, onUpdateObligationStatus }) => {
    
    const allObligations = useMemo(() => {
        return user.documents
            .flatMap(doc => (doc.obligations || []).map(ob => ({ ...ob, docName: doc.name })))
            .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime());
    }, [user.documents]);

    const daysRemaining = (dateStr: string) => {
        const diff = new Date(dateStr).getTime() - new Date().getTime();
        const days = Math.ceil(diff / (1000 * 60 * 60 * 24));
        if (days < 0) {return { text: 'Overdue', color: 'text-red-600 dark:text-red-400' };}
        if (days === 0) {return { text: 'Today', color: 'text-amber-600 dark:text-amber-400' };}
        return { text: `${days} days`, color: 'text-zinc-600 dark:text-zinc-400' };
    }

    const isPremium = user.planName === 'Premium' || user.planName === 'Enterprise';

    if (!isPremium) {
        return (
            <div className="p-4 sm:p-6 lg:p-8 h-full flex items-center justify-center">
                <div className="text-center">
                    <LockSolidIcon className="w-12 h-12 mx-auto text-zinc-300 dark:text-zinc-700 mb-4" />
                    <h3 className="text-xl font-semibold text-zinc-800 dark:text-zinc-200">Unlock Obligation Tracking</h3>
                    <p className="text-zinc-500 dark:text-zinc-400 mt-2 mb-4 max-w-md">
                        Upgrade to Premium to automatically track key dates and contractual obligations.
                    </p>
                    <Button onClick={() => setView('subscription')}>
                        Upgrade Now
                    </Button>
                </div>
            </div>
        );
    }
    
    return (
        <div className="p-4 sm:p-6 lg:p-8">
            <Card>
                <CardHeader>
                    <CardTitle>All Obligations</CardTitle>
                    <CardDescription>A consolidated view of all contractual obligations and deadlines.</CardDescription>
                </CardHeader>
                <CardContent>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Obligation</TableHead>
                                <TableHead>Document</TableHead>
                                <TableHead>Due Date</TableHead>
                                <TableHead>Time Left</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {allObligations.map(ob => {
                                const timeLeft = daysRemaining(ob.dueDate);
                                return (
                                <TableRow key={ob.id}>
                                    <TableCell className="max-w-xs truncate">{ob.description}</TableCell>
                                    <TableCell>
                                        <button onClick={() => {
                                            const doc = user.documents.find(d => d.id === ob.documentId);
                                            if (doc) { onViewDocument(doc); }
                                        }} className="font-medium text-brand-600 hover:underline">
                                            {ob.docName}
                                        </button>
                                    </TableCell>
                                    <TableCell>{new Date(ob.dueDate).toLocaleDateString()}</TableCell>
                                    <TableCell className={cn('font-medium', timeLeft.color)}>{timeLeft.text}</TableCell>
                                    <TableCell className="capitalize">{ob.status}</TableCell>
                                    <TableCell className="text-right">
                                        {ob.status === 'pending' && (
                                            <Button size="sm" variant="outline" onClick={() => onUpdateObligationStatus(ob.documentId, ob.id, 'completed')}>
                                                Mark as Complete
                                            </Button>
                                        )}
                                    </TableCell>
                                </TableRow>
                                );
                            })}
                        </TableBody>
                    </Table>
                </CardContent>
            </Card>
        </div>
    );
};

export default ObligationsPage;
