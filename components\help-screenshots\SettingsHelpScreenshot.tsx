
import React from 'react';
import Indicator from './Indicator';
import { UserIcon, PaletteIcon, ShieldCheckIcon } from '../Icons';

const SettingsHelpScreenshot: React.FC = () => {
    return (
        <div className="relative my-6 p-4 border rounded-lg bg-zinc-50 dark:bg-zinc-900 overflow-hidden select-none text-xs">
            <Indicator number={1} position="top-24 left-10" arrow="right" />
            <Indicator number={2} position="top-24 left-1/2" arrow="down" />
            <Indicator number={3} position="top-24 right-10" arrow="left" />
            
            <h1 className="text-lg font-bold text-zinc-900 dark:text-zinc-100">Settings</h1>
            <p className="text-zinc-600 dark:text-zinc-400 mt-1 mb-4">Manage your account settings.</p>

            <div className="grid grid-cols-3 gap-1 p-1 bg-zinc-200 dark:bg-zinc-800 rounded-lg">
                <div className="bg-white dark:bg-zinc-950 text-brand-600 dark:text-zinc-100 font-semibold p-2 rounded-md flex items-center justify-center shadow-sm">
                    <UserIcon className="w-4 h-4 mr-1"/> Profile
                </div>
                 <div className="text-zinc-600 dark:text-zinc-300 p-2 rounded-md flex items-center justify-center">
                    <PaletteIcon className="w-4 h-4 mr-1"/> Appearance
                </div>
                 <div className="text-zinc-600 dark:text-zinc-300 p-2 rounded-md flex items-center justify-center">
                    <ShieldCheckIcon className="w-4 h-4 mr-1"/> Security
                </div>
            </div>
            
             <div className="mt-2 w-full h-24 border-2 border-dashed border-zinc-200 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800/50"></div>
        </div>
    );
};

export default SettingsHelpScreenshot;
