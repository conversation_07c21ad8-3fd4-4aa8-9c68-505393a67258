import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient } from '../supabaseClient';

const router = Router({ mergeParams: true });

const insertSchema = z.object({
  email: z.string().email(),
  permission: z.enum(['view', 'edit']),
  avatarUrl: z.string().url().optional(),
});

const updateSchema = insertSchema.partial();

// documentId param is required
router.get('/:documentId/collaborators', requireAuth, async (req: AuthRequest<{ documentId: string }>, res) => {
  const { documentId } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('document_collaborators').select('*').eq('document_id', documentId);
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ collaborators: data });
});

router.post('/:documentId/collaborators', requireAuth, async (req: AuthRequest<{ documentId: string }>, res) => {
  const { documentId } = req.params;
  const parsed = insertSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa
    .from('document_collaborators')
    .insert({
      document_id: documentId,
      email: parsed.data.email,
      permission: parsed.data.permission,
      avatar_url: parsed.data.avatarUrl,
    })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ collaborator: data });
});

router.put('/collaborators/:id', requireAuth, async (req: AuthRequest<{ id: string }>, res) => {
  const { id } = req.params;
  const parsed = updateSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{
    email: string;
    permission: string;
    avatar_url: string;
  }> = {
    email: parsed.data.email,
    permission: parsed.data.permission,
    avatar_url: parsed.data.avatarUrl,
  };
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa.from('document_collaborators').update(patch).eq('id', id).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ collaborator: data });
});

router.delete('/collaborators/:id', requireAuth, async (req: AuthRequest<{ id: string }>, res) => {
  const { id } = req.params;
  const supa = getUserClient(getAccessToken(req));
  const { error } = await supa.from('document_collaborators').delete().eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

export default router;
