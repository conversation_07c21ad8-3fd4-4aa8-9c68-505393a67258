import { describe, it, expect, beforeAll, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

vi.mock('../server/supabaseClient', () => {
  const getUserClient = (_token: string) => ({
    from: (_table: string) => ({
      select: () => ({
        order: () => ({ data: [{ id: 'a1', document_id: 'd1', email: '<EMAIL>', status: 'pending' }], error: null })
      }),
      insert: (row: Record<string, unknown>) => ({
        select: () => ({
          single: () => Promise.resolve({
            data: {
              id: 'a2',
              document_id: row.document_id || row.documentId || 'd1',
              email: row.email,
              status: row.status,
              comments: row.comments ?? '',
            },
            error: null
          })
        })
      })
    })
  });
  return { getUserClient };
});

beforeAll(() => { process.env.TEST_BYPASS_AUTH = '1'; });

describe('Approvers routes', () => {
  it('GET /api/approvers returns approvers', async () => {
    const res = await request(app).get('/api/approvers');
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('approvers');
    expect(Array.isArray(res.body.approvers)).toBe(true);
  });

  it('POST /api/approvers creates approver', async () => {
    const res = await request(app)
      .post('/api/approvers')
      .send({
  documentId: '11111111-1111-1111-1111-111111111111',
        email: '<EMAIL>',
        status: 'pending',
        comments: ''
      })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(201);
    expect(res.body).toHaveProperty('approver');
  });
});
