import { describe, it, expect, beforeAll, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

vi.mock('../server/supabaseClient', () => {
  const getUserClient = (_token: string) => ({
    from: (_table: string) => ({
      select: (_cols: string) => ({ order: async () => ({ data: [], error: null }) }),
      insert: (row: Record<string, unknown>) => ({ select: () => ({ single: async () => ({ data: { id: 'sig-1', ...row }, error: null }) }) }),
      update: (patch: Record<string, unknown>) => ({ eq: (_c: string, id: string) => ({ select: () => ({ single: async () => ({ data: { id, ...patch }, error: null }) }) }) }),
      delete: () => ({ eq: async () => ({ error: null }) }),
    }),
  });
  return { getUserClient };
});

beforeAll(() => { process.env.TEST_BYPASS_AUTH = '1'; });

describe('Signatures routes', () => {
  it('GET /api/signatures returns list', async () => {
    const res = await request(app).get('/api/signatures');
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('signatures');
  });

  it('POST /api/signatures creates signature', async () => {
    const res = await request(app)
      .post('/api/signatures')
  .send({ documentId: '11111111-1111-1111-1111-111111111111', email: '<EMAIL>', role: 'Signer', token: 'tok' })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(201);
    expect(res.body).toHaveProperty('signature');
  });

  it('PUT /api/signatures/:id updates signature', async () => {
    const res = await request(app)
      .put('/api/signatures/sig-1')
      .send({ status: 'signed', signedAt: new Date().toISOString() })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(200);
  });
});

