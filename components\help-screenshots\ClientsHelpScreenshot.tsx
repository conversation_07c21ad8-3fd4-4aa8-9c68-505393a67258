
import React from 'react';
import { UsersIcon, PlusCircleIcon } from '../Icons';

const ClientsHelpScreenshot = () => {
  return (
    <div className="relative my-6 p-4 border rounded-lg bg-zinc-50 dark:bg-zinc-900 overflow-hidden select-none text-xs">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h1 className="text-lg font-bold text-zinc-900 dark:text-zinc-100">Clients</h1>
        </div>
        <div className="bg-brand-600 text-white font-semibold px-3 py-1.5 rounded-lg shadow flex items-center">
          <PlusCircleIcon className="w-4 h-4 mr-1"/> New Client
        </div>
      </div>

      <div className="bg-white dark:bg-zinc-800 rounded-lg shadow border border-zinc-200 dark:border-zinc-700 p-3">
         <div className="w-full h-24 border-2 border-dashed border-zinc-200 dark:border-zinc-700 rounded-md flex items-center justify-center text-center text-zinc-400">
             <div>
                <UsersIcon className="w-8 h-8 mx-auto" />
                <p>Client list appears here</p>
             </div>
         </div>
      </div>
    </div>
  );
};

export default ClientsHelpScreenshot;
