import { Connector } from '../../types';
import { QuickBooksIcon } from '../Icons';

export const quickbooksConnector: Connector = {
    id: 'quickbooks',
    name: 'QuickBooks Online',
    description: 'Automate invoicing and sync clients with your accounting software.',
    icon: QuickBooksIcon,
    auth: {
        type: 'apiKey', // Simplified for demo; would be OAuth2 in reality
        fields: [
            { key: 'api_key', label: 'API Key', type: 'password', helpText: 'Found in your QuickBooks developer settings.' },
            { key: 'company_id', label: 'Company ID', type: 'text', helpText: 'Your QuickBooks company identifier.' },
        ],
    },
    actions: [
        {
            key: 'createInvoice',
            name: 'Create Invoice',
            description: 'Creates a new invoice for a customer.',
            operation: {
                // In a real app, this would be the function that makes the API call
                perform: async (
                    data: {
                        customerId: string;
                        amount: number;
                        itemId: string;
                        dueDate?: string;
                        description?: string;
                    },
                    credentials: Record<string, unknown>
                ) => {
                    const { api_key, company_id, accessToken } = credentials as Record<string, string>;
                    const token = accessToken || api_key;

                    if (!company_id || !token) {
                        throw new Error('Missing QuickBooks credentials');
                    }

                    const response = await fetch(
                        `https://quickbooks.api.intuit.com/v3/company/${company_id}/invoice`,
                        {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                Authorization: `Bearer ${token}`,
                            },
                            body: JSON.stringify({
                                CustomerRef: { value: data.customerId },
                                Line: [
                                    {
                                        Amount: data.amount,
                                        DetailType: 'SalesItemLineDetail',
                                        SalesItemLineDetail: {
                                            ItemRef: { value: data.itemId },
                                        },
                                    },
                                ],
                                DueDate: data.dueDate,
                                PrivateNote: data.description,
                            }),
                        }
                    );

                    if (!response.ok) {
                        await response.text();
                        throw new Error(`QuickBooks API error: ${response.status}`);
                    }

                    return await response.json();
                },
                inputFields: [
                    { key: 'customerId', label: 'Customer ID', type: 'string', required: true },
                    { key: 'amount', label: 'Amount', type: 'number', required: true },
                    { key: 'itemId', label: 'Item ID', type: 'string', required: true },
                    { key: 'dueDate', label: 'Due Date', type: 'datetime' },
                    { key: 'description', label: 'Description', type: 'string' },
                ],
            }
        }
    ],
    // No triggers defined for this example, but they would go here
};
