

import React, { useState, useEffect } from 'react';
import { PricingPlan } from '../../types';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Button } from '../ui/Button';
import { CheckIcon, PlusCircleIcon, EditIcon, TrashIcon, MoreVerticalIcon } from '../Icons';
import { cn } from '../../lib/utils';
import Dropdown from '../ui/Dropdown';
import PlanEditorModal from './PlanEditorModal';
import ConfirmationModal from './ConfirmationModal';
import { apiFetch } from '../../lib/api';

interface PlanManagementPageProps {
  plans: PricingPlan[];
  onCreatePlan: (planData: Omit<PricingPlan, 'id'>) => void;
  onUpdatePlan: (plan: PricingPlan) => void;
  onDeletePlan: (planId: string) => void;
  initialSearchTerm: string | null;
  onClearSearchFilter: () => void;
}

const PlanManagementPage: React.FC<PlanManagementPageProps> = ({ plans, onCreatePlan, onUpdatePlan, onDeletePlan, initialSearchTerm, onClearSearchFilter }) => {
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [planToEdit, setPlanToEdit] = useState<PricingPlan | null>(null);
  const [planToDelete, setPlanToDelete] = useState<PricingPlan | null>(null);
  const [highlightedPlan, setHighlightedPlan] = useState<string | null>(null);
  const [isReorderMode, setIsReorderMode] = useState(false);
  const [localOrder, setLocalOrder] = useState<PricingPlan[]>(plans);
  const [dragIndex, setDragIndex] = useState<number | null>(null);
  const [isSavingOrder, setIsSavingOrder] = useState(false);

  useEffect(() => {
    // Keep local order synced when not actively reordering
    if (!isReorderMode) {setLocalOrder(plans);}
  }, [plans, isReorderMode]);

  useEffect(() => {
      if (initialSearchTerm) {
          const plan = plans.find(p => p.name === initialSearchTerm);
          if (plan) {
              setHighlightedPlan(plan.id);
              const element = document.getElementById(`plan-card-${plan.id}`);
              element?.scrollIntoView({ behavior: 'smooth', block: 'center' });
              setTimeout(() => setHighlightedPlan(null), 2500); // Highlight for 2.5s
          }
          onClearSearchFilter();
      }
  }, [initialSearchTerm, onClearSearchFilter, plans]);

  const handleEdit = (plan: PricingPlan) => {
    setPlanToEdit(plan);
    setIsEditorOpen(true);
  };
  
  const handleCreate = () => {
    setPlanToEdit(null);
    setIsEditorOpen(true);
  };
  
  const handleSave = (planData: Omit<PricingPlan, 'id'> | PricingPlan) => {
    if ('id' in planData) {
      onUpdatePlan(planData);
    } else {
      onCreatePlan(planData);
    }
    setIsEditorOpen(false);
  };
  
  const handleDeleteConfirm = () => {
    if (planToDelete) {
      onDeletePlan(planToDelete.id);
      setPlanToDelete(null);
    }
  };

  // ----- Reorder handlers -----
  const onDragStart = (index: number) => () => setDragIndex(index);
  const onDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    // Allow dropping
    e.preventDefault();
  };
  const onDrop = (targetIndex: number) => (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (dragIndex === null || dragIndex === targetIndex) {return;}
    const updated = [...localOrder];
    const [moved] = updated.splice(dragIndex, 1);
    updated.splice(targetIndex, 0, moved);
    setLocalOrder(updated);
    setDragIndex(null);
  };
  const onDragEnd = () => setDragIndex(null);

  const saveNewOrder = async () => {
    setIsSavingOrder(true);
    try {
      // Persist sequentially to preserve order intent
      for (let i = 0; i < localOrder.length; i++) {
        const p = localOrder[i];
        const sortOrder = i + 1;
        await apiFetch(`/api/pricing-plans/${p.id}`, {
          method: 'PUT',
          body: JSON.stringify({ sortOrder }),
        });
      }
      setIsReorderMode(false);
    } catch (e) {
      // eslint-disable-next-line no-console
      console.error('Failed to save plan order', e);
      alert('Failed to save order. Please try again.');
    } finally {
      setIsSavingOrder(false);
    }
  };

  return (
    <>
      <div className="p-4 sm:p-6 lg:p-8 space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
                <h1 className="text-3xl font-bold text-zinc-900 dark:text-white">Plan Management</h1>
                <p className="text-zinc-600 dark:text-zinc-400 mt-1">Create, edit, and manage subscription plans.</p>
            </div>
            <div className="flex items-center gap-3">
              {!isReorderMode && (
                <Button variant="outline" onClick={() => setIsReorderMode(true)}>
                  Reorder Plans
                </Button>
              )}
              {isReorderMode && (
                <>
                  <Button onClick={saveNewOrder} disabled={isSavingOrder}>
                    {isSavingOrder ? 'Saving…' : 'Save Order'}
                  </Button>
                  <Button variant="ghost" onClick={() => { setIsReorderMode(false); setLocalOrder(plans); }}>
                    Cancel
                  </Button>
                </>
              )}
              <Button onClick={handleCreate}>
                <PlusCircleIcon className="w-5 h-5 mr-2" />
                Create New Plan
              </Button>
            </div>
        </div>

        {!isReorderMode && (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {plans.map((plan) => (
              <Card
                id={`plan-card-${plan.id}`}
                key={plan.id}
                className={cn('flex flex-col transition-all duration-500',
                  plan.isFeatured ? 'border-2 border-brand-600' : '',
                  highlightedPlan === plan.id ? 'ring-2 ring-brand-500 ring-offset-2 dark:ring-offset-zinc-900 shadow-2xl' : ''
                )}
              >
                <CardHeader className="flex-row items-start justify-between">
                  <div>
                      <CardTitle>{plan.name}</CardTitle>
                      <div className="mt-1 flex items-baseline text-zinc-900 dark:text-white">
                          <span className="text-3xl font-extrabold tracking-tight">{plan.price}</span>
                          {plan.priceDetail && <span className="ml-1 text-base font-semibold text-zinc-500 dark:text-zinc-400">{plan.priceDetail}</span>}
                      </div>
                  </div>
                  <Dropdown>
                      <Dropdown.Trigger>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                              <MoreVerticalIcon className="w-4 h-4" />
                          </Button>
                      </Dropdown.Trigger>
                      <Dropdown.Content align="right">
                          <Dropdown.Item onClick={() => handleEdit(plan)} icon={<EditIcon className="w-4 h-4"/>}>Edit Plan</Dropdown.Item>
                          <Dropdown.Item onClick={() => setPlanToDelete(plan)} className="text-red-600 dark:text-red-500 hover:!bg-red-50 dark:hover:!bg-red-500/10" icon={<TrashIcon className="w-4 h-4"/>}>Delete Plan</Dropdown.Item>
                      </Dropdown.Content>
                  </Dropdown>
                </CardHeader>
                <CardContent className="flex-1 flex flex-col justify-between">
                  <ul role="list" className="space-y-3">
                    {plan.features.map((feature) => (
                      <li key={feature} className="flex space-x-3">
                        <CheckIcon className="flex-shrink-0 h-5 w-5 text-brand-500" />
                        <span className="text-sm text-zinc-600 dark:text-zinc-300">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <div className={cn('text-center mt-6 py-1 text-sm font-semibold rounded-md', plan.isFeatured ? 'bg-brand-100 text-brand-800 dark:bg-brand-900/50 dark:text-brand-300' : 'bg-zinc-100 text-zinc-800 dark:bg-zinc-800 dark:text-zinc-300')}>
                      {plan.isFeatured ? 'Most Popular' : 'Standard Plan'}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {isReorderMode && (
          <div className="space-y-3">
            <p className="text-sm text-zinc-600 dark:text-zinc-400">Drag and drop to set the display order. First item appears first on the site.</p>
            <div className="bg-white dark:bg-zinc-950 rounded-xl border border-zinc-200 dark:border-zinc-800 divide-y divide-zinc-100 dark:divide-zinc-800">
              {localOrder.map((plan, idx) => (
                <div
                  key={plan.id}
                  draggable
                  onDragStart={onDragStart(idx)}
                  onDragOver={onDragOver}
                  onDrop={onDrop(idx)}
                  onDragEnd={onDragEnd}
                  className={cn('flex items-center justify-between px-4 py-3 cursor-move',
                    dragIndex === idx ? 'opacity-50' : 'opacity-100')}
                >
                  <div className="flex items-center gap-3">
                    <span className="text-zinc-400 select-none">≡</span>
                    <span className="w-6 text-xs font-semibold text-zinc-500">{idx + 1}</span>
                    <span className="font-medium text-zinc-900 dark:text-zinc-100">{plan.name}</span>
                    <span className="text-zinc-500 dark:text-zinc-400">· {plan.price}{plan.priceDetail ? ` ${plan.priceDetail}` : ''}</span>
                  </div>
                  {plan.isFeatured && (
                    <span className="text-xs px-2 py-1 rounded bg-brand-100 text-brand-700 dark:bg-brand-900/50 dark:text-brand-300">Featured</span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <PlanEditorModal 
        isOpen={isEditorOpen}
        onClose={() => setIsEditorOpen(false)}
        onSave={handleSave}
        existingPlan={planToEdit}
      />
      
      {planToDelete && (
        <ConfirmationModal
            isOpen={true}
            onClose={() => setPlanToDelete(null)}
            onConfirm={handleDeleteConfirm}
            title="Delete Plan"
            message={<>Are you sure you want to delete the <strong>{planToDelete.name}</strong> plan? This action cannot be undone.</>}
            confirmText="Delete Plan"
        />
      )}
    </>
  );
};

export default PlanManagementPage;
