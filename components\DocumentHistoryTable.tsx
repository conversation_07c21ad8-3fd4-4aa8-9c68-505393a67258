import React, { useState, useMemo, useEffect, memo } from 'react';
import ErrorBoundary from './ui/ErrorBoundary';
import { Document as DocType, Folder, User, DocumentStatus, DashboardView, DocumentsPaginationState } from '../types';
// Table components removed - using virtualized list instead
import { Button } from './ui/Button';
import { SearchIcon, MoreVerticalIcon, FolderIcon, PlusCircleIcon, AlertTriangleIcon, EditIcon, TrashIcon, DocumentIcon, ArchiveIcon, FolderMoveIcon } from './Icons';
import { cn } from '../lib/utils';
import CreateFolderModal from './CreateFolderModal';
import MoveDocumentModal from './MoveDocumentModal';
import Dropdown from './ui/Dropdown';

interface DocumentHistoryTableProps {
  user: User;
  onView: (doc: DocType) => void;
  onDeleteDocument: (documentId: string) => void;
  onCreateFolder: (name: string) => void;
  onUpdateFolder: (folderId: string, newName: string) => void;
  onDeleteFolder: (folderId: string) => void;
  onMoveDocument: (documentId: string, folderId: string | null) => void;
  onUpdateDocumentStatus: (docId: string, newStatus: DocumentStatus) => void;
  setView: (view: DashboardView) => void;
  documentsPagination: DocumentsPaginationState;
  onLoadMoreDocuments: () => void;
  // New: allow parent to suggest which folder to show
  initialSelectedFolderId?: string | 'all' | 'uncategorized' | 'archived' | null;
  // New: highlight and anchor a specific document
  highlightDocumentId?: string;
}

const StatusBadge: React.FC<{ status: DocumentStatus }> = memo(({ status }) => {
    const baseClasses = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize';
    const statusClasses = {
        draft: 'bg-zinc-100 text-zinc-800 dark:bg-zinc-800 dark:text-zinc-300',
        'in-review': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-300',
        approved: 'bg-sky-100 text-sky-800 dark:bg-sky-900/50 dark:text-sky-300',
        'out-for-signature': 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300',
        completed: 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300',
        archived: 'bg-zinc-100 text-zinc-600 dark:bg-zinc-800 dark:text-zinc-400',
    };
    return <span className={cn(baseClasses, status ? statusClasses[status] : '')}>{status.replace('-', ' ')}</span>;
});

// Row component for document list
const DocumentRow: React.FC<{ 
  doc: DocType;
  onView: (doc: DocType) => void;
  onUpdateDocumentStatus: (docId: string, newStatus: DocumentStatus) => void;
  setDocToMove: (doc: DocType) => void;
  setIsMoveModalOpen: (open: boolean) => void;
  setItemToDelete: (item: { type: 'folder' | 'document'; id: string; name: string }) => void;
  justSavedDocId: string | null;
  formatDate: (date: string) => string;
}> = memo(({ doc, onView, onUpdateDocumentStatus, setDocToMove, setIsMoveModalOpen, setItemToDelete, justSavedDocId, formatDate }) => {
  // Additional safety check for the specific document
  if (!doc || !doc.id) {
    return (
      <div className="border-b border-zinc-200 dark:border-zinc-800">
        <div className="flex items-center min-h-[60px] px-4 text-gray-500">
          Document not found
        </div>
      </div>
    );
  }
  
  return (
    <div className="border-b border-zinc-200 dark:border-zinc-800">
      <div className={cn('flex items-center min-h-[60px] px-4 hover:bg-zinc-50 dark:hover:bg-zinc-900/50', justSavedDocId === doc.id ? 'bg-amber-50 dark:bg-amber-900/20 ring-2 ring-amber-400' : '')} id={`doc-row-${doc.id}`}>
        <div className="flex-1 min-w-0 cursor-pointer" onClick={() => onView(doc)}>
          <div className="font-medium text-zinc-900 dark:text-zinc-200 truncate">{doc.name}</div>
        </div>
        <div className="flex-shrink-0 mx-4 cursor-pointer" onClick={() => onView(doc)}>
          <StatusBadge status={doc.status} />
        </div>
        <div className="hidden sm:block flex-shrink-0 mx-4 text-zinc-500 dark:text-zinc-400 cursor-pointer" onClick={() => onView(doc)}>
          {formatDate(doc.updatedAt)}
        </div>
        <div className="flex-shrink-0" onClick={e => e.stopPropagation()}>
          <Dropdown>
            <Dropdown.Trigger>
              <Button variant="ghost" size="icon" className="h-8 w-8 dark:text-zinc-400 dark:hover:bg-zinc-800">
                <MoreVerticalIcon className="h-4 w-4" />
              </Button>
            </Dropdown.Trigger>
            <Dropdown.Content>
              <Dropdown.Item onClick={() => onView(doc)} icon={<DocumentIcon className="w-4 h-4"/>}>View</Dropdown.Item>
              <Dropdown.Item onClick={() => { setDocToMove(doc); setIsMoveModalOpen(true); }} icon={<FolderMoveIcon className="w-4 h-4"/>}>Move to...</Dropdown.Item>
              {doc.status === 'completed' && <Dropdown.Item onClick={() => onUpdateDocumentStatus(doc.id, 'archived')} icon={<ArchiveIcon className="w-4 h-4"/>}>Archive</Dropdown.Item>}
              <Dropdown.Item onClick={() => setItemToDelete({ type: 'document', id: doc.id, name: doc.name })} className="text-red-600 dark:text-red-500 hover:bg-red-50 dark:hover:bg-red-500/10" icon={<TrashIcon className="w-4 h-4"/>}>Delete</Dropdown.Item>
            </Dropdown.Content>
          </Dropdown>
        </div>
      </div>
    </div>
  );
});


const DocumentHistoryTable: React.FC<DocumentHistoryTableProps> = ({ user, onView, onDeleteDocument, onCreateFolder, onUpdateFolder, onDeleteFolder, onMoveDocument, onUpdateDocumentStatus, setView, documentsPagination, onLoadMoreDocuments, initialSelectedFolderId, highlightDocumentId }) => {
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>('all');
  const [justSavedDocId, setJustSavedDocId] = useState<string | null>(null);

  // Apply externally suggested folder selection
  useEffect(() => {
    if (typeof initialSelectedFolderId !== 'undefined') {
      setSelectedFolderId(initialSelectedFolderId === null ? 'uncategorized' : initialSelectedFolderId);
    }
  }, [initialSelectedFolderId]);
  const [searchTerm, setSearchTerm] = useState('');
  
  const [isCreateFolderModalOpen, setIsCreateFolderModalOpen] = useState(false);
  const [folderToEdit, setFolderToEdit] = useState<Folder | null>(null);
  
  const [isMoveModalOpen, setIsMoveModalOpen] = useState(false);
  const [docToMove, setDocToMove] = useState<DocType | null>(null);

  const [itemToDelete, setItemToDelete] = useState<{ type: 'folder' | 'document', id: string, name: string } | null>(null);

  const { hasMore, loading: isLoading, total } = documentsPagination;

  const filteredDocuments = useMemo(() => {
    let docs = user.documents.filter(d => d.status !== 'archived'); // By default, don't show archived
    if (selectedFolderId === 'uncategorized') {
      docs = docs.filter(d => !d.folderId);
    } else if (selectedFolderId === 'archived') {
      docs = user.documents.filter(d => d.status === 'archived');
    } else if (selectedFolderId && selectedFolderId !== 'all') {
      docs = docs.filter(d => d.folderId === selectedFolderId);
    }
    
    if (searchTerm) {
      docs = docs.filter(d => d.name.toLowerCase().includes(searchTerm.toLowerCase()));
    }
    return docs.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
  }, [user.documents, selectedFolderId, searchTerm]);

  // When asked to highlight a document, scroll to it and apply a temporary highlight
  useEffect(() => {
    if (!highlightDocumentId) {return;}
    const exists = user.documents.some(d => d.id === highlightDocumentId);
    if (!exists) {return;}
    setJustSavedDocId(highlightDocumentId);
    // Delay to ensure row is rendered
    setTimeout(() => {
      const el = document.getElementById(`doc-row-${highlightDocumentId}`);
      el?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }, 50);
    const t = setTimeout(() => setJustSavedDocId(null), 4000);
    return () => clearTimeout(t);
  }, [highlightDocumentId, user.documents]);

  const currentFolder = user.folders.find(f => f.id === selectedFolderId);
  const breadcrumb = currentFolder ? `Documents / ${currentFolder.name}` : selectedFolderId === 'uncategorized' ? 'Documents / Uncategorized' : selectedFolderId === 'archived' ? 'Documents / Archived' : 'Documents / All Documents';

  const handleSaveFolder = (name: string) => {
    if (folderToEdit) {
      onUpdateFolder(folderToEdit.id, name);
    } else {
      onCreateFolder(name);
    }
    setIsCreateFolderModalOpen(false);
    setFolderToEdit(null);
  };

  const confirmDelete = () => {
    if (!itemToDelete) {return;}
    if (itemToDelete.type === 'document') {
      onDeleteDocument(itemToDelete.id);
    } else {
      onDeleteFolder(itemToDelete.id);
    }
    setItemToDelete(null);
  };

  const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

  return (
    <>
    <div className="flex flex-col md:flex-row gap-6 h-full">
      <aside className="w-full md:w-64 flex-shrink-0 bg-white dark:bg-zinc-950 p-4 rounded-xl border border-zinc-200 dark:border-zinc-800">
        <Button className="w-full mb-4" onClick={() => setIsCreateFolderModalOpen(true)}>
          <PlusCircleIcon className="w-5 h-5 mr-2"/> New Folder
        </Button>
        <nav className="space-y-1">
          <button onClick={() => setSelectedFolderId('all')} className={cn('w-full text-left flex items-center p-2 rounded-md text-sm font-medium', selectedFolderId === 'all' ? 'bg-brand-50 text-brand-700 dark:bg-brand-900/40 dark:text-brand-300' : 'hover:bg-zinc-100 dark:hover:bg-zinc-800 text-zinc-700 dark:text-zinc-300')}>
            <FolderIcon className="w-5 h-5 mr-3"/> All Documents
          </button>
          <button onClick={() => setSelectedFolderId('uncategorized')} className={cn('w-full text-left flex items-center p-2 rounded-md text-sm font-medium', selectedFolderId === 'uncategorized' ? 'bg-brand-50 text-brand-700 dark:bg-brand-900/40 dark:text-brand-300' : 'hover:bg-zinc-100 dark:hover:bg-zinc-800 text-zinc-700 dark:text-zinc-300')}>
            <FolderIcon className="w-5 h-5 mr-3"/> Uncategorized
          </button>
          <div className="border-t my-2 border-zinc-200 dark:border-zinc-800"></div>
          {user.folders.map(folder => (
            <div key={folder.id} className="group flex items-center justify-between rounded-md hover:bg-zinc-100 dark:hover:bg-zinc-800">
              <button onClick={() => setSelectedFolderId(folder.id)} className={cn('flex-1 text-left flex items-center p-2 text-sm font-medium', selectedFolderId === folder.id ? 'bg-brand-50 text-brand-700 dark:bg-brand-900/40 dark:text-brand-300' : 'text-zinc-700 dark:text-zinc-300')}>
                <FolderIcon className="w-5 h-5 mr-3"/> {folder.name}
              </button>
              <div className="opacity-0 group-hover:opacity-100 transition-opacity pr-1" onClick={e => e.stopPropagation()}>
                 <Dropdown>
                    <Dropdown.Trigger>
                        <Button variant="ghost" size="icon" className="h-8 w-8 dark:text-zinc-400 dark:hover:bg-zinc-800">
                            <MoreVerticalIcon className="h-4 w-4" />
                        </Button>
                    </Dropdown.Trigger>
                    <Dropdown.Content>
                        <Dropdown.Item onClick={() => { setFolderToEdit(folder); setIsCreateFolderModalOpen(true); }} icon={<EditIcon className="w-4 h-4"/>}>
                             Rename
                        </Dropdown.Item>
                        <Dropdown.Item onClick={() => setItemToDelete({ type: 'folder', id: folder.id, name: folder.name })} className="text-red-600 dark:text-red-500 hover:bg-red-50 dark:hover:bg-red-500/10" icon={<TrashIcon className="w-4 h-4"/>}>
                            Delete
                        </Dropdown.Item>
                    </Dropdown.Content>
                 </Dropdown>
              </div>
            </div>
          ))}
          <div className="border-t my-2 border-zinc-200 dark:border-zinc-800"></div>
           <button onClick={() => setSelectedFolderId('archived')} className={cn('w-full text-left flex items-center p-2 rounded-md text-sm font-medium', selectedFolderId === 'archived' ? 'bg-brand-50 text-brand-700 dark:bg-brand-900/40 dark:text-brand-300' : 'hover:bg-zinc-100 dark:hover:bg-zinc-800 text-zinc-700 dark:text-zinc-300')}>
            <ArchiveIcon className="w-5 h-5 mr-3"/> Archived
          </button>
        </nav>
      </aside>
      
      <main className="flex-1 bg-white dark:bg-zinc-950 p-6 rounded-xl border border-zinc-200 dark:border-zinc-800 flex flex-col">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-2">
            <div>
                <h2 className="text-xl font-bold text-zinc-900 dark:text-white">Your Documents</h2>
                <p className="text-sm text-zinc-500 dark:text-zinc-400">{breadcrumb}</p>
            </div>
            <div className="relative w-full sm:w-64">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <SearchIcon className="h-5 w-5 text-zinc-400" />
              </div>
              <input type="text" placeholder="Search documents..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 focus:border-brand-500 focus:ring-brand-500 sm:text-sm p-2" />
            </div>
        </div>
        {justSavedDocId && (
          <div className="mb-4 text-sm text-amber-800 bg-amber-50 dark:bg-amber-900/30 dark:text-amber-200 border border-amber-200 dark:border-amber-800 rounded-md px-3 py-2">
            New document saved.
            <a
              href={`#doc-row-${justSavedDocId}`}
              onClick={(e) => { e.preventDefault(); document.getElementById(`doc-row-${justSavedDocId}`)?.scrollIntoView({ behavior: 'smooth', block: 'center' }); }}
              className="ml-2 font-medium underline"
            >
              Jump to it
            </a>
          </div>
        )}
        <div className="border-t border-zinc-200 dark:border-zinc-800 flex-1 overflow-hidden">
          {isLoading && user.documents.length === 0 ? (
            <div className="h-96 flex items-center justify-center">
              <div className="text-sm text-zinc-500 dark:text-zinc-400">Loading documents...</div>
            </div>
          ) : filteredDocuments.length > 0 ? (
            <div className="h-full">
              <div className="grid grid-cols-12 gap-4 px-4 py-3 bg-zinc-50 dark:bg-zinc-900/50 border-b border-zinc-200 dark:border-zinc-800 text-sm font-medium text-zinc-700 dark:text-zinc-300">
                <div className="col-span-5">Name</div>
                <div className="col-span-2">Status</div>
                <div className="col-span-3 hidden sm:block">Date Updated</div>
                <div className="col-span-2 text-right">Actions</div>
              </div>
              <div className="max-h-[600px] overflow-y-auto">
                <ErrorBoundary fallback={
                  <div className="flex items-center justify-center h-[600px] text-gray-500">
                    Unable to load documents
                  </div>
                }>
                  {filteredDocuments.map((doc) => (
                    <DocumentRow
                      key={doc.id}
                      doc={doc}
                      onView={onView}
                      onUpdateDocumentStatus={onUpdateDocumentStatus}
                      setDocToMove={setDocToMove}
                      setIsMoveModalOpen={setIsMoveModalOpen}
                      setItemToDelete={setItemToDelete}
                      justSavedDocId={justSavedDocId}
                      formatDate={formatDate}
                    />
                  ))}
                </ErrorBoundary>
              </div>
            </div>
          ) : (
            <div className="h-96 flex items-center justify-center">
              <div className="text-center">
                <DocumentIcon className="mx-auto h-12 w-12 text-zinc-300 dark:text-zinc-700" />
                <h3 className="mt-2 text-lg font-semibold text-zinc-800 dark:text-zinc-200">No Documents Found</h3>
                <p className="mt-1 text-sm text-zinc-500 dark:text-zinc-400">There are no documents in this view. Try another folder or create a new one.</p>
                <Button className="mt-4" onClick={() => setView('generate')}>
                  <PlusCircleIcon className="w-5 h-5 mr-2"/>
                  Create Document
                </Button>
              </div>
            </div>
          )}
        </div>
        {(user.documents.length > 0 || (typeof total === 'number' && total > 0)) && (
          <div className="pt-4 flex flex-col items-center gap-3">
            <p className="text-sm text-zinc-500 dark:text-zinc-400">
              Loaded {user.documents.length}
              {typeof total === 'number' ? ` of ${total}` : ''} documents
            </p>
            {hasMore && (
              <Button variant="outline" onClick={onLoadMoreDocuments} disabled={isLoading}>
                {isLoading ? 'Loading more...' : 'Load more'}
              </Button>
            )}
          </div>
        )}
      </main>
    </div>

    <CreateFolderModal isOpen={isCreateFolderModalOpen} onClose={() => { setIsCreateFolderModalOpen(false); setFolderToEdit(null); }} onSave={handleSaveFolder} existingFolder={folderToEdit} />
    {docToMove && <MoveDocumentModal isOpen={isMoveModalOpen} onClose={() => setIsMoveModalOpen(false)} onMove={(folderId) => { onMoveDocument(docToMove.id, folderId); setIsMoveModalOpen(false); }} document={docToMove} folders={user.folders} />}
    {itemToDelete && (
        <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-md p-6">
                <div className="flex items-start gap-4">
                    <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 sm:mx-0 sm:h-10 sm:w-10">
                        <AlertTriangleIcon className="h-6 w-6 text-red-600 dark:text-red-400" />
                    </div>
                    <div className="mt-0 text-center sm:text-left">
                        <h3 className="text-lg leading-6 font-medium text-zinc-900 dark:text-zinc-50">Delete {itemToDelete.type}</h3>
                        <p className="mt-2 text-sm text-zinc-500 dark:text-zinc-400">
                          Are you sure you want to delete "{itemToDelete.name}"? 
                          {itemToDelete.type === 'folder' && ' All documents inside will become uncategorized.'} This action cannot be undone.
                        </p>
                    </div>
                </div>
                <div className="mt-5 sm:mt-4 flex flex-col-reverse sm:flex-row-reverse gap-3">
                     <Button variant="destructive" onClick={confirmDelete}>Delete</Button>
                     <Button variant="outline" onClick={() => setItemToDelete(null)}>Cancel</Button>
                </div>
            </div>
        </div>
    )}
    </>
  );
};

export default memo(DocumentHistoryTable);
