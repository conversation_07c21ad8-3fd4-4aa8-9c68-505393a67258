import type { Response, NextFunction } from 'express';
import type { AuthRequest } from './auth';
import { logger } from '../lib/logger';
import { Sentry } from '../lib/monitoring';

export function errorHandler(err: unknown, req: AuthRequest, res: Response, _next: NextFunction) {
  const error = err instanceof Error ? err : new Error(String(err));
  logger.error({
    err: error,
    route: req.originalUrl,
    userId: req.user?.id,
  }, 'Unhandled error');
  if (process.env.NODE_ENV === 'production' && process.env.SENTRY_DSN) {
    Sentry.captureException(error, {
      user: req.user ? { id: req.user.id } : undefined,
      tags: { route: req.originalUrl },
    });
  }
  res.status(500).json({ error: 'Internal Server Error' });
}
