import React, { useState, useRef, useEffect, useLayoutEffect, createContext, useContext } from 'react';
import { createPortal } from 'react-dom';
import { cn } from '../../lib/utils';

interface DropdownContextProps {
  isOpen: boolean;
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
  rootRef: React.RefObject<HTMLDivElement>;
  setPortalEl: (el: HTMLDivElement | null) => void;
}

const DropdownContext = createContext<DropdownContextProps | null>(null);

const useDropdown = () => {
  const context = useContext(DropdownContext);
  if (!context) {throw new Error('useDropdown must be used within a Dropdown component');}
  return context;
};

// FIX: Changed component typing from `React.FC` to an inferred function type to allow attaching sub-components.
const Dropdown = ({ children }: { children: React.ReactNode }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [_portalEl, setPortalEl] = useState<HTMLDivElement | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      const clickedInsideRoot = dropdownRef.current?.contains(target) ?? false;
      // Detect clicks within any dropdown portal without relying on local state
      const clickedInsideAnyPortal = !!target.closest('[data-dropdown-portal="true"]');
      if (!clickedInsideRoot && !clickedInsideAnyPortal) {
        setIsOpen(false);
      }
    };
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {setIsOpen(false);}
    };

    document.addEventListener('mousedown', handleClickOutside);
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <DropdownContext.Provider value={{ isOpen, setIsOpen, rootRef: dropdownRef, setPortalEl }}>
      <div ref={dropdownRef} className="relative inline-block text-left">
        {children}
      </div>
    </DropdownContext.Provider>
  );
};

const Trigger: React.FC<{ children: React.ReactNode; className?: string }> = ({ children, className }) => {
  const { isOpen, setIsOpen } = useDropdown();
  return (
    <div onClick={() => setIsOpen(p => !p)} aria-haspopup="true" aria-expanded={isOpen} className={cn("cursor-pointer", className)}>
      {children}
    </div>
  );
};

const Content: React.FC<{ children: React.ReactNode; align?: 'left' | 'right'; className?: string; side?: 'top' | 'bottom' }> = ({ children, align = 'right', className, side = 'bottom' }) => {
  const { isOpen, rootRef, setPortalEl } = useDropdown();
  const portalContainerRef = useRef<HTMLDivElement>(null);
  const [pos, setPos] = useState<{ top: number; left: number } | null>(null);

  useEffect(() => {
    setPortalEl(portalContainerRef.current);
    return () => setPortalEl(null);
  }, [setPortalEl]);

  const updatePosition = () => {
    const root = rootRef.current;
    if (!root) {return;}
    const rect = root.getBoundingClientRect();
    const width = 224; // w-56
    const margin = 8; // mt-2 / mb-2
    let left = align === 'right' ? rect.right - width : rect.left;
    let top = side === 'bottom' ? rect.bottom + margin : rect.top - margin; // for top we may overlap slightly
    // Keep within viewport margins
    const vw = window.innerWidth;
    const vh = window.innerHeight;
    if (left + width > vw - 8) {left = vw - width - 8;}
    if (left < 8) {left = 8;}
    if (top > vh - 8) {top = vh - 8;} // basic clamp
    if (top < 8) {top = 8;}
    setPos({ top, left });
  };

  useLayoutEffect(() => {
    if (!isOpen) {return;}
    updatePosition();
    const onScroll = () => updatePosition();
    const onResize = () => updatePosition();
    window.addEventListener('scroll', onScroll, true);
    window.addEventListener('resize', onResize);
    return () => {
      window.removeEventListener('scroll', onScroll, true);
      window.removeEventListener('resize', onResize);
    };
  }, [isOpen, align, side]);

  if (!isOpen || !pos) {return null;}

  return createPortal(
    <div
      ref={portalContainerRef}
      className={cn(
        'fixed z-50 w-56 rounded-md shadow-lg bg-white dark:bg-zinc-800 ring-1 ring-black/5 dark:ring-zinc-700 focus:outline-none',
        className
      )}
      data-dropdown-portal="true"
      style={{ top: pos.top, left: pos.left }}
      role="menu"
      aria-orientation="vertical"
    >
      <div className="py-1" role="none">
        {children}
      </div>
    </div>,
    document.body
  );
};

// FIX: Add a 'disabled' prop to Dropdown.Item to prevent actions and apply disabled styles, resolving a type error.
const Item: React.FC<{ children: React.ReactNode; onClick?: (e: React.MouseEvent<HTMLAnchorElement>) => void; className?: string; disabled?: boolean; icon?: React.ReactNode; }> = ({ children, onClick, className, disabled = false, icon }) => {
  const { setIsOpen } = useDropdown();
  const handleClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault();
    if (disabled) {
        return;
    }
    if (onClick) {onClick(e);}
    setIsOpen(false);
  };
  return (
    <a
      href="#"
      onClick={handleClick}
      className={cn(
        // Layout ensures icons align left with consistent spacing
        "text-zinc-700 dark:text-zinc-200 block text-sm",
        // Two-column grid: fixed 1rem icon column + flexible text
        "grid grid-cols-[1rem_auto] items-center gap-2 px-3 py-2",
        !disabled && "hover:bg-zinc-100 dark:hover:bg-zinc-700/60",
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      role="menuitem"
      aria-disabled={disabled}
    >
      {icon ? <span className="inline-flex w-4 h-4 items-center justify-center shrink-0">{icon}</span> : <span className="inline-block w-4 h-4" />}
      <span className="truncate leading-5">{children}</span>
    </a>
  );
};

const Separator: React.FC<{ className?: string }> = ({ className }) => {
  return <div className={cn("-mx-1 my-1 h-px bg-zinc-100 dark:bg-zinc-700", className)} />;
};

Dropdown.Trigger = Trigger;
Dropdown.Content = Content;
Dropdown.Item = Item;
Dropdown.Separator = Separator;

export default Dropdown;
