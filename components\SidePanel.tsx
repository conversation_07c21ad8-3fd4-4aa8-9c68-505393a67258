import React from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from './Icons';
import { cn } from '../lib/utils';

interface SidePanelProps {
  suggestedPrompts: string[];
  onPromptClick: (prompt: string) => void;
  quotaText: React.ReactNode;
  isLoading?: boolean;
  atQuotaLimit?: boolean;
  isExpanded: boolean;
  onToggle: () => void;
}

const SidePanel: React.FC<SidePanelProps> = ({
  suggestedPrompts,
  onPromptClick,
  quotaText,
  isLoading = false,
  atQuotaLimit = false,
  isExpanded,
  onToggle
}) => {

  return (
    <>
      {/* Mobile backdrop */}
      {isExpanded && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onToggle}
        />
      )}
      
      {/* Side panel */}
      <div className={cn(
          "absolute top-0 left-0 h-full bg-zinc-50 dark:bg-zinc-800 border-r border-zinc-200 dark:border-zinc-700 z-10 transition-transform duration-300 ease-in-out",
          "flex flex-col",
          isExpanded ? "translate-x-0" : "-translate-x-full",
          "w-72"
        )}>
        {/* Header */}
        <div className="flex items-center p-4 border-b border-zinc-200 dark:border-zinc-800">
          <h2 className={cn(
            "font-semibold text-zinc-800 dark:text-zinc-200 transition-opacity duration-200",
            isExpanded ? "opacity-100" : "opacity-0 lg:opacity-0"
          )}>
            Quick Actions
          </h2>
        </div>

        {/* Panel Content */}
        <div className={cn(
          "flex-1 overflow-y-auto transition-opacity duration-200",
          isExpanded ? "opacity-100" : "opacity-0 lg:opacity-0"
        )}>
          {/* Suggested Prompts Section */}
          <div className="p-4">
            <h3 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-3">
              Suggested Prompts
            </h3>
            {!isLoading && !atQuotaLimit && (
              <div className="space-y-2">
                {suggestedPrompts.map((prompt, index) => (
                  <button
                    key={index}
                    onClick={() => onPromptClick(prompt)}
                    className="w-full text-left p-3 bg-zinc-50 dark:bg-zinc-800 hover:bg-zinc-100 dark:hover:bg-zinc-700 text-zinc-700 dark:text-zinc-300 text-sm rounded-lg transition-colors border border-zinc-200 dark:border-zinc-700"
                  >
                    {prompt}
                  </button>
                ))}
              </div>
            )}
            {(isLoading || atQuotaLimit) && (
              <div className="text-sm text-zinc-500 dark:text-zinc-400 italic">
                {isLoading ? "Processing..." : "Quota limit reached"}
              </div>
            )}
          </div>

          {/* Quota Information Section */}
          <div className="p-4 border-t border-zinc-200 dark:border-zinc-800">
            <h3 className="text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-3">
              Usage Information
            </h3>
            <div className="text-sm text-zinc-600 dark:text-zinc-400">
              {quotaText}
            </div>
          </div>
        </div>
      </div>

      {/* Toggle button - always visible */}
      <button
        onClick={onToggle}
        className={cn(
          "absolute top-4 z-20 p-2 bg-white dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-r-lg shadow-lg transition-all duration-300",
          "hover:bg-zinc-50 dark:hover:bg-zinc-700",
          isExpanded ? "left-[17rem]" : "left-0"
        )}
        aria-label={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
      >
        {isExpanded ? (
          <ChevronLeftIcon className="w-4 h-4 text-zinc-600 dark:text-zinc-400" />
        ) : (
          <ChevronRightIcon className="w-4 h-4 text-zinc-600 dark:text-zinc-400" />
        )}
      </button>
    </>
  );
};

export default SidePanel;