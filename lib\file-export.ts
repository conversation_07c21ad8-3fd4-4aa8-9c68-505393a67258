// Augment the global Window interface for TypeScript
declare global {
  interface Window {
    jspdf: {
      jsPDF: new (orientation?: string, unit?: string, format?: string) => {
        internal: { pageSize: { getWidth(): number; getHeight(): number } };
        addPage(): void;
        addImage(imageData: string, format: string, x: number, y: number, width: number, height: number, alias?: string, compression?: string): void;
        save(filename: string): void;
      };
    };
    html2canvas: (element: HTMLElement, options?: Record<string, unknown>) => Promise<HTMLCanvasElement>;
    htmlDocx: {
      asBlob(content: string): Blob;
    };
    saveAs: (blob: Blob, filename: string) => void;
  }
}

// Store script sources in a constant for easy management
const SCRIPT_URLS = {
  jspdf: 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js',
  html2canvas: 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js',
  // FIX: Switched to a more reliable library for DOCX export.
  htmlDocx: 'https://unpkg.com/html-docx-js/dist/html-docx.js',
  fileSaver: 'https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js',
};

// Keep track of loaded scripts to avoid reloading
const loadedScripts: { [key: string]: Promise<void> } = {};

/**
 * Dynamically loads a script and returns a promise that resolves on load.
 * Caches promises to prevent duplicate script injection.
 * @param src - The URL of the script to load.
 * @returns A promise that resolves when the script is loaded.
 */
function loadScript(src: string): Promise<void> {
  if (loadedScripts[src]) {
    return loadedScripts[src];
  }

  const promise = new Promise<void>((resolve, reject) => {
    const script = document.createElement('script');
    script.src = src;
    script.async = true;
    script.onload = () => resolve();
    script.onerror = () => {
        reject(new Error(`Failed to load script: ${src}`));
        delete loadedScripts[src]; // Allow retrying on failure
    };
    document.head.appendChild(script);
  });

  loadedScripts[src] = promise;
  return promise;
}

/**
 * Exports the content of a DOM element as a multi-page PDF with proper margins.
 * It renders the entire element to a single canvas, then programmatically slices
 * the canvas into page-sized chunks and adds each to a new page in the PDF.
 * @param element - The single container element holding all the content.
 * @param fileName - The desired name of the output PDF file.
 */
export const exportAsPdf = async (element: HTMLElement, fileName:string): Promise<void> => {
  // Load dependencies
  await Promise.all([
    loadScript(SCRIPT_URLS.jspdf),
    loadScript(SCRIPT_URLS.html2canvas),
  ]);

  const { jsPDF } = window.jspdf;

  // Clone the element to render it without its original CSS padding, which would
  // otherwise create a "double margin" effect in the PDF.
  const clone = element.cloneNode(true) as HTMLElement;
  clone.style.width = `${element.offsetWidth}px`;
  clone.style.padding = '0';
  clone.style.margin = '0';
  clone.style.boxShadow = 'none';
  clone.style.minHeight = 'auto';

  // Position the clone off-screen for rendering.
  clone.style.position = 'absolute';
  clone.style.left = '-9999px';
  clone.style.top = '0';
  document.body.appendChild(clone);
  
  // Render the modified clone to a canvas.
  const canvas = await window.html2canvas(clone, {
    scale: 2,
    useCORS: true,
    logging: false,
    scrollY: 0, // The clone is not scrolled.
    windowWidth: clone.scrollWidth,
    windowHeight: clone.scrollHeight,
  });
  
  // Clean up by removing the clone.
  document.body.removeChild(clone);

  // A4 page dimensions in points and margin settings
  const pdf = new jsPDF('p', 'pt', 'a4');
  const pdfWidth = pdf.internal.pageSize.getWidth();
  const pdfHeight = pdf.internal.pageSize.getHeight();
  const margin = 40; // 40pt margin (approx 0.55 inch)

  const contentWidth = pdfWidth - margin * 2;
  const contentHeight = pdfHeight - margin * 2;

  const canvasWidth = canvas.width;
  const canvasHeight = canvas.height;
  const scale = contentWidth / canvasWidth;
  const scaledCanvasHeight = canvasHeight * scale;
  const totalPages = Math.ceil(scaledCanvasHeight / contentHeight);
  
  let yCanvasPos = 0;

  for (let i = 1; i <= totalPages; i++) {
    if (i > 1) {
      pdf.addPage();
    }
    
    const sliceHeightPx = Math.min(canvasHeight - yCanvasPos, contentHeight / scale);

    const pageCanvas = document.createElement('canvas');
    pageCanvas.width = canvasWidth;
    pageCanvas.height = sliceHeightPx;
    const pageCtx = pageCanvas.getContext('2d');

    if (pageCtx) {
      pageCtx.drawImage(
        canvas,
        0, yCanvasPos, canvasWidth, sliceHeightPx, // Source
        0, 0, canvasWidth, sliceHeightPx  // Destination
      );

      const finalImageHeightOnPdf = sliceHeightPx * scale;

      pdf.addImage(
        pageCanvas.toDataURL('image/png'), 'PNG',
        margin, margin,
        contentWidth, finalImageHeightOnPdf,
        undefined, 'FAST'
      );
    }
    
    yCanvasPos += sliceHeightPx;
  }
  
  pdf.save(`${fileName}.pdf`);
};


/**
 * Exports an HTML string as a Word (.docx) document using the html-docx-js library.
 * Loads required scripts on-demand.
 * @param htmlContent - The HTML content string of the document.
 * @param fileName - The desired name of the output .docx file.
 */
export const exportAsDocx = async (htmlContent: string, fileName: string): Promise<void> => {
    await Promise.all([
        loadScript(SCRIPT_URLS.htmlDocx),
        loadScript(SCRIPT_URLS.fileSaver),
    ]);

    const content = `<!DOCTYPE html><html><head><meta charset="UTF-8"></head><body>${htmlContent}</body></html>`;
    const file = window.htmlDocx.asBlob(content);
    window.saveAs(file, `${fileName}.docx`);
};