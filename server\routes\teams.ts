import { Router } from 'express';
import { z } from 'zod';
import { AuthRequest, requireAuth } from '../middleware/auth';
import { requirePremium } from '../middleware/plan';
import { getUserClient } from '../supabaseClient';

const router = Router();

const teamInsert = z.object({ name: z.string().min(1), status: z.enum(['active','suspended']).default('active') });
const teamUpdate = z.object({ name: z.string().min(1).optional(), status: z.enum(['active','suspended']).optional() });

router.get('/', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  if (!req.accessToken) {
    return res.status(401).json({ error: 'Access token required' });
  }
  const supa = getUserClient(req.accessToken);
  // Teams where user is owner or member are accessible via RLS
  const { data, error } = await supa.from('teams').select('*').order('created_at', { ascending: false });
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ teams: data });
});

router.post('/', requireAuth, requirePremium, async (req: AuthRequest, res) => {
  const parsed = teamInsert.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  if (!req.accessToken) {
    return res.status(401).json({ error: 'Access token required' });
  }
  const supa = getUserClient(req.accessToken);
  const { data: auth } = await supa.auth.getUser();
  const ownerId = auth.user?.id || req.user?.id;
  const { data, error } = await supa
    .from('teams')
    .insert({ name: parsed.data.name, owner_id: ownerId, status: parsed.data.status })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  // auto add owner as member Admin
  await supa.from('team_members').insert({ team_id: data.id, user_id: ownerId, email: auth.user?.email || req.user?.email, role: 'Admin' });
  res.status(201).json({ team: data });
});

router.put('/:teamId', requireAuth, requirePremium, async (req: AuthRequest<{ teamId: string }>, res) => {
  const { teamId } = req.params;
  const parsed = teamUpdate.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  if (!req.accessToken) {
    return res.status(401).json({ error: 'Access token required' });
  }
  const patch: Partial<{ name: string; status: string }> = { name: parsed.data.name, status: parsed.data.status };
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const supa = getUserClient(req.accessToken);
  const { data, error } = await supa.from('teams').update(patch).eq('id', teamId).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ team: data });
});

router.delete('/:teamId', requireAuth, requirePremium, async (req: AuthRequest<{ teamId: string }>, res) => {
  const { teamId } = req.params;
  if (!req.accessToken) {
    return res.status(401).json({ error: 'Access token required' });
  }
  const supa = getUserClient(req.accessToken);
  const { error } = await supa.from('teams').delete().eq('id', teamId);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

// Team members
const memberInsert = z.object({ userId: z.string().uuid(), email: z.string().email(), role: z.enum(['Admin','Member']) });
const memberUpdate = z.object({ role: z.enum(['Admin','Member']) });

router.get('/:teamId/members', requireAuth, async (req: AuthRequest<{ teamId: string }>, res) => {
  const { teamId } = req.params;
  if (!req.accessToken) {
    return res.status(401).json({ error: 'Access token required' });
  }
  const supa = getUserClient(req.accessToken);
  const { data, error } = await supa.from('team_members').select('*').eq('team_id', teamId);
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ members: data });
});

router.post('/:teamId/members', requireAuth, async (req: AuthRequest<{ teamId: string }>, res) => {
  const { teamId } = req.params;
  const parsed = memberInsert.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  if (!req.accessToken) {
    return res.status(401).json({ error: 'Access token required' });
  }
  const supa = getUserClient(req.accessToken);
  const { data, error } = await supa
    .from('team_members')
    .insert({ team_id: teamId, user_id: parsed.data.userId, email: parsed.data.email, role: parsed.data.role })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ member: data });
});

router.put('/:teamId/members/:userId', requireAuth, async (req: AuthRequest<{ teamId: string; userId: string }>, res) => {
  const { teamId, userId } = req.params;
  const parsed = memberUpdate.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  if (!req.accessToken) {
    return res.status(401).json({ error: 'Access token required' });
  }
  const supa = getUserClient(req.accessToken);
  const { data, error } = await supa
    .from('team_members')
    .update({ role: parsed.data.role })
    .eq('team_id', teamId)
    .eq('user_id', userId)
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ member: data });
});

router.delete('/:teamId/members/:userId', requireAuth, async (req: AuthRequest<{ teamId: string; userId: string }>, res) => {
  const { teamId, userId } = req.params;
  if (!req.accessToken) {
    return res.status(401).json({ error: 'Access token required' });
  }
  const supa = getUserClient(req.accessToken);
  const { error } = await supa.from('team_members').delete().eq('team_id', teamId).eq('user_id', userId);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

// Team API keys
const apiKeyInsert = z.object({ name: z.string().min(1), key: z.string().min(1) });

router.get('/:teamId/api-keys', requireAuth, async (req: AuthRequest<{ teamId: string }>, res) => {
  const { teamId } = req.params;
  if (!req.accessToken) {
    return res.status(401).json({ error: 'Access token required' });
  }
  const supa = getUserClient(req.accessToken);
  const { data, error } = await supa.from('team_api_keys').select('*').eq('team_id', teamId).order('created_at', { ascending: false });
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ apiKeys: data });
});

router.post('/:teamId/api-keys', requireAuth, async (req: AuthRequest<{ teamId: string }>, res) => {
  const { teamId } = req.params;
  const parsed = apiKeyInsert.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  if (!req.accessToken) {
    return res.status(401).json({ error: 'Access token required' });
  }
  const supa = getUserClient(req.accessToken);
  const { data, error } = await supa
    .from('team_api_keys')
    .insert({ team_id: teamId, name: parsed.data.name, key: parsed.data.key })
    .select('*')
    .single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(201).json({ apiKey: data });
});

router.delete('/:teamId/api-keys/:id', requireAuth, async (req: AuthRequest<{ teamId: string; id: string }>, res) => {
  const { teamId, id } = req.params;
  if (!req.accessToken) {
    return res.status(401).json({ error: 'Access token required' });
  }
  const supa = getUserClient(req.accessToken);
  const { error } = await supa.from('team_api_keys').delete().eq('team_id', teamId).eq('id', id);
  if (error) {return res.status(500).json({ error: error.message });}
  res.status(204).end();
});

// Team SSO config
const ssoUpdate = z.object({
  enabled: z.boolean().optional(),
  idpUrl: z.string().optional(),
  entityId: z.string().optional(),
  certificate: z.string().optional(),
});

router.get('/:teamId/sso-config', requireAuth, async (req: AuthRequest<{ teamId: string }>, res) => {
  const { teamId } = req.params;
  if (!req.accessToken) {
    return res.status(401).json({ error: 'Access token required' });
  }
  const supa = getUserClient(req.accessToken);
  const { data, error } = await supa.from('team_sso_config').select('*').eq('team_id', teamId).single();
  if (error && error.code !== 'PGRST116') {return res.status(500).json({ error: error.message });}
  res.json({ ssoConfig: data || null });
});

router.put('/:teamId/sso-config', requireAuth, async (req: AuthRequest<{ teamId: string }>, res) => {
  const { teamId } = req.params;
  const parsed = ssoUpdate.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  if (!req.accessToken) {
    return res.status(401).json({ error: 'Access token required' });
  }
  const patch: Partial<{ enabled: boolean; idp_url: string; entity_id: string; certificate: string }> = {
    enabled: parsed.data.enabled,
    idp_url: parsed.data.idpUrl,
    entity_id: parsed.data.entityId,
    certificate: parsed.data.certificate,
  };
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const supa = getUserClient(req.accessToken);
  const { data, error } = await supa.from('team_sso_config').upsert({ team_id: teamId, ...patch }, { onConflict: 'team_id' }).select('*').single();
  if (error) {return res.status(500).json({ error: error.message });}
  res.json({ ssoConfig: data });
});

export default router;
