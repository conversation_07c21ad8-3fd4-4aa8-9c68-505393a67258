
import React from 'react';
import Indicator from './Indicator';
import { UserIcon, PaletteIcon, ShieldCheckIcon, KeyIcon, ShieldLockIcon } from '../Icons';

const ApiSsoHelpScreenshot: React.FC = () => {
    return (
        <div className="relative my-6 p-4 border rounded-lg bg-zinc-50 dark:bg-zinc-900 overflow-hidden select-none text-xs">
            <Indicator number={1} position="top-24 right-[11rem]" arrow="down" />
            <Indicator number={2} position="top-24 right-8" arrow="down" />
            
            <h1 className="text-lg font-bold text-zinc-900 dark:text-zinc-100">Settings</h1>
            <p className="text-zinc-600 dark:text-zinc-400 mt-1 mb-4">Manage your account settings.</p>

            <div className="grid grid-cols-5 gap-1 p-1 bg-zinc-200 dark:bg-zinc-800 rounded-lg">
                <div className="text-zinc-600 dark:text-zinc-300 p-2 rounded-md flex items-center justify-center">
                    <UserIcon className="w-4 h-4 mr-1"/> Profile
                </div>
                <div className="text-zinc-600 dark:text-zinc-300 p-2 rounded-md flex items-center justify-center">
                    <PaletteIcon className="w-4 h-4 mr-1"/> Appearance
                </div>
                <div className="text-zinc-600 dark:text-zinc-300 p-2 rounded-md flex items-center justify-center">
                    <ShieldCheckIcon className="w-4 h-4 mr-1"/> Security
                </div>
                <div className="bg-white dark:bg-zinc-950 text-brand-600 dark:text-zinc-100 font-semibold p-2 rounded-md flex items-center justify-center shadow-sm">
                    <KeyIcon className="w-4 h-4 mr-1"/> API
                </div>
                <div className="text-zinc-600 dark:text-zinc-300 p-2 rounded-md flex items-center justify-center">
                    <ShieldLockIcon className="w-4 h-4 mr-1"/> SSO
                </div>
            </div>
            
             <div className="mt-2 w-full h-24 border-2 border-dashed border-zinc-200 dark:border-zinc-700 rounded-md bg-white dark:bg-zinc-800/50"></div>
        </div>
    );
};

export default ApiSsoHelpScreenshot;
