import express, { Request, Response } from 'express';
import request from 'supertest';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock authentication middleware
vi.mock('../server/middleware/auth', () => ({
  requireAuth: (_req: Request, _res: Response, next: () => void) => {
    (_req as any).accessToken = 'test-token';
    (_req as any).user = { id: 'test-user-id' };
    next();
  },
  getAccessToken: (_req: Request) => 'test-token',
}));

// Mock plan middleware
vi.mock('../server/middleware/plan', () => ({
  requirePremium: (_req: Request, _res: Response, next: () => void) => {
    // Simulate premium user for testing
    next();
  },
}));

function createSupabaseMock() {
  return {
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
    })),
    auth: {
      getUser: vi.fn().mockResolvedValue({
        data: { user: { id: 'test-user-id', email: '<EMAIL>' } },
        error: null
      })
    }
  };
}

describe('API Endpoints Integration Tests', () => {
  let app: express.Express;
  let supabaseMock: ReturnType<typeof createSupabaseMock>;

  beforeEach(async () => {
    // Set up test environment
    process.env.TEST_BYPASS_AUTH = '1';

    app = express();
    app.use(express.json());
    supabaseMock = createSupabaseMock();

    vi.doMock('../server/supabaseClient', () => ({
      getUserClient: () => supabaseMock,
      supabaseAdmin: supabaseMock,
    }));

    // Import routes after mocking
    const { default: teamsRouter } = await import('../server/routes/teams');
    const { default: documentVersionsRouter } = await import('../server/routes/documentVersions');

    // Mount routes as they are in the actual server
    app.use('/api/teams', teamsRouter);
    app.use('/api/documents', documentVersionsRouter);
  });

  afterEach(() => {
    vi.clearAllMocks();
    vi.resetModules();
  });

  describe('Teams API - Issue 1 Resolution', () => {
    it('should return teams data for authenticated premium users', async () => {
      const mockTeams = [
        { id: 'team1', name: 'Test Team 1', owner_id: 'test-user-id', status: 'active', created_at: '2024-01-01T00:00:00Z' },
        { id: 'team2', name: 'Test Team 2', owner_id: 'test-user-id', status: 'active', created_at: '2024-01-01T01:00:00Z' }
      ];

      supabaseMock.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({
          data: mockTeams,
          error: null
        })
      });

      const response = await request(app)
        .get('/api/teams');

      expect(response.status).toBe(200);
      expect(response.body.teams).toHaveLength(2);
      expect(response.body.teams[0].name).toBe('Test Team 1');
    });

    it('should handle database errors gracefully without 500 errors', async () => {
      supabaseMock.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({
          data: null,
          error: { message: 'Database connection error', code: 'PGRST301' }
        })
      });

      const response = await request(app)
        .get('/api/teams');

      expect(response.status).toBe(500);
      expect(response.body.error).toBe('Database connection error');
      // Verify it's a controlled 500, not an unhandled exception
    });
  });

  describe('Document Version Restore - Issue 2 Resolution', () => {
    it('should restore a document version successfully', async () => {
      const versionId = 'test-version-id';
      const documentId = 'test-document-id';
      
      // Mock version lookup
      const versionSelectMock = {
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: { document_id: documentId, content: 'Restored content' },
          error: null
        })
      };

      // Mock document lookup
      const documentSelectMock = {
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: { id: documentId, content: 'Current content' },
          error: null
        })
      };

      // Mock version creation (backup)
      const insertMock = {
        insert: vi.fn().mockResolvedValue({ error: null })
      };

      // Mock document update
      const updateMock = {
        update: vi.fn().mockReturnValue({
          eq: vi.fn().mockResolvedValue({ error: null })
        })
      };

      let callCount = 0;
      supabaseMock.from.mockImplementation((): any => {
        callCount++;
        if (callCount === 1) {
          return { select: vi.fn().mockReturnValue(versionSelectMock) };
        } else if (callCount === 2) {
          return { select: vi.fn().mockReturnValue(documentSelectMock) };
        } else if (callCount === 3) {
          return insertMock;
        } else {
          return updateMock;
        }
      });

      const response = await request(app)
        .put(`/api/documents/versions/${versionId}/restore`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.document.content).toBe('Restored content');
    });

    it('should return 404 for non-existent version', async () => {
      supabaseMock.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: null,
          error: { message: 'Document version not found', code: 'PGRST116' }
        })
      });

      const response = await request(app)
        .put('/api/documents/versions/non-existent-version/restore');

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Document version not found');
    });
  });

  describe('Route Consistency Verification', () => {
    it('should confirm document versions are mounted at /api/documents', async () => {
      // This test verifies the routing fix
      const response = await request(app)
        .get('/api/documents/test-doc-id/versions');

      // Should not be 404 (route exists), might be other error due to mocking
      expect(response.status).not.toBe(404);
    });
  });
});
