// @vitest-environment jsdom
import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';
import { describe, it, expect, vi } from 'vitest';
import WorkflowCanvas from '../components/WorkflowCanvas';

describe('WorkflowCanvas', () => {
  const user = { 
    id: 'user-1', 
    name: 'Test User', 
    planName: 'Premium',
    email: '<EMAIL>',
    password: '',
    isVerified: true,
    status: 'active' as const,
    documents: [],
    folders: [],
    quotaUsed: 0,
    quotaTotal: 100
  };
  const template = {
    id: 'template-1',
    name: 'Test Workflow',
    nodes: [
      { id: 'node-1', type: 'trigger' as const, data: { label: 'Start', triggerType: 'document-created' as const }, position: { x: 0, y: 0 } },
      { id: 'node-2', type: 'approval' as const, data: { label: 'Approve' }, position: { x: 100, y: 100 } },
    ],
    edges: [
      { id: 'edge-1', source: 'node-1', target: 'node-2' },
    ],
    status: 'active' as const
  };
  const onSave = vi.fn();
  const onBack = vi.fn();

  it('renders the WorkflowCanvas component', () => {
    const { container } = render(
      <WorkflowCanvas user={user} template={template} onSave={onSave} onBack={onBack} />
    );
    expect(container).toBeInTheDocument();
  });
});
