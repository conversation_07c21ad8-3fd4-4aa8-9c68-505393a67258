
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/IntegrationConfigurationModal.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> IntegrationConfigurationModal.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/90</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/90</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import React, { useState, useEffect } from 'react';<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
import { Integration, IntegrationConfig, DocumentStatus } from '../types';
<span class="cstat-no" title="statement not covered" >import { Button } from './ui/Button';</span>
<span class="cstat-no" title="statement not covered" >import { CloseIcon, SettingsIcon } from './Icons';</span>
<span class="cstat-no" title="statement not covered" >import { cn } from '../lib/utils';</span>
&nbsp;
interface IntegrationConfigurationModalProps {
  isOpen: boolean;
  onClose: () =&gt; void;
  integration: Integration | null;
  onSave: (config: IntegrationConfig) =&gt; void;
}
&nbsp;
<span class="cstat-no" title="statement not covered" >const ToggleSwitch: React.FC&lt;{ checked: boolean; onChange: (checked: boolean) =&gt; void; label: string; description: string; }&gt; = ({ checked, onChange, label, description }) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="flex items-center justify-between"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;label className="font-medium text-zinc-800 dark:text-zinc-200"&gt;{label}&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;p className="text-sm text-zinc-500 dark:text-zinc-400"&gt;{description}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;button</span>
<span class="cstat-no" title="statement not covered" >            type="button"</span>
<span class="cstat-no" title="statement not covered" >            role="switch"</span>
<span class="cstat-no" title="statement not covered" >            aria-checked={checked}</span>
<span class="cstat-no" title="statement not covered" >            onClick={() =&gt; onChange(!checked)}</span>
<span class="cstat-no" title="statement not covered" >            className={cn(</span>
<span class="cstat-no" title="statement not covered" >                'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 dark:ring-offset-zinc-900',</span>
<span class="cstat-no" title="statement not covered" >                checked ? 'bg-brand-600' : 'bg-zinc-200 dark:bg-zinc-700'</span>
<span class="cstat-no" title="statement not covered" >            )}</span>
        &gt;
<span class="cstat-no" title="statement not covered" >            &lt;span</span>
<span class="cstat-no" title="statement not covered" >                aria-hidden="true"</span>
<span class="cstat-no" title="statement not covered" >                className={cn(</span>
<span class="cstat-no" title="statement not covered" >                    'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',</span>
<span class="cstat-no" title="statement not covered" >                    checked ? 'translate-x-5' : 'translate-x-0'</span>
<span class="cstat-no" title="statement not covered" >                )}</span>
<span class="cstat-no" title="statement not covered" >            /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
);
&nbsp;
<span class="cstat-no" title="statement not covered" >const allStatuses: DocumentStatus[] = ['completed', 'approved', 'out-for-signature', 'in-review', 'draft'];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >const IntegrationConfigurationModal: React.FC&lt;IntegrationConfigurationModalProps&gt; = ({ isOpen, onClose, integration, onSave }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const [config, setConfig] = useState&lt;IntegrationConfig&gt;({</span>
<span class="cstat-no" title="statement not covered" >        autoCreateCustomer: true,</span>
<span class="cstat-no" title="statement not covered" >        invoiceTriggerStatus: 'completed',</span>
<span class="cstat-no" title="statement not covered" >        defaultInvoiceItem: 'General Services',</span>
<span class="cstat-no" title="statement not covered" >    });</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (integration?.config) {</span>
<span class="cstat-no" title="statement not covered" >            setConfig(integration.config);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >    }, [integration]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!isOpen || !integration) {return null;}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const handleSave = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        onSave(config);</span>
<span class="cstat-no" title="statement not covered" >    };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;header className="flex items-center justify-between p-4 border-b dark:border-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200 flex items-center gap-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;SettingsIcon className="w-5 h-5 text-brand-600" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                        Configure {integration.name}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;Button variant="ghost" size="icon" onClick={onClose}&gt;&lt;CloseIcon className="w-5 h-5"/&gt;&lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/header&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;main className="p-6 space-y-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;ToggleSwitch</span>
<span class="cstat-no" title="statement not covered" >                        checked={config.autoCreateCustomer}</span>
<span class="cstat-no" title="statement not covered" >                        onChange={(value) =&gt; setConfig(c =&gt; ({ ...c, autoCreateCustomer: value }))}</span>
<span class="cstat-no" title="statement not covered" >                        label="Auto-create new customers"</span>
<span class="cstat-no" title="statement not covered" >                        description="Automatically create a customer when a new client is added."</span>
<span class="cstat-no" title="statement not covered" >                    /&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;label className="block text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Invoice Trigger Status&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p className="text-sm text-zinc-500 dark:text-zinc-400 mb-2"&gt;Create an invoice when a document status changes to:&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;select</span>
<span class="cstat-no" title="statement not covered" >                            value={config.invoiceTriggerStatus}</span>
<span class="cstat-no" title="statement not covered" >                            onChange={(e) =&gt; setConfig(c =&gt; ({ ...c, invoiceTriggerStatus: e.target.value as DocumentStatus }))}</span>
<span class="cstat-no" title="statement not covered" >                            className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 shadow-sm p-2"</span>
                        &gt;
<span class="cstat-no" title="statement not covered" >                            {allStatuses.map(s =&gt; &lt;option key={s} value={s} className="capitalize"&gt;{s.replace('-', ' ')}&lt;/option&gt;)}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/select&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;label className="block text-sm font-medium text-zinc-700 dark:text-zinc-300"&gt;Default Service/Product Item&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p className="text-sm text-zinc-500 dark:text-zinc-400 mb-2"&gt;The default item to use when creating invoices.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;input</span>
<span class="cstat-no" title="statement not covered" >                            type="text"</span>
<span class="cstat-no" title="statement not covered" >                            value={config.defaultInvoiceItem}</span>
<span class="cstat-no" title="statement not covered" >                            onChange={(e) =&gt; setConfig(c =&gt; ({ ...c, defaultInvoiceItem: e.target.value }))}</span>
<span class="cstat-no" title="statement not covered" >                            className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 shadow-sm p-2"</span>
<span class="cstat-no" title="statement not covered" >                        /&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/main&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t dark:border-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;Button variant="outline" onClick={onClose}&gt;Cancel&lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;Button onClick={handleSave}&gt;Save Configuration&lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/footer&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
    );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default IntegrationConfigurationModal;</span></pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    