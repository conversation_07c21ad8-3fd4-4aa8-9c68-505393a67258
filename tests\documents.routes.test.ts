import request from 'supertest';
import express, { Request, Response } from 'express';
import { describe, it, beforeEach, afterEach, expect, vi } from 'vitest';
import { DEFAULT_SUPABASE_ERROR_MESSAGE } from '../server/lib/httpErrors';

vi.mock('../server/middleware/auth', () => ({
  requireAuth: (_req: Request, _res: Response, next: () => void) => {
    (_req as any).accessToken = 'test-token';
    (_req as any).user = { id: 'user-1' };
    next();
  },
  getAccessToken: (_req: Request) => 'test-token',
}));

vi.mock('../server/lib/batchUtils', () => ({
  batchFetchDocuments: vi.fn(),
}));

function createSupabaseMock() {
  const fromObj = {
    select: vi.fn().mockReturnThis(),
    order: vi.fn().mockReturnThis(),
    range: vi.fn().mockResolvedValue({ data: [], error: null, count: 0 }),
    insert: vi.fn().mockReturnThis(),
    update: vi.fn().mockReturnThis(),
    eq: vi.fn().mockReturnThis(),
    single: vi.fn().mockReturnThis(),
    gte: vi.fn().mockReturnThis(),
    count: 1,
    delete: vi.fn(() => ({
      eq: vi.fn().mockResolvedValue({ error: null })
    })),
    auth: { getUser: vi.fn() },
  };
  return {
    from: vi.fn(() => fromObj),
    auth: { getUser: vi.fn() },
  };
}

describe('documents API routes', () => {
  let app: express.Express;
  let supabaseMock: ReturnType<typeof createSupabaseMock>;

  beforeEach(async () => {
    app = express();
    app.use(express.json());
    supabaseMock = createSupabaseMock();
    vi.doMock('../server/supabaseClient', () => ({
      getUserClient: () => supabaseMock,
      supabaseAdmin: supabaseMock,
    }));
    
    // Also mock the getUserClient import
    vi.mock('../server/supabaseClient', () => ({
      getUserClient: () => supabaseMock,
      supabaseAdmin: supabaseMock,
    }));
    
    // Mock batchFetchDocuments
    const { batchFetchDocuments } = await import('../server/lib/batchUtils');
    vi.mocked(batchFetchDocuments).mockResolvedValue([]);
    
    const documentsRouter = (await import('../server/routes/documents')).default;
    app.use('/documents', documentsRouter);
  });

  afterEach(() => {
    vi.resetModules();
    vi.clearAllMocks();
  });

  it('GET /documents returns documents', async () => {
    const from = supabaseMock.from();
    from.select.mockReturnThis();
    from.order.mockReturnThis();
    from.range.mockResolvedValue({
      data: [{ id: '1', name: 'Doc1', status: 'draft', folder_id: null, created_at: '2024-01-01', updated_at: '2024-01-02', content: '', client_id: null, value: null }],
      error: null,
      count: 1,
    });

    // Mock batchFetchDocuments to return enriched documents
    const { batchFetchDocuments } = await import('../server/lib/batchUtils');
    vi.mocked(batchFetchDocuments).mockResolvedValue([
      { id: '1', name: 'Doc1', comments: [], activityLogs: [], versions: [], collaborators: [], signatures: [] }
    ] as any);

    const res = await request(app).get('/documents');
    expect(res.status).toBe(200);
    expect(res.body.documents[0]).toMatchObject({ id: '1', name: 'Doc1' });
    expect(res.body.pagination).toMatchObject({ limit: expect.any(Number), offset: 0, nextOffset: 1, total: 1, hasMore: false });
  });

  it('POST /documents creates a document', async () => {
    // Mock plan check (free user, under quota)
    const from = supabaseMock.from();
    // Plan check: .from('profiles').select('plan_name').eq('id', userId).single()
    from.select.mockReturnThis();
    from.eq.mockReturnThis();
    from.single
      .mockResolvedValueOnce({ data: { plan_name: 'Registered User' } }) // plan
      .mockResolvedValueOnce({ data: { id: 'doc-1', name: 'Doc2' }, error: null }) // document insert
      .mockResolvedValueOnce({ data: { id: 'usage-1' }, error: null }); // usage_events insert
    // Usage count: .from('usage_events').select('id', { count: 'exact', head: true })
    from.select.mockReturnThis();
    from.gte = vi.fn().mockReturnThis();
    from.count = 1;
    // Insert mocks
    from.insert.mockReturnThis();
    from.order.mockReturnThis();
    from.update.mockReturnThis();
    from.delete.mockReturnThis();
    supabaseMock.auth.getUser.mockResolvedValue({ data: { user: { id: 'user-1' } } });
    const res = await request(app).post('/documents').send({ name: 'Doc2', content: 'abc' });
    expect(res.status).toBe(201);
    expect(res.body.document).toMatchObject({ name: 'Doc2' });
  });

  it('PUT /documents/:id updates a document', async () => {
    const from = supabaseMock.from();
    from.update.mockReturnThis();
    from.eq.mockReturnThis();
    from.select.mockReturnThis();
    from.single.mockResolvedValue({ data: { id: 'doc-2', name: 'Updated Doc' }, error: null });
    const res = await request(app).put('/documents/doc-2').send({ name: 'Updated Doc' });
    expect(res.status).toBe(200);
    expect(res.body.document).toMatchObject({ id: 'doc-2', name: 'Updated Doc' });
  });

  it('DELETE /documents/:id deletes a document', async () => {
    const _from = supabaseMock.from();
    // delete().eq() is already mocked in the factory
    const res = await request(app).delete('/documents/doc-3');
    expect(res.status).toBe(204);
  });

  it('GET /documents returns generic message for unexpected Supabase error', async () => {
    const from = supabaseMock.from();
    from.select.mockReturnThis();
    from.order.mockReturnThis();
    from.range.mockResolvedValueOnce({ data: null, error: { message: 'db down', code: '99999' } as any });

    const res = await request(app).get('/documents');

    expect(res.status).toBe(500);
    expect(res.body.error).toBe(DEFAULT_SUPABASE_ERROR_MESSAGE);
  });
});
