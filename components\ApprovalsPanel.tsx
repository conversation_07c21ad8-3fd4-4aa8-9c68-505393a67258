
import React from 'react';
import { Document as DocType, User } from '../types';
import { CheckCircleIcon, ClockIcon, ThumbsDownIcon } from './Icons';
import { cn } from '../lib/utils';

interface ApprovalsPanelProps {
  document: DocType;
  allUsers: User[];
}

const ApprovalsPanel: React.FC<ApprovalsPanelProps> = ({ document, allUsers }) => {
    
    const getApproverInfo = (email: string) => {
        return allUsers.find(u => u.email === email) || { name: email, avatarUrl: '' };
    }

    const statusInfo = {
        pending: { text: 'Pending', icon: ClockIcon, classes: 'bg-zinc-100 text-zinc-600' },
        approved: { text: 'Approved', icon: CheckCircleIcon, classes: 'bg-green-100 text-green-800' },
        'changes-requested': { text: 'Changes Requested', icon: ThumbsDownIcon, classes: 'bg-amber-100 text-amber-800' },
    };

    if (document.status === 'draft' || !document.approvers || document.approvers.length === 0) {
        return (
            <div className="flex-1 flex items-center justify-center p-6 text-center">
                <p className="text-sm text-zinc-500">
                    No approval requests have been sent for this document.
                </p>
            </div>
        );
    }
    
    return (
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
            <h4 className="font-semibold text-zinc-800 dark:text-zinc-200 px-2">Approval Status</h4>
            {document.approvers.map((approver, index) => {
                const approverInfo = getApproverInfo(approver.email);
                const currentStatus = statusInfo[approver.status];
                const Icon = currentStatus.icon;

                return (
                    <div key={index} className="flex items-center gap-3 p-2 rounded-lg">
                        <img src={approverInfo.avatarUrl} alt={approverInfo.name} className="w-9 h-9 rounded-full"/>
                        <div className="flex-1">
                            <p className="text-sm font-medium text-zinc-800 dark:text-zinc-200">{approverInfo.name}</p>
                            <p className="text-xs text-zinc-500 dark:text-zinc-400">{approver.email}</p>
                        </div>
                        <div className={cn(
                            "flex items-center text-xs font-medium px-2 py-1 rounded-full",
                            currentStatus.classes
                        )}>
                            <Icon className="w-4 h-4 mr-1"/>
                            {currentStatus.text}
                        </div>
                    </div>
                );
            })}
        </div>
    );
};

export default ApprovalsPanel;