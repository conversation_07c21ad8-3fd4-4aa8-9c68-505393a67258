<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Contract & Legal Clause Generator</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script>
      // Add dark class asap to avoid FOUC
      try {
        if (
          localStorage.theme === 'dark' ||
          (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)
        ) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      } catch(e) {}
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script&display=swap" rel="stylesheet">
    <style>
      body {
        font-family: 'Inter', sans-serif;
        background-color: #f4f4f5; /* bg-zinc-100 */
        transition: background-color 0.3s ease;
      }
       html.dark body {
        background-color: #09090b; /* bg-zinc-950 */
      }
      * {
        transition: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter, 0.2s ease-in-out;
      }
      /* Custom Scrollbar */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      ::-webkit-scrollbar-track {
        background: #e4e4e7; /* zinc-200 */
      }
      ::-webkit-scrollbar-thumb {
        background: #a1a1aa; /* zinc-400 */
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background: #71717a; /* zinc-500 */
      }
      html.dark ::-webkit-scrollbar-track {
        background: #27272a; /* zinc-800 */
      }
      html.dark ::-webkit-scrollbar-thumb {
        background: #52525b; /* zinc-600 */
      }
      html.dark ::-webkit-scrollbar-thumb:hover {
        background: #71717a; /* zinc-500 */
      }


      @keyframes bg-pan {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
      }
      .animated-gradient {
        background-size: 200% 200%;
        animation: bg-pan 15s ease infinite;
      }
      @keyframes typing-cursor {
        from { opacity: 1; }
        to { opacity: 0; }
      }
      .typing-cursor {
        animation: typing-cursor 0.7s infinite;
      }
      @keyframes fade-in-up {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
      }
      .animate-fade-in-up {
        animation: fade-in-up 0.5s ease-out forwards;
      }
      @keyframes float-up-down {
        0%, 100% { transform: translateY(0); }
        50% { transform: translateY(-10px); }
      }
      .animate-float {
        animation: float-up-down 4s ease-in-out infinite;
      }
       @keyframes fade-in-up-bubble {
        from { opacity: 0; transform: translateY(10px) scale(0.95); }
        to { opacity: 1; transform: translateY(0) scale(1); }
      }
      /* Document Editor Styles */
      .document-editor-content {
        outline: none;
        line-height: 1.7;
        font-family: Georgia, serif;
        font-size: 11pt;
        color: #18181b; /* zinc-900 */
      }
      html.dark .document-editor-content { color: #d4d4d8; /* zinc-300 */ }
      .document-editor-content h1 { font-size: 18pt; font-weight: bold; margin-top: 1.5rem; margin-bottom: 1rem; color: #09090b;}
      html.dark .document-editor-content h1 { color: #fafafa; }
      .document-editor-content h2 { font-size: 14pt; font-weight: bold; margin-top: 1.25rem; margin-bottom: 0.75rem; color: #18181b;}
      html.dark .document-editor-content h2 { color: #f4f4f5; }
      .document-editor-content ul { list-style-type: disc; margin-left: 2rem; margin-bottom: 1rem; }
      .document-editor-content p { margin-bottom: 1rem; }
      .document-editor-content[contenteditable=true]:empty:before {
        content: attr(data-placeholder);
        color: #a1a1aa; /* zinc-400 */
        font-style: italic;
        cursor: text;
      }
      
      /* A4 Page Styling */
      .page-container {
        padding: 1rem; /* Reduced padding for mobile */
        background-color: #e4e4e7; /* zinc-200 */
      }
      html.dark .page-container { background-color: #18181b; /* zinc-900 */ }
      .a4-page {
        background: white;
        width: 100%;
        min-height: auto;
        display: block;
        margin: 0 auto;
        box-shadow: 0 0 0.5cm rgba(0,0,0,0.15);
        padding: 1.5rem; /* Mobile padding */
        position: relative; /* For comment button positioning */
        box-sizing: border-box;
      }
       html.dark .a4-page {
        background: #27272a; /* zinc-800 */
        box-shadow: 0 0 0.5cm rgba(0,0,0,0.3);
      }
      
      @media (min-width: 1024px) {
        .page-container {
          padding: 2rem;
        }
        .a4-page {
          max-width: 21cm; /* Changed from fixed width to be responsive */
          min-height: 29.7cm;
          margin-bottom: 0.5cm;
          padding: 2cm;
        }
      }

      @media print {
        body, .page-container {
          margin: 0;
          box-shadow: 0;
          background: white;
        }
        .a4-page {
          box-shadow: none;
          margin: 0;
          width: auto;
          min-height: auto;
        }
      }

      /* Commenting Styles */
      .comment-highlight {
        background-color: rgba(161, 216, 255, 0.4);
        cursor: pointer;
      }
      .comment-highlight:hover, .comment-highlight.active {
        background-color: rgba(161, 216, 255, 0.7);
      }
      html.dark .comment-highlight { background-color: rgba(79, 70, 229, 0.4); }
      html.dark .comment-highlight:hover, html.dark .comment-highlight.active { background-color: rgba(79, 70, 229, 0.6); }

      .comment-add-button {
        position: absolute;
        z-index: 10;
        transform: translateY(-100%);
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      }
      .comment-thread {
        scroll-margin-top: 1rem;
      }

      /* E-Signature Styles */
      .signature-font {
        font-family: 'Dancing Script', cursive;
        font-size: 2.5rem;
        line-height: 1;
        color: #4338ca; /* brand-700 */
      }
      html.dark .signature-font { color: #818cf8; /* brand-400 */ }
      
      /* Version Comparison (Redline) Styles */
      .version-comparison-content del {
        background-color: #f8d7da; /* Bootstrap's danger light */
        text-decoration: line-through;
        color: #721c24;
      }
       .version-comparison-content ins {
        background-color: #d4edda; /* Bootstrap's success light */
        text-decoration: none;
        color: #155724;
      }
       html.dark .version-comparison-content del {
        background-color: #582121;
        color: #f8d7da;
      }
      html.dark .version-comparison-content ins {
        background-color: #1c4b32;
        color: #d4edda;
      }

    </style>
  <script type="importmap">
{
  "imports": {
    "react": "https://aistudiocdn.com/react@^19.1.1",
    "react/": "https://aistudiocdn.com/react@^19.1.1/",
    "react-dom/": "https://aistudiocdn.com/react-dom@^19.1.1/",
    "@google/genai": "https://aistudiocdn.com/@google/genai@^1.17.0"
  }
}
</script>
</head>
  <body>
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>

    <!-- Charting Library -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  </body>
</html>
