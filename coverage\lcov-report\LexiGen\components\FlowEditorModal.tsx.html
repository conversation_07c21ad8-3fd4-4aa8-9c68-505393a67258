
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for LexiGen/components/FlowEditorModal.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">LexiGen/components</a> FlowEditorModal.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/214</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/214</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a>
<a name='L213'></a><a href='#L213'>213</a>
<a name='L214'></a><a href='#L214'>214</a>
<a name='L215'></a><a href='#L215'>215</a>
<a name='L216'></a><a href='#L216'>216</a>
<a name='L217'></a><a href='#L217'>217</a>
<a name='L218'></a><a href='#L218'>218</a>
<a name='L219'></a><a href='#L219'>219</a>
<a name='L220'></a><a href='#L220'>220</a>
<a name='L221'></a><a href='#L221'>221</a>
<a name='L222'></a><a href='#L222'>222</a>
<a name='L223'></a><a href='#L223'>223</a>
<a name='L224'></a><a href='#L224'>224</a>
<a name='L225'></a><a href='#L225'>225</a>
<a name='L226'></a><a href='#L226'>226</a>
<a name='L227'></a><a href='#L227'>227</a>
<a name='L228'></a><a href='#L228'>228</a>
<a name='L229'></a><a href='#L229'>229</a>
<a name='L230'></a><a href='#L230'>230</a>
<a name='L231'></a><a href='#L231'>231</a>
<a name='L232'></a><a href='#L232'>232</a>
<a name='L233'></a><a href='#L233'>233</a>
<a name='L234'></a><a href='#L234'>234</a>
<a name='L235'></a><a href='#L235'>235</a>
<a name='L236'></a><a href='#L236'>236</a>
<a name='L237'></a><a href='#L237'>237</a>
<a name='L238'></a><a href='#L238'>238</a>
<a name='L239'></a><a href='#L239'>239</a>
<a name='L240'></a><a href='#L240'>240</a>
<a name='L241'></a><a href='#L241'>241</a>
<a name='L242'></a><a href='#L242'>242</a>
<a name='L243'></a><a href='#L243'>243</a>
<a name='L244'></a><a href='#L244'>244</a>
<a name='L245'></a><a href='#L245'>245</a>
<a name='L246'></a><a href='#L246'>246</a>
<a name='L247'></a><a href='#L247'>247</a>
<a name='L248'></a><a href='#L248'>248</a>
<a name='L249'></a><a href='#L249'>249</a>
<a name='L250'></a><a href='#L250'>250</a>
<a name='L251'></a><a href='#L251'>251</a>
<a name='L252'></a><a href='#L252'>252</a>
<a name='L253'></a><a href='#L253'>253</a>
<a name='L254'></a><a href='#L254'>254</a>
<a name='L255'></a><a href='#L255'>255</a>
<a name='L256'></a><a href='#L256'>256</a>
<a name='L257'></a><a href='#L257'>257</a>
<a name='L258'></a><a href='#L258'>258</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >import React, { useState, useEffect } from 'react';<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
<span class="cstat-no" title="statement not covered" >import { Button } from './ui/Button';</span>
<span class="cstat-no" title="statement not covered" >import { CloseIcon, ArrowRightIcon } from './Icons';</span>
import { User, Flow, Connector } from '../types';
<span class="cstat-no" title="statement not covered" >import { ALL_CONNECTORS } from './connectors';</span>
<span class="cstat-no" title="statement not covered" >import { cn } from '../lib/utils';</span>
&nbsp;
interface FlowEditorModalProps {
  isOpen: boolean;
  onClose: () =&gt; void;
  user: User;
  onSave: (flowData: Omit&lt;Flow, 'id' | 'userId' | 'createdAt'&gt;, flowId?: string) =&gt; void;
  initialFlow?: Flow | null;
}
&nbsp;
type FlowData = {
    name: string;
    triggerConnectionId: string | null;
    triggerKey: string | null;
    actionConnectionId: string | null;
    actionKey: string | null;
    fieldMapping: Record&lt;string, string&gt;;
};
&nbsp;
<span class="cstat-no" title="statement not covered" >const FlowEditorModal: React.FC&lt;FlowEditorModalProps&gt; = ({ isOpen, onClose, user, onSave, initialFlow }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    const [step, setStep] = useState(1);</span>
<span class="cstat-no" title="statement not covered" >    const [flowData, setFlowData] = useState&lt;FlowData&gt;({</span>
<span class="cstat-no" title="statement not covered" >        name: '',</span>
<span class="cstat-no" title="statement not covered" >        triggerConnectionId: null,</span>
<span class="cstat-no" title="statement not covered" >        triggerKey: null,</span>
<span class="cstat-no" title="statement not covered" >        actionConnectionId: null,</span>
<span class="cstat-no" title="statement not covered" >        actionKey: null,</span>
<span class="cstat-no" title="statement not covered" >        fieldMapping: {},</span>
<span class="cstat-no" title="statement not covered" >    });</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    useEffect(() =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (initialFlow) {</span>
<span class="cstat-no" title="statement not covered" >            setFlowData({</span>
<span class="cstat-no" title="statement not covered" >                name: initialFlow.name,</span>
<span class="cstat-no" title="statement not covered" >                triggerConnectionId: initialFlow.trigger.connectionId,</span>
<span class="cstat-no" title="statement not covered" >                triggerKey: initialFlow.trigger.triggerKey,</span>
<span class="cstat-no" title="statement not covered" >                actionConnectionId: initialFlow.action.connectionId,</span>
<span class="cstat-no" title="statement not covered" >                actionKey: initialFlow.action.actionKey,</span>
<span class="cstat-no" title="statement not covered" >                fieldMapping: initialFlow.fieldMapping,</span>
<span class="cstat-no" title="statement not covered" >            });</span>
<span class="cstat-no" title="statement not covered" >            setStep(1);</span>
<span class="cstat-no" title="statement not covered" >        } else {</span>
<span class="cstat-no" title="statement not covered" >             setFlowData({ name: '', triggerConnectionId: null, triggerKey: null, actionConnectionId: null, actionKey: null, fieldMapping: {} });</span>
<span class="cstat-no" title="statement not covered" >             setStep(1);</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >    }, [initialFlow, isOpen]);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    if (!isOpen) {return null;}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const userConnections = user.connections || [];</span>
    
<span class="cstat-no" title="statement not covered" >    const getConnectorById = (id: string) =&gt; ALL_CONNECTORS.find(c =&gt; c.id === id);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const getConnectorForConnection = (connectionId: string): Connector | undefined =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        const conn = userConnections.find(c =&gt; c.id === connectionId);</span>
<span class="cstat-no" title="statement not covered" >        return conn ? getConnectorById(conn.connectorId) : undefined;</span>
<span class="cstat-no" title="statement not covered" >    };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const selectedTriggerConnector = flowData.triggerConnectionId ? getConnectorForConnection(flowData.triggerConnectionId) : null;</span>
<span class="cstat-no" title="statement not covered" >    const selectedActionConnector = flowData.actionConnectionId ? getConnectorForConnection(flowData.actionConnectionId) : null;</span>
    
<span class="cstat-no" title="statement not covered" >    const selectedTrigger = selectedTriggerConnector?.triggers?.find(t =&gt; t.key === flowData.triggerKey);</span>
<span class="cstat-no" title="statement not covered" >    const selectedAction = selectedActionConnector?.actions?.find(a =&gt; a.key === flowData.actionKey);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const handleSave = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        if (!flowData.name || !flowData.triggerConnectionId || !flowData.triggerKey || !flowData.actionConnectionId || !flowData.actionKey) {</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >        onSave({</span>
<span class="cstat-no" title="statement not covered" >            name: flowData.name,</span>
<span class="cstat-no" title="statement not covered" >            trigger: { connectionId: flowData.triggerConnectionId, triggerKey: flowData.triggerKey },</span>
<span class="cstat-no" title="statement not covered" >            action: { connectionId: flowData.actionConnectionId, actionKey: flowData.actionKey },</span>
<span class="cstat-no" title="statement not covered" >            fieldMapping: flowData.fieldMapping,</span>
<span class="cstat-no" title="statement not covered" >            status: initialFlow?.status || 'inactive'</span>
<span class="cstat-no" title="statement not covered" >        }, initialFlow?.id);</span>
<span class="cstat-no" title="statement not covered" >    };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const steps = [</span>
<span class="cstat-no" title="statement not covered" >        { name: 'Trigger', isComplete: !!(flowData.triggerConnectionId &amp;&amp; flowData.triggerKey) },</span>
<span class="cstat-no" title="statement not covered" >        { name: 'Action', isComplete: !!(flowData.actionConnectionId &amp;&amp; flowData.actionKey) },</span>
<span class="cstat-no" title="statement not covered" >        { name: 'Map Fields', isComplete: true },</span>
<span class="cstat-no" title="statement not covered" >        { name: 'Review', isComplete: !!flowData.name },</span>
<span class="cstat-no" title="statement not covered" >    ];</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    const renderStepContent = () =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        switch (step) {</span>
<span class="cstat-no" title="statement not covered" >            case 1: // Select Trigger</span>
<span class="cstat-no" title="statement not covered" >                return (</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;h3 className="font-semibold text-lg mb-2"&gt;1. Choose a Trigger&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p className="text-sm text-zinc-500 mb-4"&gt;Select the event that starts your flow.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;div className="grid grid-cols-2 gap-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                             &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;label className="font-medium text-sm"&gt;App&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;div className="space-y-2 mt-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    {userConnections.map(conn =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                                        const connector = getConnectorById(conn.connectorId);</span>
<span class="cstat-no" title="statement not covered" >                                        if (!connector || !connector.triggers || connector.triggers.length === 0) {return null;}</span>
<span class="cstat-no" title="statement not covered" >                                        return (</span>
<span class="cstat-no" title="statement not covered" >                                            &lt;button key={conn.id} onClick={() =&gt; setFlowData({...flowData, triggerConnectionId: conn.id, triggerKey: null })} className={cn("w-full flex items-center gap-3 p-3 border rounded-lg text-left", flowData.triggerConnectionId === conn.id ? 'border-brand-500 ring-2 ring-brand-200' : 'border-zinc-300')}&gt;</span>
<span class="cstat-no" title="statement not covered" >                                                &lt;connector.icon className="w-6 h-6"/&gt; {connector.name}</span>
<span class="cstat-no" title="statement not covered" >                                            &lt;/button&gt;</span>
                                        )
<span class="cstat-no" title="statement not covered" >                                    })}</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                            {selectedTriggerConnector &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                                &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                                     &lt;label className="font-medium text-sm"&gt;Event&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                                     &lt;div className="space-y-2 mt-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                        {selectedTriggerConnector.triggers?.map(trigger =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                                            &lt;button key={trigger.key} onClick={() =&gt; setFlowData({...flowData, triggerKey: trigger.key})} className={cn("w-full p-3 border rounded-lg text-left", flowData.triggerKey === trigger.key ? 'border-brand-500 ring-2 ring-brand-200' : 'border-zinc-300')}&gt;</span>
<span class="cstat-no" title="statement not covered" >                                                &lt;p className="font-semibold"&gt;{trigger.name}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                                                &lt;p className="text-xs text-zinc-500"&gt;{trigger.description}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                                            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                                        ))}</span>
<span class="cstat-no" title="statement not covered" >                                     &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/div&gt;</span>
                            )}
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                );
<span class="cstat-no" title="statement not covered" >            case 2: // Select Action</span>
<span class="cstat-no" title="statement not covered" >                return (</span>
<span class="cstat-no" title="statement not covered" >                     &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;h3 className="font-semibold text-lg mb-2"&gt;2. Choose an Action&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p className="text-sm text-zinc-500 mb-4"&gt;Select the event that your flow will perform.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;div className="grid grid-cols-2 gap-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                             &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;label className="font-medium text-sm"&gt;App&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;div className="space-y-2 mt-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    {userConnections.map(conn =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                                        const connector = getConnectorById(conn.connectorId);</span>
<span class="cstat-no" title="statement not covered" >                                        if (!connector || !connector.actions || connector.actions.length === 0) {return null;}</span>
<span class="cstat-no" title="statement not covered" >                                        return (</span>
<span class="cstat-no" title="statement not covered" >                                            &lt;button key={conn.id} onClick={() =&gt; setFlowData({...flowData, actionConnectionId: conn.id, actionKey: null })} className={cn("w-full flex items-center gap-3 p-3 border rounded-lg text-left", flowData.actionConnectionId === conn.id ? 'border-brand-500 ring-2 ring-brand-200' : 'border-zinc-300')}&gt;</span>
<span class="cstat-no" title="statement not covered" >                                                &lt;connector.icon className="w-6 h-6"/&gt; {connector.name}</span>
<span class="cstat-no" title="statement not covered" >                                            &lt;/button&gt;</span>
                                        )
<span class="cstat-no" title="statement not covered" >                                    })}</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                            {selectedActionConnector &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >                                &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                                     &lt;label className="font-medium text-sm"&gt;Event&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                                     &lt;div className="space-y-2 mt-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                        {selectedActionConnector.actions?.map(action =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                                            &lt;button key={action.key} onClick={() =&gt; setFlowData({...flowData, actionKey: action.key})} className={cn("w-full p-3 border rounded-lg text-left", flowData.actionKey === action.key ? 'border-brand-500 ring-2 ring-brand-200' : 'border-zinc-300')}&gt;</span>
<span class="cstat-no" title="statement not covered" >                                                &lt;p className="font-semibold"&gt;{action.name}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                                                &lt;p className="text-xs text-zinc-500"&gt;{action.description}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                                            &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >                                        ))}</span>
<span class="cstat-no" title="statement not covered" >                                     &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/div&gt;</span>
                            )}
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                );
<span class="cstat-no" title="statement not covered" >            case 3: // Map fields</span>
<span class="cstat-no" title="statement not covered" >                 return (</span>
<span class="cstat-no" title="statement not covered" >                     &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;h3 className="font-semibold text-lg mb-2"&gt;3. Map Data Fields&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p className="text-sm text-zinc-500 mb-4"&gt;Connect the data from your trigger to your action.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        {(selectedAction?.operation.inputFields || []).map(inputField =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                            &lt;div key={inputField.key} className="grid grid-cols-2 items-center gap-4 mb-3 p-3 bg-zinc-50 rounded-lg"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;div className="text-right"&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;p className="font-medium"&gt;{inputField.label}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;p className="text-xs text-zinc-500"&gt;{selectedActionConnector?.name}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;select </span>
<span class="cstat-no" title="statement not covered" >                                        value={flowData.fieldMapping[inputField.key] || ''} </span>
<span class="cstat-no" title="statement not covered" >                                        onChange={(e) =&gt; setFlowData(prev =&gt; ({...prev, fieldMapping: {...prev.fieldMapping, [inputField.key]: e.target.value}}))}</span>
<span class="cstat-no" title="statement not covered" >                                        className="w-full p-2 border border-zinc-300 rounded-md"</span>
                                    &gt;
<span class="cstat-no" title="statement not covered" >                                        &lt;option value=""&gt;Select a field...&lt;/option&gt;</span>
<span class="cstat-no" title="statement not covered" >                                        {(selectedTrigger?.operation.outputFields || []).map(outputField =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                                            &lt;option key={outputField.key} value={outputField.key}&gt;{outputField.label}&lt;/option&gt;</span>
<span class="cstat-no" title="statement not covered" >                                        ))}</span>
<span class="cstat-no" title="statement not covered" >                                    &lt;/select&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        ))}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                );
<span class="cstat-no" title="statement not covered" >            case 4: // Review</span>
<span class="cstat-no" title="statement not covered" >                return (</span>
<span class="cstat-no" title="statement not covered" >                     &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;h3 className="font-semibold text-lg mb-2"&gt;4. Review and Save&lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;p className="text-sm text-zinc-500 mb-4"&gt;Give your flow a name and save it.&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;div className="p-4 bg-zinc-50 rounded-lg mb-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;p&gt;&lt;strong&gt;Trigger:&lt;/strong&gt; {selectedTrigger?.name} in {selectedTriggerConnector?.name}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;p&gt;&lt;strong&gt;Action:&lt;/strong&gt; {selectedAction?.name} in {selectedActionConnector?.name}&lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                         &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;label htmlFor="flowName" className="font-medium text-sm"&gt;Flow Name&lt;/label&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;input</span>
<span class="cstat-no" title="statement not covered" >                                id="flowName"</span>
<span class="cstat-no" title="statement not covered" >                                type="text"</span>
<span class="cstat-no" title="statement not covered" >                                value={flowData.name}</span>
<span class="cstat-no" title="statement not covered" >                                onChange={e =&gt; setFlowData({...flowData, name: e.target.value})}</span>
<span class="cstat-no" title="statement not covered" >                                placeholder="e.g., Create Invoice from Completed Contract"</span>
<span class="cstat-no" title="statement not covered" >                                className="w-full mt-1 p-2 border border-zinc-300 rounded-md"</span>
<span class="cstat-no" title="statement not covered" >                            /&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
                );
<span class="cstat-no" title="statement not covered" >            default: return null;</span>
<span class="cstat-no" title="statement not covered" >        }</span>
<span class="cstat-no" title="statement not covered" >    }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >    return (</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-4xl h-[80vh] flex flex-col"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;header className="flex items-center justify-between p-4 border-b dark:border-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200"&gt;{initialFlow ? 'Edit Flow' : 'Create New Flow'}&lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;Button variant="ghost" size="icon" onClick={onClose}&gt;&lt;CloseIcon className="w-5 h-5"/&gt;&lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/header&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="p-6 border-b dark:border-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;ol className="flex items-center w-full"&gt;</span>
<span class="cstat-no" title="statement not covered" >                        {steps.map((s, index) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >                            &lt;li key={s.name} className={cn("flex w-full items-center", index &lt; steps.length - 1 &amp;&amp; "after:content-[''] after:w-full after:h-1 after:border-b after:border-zinc-300 dark:after:border-zinc-700 after:border-1 after:inline-block", index &lt; step - 1 &amp;&amp; 'after:border-brand-500')}&gt;</span>
<span class="cstat-no" title="statement not covered" >                                &lt;span className={cn("flex items-center justify-center w-10 h-10 rounded-full shrink-0", index &lt; step ? "bg-brand-600 text-white" : "bg-zinc-200 dark:bg-zinc-700 dark:text-zinc-200")}&gt;</span>
<span class="cstat-no" title="statement not covered" >                                    {index + 1}</span>
<span class="cstat-no" title="statement not covered" >                                &lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >                            &lt;/li&gt;</span>
<span class="cstat-no" title="statement not covered" >                        ))}</span>
<span class="cstat-no" title="statement not covered" >                    &lt;/ol&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;main className="p-6 flex-1 overflow-y-auto"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {renderStepContent()}</span>
<span class="cstat-no" title="statement not covered" >                &lt;/main&gt;</span>
<span class="cstat-no" title="statement not covered" >                 &lt;footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-between items-center rounded-b-2xl border-t dark:border-zinc-800"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;Button variant="outline" onClick={() =&gt; setStep(s =&gt; Math.max(1, s - 1))} disabled={step === 1}&gt;Back&lt;/Button&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                    {step &lt; 4 ? (</span>
<span class="cstat-no" title="statement not covered" >                        &lt;Button onClick={() =&gt; setStep(s =&gt; Math.min(4, s + 1))} disabled={!steps[step-1].isComplete}&gt;</span>
<span class="cstat-no" title="statement not covered" >                            Continue &lt;ArrowRightIcon className="w-4 h-4 ml-2"/&gt;</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/Button&gt;</span>
                    ) : (
<span class="cstat-no" title="statement not covered" >                        &lt;Button onClick={handleSave} disabled={!steps[step-1].isComplete}&gt;</span>
<span class="cstat-no" title="statement not covered" >                            {initialFlow ? 'Save Flow' : 'Save &amp; Activate'}</span>
<span class="cstat-no" title="statement not covered" >                        &lt;/Button&gt;</span>
                    )}
<span class="cstat-no" title="statement not covered" >                    &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/footer&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
    );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default FlowEditorModal;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-09-13T08:35:56.562Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    