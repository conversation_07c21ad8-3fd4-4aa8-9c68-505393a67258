
import React from 'react';
import { ClockIcon, UndoIcon } from '../Icons';

const VersioningHelpScreenshot = () => {
  return (
    <div className="relative my-6 p-4 border rounded-lg bg-zinc-50 dark:bg-zinc-900 overflow-hidden select-none text-xs">
        <div className="p-2">
            <h3 className="font-semibold text-zinc-800 dark:text-zinc-200 flex items-center gap-2">
                <ClockIcon className="w-5 h-5"/>
                Versions
            </h3>
            <div className="mt-2 space-y-2">
                <div className="p-2 rounded-md bg-white dark:bg-zinc-800">
                    <p className="font-medium">Current Version</p>
                    <p className="text-zinc-500">Saved on {new Date().toLocaleDateString()}</p>
                </div>
                 <div className="flex items-center justify-between p-2 rounded-md bg-white dark:bg-zinc-800">
                    <div>
                        <p className="font-medium">Version 1</p>
                        <p className="text-zinc-500">Saved on {new Date(Date.now() - 86400000).toLocaleDateString()}</p>
                    </div>
                    <div className="flex items-center gap-1 text-zinc-600 dark:text-zinc-300 font-semibold px-2 py-1 rounded-md border border-zinc-300 dark:border-zinc-600 bg-white dark:bg-zinc-700">
                        <UndoIcon className="w-3 h-3 mr-1"/>Restore
                    </div>
                </div>
            </div>
        </div>
    </div>
  );
};
export default VersioningHelpScreenshot;
