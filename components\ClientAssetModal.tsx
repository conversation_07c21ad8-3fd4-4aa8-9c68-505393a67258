import React, { useEffect, useState } from 'react';
import { Button } from './ui/Button';
import { CloseIcon, LinkIcon } from './Icons';
import { ClientAsset } from '../types';

interface ClientAssetModalProps {
  isOpen: boolean;
  onClose: () => void;
  existingAsset?: ClientAsset | null;
  onSave: (data: { name: string; url?: string; file?: File }) => void | Promise<void>;
}

const ClientAssetModal: React.FC<ClientAssetModalProps> = ({ isOpen, onClose, existingAsset, onSave }) => {
  const [name, setName] = useState('');
  const [url, setUrl] = useState('');
  const [file, setFile] = useState<File | null>(null);

  useEffect(() => {
    if (existingAsset) {
      setName(existingAsset.name);
      setUrl(existingAsset.url);
      setFile(null);
    } else {
      setName('');
      setUrl('https://');
      setFile(null);
    }
  }, [existingAsset, isOpen]);

  if (!isOpen) {return null;}

  const handleSave = () => {
    if (!name.trim()) {return;}
    if (file) {
      onSave({ name: name.trim(), file });
    } else {
      if (!url.trim()) {return;}
      onSave({ name: name.trim(), url: url.trim() });
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-md">
        <header className="flex items-center justify-between p-4 border-b dark:border-zinc-800">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <LinkIcon className="w-5 h-5 text-brand-600"/>
            {existingAsset ? 'Edit Asset' : 'Add Asset'}
          </h2>
          <Button variant="ghost" size="icon" onClick={onClose}><CloseIcon className="w-5 h-5"/></Button>
        </header>
        <main className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium dark:text-zinc-300">Name</label>
            <input value={name} onChange={e => setName(e.target.value)} type="text" className="mt-1 w-full p-2 border rounded-md dark:bg-zinc-800 dark:border-zinc-700"/>
          </div>
          <div className="grid grid-cols-1 gap-4">
            <div>
              <label className="block text-sm font-medium dark:text-zinc-300">Upload File</label>
              <input type="file" accept="image/*,.pdf,.doc,.docx" onChange={(e) => setFile(e.target.files?.[0] || null)} className="mt-1 block w-full text-sm text-zinc-700 dark:text-zinc-300" />
            </div>
            <div>
              <label className="block text-sm font-medium dark:text-zinc-300">Or Link URL</label>
              <input value={url} onChange={e => setUrl(e.target.value)} type="url" className="mt-1 w-full p-2 border rounded-md dark:bg-zinc-800 dark:border-zinc-700" placeholder="https://..." disabled={!!file} />
              <p className="text-xs text-zinc-500 mt-1">Provide either a file to upload or a link URL.</p>
            </div>
          </div>
        </main>
        <footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t dark:border-zinc-800">
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSave} disabled={!name.trim() || (!file && !url.trim())}>Save</Button>
        </footer>
      </div>
    </div>
  );
};

export default ClientAssetModal;
