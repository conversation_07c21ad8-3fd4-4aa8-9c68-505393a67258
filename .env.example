PORT=3002
CLIENT_ORIGIN=http://localhost:5173
VITE_API_BASE=http://localhost:3002

# AI keys (server-only)
GEMINI_API_KEY=your-gemini-api-key

# Redis configuration (optional - caching disabled if not set)
REDIS_URL=redis://127.0.0.1:6379
REDIS_ENABLED=false

# Sentry configuration (only used in production)
SENTRY_DSN=
SENTRY_TRACES_SAMPLE_RATE=0.1
SENTRY_RELEASE=
SENTRY_ENVIRONMENT=

# Supabase settings

SUPABASE_URL=https://hrzspijfqgjducjixddr.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_JWT_SECRET=your-jwt-secret

# Frontend configuration (required for the Supabase client to initialize)
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-anon-key

# Testing helpers
# WARNING: Do not enable TEST_BYPASS_AUTH outside of local development; it disables authentication.
TEST_BYPASS_AUTH=0

