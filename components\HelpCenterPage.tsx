
import React, { useState, useMemo, useEffect } from 'react';
import { HELP_TOPICS } from '../constants';
import { SearchIcon, ArrowLeftIcon } from './Icons';
import { cn } from '../lib/utils';
import { HelpTopic, HelpCategory } from '../types';
import { sanitizeHtml } from '../lib/sanitize';
// FIX: Import the 'Button' component to resolve the 'Cannot find name' error.
import { Button } from './ui/Button';

interface HelpCenterPageProps {
  initialTopicId?: string | null;
}

const HelpCenterPage: React.FC<HelpCenterPageProps> = ({ initialTopicId }) => {
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedCategory, setSelectedCategory] = useState<HelpCategory | null>(null);
    const [activeTopic, setActiveTopic] = useState<HelpTopic | null>(null);

    // Effect to handle deep linking to a specific topic
    useEffect(() => {
        if (initialTopicId) {
            for (const category of HELP_TOPICS) {
                const topic = category.topics.find(t => t.id === initialTopicId);
                if (topic) {
                    setSelectedCategory(category);
                    setActiveTopic(topic);
                    break;
                }
            }
        }
    }, [initialTopicId]);

    const filteredHelpTopics = useMemo((): HelpCategory[] => {
        if (!searchTerm.trim()) {
            return HELP_TOPICS;
        }
        const lowercasedFilter = searchTerm.toLowerCase();
        
        const _isMatch = (content: string | Array<{type: string; value: string}>): boolean => {
            if (typeof content === 'string') {return content.toLowerCase().includes(lowercasedFilter);}
            if (Array.isArray(content)) {return content.some(block => block.type === 'text' && block.value.toLowerCase().includes(lowercasedFilter));}
            return false;
        }

        return HELP_TOPICS
            .map(category => ({
                ...category,
                topics: category.topics.filter(topic => {
                    const titleMatch = topic.title.toLowerCase().includes(lowercasedFilter);
                    const contentMatch = Array.isArray(topic.content) 
                        ? topic.content.some(block => block.type === 'text' && block.value?.toLowerCase().includes(lowercasedFilter))
                        : typeof topic.content === 'string' && topic.content.toLowerCase().includes(lowercasedFilter);
                    return titleMatch || contentMatch;
                })
            }))
            .filter(category => category.topics.length > 0);
    }, [searchTerm]);

    const handleSelectCategory = (category: HelpCategory) => {
        setSelectedCategory(category);
        setActiveTopic(category.topics[0]);
    };

    const HubView = () => (
        <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
                <h1 className="text-4xl font-extrabold text-zinc-900 dark:text-white">How can we help?</h1>
                <div className="relative mt-6 max-w-lg mx-auto">
                    <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4">
                        <SearchIcon className="h-5 w-5 text-zinc-400" />
                    </div>
                    <input
                        type="search"
                        placeholder="Search for articles..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="block w-full rounded-full border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-11 focus:border-brand-500 focus:ring-brand-500 text-lg p-4"
                    />
                </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredHelpTopics.map(category => (
                    <button key={category.category} onClick={() => handleSelectCategory(category)} className="p-6 bg-white dark:bg-zinc-900 rounded-xl border border-zinc-200 dark:border-zinc-800 text-left hover:border-brand-500 hover:shadow-lg transition-all transform hover:-translate-y-1">
                        <category.icon className="w-8 h-8 text-brand-600 dark:text-brand-400 mb-3" />
                        <h2 className="font-semibold text-zinc-800 dark:text-zinc-200">{category.category}</h2>
                        <p className="text-sm text-zinc-500 dark:text-zinc-400 mt-1">{category.topics.length} articles</p>
                    </button>
                ))}
            </div>
            {searchTerm && filteredHelpTopics.length === 0 && (
                 <p className="text-center text-zinc-500">No articles found for "{searchTerm}".</p>
            )}
        </div>
    );

    const ArticleView = () => {
        if (!selectedCategory || !activeTopic) {return null;}
        
        return (
            <div className="flex h-full gap-8">
                <aside className="w-72 flex-shrink-0 bg-white dark:bg-zinc-950 p-4 rounded-xl border border-zinc-200 dark:border-zinc-800 flex flex-col">
                    <Button variant="ghost" onClick={() => setSelectedCategory(null)} className="mb-4 text-zinc-600 dark:text-zinc-300">
                        <ArrowLeftIcon className="w-4 h-4 mr-2"/>
                        All Categories
                    </Button>
                    <h2 className="px-2 font-semibold text-zinc-800 dark:text-zinc-200">{selectedCategory.category}</h2>
                    <nav className="flex-1 overflow-y-auto mt-2">
                        <ul className="space-y-1">
                            {selectedCategory.topics.map(topic => (
                                <li key={topic.id}>
                                    <a href="#" onClick={(e) => { e.preventDefault(); setActiveTopic(topic); }} className={cn('block p-2 text-sm rounded-md', activeTopic.id === topic.id ? 'bg-brand-50 text-brand-700 font-medium dark:bg-brand-900/50 dark:text-brand-300' : 'text-zinc-600 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800')}>
                                        {topic.title}
                                    </a>
                                </li>
                            ))}
                        </ul>
                    </nav>
                </aside>
                <main className="flex-1 bg-white dark:bg-zinc-950 p-6 md:p-8 rounded-xl border border-zinc-200 dark:border-zinc-800 overflow-y-auto">
                    <article className="prose prose-zinc dark:prose-invert max-w-none">
                        {Array.isArray(activeTopic.content) ? (
                            activeTopic.content.map((block, index) => {
                            if (block.type === 'text') {return <div key={index} dangerouslySetInnerHTML={{ __html: sanitizeHtml(block.value) }} />;}
                            if (block.type === 'screenshot') { const Screenshot = block.component; return <div className="my-8" key={index}><Screenshot /></div>; }
                            return null;
                            })
                        ) : (
                            <div dangerouslySetInnerHTML={{ __html: sanitizeHtml(activeTopic.content) }} />
                        )}
                    </article>
                </main>
                 <style>{`
                    .prose h2 { font-size: 1.5rem; font-weight: 700; margin-top: 2em; margin-bottom: 1em; padding-bottom: 0.3em; border-bottom: 1px solid #e4e4e7; }
                    .prose h4 { font-size: 1.1rem; font-weight: 600; margin-top: 1.5em; margin-bottom: 0.5em; }
                    .dark .prose h2 { border-bottom-color: #3f3f46; }
                    .prose p, .prose li { color: #3f3f46; line-height: 1.7; }
                    .dark .prose p, .dark .prose li { color: #d4d4d8; }
                    .prose ul { list-style-type: disc; padding-left: 1.5rem; }
                    .prose ol { list-style-type: decimal; padding-left: 1.5rem; }
                    .prose code { background-color: #f4f4f5; padding: 0.2em 0.4em; margin: 0; font-size: 85%; border-radius: 6px; }
                    .dark .prose code { background-color: #27272a; }
                    .prose pre { background-color: #f4f4f5; padding: 1rem; border-radius: 8px; overflow-x: auto; }
                    .dark .prose pre { background-color: #18181b; }
                    .prose pre code { background-color: transparent; padding: 0; }
               `}</style>
            </div>
        );
    };
    
    return (
        <div className="p-4 sm:p-6 lg:p-8 h-full">
            {selectedCategory ? <ArticleView /> : <HubView />}
        </div>
    );
};

export default HelpCenterPage;
