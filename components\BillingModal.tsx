import React, { useState, useEffect, useRef } from 'react';
import { Button } from './ui/Button';
import { CloseIcon, BillingIcon, LockIcon, CheckCircleIcon, XCircleIcon } from './Icons';
import { apiFetch } from '../lib/api';

interface BillingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onPaymentSuccess: (planName: string) => void;
  planName: string;
  planPrice?: string;
  planPriceDetail?: string;
}

type PaymentStatus = 'idle' | 'creating' | 'confirming' | 'succeeded' | 'failed' | 'canceled';

interface CheckoutResponse {
  sessionId: string;
  status: 'pending';
  checkoutUrl?: string | null;
  plan: {
    name: string;
    price: string;
    priceDetail: string | null;
  };
}

interface ConfirmResponse {
  success: boolean;
  planName: string;
}

const BillingModal: React.FC<BillingModalProps> = ({
  isOpen,
  onClose,
  onPaymentSuccess,
  planName,
  planPrice,
  planPriceDetail,
}) => {
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus>('idle');
  const [statusMessage, setStatusMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isCancelling, setIsCancelling] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [manualCheckoutUrl, setManualCheckoutUrl] = useState<string | null>(null);
  const [requiresManualCheckout, setRequiresManualCheckout] = useState(false);
  const successTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const cancelRequestedRef = useRef(false);

  const displayPlanName = planName?.trim() || 'Premium';
  const displayPrice = planPrice || '$49';
  const displayPriceDetail = planPriceDetail || '/mo';

  useEffect(() => () => {
    if (successTimerRef.current) {
      clearTimeout(successTimerRef.current);
    }
  }, []);

  useEffect(() => {
    if (!isOpen) {
      cancelRequestedRef.current = false;
      setPaymentStatus('idle');
      setStatusMessage(null);
      setErrorMessage(null);
      setIsProcessing(false);
      setIsCancelling(false);
      setSessionId(null);
      setManualCheckoutUrl(null);
      setRequiresManualCheckout(false);
      if (successTimerRef.current) {
        clearTimeout(successTimerRef.current);
        successTimerRef.current = null;
      }
    }
  }, [isOpen]);

  const parseError = (err: unknown): string => {
    if (err instanceof Error) {
      try {
        const parsed = JSON.parse(err.message);
        if (parsed && typeof parsed.error === 'string') {
          return parsed.error;
        }
      } catch {
        // non-JSON error
      }
      return err.message || 'An unexpected error occurred.';
    }
    return 'An unexpected error occurred.';
  };

  const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  const handlePayment = async () => {
    if (isProcessing || paymentStatus === 'succeeded') {
      return;
    }

    cancelRequestedRef.current = false;
    setErrorMessage(null);
    setStatusMessage(null);
    setManualCheckoutUrl(null);
    setRequiresManualCheckout(false);
    setIsProcessing(true);

    try {
      setPaymentStatus('creating');
      setStatusMessage('Contacting the billing provider...');
      const checkout = await apiFetch<CheckoutResponse>('/api/stripe/checkout', {
        method: 'POST',
        body: JSON.stringify({ planName: displayPlanName }),
      });

      setSessionId(checkout.sessionId);
      setPaymentStatus('confirming');
      setManualCheckoutUrl(checkout.checkoutUrl ?? null);

      let manualCheckoutNeeded = false;

      if (checkout.checkoutUrl && typeof window !== 'undefined') {
        setStatusMessage('Opening secure checkout...');
        const checkoutWindow = window.open(checkout.checkoutUrl, '_blank', 'noopener,noreferrer');
        if (!checkoutWindow) {
          manualCheckoutNeeded = true;
          setRequiresManualCheckout(true);
          setStatusMessage('We couldn\'t open the secure checkout automatically. Use the button below to continue.');
        } else {
          checkoutWindow.focus();
          setRequiresManualCheckout(false);
        }
      } else {
        setStatusMessage('Awaiting payment confirmation...');
      }

      const pollForConfirmation = async (): Promise<ConfirmResponse> => {
        const maxAttempts = 15;
        const pollDelay = 2000;

        for (let attempt = 0; attempt < maxAttempts; attempt += 1) {
          if (cancelRequestedRef.current) {
            throw new Error('Payment canceled by user.');
          }

          try {
            return await apiFetch<ConfirmResponse>('/api/stripe/confirm', {
              method: 'POST',
              body: JSON.stringify({ sessionId: checkout.sessionId }),
            });
          } catch (err) {
            const message = parseError(err).toLowerCase();
            if (message.includes('not been paid yet')) {
              await sleep(pollDelay);
              continue;
            }

            if (message.includes('already confirmed')) {
              return { success: true, planName: displayPlanName };
            }

            throw err;
          }
        }

        throw new Error(
          JSON.stringify({
            error: 'Payment confirmation timed out. Please return to the checkout window to complete your purchase.',
          }),
        );
      };

      await sleep(900);

      if (cancelRequestedRef.current) {
        setPaymentStatus('canceled');
        setStatusMessage('Payment canceled. No changes were made to your plan.');
        setManualCheckoutUrl(null);
        setRequiresManualCheckout(false);
        return;
      }

      const waitingMessage = checkout.checkoutUrl
        ? manualCheckoutNeeded
          ? 'Waiting for payment confirmation. Click "Open secure checkout" below if the payment window didn\'t appear.'
          : 'Waiting for payment confirmation. Please complete checkout in the opened window.'
        : 'Waiting for payment confirmation from the billing provider...';
      setStatusMessage(waitingMessage);
      const confirmation = await pollForConfirmation();

      setPaymentStatus('succeeded');
      setStatusMessage('Payment successful! Upgrading your plan...');
      setSessionId(null);
      setManualCheckoutUrl(null);
      setRequiresManualCheckout(false);

      successTimerRef.current = setTimeout(() => {
        onPaymentSuccess(confirmation.planName);
      }, 900);
    } catch (err) {
      if (cancelRequestedRef.current) {
        setSessionId(null);
        setManualCheckoutUrl(null);
        setRequiresManualCheckout(false);
        return;
      }
      setPaymentStatus('failed');
      setStatusMessage(null);
      setSessionId(null);
      setErrorMessage(parseError(err));
      setManualCheckoutUrl(null);
      setRequiresManualCheckout(false);
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePaymentCancel = async () => {
    if (paymentStatus === 'succeeded') {
      onClose();
      return;
    }

    if (!sessionId && paymentStatus === 'idle') {
      onClose();
      return;
    }

    cancelRequestedRef.current = true;
    setErrorMessage(null);

    if (!sessionId) {
      setPaymentStatus('canceled');
      setStatusMessage('Payment canceled.');
      setIsProcessing(false);
      setManualCheckoutUrl(null);
      setRequiresManualCheckout(false);
      return;
    }

    setIsCancelling(true);
    try {
      await apiFetch('/api/stripe/cancel', {
        method: 'POST',
        body: JSON.stringify({ sessionId }),
      });
      setPaymentStatus('canceled');
      setStatusMessage('Payment canceled. No changes were made to your plan.');
      setSessionId(null);
      setManualCheckoutUrl(null);
      setRequiresManualCheckout(false);
    } catch (err) {
      setErrorMessage(parseError(err));
    } finally {
      setIsCancelling(false);
      setIsProcessing(false);
    }
  };

  const handleClose = async () => {
    cancelRequestedRef.current = true;
    if (successTimerRef.current) {
      clearTimeout(successTimerRef.current);
      successTimerRef.current = null;
    }

    if (sessionId && paymentStatus !== 'succeeded') {
      try {
        await apiFetch('/api/stripe/cancel', {
          method: 'POST',
          body: JSON.stringify({ sessionId }),
        });
      } catch (err) {
        cancelRequestedRef.current = false;
        setIsProcessing(false);
        setIsCancelling(false);
        setPaymentStatus('failed');
        setStatusMessage(null);
        setErrorMessage(parseError(err));
        return;
      }
    }

    setManualCheckoutUrl(null);
    setRequiresManualCheckout(false);
    onClose();
  };

  if (!isOpen) {
    return null;
  }

  const statusClasses = (() => {
    switch (paymentStatus) {
      case 'succeeded':
        return 'border-green-200 bg-green-50 text-green-700';
      case 'canceled':
        return 'border-amber-200 bg-amber-50 text-amber-700';
      default:
        return 'border-blue-200 bg-blue-50 text-blue-700';
    }
  })();

  const statusIcon = (() => {
    switch (paymentStatus) {
      case 'succeeded':
        return <CheckCircleIcon className="w-4 h-4 mt-0.5" />;
      case 'canceled':
        return <XCircleIcon className="w-4 h-4 mt-0.5" />;
      default:
        return <BillingIcon className="w-4 h-4 mt-0.5" />;
    }
  })();

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-md">
        <header className="flex items-center justify-between p-4 border-b border-zinc-200">
          <h2 className="text-lg font-semibold text-zinc-800">Upgrade to {displayPlanName}</h2>
          <button onClick={() => { void handleClose(); }} className="p-2 text-zinc-500 hover:bg-zinc-100 rounded-lg">
            <CloseIcon className="w-6 h-6" />
          </button>
        </header>
        <main className="p-6 space-y-4">
          <div className="p-4 bg-zinc-50 rounded-lg border border-zinc-200 flex justify-between items-center">
            <div>
              <h3 className="font-semibold text-zinc-800">{displayPlanName} Plan</h3>
              <p className="text-sm text-zinc-600">Billed monthly. Cancel anytime.</p>
            </div>
            <p className="text-2xl font-bold text-zinc-900">
              {displayPrice}
              {displayPriceDetail && (
                <span className="text-base font-medium text-zinc-500 ml-1">{displayPriceDetail}</span>
              )}
            </p>
          </div>
          <div className="space-y-4">
            <div>
              <label htmlFor="cardholderName" className="block text-sm font-medium text-zinc-700">Cardholder Name</label>
              <input
                type="text"
                id="cardholderName"
                placeholder="Jane Doe"
                className="mt-1 block w-full rounded-md border-zinc-300 p-2"
                disabled
              />
            </div>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-zinc-700">Email</label>
              <input
                type="email"
                id="email"
                placeholder="<EMAIL>"
                className="mt-1 block w-full rounded-md border-zinc-300 p-2"
                disabled
              />
            </div>
            <div>
              <label htmlFor="cardNumber" className="block text-sm font-medium text-zinc-700">Card Information</label>
              <div className="mt-1 relative flex items-center justify-center p-3 h-12 bg-zinc-100 border-2 border-dashed border-zinc-300 rounded-md text-zinc-500 text-sm">
                <BillingIcon className="w-5 h-5 mr-2" />
                <span>Secure checkout is handled by the backend. No card entry is required in this demo.</span>
              </div>
            </div>
          </div>
        </main>
        <footer className="p-4 bg-zinc-50 flex flex-col gap-3 rounded-b-2xl border-t border-zinc-200">
          {statusMessage && (
            <div className={`flex items-start gap-2 rounded-md border px-3 py-2 text-sm ${statusClasses}`}>
              {statusIcon}
              <span>{statusMessage}</span>
            </div>
          )}
          {requiresManualCheckout && manualCheckoutUrl && (
            <Button
              variant="outline"
              onClick={() => {
                if (!manualCheckoutUrl || typeof window === 'undefined') {
                  return;
                }
                const manualWindow = window.open(manualCheckoutUrl, '_blank', 'noopener,noreferrer');
                if (manualWindow) {
                  manualWindow.focus();
                }
              }}
              disabled={paymentStatus !== 'confirming'}
              className="w-full"
            >
              Open secure checkout
            </Button>
          )}
          {errorMessage && (
            <div className="flex items-start gap-2 rounded-md border border-red-200 bg-red-50 px-3 py-2 text-sm text-red-700">
              <XCircleIcon className="w-4 h-4 mt-0.5" />
              <span>{errorMessage}</span>
            </div>
          )}
          <Button onClick={handlePayment} disabled={isProcessing || paymentStatus === 'succeeded'} className="w-full">
            {isProcessing ? 'Processing payment...' : `Upgrade to ${displayPlanName}`}
          </Button>
          <Button
            variant="outline"
            onClick={() => { void handlePaymentCancel(); }}
            disabled={isCancelling || paymentStatus === 'succeeded'}
            className="w-full"
          >
            {isCancelling ? 'Canceling...' : 'Cancel Payment'}
          </Button>
          <p className="text-xs text-zinc-500 text-center flex items-center justify-center gap-1">
            <LockIcon className="w-3 h-3" />
            Payments are processed securely via our backend billing API.
          </p>
        </footer>
      </div>
    </div>
  );
};

export default BillingModal;
