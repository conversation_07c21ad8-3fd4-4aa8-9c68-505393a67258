// @vitest-environment jsdom
import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import FlowsPage from '../components/FlowsPage';
import React from 'react';

describe('FlowsPage', () => {
  it('renders FlowsPage component', () => {
    const user = { flows: [], id: 'u1', name: 'Test User', email: '<EMAIL>', documents: [], folders: [] };
    render(<FlowsPage user={user as any} onCreateFlow={() => {}} onUpdateFlow={() => {}} onDeleteFlow={() => {}} />);
    // There are multiple elements with 'Flows', so use getAllByText
    expect(screen.getAllByText(/Flows/i).length).toBeGreaterThan(0);
  });
});
