name: Enforce Tests Changed

on:
  pull_request:
    branches: [ main, master ]

jobs:
  enforce:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get changed files
        id: changes
        uses: tj-actions/changed-files@v45
        with:
          files: |
            server/routes/**
            components/**

      - name: Get changed test files
        id: testchanges
        uses: tj-actions/changed-files@v45
        with:
          files: |
            **/*.test.ts
            **/*.test.tsx
            tests/**

      - name: Enforce tests on feature changes
        run: |
          echo "Changed feature files: ${{ steps.changes.outputs.all_changed_files }}"
          echo "Changed test files: ${{ steps.testchanges.outputs.all_changed_files }}"
          if [[ -n "${{ steps.changes.outputs.all_changed_files }}" && -z "${{ steps.testchanges.outputs.all_changed_files }}" ]]; then
            echo "Error: Feature files changed without accompanying tests." >&2
            exit 1
          fi

