-- Protect critical profile fields from being updated by non-service_role clients
create or replace function public.profiles_guard_protected_columns()
returns trigger as $$
declare
  jwt_role text := current_setting('request.jwt.claim.role', true);
begin
  -- Only allow changes by service_role
  if coalesce(jwt_role, '') <> 'service_role' then
    if (new.is_admin          is distinct from old.is_admin)
    or (new.plan_name         is distinct from old.plan_name)
    or (new.status            is distinct from old.status)
    or (new.quota_used        is distinct from old.quota_used)
    or (new.quota_total       is distinct from old.quota_total)
    or (new.subscription_id   is distinct from old.subscription_id)
    or (new.subscription_status is distinct from old.subscription_status)
    or (new.customer_id       is distinct from old.customer_id)
    or (new.team_id           is distinct from old.team_id)
    or (new.plan_expiry_date  is distinct from old.plan_expiry_date)
    then
      raise exception 'update of protected profile fields not allowed'
        using errcode = '42501';
    end if;
  end if;
  return new;
end;
$$ language plpgsql security definer;

drop trigger if exists profiles_guard_protected_columns_trg on public.profiles;
create trigger profiles_guard_protected_columns_trg
before update on public.profiles
for each row execute function public.profiles_guard_protected_columns();

