import { SupabaseClient } from '@supabase/supabase-js';
import { getFromCache, setCache, cacheKeys, CACHE_TTL } from './redis';

// Custom error classes for better error handling
class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

class DatabaseError extends Error {
  constructor(message: string, public originalError?: Error) {
    super(message);
    this.name = 'DatabaseError';
  }
}

interface BatchDocumentRequest {
  documentIds: string[];
  includeComments?: boolean;
  includeMetadata?: boolean;
  includeActivityLogs?: boolean;
  includeVersions?: boolean;
  includeCollaborators?: boolean;
  includeSignatures?: boolean;
}

interface DocumentWithMetadata {
  id: string;
  name: string;
  status: string;
  folder_id: string | null;
  created_at: string;
  updated_at: string;
  comments?: CommentThread[];
  metadata?: DocumentMetadata;
  activityLogs?: ActivityLog[];
  versions?: DocumentVersion[];
  collaborators?: Collaborator[];
  signatures?: Signature[];
}

interface CommentThread {
  id: string;
  document_id: string;
  text_selection: string;
  is_resolved: boolean;
  created_at: string;
  comments: Comment[];
}

interface Comment {
  id: string;
  thread_id: string;
  author_email: string;
  content: string;
  created_at: string;
}

interface DocumentMetadata {
  wordCount: number;
  lastModified: string;
  version: number;
}

interface ActivityLog {
  id: string;
  document_id: string;
  user_email: string;
  type: string;
  details: string;
  timestamp: string;
}

interface DocumentVersion {
  id: string;
  document_id: string;
  content: string;
  saved_at: string;
  version: number;
}

interface Collaborator {
  id: string;
  document_id: string;
  email: string;
  permission: 'view' | 'edit';
  avatar_url?: string;
}

interface Signature {
  id: string;
  document_id: string;
  email: string;
  role: string;
  status: 'pending' | 'signed';
  signed_at?: string;
  token: string;
}

/**
 * Batch fetch documents with optional comments and metadata
 * Uses caching and optimized queries to reduce database load
 */
export async function batchFetchDocuments(
  supabaseClient: SupabaseClient,
  userId: string,
  request: BatchDocumentRequest
): Promise<DocumentWithMetadata[]> {
  // Input validation
  if (!supabaseClient) {
    throw new ValidationError('Supabase client is required');
  }
  
  if (!userId || typeof userId !== 'string' || userId.trim().length === 0) {
    throw new ValidationError('Valid user ID is required');
  }
  
  if (!request || typeof request !== 'object') {
    throw new ValidationError('Request object is required');
  }
  
  const { 
    documentIds, 
    includeComments = false, 
    includeMetadata = false, 
    includeActivityLogs = false,
    includeVersions = false,
    includeCollaborators = false,
    includeSignatures = false
  } = request;
  
  // Validate documentIds
  if (!Array.isArray(documentIds)) {
    throw new ValidationError('documentIds must be an array');
  }
  
  if (documentIds.length === 0) {
    return [];
  }
  
  // Validate each document ID
  for (const id of documentIds) {
    if (!id || typeof id !== 'string' || id.trim().length === 0) {
      throw new ValidationError('All document IDs must be non-empty strings');
    }
  }
  
  // Remove duplicates and validate format
  const uniqueDocumentIds = [...new Set(documentIds.map(id => id.trim()))];
  
  // Limit batch size to prevent overwhelming the database
  const MAX_BATCH_SIZE = 50;
  if (uniqueDocumentIds.length > MAX_BATCH_SIZE) {
    throw new ValidationError(`Batch size cannot exceed ${MAX_BATCH_SIZE} documents`);
  }
  
  // Try to get cached documents first
  let cachedResult = null;
  try {
    const cacheKey = cacheKeys.batchDocuments(userId, uniqueDocumentIds.sort().join(','));
    cachedResult = await getFromCache(cacheKey);
    
    if (cachedResult && !includeComments && !includeMetadata && !includeActivityLogs && !includeVersions && !includeCollaborators && !includeSignatures) {
      return cachedResult as DocumentWithMetadata[];
    }
  } catch {
    // Cache error but don't fail the request
  }
  
  // Fetch documents in a single query
  const { data: documents, error: docsError } = await supabaseClient
    .from('documents')
    .select('id, name, status, folder_id, created_at, updated_at, content, client_id, value')
    .in('id', uniqueDocumentIds)
    .eq('user_id', userId);
    
  if (docsError) {
    throw new DatabaseError(`Failed to fetch documents: ${docsError.message}`, docsError);
  }
  
  if (!documents) {
    return [];
  }
  
  // Validate that user has access to all requested documents
  const foundDocumentIds = new Set(documents.map(doc => doc.id));
  const missingDocumentIds = uniqueDocumentIds.filter(id => !foundDocumentIds.has(id));
  
  if (missingDocumentIds.length > 0) {
    // User requested access to documents they don't own or that don't exist
  }
  
  const result: DocumentWithMetadata[] = documents.map(doc => ({ ...doc }));
  
  // Batch fetch comments if requested
  if (includeComments) {
    const { data: threads, error: threadsError } = await supabaseClient
      .from('comment_threads')
      .select(`
        id,
        document_id,
        text_selection,
        is_resolved,
        created_at,
        comments:comments(
          id,
          thread_id,
          author_email,
          content,
          created_at
        )
      `)
      .in('document_id', documentIds);
      
    if (threadsError) {
      throw new DatabaseError(`Failed to fetch comments: ${threadsError.message}`, threadsError);
    }
    
    // Group comments by document ID
    const commentsByDocId = new Map<string, CommentThread[]>();
    threads?.forEach(thread => {
      if (!commentsByDocId.has(thread.document_id)) {
        commentsByDocId.set(thread.document_id, []);
      }
      const comments = commentsByDocId.get(thread.document_id);
      if (comments) {
        comments.push(thread);
      }
    });
    
    // Attach comments to documents
    result.forEach(doc => {
      doc.comments = commentsByDocId.get(doc.id) || [];
    });
  }
  
  // Batch fetch activity logs if requested
  if (includeActivityLogs) {
    const { data: activityLogs, error: activityLogsError } = await supabaseClient
      .from('activity_logs')
      .select('id, document_id, user_email, type, details, timestamp')
      .in('document_id', documentIds)
      .order('timestamp', { ascending: false });
      
    if (activityLogsError) {
      throw new DatabaseError(`Failed to fetch activity logs: ${activityLogsError.message}`, activityLogsError);
    }
    
    // Group activity logs by document ID
    const activityLogsByDocId = new Map<string, ActivityLog[]>();
    activityLogs?.forEach(log => {
      if (!activityLogsByDocId.has(log.document_id)) {
        activityLogsByDocId.set(log.document_id, []);
      }
      const logs = activityLogsByDocId.get(log.document_id);
      if (logs) {
        logs.push(log);
      }
    });
    
    // Attach activity logs to documents
    result.forEach(doc => {
      doc.activityLogs = activityLogsByDocId.get(doc.id) || [];
    });
  }

  // Batch fetch versions if requested
  if (includeVersions) {
    const { data: versions, error: versionsError } = await supabaseClient
      .from('document_versions')
      .select('id, document_id, content, saved_at')
      .in('document_id', documentIds)
      .order('saved_at', { ascending: true });

    if (versionsError) {
      throw new DatabaseError(`Failed to fetch versions: ${versionsError.message}`, versionsError);
    }

    // Group versions by document ID and compute version numbers
    const versionsByDocId = new Map<string, DocumentVersion[]>();
    versions?.forEach(version => {
      const docVersions = versionsByDocId.get(version.document_id) || [];
      const versionNumber = docVersions.length + 1;
      docVersions.push({ ...version, version: versionNumber });
      versionsByDocId.set(version.document_id, docVersions);
    });

    // Attach versions to documents (latest version first)
    result.forEach(doc => {
      const docVersions = versionsByDocId.get(doc.id) || [];
      doc.versions = docVersions.sort((a, b) => b.version - a.version);
    });
  }

  // Batch fetch collaborators if requested
  if (includeCollaborators) {
    const { data: collaborators, error: collaboratorsError } = await supabaseClient
      .from('document_collaborators')
      .select('id, document_id, email, permission, avatar_url')
      .in('document_id', documentIds);
      
    if (collaboratorsError) {
      throw new DatabaseError(`Failed to fetch collaborators: ${collaboratorsError.message}`, collaboratorsError);
    }
    
    // Group collaborators by document ID
    const collaboratorsByDocId = new Map<string, Collaborator[]>();
    collaborators?.forEach(collaborator => {
      if (!collaboratorsByDocId.has(collaborator.document_id)) {
        collaboratorsByDocId.set(collaborator.document_id, []);
      }
      const docCollaborators = collaboratorsByDocId.get(collaborator.document_id);
      if (docCollaborators) {
        docCollaborators.push(collaborator);
      }
    });
    
    // Attach collaborators to documents
    result.forEach(doc => {
      doc.collaborators = collaboratorsByDocId.get(doc.id) || [];
    });
  }

  // Batch fetch signatures if requested
  if (includeSignatures) {
    const { data: signatures, error: signaturesError } = await supabaseClient
      .from('signatures')
      .select('id, document_id, email, role, status, signed_at, token')
      .in('document_id', documentIds)
      .order('signed_at', { ascending: false });
      
    if (signaturesError) {
      throw new DatabaseError(`Failed to fetch signatures: ${signaturesError.message}`, signaturesError);
    }
    
    // Group signatures by document ID
    const signaturesByDocId = new Map<string, Signature[]>();
    signatures?.forEach(signature => {
      if (!signaturesByDocId.has(signature.document_id)) {
        signaturesByDocId.set(signature.document_id, []);
      }
      const docSignatures = signaturesByDocId.get(signature.document_id);
      if (docSignatures) {
        docSignatures.push(signature);
      }
    });
    
    // Attach signatures to documents
    result.forEach(doc => {
      doc.signatures = signaturesByDocId.get(doc.id) || [];
    });
  }

  // Calculate metadata if requested
  if (includeMetadata) {
    // Fetch document content for metadata calculation
    const { data: contentData, error: contentError } = await supabaseClient
      .from('documents')
      .select('id, content, updated_at')
      .in('id', documentIds)
      .eq('user_id', userId);

    if (contentError) {
      throw new DatabaseError(`Failed to fetch document content: ${contentError.message}`, contentError);
    }

    // Fetch versions for each document to determine current version number
    const { data: versionRows, error: versionError } = await supabaseClient
      .from('document_versions')
      .select('document_id')
      .in('document_id', documentIds);

    if (versionError) {
      throw new DatabaseError(`Failed to fetch document versions: ${versionError.message}`, versionError);
    }

    // Count versions per document
    const versionCounts = new Map<string, number>();
    versionRows?.forEach(row => {
      versionCounts.set(row.document_id, (versionCounts.get(row.document_id) || 0) + 1);
    });

    // Calculate metadata for each document
    const metadataByDocId = new Map<string, DocumentMetadata>();
    contentData?.forEach(doc => {
      const wordCount = doc.content.split(/\s+/).filter(word => word.length > 0).length;
      metadataByDocId.set(doc.id, {
        wordCount,
        lastModified: doc.updated_at,
        version: (versionCounts.get(doc.id) || 0) + 1
      });
    });

    // Attach metadata to documents
    result.forEach(doc => {
      doc.metadata = metadataByDocId.get(doc.id);
    });
  }
  
  // Cache the result (only if not including dynamic data)
  if (!includeComments && !includeMetadata && !includeActivityLogs && !includeVersions && !includeCollaborators && !includeSignatures) {
    try {
      const cacheKey = cacheKeys.batchDocuments(userId, uniqueDocumentIds.sort().join(','));
      await setCache(cacheKey, result, CACHE_TTL.DOCUMENTS_LIST);
    } catch {
      // Cache error but don't fail the request
    }
  }
  
  return result;
}

/**
 * Batch fetch comments for multiple documents
 */
export async function batchFetchComments(
  supabaseClient: SupabaseClient,
  documentIds: string[]
): Promise<Map<string, CommentThread[]>> {
  if (documentIds.length === 0) {
    return new Map();
  }
  
  const cacheKey = cacheKeys.batchComments(documentIds.sort().join(','));
  const cachedResult = await getFromCache(cacheKey);
  
  if (cachedResult) {
    return new Map(cachedResult as [string, CommentThread[]][]);
  }
  
  const { data: threads, error } = await supabaseClient
    .from('comment_threads')
    .select(`
      id,
      document_id,
      text_selection,
      is_resolved,
      created_at,
      comments:comments(
        id,
        thread_id,
        author_email,
        content,
        created_at
      )
    `)
    .in('document_id', documentIds)
    .order('created_at', { ascending: false });
    
  if (error) {
    throw new Error(`Failed to fetch comments: ${error.message}`);
  }
  
  // Group by document ID
  const result = new Map<string, CommentThread[]>();
  threads?.forEach(thread => {
    if (!result.has(thread.document_id)) {
      result.set(thread.document_id, []);
    }
    const threads = result.get(thread.document_id);
    if (threads) {
      threads.push(thread);
    }
  });
  
  // Cache the result
  await setCache(cacheKey, Array.from(result.entries()), CACHE_TTL.COMMENTS);
  
  return result;
}