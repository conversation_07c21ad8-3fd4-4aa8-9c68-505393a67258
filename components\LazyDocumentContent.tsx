import React, { useState, useEffect, useRef, forwardRef } from 'react';
import MarkdownRenderer from './MarkdownRenderer';

interface LazyDocumentContentProps {
  content: string;
  threshold?: number;
  rootMargin?: string;
}

const LazyDocumentContent = forwardRef<HTMLDivElement, LazyDocumentContentProps>(
  ({ content, threshold = 0.1, rootMargin = '50px' }, ref) => {
    const [isVisible, setIsVisible] = useState(false);
    const [hasLoaded, setHasLoaded] = useState(false);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting && !hasLoaded) {
            setIsVisible(true);
            setHasLoaded(true);
          }
        },
        {
          threshold,
          rootMargin
        }
      );

      const currentContainer = containerRef.current;
      if (currentContainer) {
        observer.observe(currentContainer);
      }

      return () => {
        if (currentContainer) {
          observer.unobserve(currentContainer);
        }
      };
    }, [threshold, rootMargin, hasLoaded]);

    return (
      <div ref={containerRef} className="min-h-[200px]">
        {isVisible || hasLoaded ? (
          <MarkdownRenderer content={content} ref={ref} />
        ) : (
          <div className="flex items-center justify-center h-48 bg-zinc-50 dark:bg-zinc-800 rounded-lg">
            <div className="text-zinc-500 dark:text-zinc-400 text-sm">
              Loading document content...
            </div>
          </div>
        )}
      </div>
    );
  }
);

LazyDocumentContent.displayName = 'LazyDocumentContent';

export default LazyDocumentContent;