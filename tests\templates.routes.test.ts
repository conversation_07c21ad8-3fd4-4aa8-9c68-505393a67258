import { describe, it, expect, beforeAll, beforeEach, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

// Mock admin flag for testing
let mockIsAdmin = true;

vi.mock('../server/supabaseClient', () => {
  const getUserClient = (_token: string) => ({
    auth: {
      getUser: async () => ({ data: { user: { id: 'test-user', email: '<EMAIL>' } }, error: null }),
    },
    from: (table: string) => {
      if (table === 'templates') {
        return {
          select: (_cols: string) => ({ order: async () => ({ data: [{ id: 'template-1', title: 'Test Template', category: 'Legal' }], error: null }) }),
        };
      }
      if (table === 'profiles') {
        return {
          select: (_cols: string) => ({
            eq: (_col: string, _val: unknown) => ({
              single: async () => ({ data: { is_admin: mockIsAdmin }, error: null }),
            }),
          }),
        };
      }
      return {
        select: () => ({ data: [], error: null }),
      };
    },
  });
  
  const supabaseAdmin = {
    from: (table: string) => {
      if (table === 'templates') {
        return {
          insert: (row: Record<string, unknown>) => ({ select: () => ({ single: async () => ({ data: { id: 'template-1', ...row }, error: null }) }) }),
          update: (patch: Record<string, unknown>) => ({ eq: (_col: string, id: string) => ({ select: () => ({ single: async () => ({ data: { id, ...patch }, error: null }) }) }) }),
          delete: () => ({ eq: async () => ({ error: null }) }),
        };
      }
      return {
        insert: () => ({ data: null, error: null }),
        update: () => ({ data: null, error: null }),
        delete: () => ({ data: null, error: null }),
      };
    },
  };
  
  return { getUserClient, supabaseAdmin };
});

vi.mock('../middleware/auth', () => ({
  requireAuth: (req: any, res: any, next: any) => {
    req.user = { id: 'test-user', email: '<EMAIL>' };
    req.accessToken = 'test-token';
    next();
  },
  getAccessToken: () => 'test-token',
}));

beforeAll(() => {
  process.env.TEST_BYPASS_AUTH = '1';
  (global as any).mockIsAdmin = true;
});

beforeEach(() => {
  mockIsAdmin = true;
  (global as any).mockIsAdmin = true;
});

describe('Templates routes', () => {
  describe('GET /api/templates', () => {
    it('should return templates list', async () => {
      const res = await request(app)
        .get('/api/templates')
        .set('Authorization', 'Bearer test-token');
      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('templates');
      expect(Array.isArray(res.body.templates)).toBe(true);
    });
  });

  describe('POST /api/templates', () => {
    it('should create a new template (admin only)', async () => {
      const templateData = {
        title: 'New Template',
        description: 'A test template',
        category: 'Legal',
        prompt: 'Generate a legal document',
        requiredPlan: 'Premium'
      };
      const res = await request(app)
        .post('/api/templates')
        .send(templateData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(201);
      expect(res.body).toHaveProperty('template');
      expect(res.body.template).toHaveProperty('title', 'New Template');
    });

    it('should return 400 for invalid template data', async () => {
      const res = await request(app)
        .post('/api/templates')
        .send({ title: '' })
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(400);
    });

    it('should return 400 for missing required fields', async () => {
      const res = await request(app)
        .post('/api/templates')
        .send({ title: 'Test' })
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(400);
    });

    it('should return 400 for invalid requiredPlan enum', async () => {
      const templateData = {
        title: 'New Template',
        description: 'A test template',
        category: 'Legal',
        prompt: 'Generate a legal document',
        requiredPlan: 'InvalidPlan'
      };
      const res = await request(app)
        .post('/api/templates')
        .send(templateData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(400);
    });
  });

  describe('PUT /api/templates/:id', () => {
    it('should update template (admin only)', async () => {
      const updateData = {
        title: 'Updated Template',
        description: 'Updated description'
      };
      const res = await request(app)
        .put('/api/templates/template-1')
        .send(updateData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('template');
    });

    it('should return 400 for invalid update data', async () => {
      const res = await request(app)
        .put('/api/templates/template-1')
        .send({ title: '' })
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(400);
    });

    it('should allow partial updates', async () => {
      const updateData = { title: 'Partially Updated' };
      const res = await request(app)
        .put('/api/templates/template-1')
        .send(updateData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(200);
      expect(res.body).toHaveProperty('template');
    });

    it('should return 400 for invalid enum values in partial update', async () => {
      const updateData = { requiredPlan: 'InvalidPlan' };
      const res = await request(app)
        .put('/api/templates/template-1')
        .send(updateData)
        .set('Authorization', 'Bearer test-token')
        .set('Content-Type', 'application/json');
      expect(res.status).toBe(400);
    });
  });

  describe('DELETE /api/templates/:id', () => {
    it('should delete template (admin only)', async () => {
      const res = await request(app)
        .delete('/api/templates/template-1')
        .set('Authorization', 'Bearer test-token');
      expect(res.status).toBe(204);
    });
  });
});