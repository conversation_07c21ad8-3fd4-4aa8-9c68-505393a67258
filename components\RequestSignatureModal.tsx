

import React, { useState } from 'react';
import { Document as DocType, User, Signature } from '../types';
import { Button } from './ui/Button';
// FIX: Replace non-existent UserPlusIcon with PlusCircleIcon
import { CloseIcon, TrashIcon, PlusCircleIcon } from './Icons';

interface RequestSignatureModalProps {
  isOpen: boolean;
  onClose: () => void;
  document: DocType;
  allUsers: User[];
  onRequest: (docId: string, signers: Omit<Signature, 'id' | 'status' | 'token'>[]) => void;
}

interface SignerInput {
    email: string;
    role: string;
}

const RequestSignatureModal: React.FC<RequestSignatureModalProps> = ({ isOpen, onClose, document, allUsers, onRequest }) => {
  const [signers, setSigners] = useState<SignerInput[]>([{ email: '', role: '' }]);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  if (!isOpen) {return null;}

  const handleSignerChange = (index: number, field: 'email' | 'role', value: string) => {
    const newSigners = [...signers];
    newSigners[index][field] = value;
    setSigners(newSigners);
  };
  
  const addSigner = () => setSigners([...signers, { email: '', role: '' }]);
  const removeSigner = (index: number) => setSigners(signers.filter((_, i) => i !== index));

  const handleSendRequest = () => {
    setError('');
    const validSigners = signers.filter(s => s.email && s.role);
    if (validSigners.length === 0) {
        setError('Please add at least one signer.');
        return;
    }
    
    for (const signer of validSigners) {
        if (!allUsers.find(u => u.email === signer.email)) {
            setError(`User with email "${signer.email}" not found.`);
            return;
        }
    }
    
    onRequest(document.id, validSigners);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-lg">
        <header className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold text-slate-800">Request Signatures</h2>
          <button onClick={onClose} className="p-2 text-slate-500 hover:bg-slate-100 rounded-lg">
            <CloseIcon className="w-6 h-6" />
          </button>
        </header>
        <main className="p-6 space-y-4 max-h-[60vh] overflow-y-auto">
            <p className="text-sm text-slate-600">
                Invite people to sign "{document.name}". The document will be locked for editing once the request is sent.
            </p>
            
            <div className="space-y-3">
                {signers.map((signer, index) => (
                    <div key={index} className="flex items-center gap-2 p-2 border rounded-lg">
                        <div className="flex-1 grid grid-cols-2 gap-2">
                             <input
                                type="email"
                                value={signer.email}
                                onChange={(e) => handleSignerChange(index, 'email', e.target.value)}
                                placeholder="Signer's Email"
                                className="w-full text-sm rounded-md border-slate-300 p-2"
                            />
                            <input
                                type="text"
                                value={signer.role}
                                onChange={(e) => handleSignerChange(index, 'role', e.target.value)}
                                placeholder="Signer's Role (e.g., Client)"
                                className="w-full text-sm rounded-md border-slate-300 p-2"
                            />
                        </div>
                        <Button variant="ghost" size="icon" className="text-slate-500 hover:text-red-600" onClick={() => removeSigner(index)} disabled={signers.length === 1}>
                            <TrashIcon className="w-4 h-4"/>
                        </Button>
                    </div>
                ))}
            </div>
            
            <Button variant="outline" size="sm" onClick={addSigner}>
                <PlusCircleIcon className="w-4 h-4 mr-2"/> Add Another Signer
            </Button>

             <div>
                <label htmlFor="message" className="block text-sm font-medium text-slate-700 mb-1">
                    Custom Message (Optional)
                </label>
                <textarea
                    id="message"
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="e.g., Please review and sign the attached agreement."
                    className="block w-full text-sm rounded-md border-slate-300 p-2"
                    rows={3}
                />
             </div>
             {error && <p className="text-sm text-red-500">{error}</p>}
        </main>
        <footer className="p-4 bg-slate-50 flex justify-end gap-3 rounded-b-2xl border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSendRequest}>
            Send for Signature
          </Button>
        </footer>
      </div>
    </div>
  );
};

export default RequestSignatureModal;
