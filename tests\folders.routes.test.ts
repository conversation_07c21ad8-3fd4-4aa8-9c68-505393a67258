import request from 'supertest';
import express from 'express';
import { describe, it, beforeEach, afterEach, expect, vi } from 'vitest';

// Manual mock for requireAuth middleware
vi.mock('../server/middleware/auth', () => ({
	requireAuth: (_req: express.Request, _res: express.Response, next: () => void) => {
		(_req as any).accessToken = 'test-token';
		(_req as any).user = { id: 'user-1' };
		next();
	},
	getAccessToken: (_req: express.Request) => 'test-token',
}));

function createSupabaseMock() {
	// The object returned by .from()
	const fromObj = {
		select: vi.fn().mockReturnThis(),
		order: vi.fn().mockReturnThis(),
		insert: vi.fn().mockReturnThis(),
		update: vi.fn().mockReturnThis(),
		eq: vi.fn().mockReturnThis(),
		single: vi.fn().mockReturnThis(),
		delete: vi.fn(() => ({
			eq: vi.fn().mockResolvedValue({ error: null })
		})),
		auth: { getUser: vi.fn() },
	};
	return {
		from: vi.fn(() => fromObj),
		auth: { getUser: vi.fn() },
	};
}

describe('folders API routes', () => {
	let app: express.Express;
	let supabaseMock: ReturnType<typeof createSupabaseMock>;

	beforeEach(async () => {
		app = express();
		app.use(express.json());
		supabaseMock = createSupabaseMock();
		vi.doMock('../server/supabaseClient', () => ({
			getUserClient: () => supabaseMock,
			supabaseAdmin: supabaseMock,
		}));
		
		// Also mock the getUserClient import
		vi.mock('../server/supabaseClient', () => ({
			getUserClient: () => supabaseMock,
			supabaseAdmin: supabaseMock,
		}));
		const foldersRouter = (await import('../server/routes/folders')).default;
		app.use('/folders', foldersRouter);
	});

	afterEach(() => {
		vi.resetModules();
		vi.clearAllMocks();
	});


		it('GET /folders returns folders', async () => {
			const from = supabaseMock.from();
			from.select.mockReturnThis();
			from.order.mockResolvedValue({ data: [{ id: '1', name: 'Test Folder' }], error: null });
			const res = await request(app).get('/folders');
			expect(res.status).toBe(200);
			expect(res.body.folders).toEqual([{ id: '1', name: 'Test Folder' }]);
		});


			it('POST /folders creates a folder', async () => {
				const from = supabaseMock.from();
				supabaseMock.auth.getUser.mockResolvedValue({ data: { user: { id: 'user-1' } } });
				from.insert.mockReturnThis();
				from.select.mockReturnThis();
				from.single.mockResolvedValue({ data: { id: '2', name: 'New Folder' }, error: null });
				const res = await request(app).post('/folders').send({ name: 'New Folder' });
				expect(res.status).toBe(201);
				expect(res.body.folder).toEqual({ id: '2', name: 'New Folder' });
			});


		it('PUT /folders/:id updates a folder', async () => {
			const from = supabaseMock.from();
			from.update.mockReturnThis();
			from.eq.mockReturnThis();
			from.select.mockReturnThis();
			from.single.mockResolvedValue({ data: { id: '3', name: 'Updated Folder' }, error: null });
			const res = await request(app).put('/folders/3').send({ name: 'Updated Folder' });
			expect(res.status).toBe(200);
			expect(res.body.folder).toEqual({ id: '3', name: 'Updated Folder' });
		});


		it('DELETE /folders/:id deletes a folder', async () => {
			const _from = supabaseMock.from();
			// delete().eq() is already mocked in the factory
			const res = await request(app).delete('/folders/4');
			expect(res.status).toBe(204);
		});
});
