import React from 'react';
import { Notification } from '../types';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from './ui/Card';
import { Button } from './ui/Button';
import { BellIcon } from './Icons';
import { cn } from '../lib/utils';

// Helper function to format time since notification
function timeAgo(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  let interval = seconds / 31536000;
  if (interval > 1) {return Math.floor(interval) + "y ago";}
  interval = seconds / 2592000;
  if (interval > 1) {return Math.floor(interval) + "mo ago";}
  interval = seconds / 86400;
  if (interval > 1) {return Math.floor(interval) + "d ago";}
  interval = seconds / 3600;
  if (interval > 1) {return Math.floor(interval) + "h ago";}
  interval = seconds / 60;
  if (interval > 1) {return Math.floor(interval) + "m ago";}
  return "just now";
}

interface NotificationsPageProps {
  notifications: Notification[];
  onMarkAllNotificationsRead: () => void;
  onNotificationClick: (notification: Notification) => void;
}

const NotificationsPage: React.FC<NotificationsPageProps> = ({ notifications, onMarkAllNotificationsRead, onNotificationClick }) => {
  const sortedNotifications = [...notifications].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <Card>
        <CardHeader className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2">
          <div>
            <CardTitle>All Notifications</CardTitle>
            <CardDescription>Your complete notification history.</CardDescription>
          </div>
          <Button variant="outline" onClick={onMarkAllNotificationsRead} className="w-full sm:w-auto">
            Mark All as Read
          </Button>
        </CardHeader>
        <CardContent>
          {sortedNotifications.length > 0 ? (
            <ul className="divide-y divide-zinc-200">
              {sortedNotifications.map(notif => (
                <li key={notif.id}>
                  <button
                    onClick={() => onNotificationClick(notif)}
                    className="w-full text-left p-4 hover:bg-zinc-50 rounded-lg flex items-start gap-4 transition-colors"
                  >
                    {!notif.isRead && (
                      <div className="w-2.5 h-2.5 rounded-full bg-brand-500 mt-1.5 flex-shrink-0" aria-label="Unread"></div>
                    )}
                    <div className={cn("flex-1", notif.isRead ? 'pl-[26px]' : '')}>
                      <p className={cn("text-sm text-zinc-800", !notif.isRead && "font-semibold")}>
                        {notif.message}
                      </p>
                    </div>
                    <p className="text-xs text-zinc-500 mt-1 flex-shrink-0">{timeAgo(notif.createdAt)}</p>
                  </button>
                </li>
              ))}
            </ul>
          ) : (
            <div className="py-24 text-center text-sm text-zinc-500">
              <BellIcon className="w-12 h-12 mx-auto text-zinc-300 mb-4" />
              <h3 className="font-semibold text-lg text-zinc-800">No Notifications</h3>
              <p>You're all caught up!</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default NotificationsPage;
