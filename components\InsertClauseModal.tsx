import React, { useState, useMemo } from 'react';
import { Button } from './ui/Button';
import { CloseIcon, LibraryIcon, SearchIcon } from './Icons';
import { User, Clause } from '../types';

interface InsertClauseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onInsert: (content: string) => void;
  user: User;
}

const InsertClauseModal: React.FC<InsertClauseModalProps> = ({ isOpen, onClose, onInsert, user }) => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredClauses = useMemo(() => {
    const clauses = user.clauses || [];
    if (!searchTerm) {return clauses;}
    return clauses.filter(
      c =>
        c.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        c.tags.some(t => t.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }, [user.clauses, searchTerm]);

  if (!isOpen) {return null;}
  
  const handleInsert = (clause: Clause) => {
    onInsert(clause.content);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-2xl flex flex-col h-[70vh]">
        <header className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-semibold text-slate-800 flex items-center">
            <LibraryIcon className="w-5 h-5 mr-2 text-blue-700" />
            Insert from Clause Library
          </h2>
          <button onClick={onClose} className="p-2 text-slate-500 hover:bg-slate-100 rounded-lg">
            <CloseIcon className="w-6 h-6" />
          </button>
        </header>
        <div className="p-4 border-b">
             <div className="relative w-full">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <SearchIcon className="h-5 w-5 text-slate-400" />
                </div>
                <input
                    type="text"
                    placeholder="Search clauses by title or tag..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="block w-full rounded-md border-slate-300 pl-10 p-2"
                />
            </div>
        </div>
        <main className="p-4 flex-1 overflow-y-auto space-y-2">
            {filteredClauses.length > 0 ? (
                filteredClauses.map(clause => (
                    <div key={clause.id} className="p-3 rounded-lg border bg-slate-50 flex justify-between items-start">
                        <div>
                            <h3 className="font-medium text-slate-800">{clause.title}</h3>
                             <div className="mt-1 flex flex-wrap gap-1.5">
                                {clause.tags.map(tag => (
                                    <span key={tag} className="px-1.5 py-0.5 bg-slate-200 text-slate-700 text-xs font-medium rounded-full">{tag}</span>
                                ))}
                            </div>
                        </div>
                        <Button size="sm" onClick={() => handleInsert(clause)}>Insert</Button>
                    </div>
                ))
            ) : (
                <p className="text-center text-slate-500 pt-10">No matching clauses found.</p>
            )}
        </main>
        <footer className="p-4 bg-slate-50 flex justify-end gap-3 rounded-b-2xl border-t">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
        </footer>
      </div>
    </div>
  );
};

export default InsertClauseModal;