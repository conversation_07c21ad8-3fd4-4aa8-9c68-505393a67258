
import React, { useState, useEffect } from 'react';
import { Template } from '../../types';
import { Button } from '../ui/Button';
import { CloseIcon, EditIcon } from '../Icons';

interface TemplateEditorModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: Omit<Template, 'id'> | Template) => void;
  existingTemplate?: Template | null;
}

const TemplateEditorModal: React.FC<TemplateEditorModalProps> = ({ isOpen, onClose, onSave, existingTemplate }) => {
  const [template, setTemplate] = useState<Omit<Template, 'id'>>({
    title: '',
    description: '',
    category: '',
    prompt: '',
    requiredPlan: 'Registered User',
  });

  useEffect(() => {
    if (existingTemplate) {
      setTemplate(existingTemplate);
    } else {
      setTemplate({ title: '', description: '', category: '', prompt: '', requiredPlan: 'Registered User' });
    }
  }, [existingTemplate, isOpen]);

  if (!isOpen) {return null;}

  const handleInputChange = (field: keyof Omit<Template, 'id'>, value: string | 'Registered User' | 'Premium') => {
    setTemplate(prev => ({ ...prev, [field]: value }));
  };
  
  const handleSave = () => {
    if (existingTemplate) {
      onSave({ ...template, id: existingTemplate.id });
    } else {
      onSave(template);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-2xl">
        <header className="flex items-center justify-between p-4 border-b dark:border-zinc-800">
          <h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200 flex items-center">
            <EditIcon className="w-5 h-5 mr-2 text-brand-700 dark:text-brand-400" />
            {existingTemplate ? 'Edit Template' : 'Create New Template'}
          </h2>
          <button onClick={onClose} className="p-2 text-zinc-500 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800 rounded-lg">
            <CloseIcon className="w-6 h-6" />
          </button>
        </header>
        <main className="p-6 space-y-4 max-h-[60vh] overflow-y-auto">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div><label className="block text-sm font-medium dark:text-zinc-300">Title</label><input type="text" value={template.title} onChange={e => handleInputChange('title', e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" /></div>
            <div><label className="block text-sm font-medium dark:text-zinc-300">Category</label><input type="text" value={template.category} onChange={e => handleInputChange('category', e.target.value)} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" /></div>
          </div>
          <div><label className="block text-sm font-medium dark:text-zinc-300">Description</label><textarea value={template.description} onChange={e => handleInputChange('description', e.target.value)} rows={3} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" /></div>
          <div><label className="block text-sm font-medium dark:text-zinc-300">AI Prompt</label><textarea value={template.prompt} onChange={e => handleInputChange('prompt', e.target.value)} rows={5} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2" /></div>
          <div>
            <label className="block text-sm font-medium dark:text-zinc-300">Required Plan</label>
            <select value={template.requiredPlan} onChange={e => handleInputChange('requiredPlan', e.target.value as 'Registered User' | 'Premium')} className="mt-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2">
              <option value="Registered User">Registered User (Free)</option>
              <option value="Premium">Premium</option>
            </select>
          </div>
        </main>
        <footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t dark:border-zinc-800">
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSave}>Save Template</Button>
        </footer>
      </div>
    </div>
  );
};

export default TemplateEditorModal;