
import React from 'react';
import Indicator from './Indicator';
import { FolderIcon, PlusCircleIcon } from '../Icons';

const FolderItem = ({ name, active }: { name: string, active?: boolean }) => (
    <div className={`flex items-center p-2 rounded-md ${active ? 'bg-brand-50 text-brand-700' : 'text-zinc-700'}`}>
        <FolderIcon className="w-4 h-4 mr-2"/> {name}
    </div>
);

const FoldersHelpScreenshot: React.FC = () => {
    return (
        <div className="relative my-6 p-4 border rounded-lg bg-zinc-50 dark:bg-zinc-900 flex gap-4 overflow-hidden select-none text-xs">
            <Indicator number={1} position="top-2 left-16" arrow="down" />
            <Indicator number={2} position="top-1/3 -right-1" arrow="left" />

            <aside className="w-40 flex-shrink-0 bg-white dark:bg-zinc-800 p-2 rounded-lg border border-zinc-200 dark:border-zinc-700">
                <div className="bg-brand-600 text-white font-semibold px-3 py-2 rounded-md text-center mb-2 flex items-center justify-center">
                    <PlusCircleIcon className="w-4 h-4 mr-1"/> New Folder
                </div>
                <nav className="space-y-0.5">
                    <FolderItem name="All Documents" active />
                    <FolderItem name="Uncategorized" />
                    <FolderItem name="Client Agreements" />
                </nav>
            </aside>
            <main className="flex-1 bg-white dark:bg-zinc-800 p-3 rounded-lg border border-zinc-200 dark:border-zinc-700">
                <h2 className="font-bold text-zinc-900 dark:text-zinc-100">Your Documents</h2>
                <div className="mt-2 h-24 border-2 border-dashed border-zinc-200 dark:border-zinc-700 rounded-md"></div>
            </main>
        </div>
    );
};

export default FoldersHelpScreenshot;
