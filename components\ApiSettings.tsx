
import React, { useState } from 'react';
import { Team, ApiKey } from '../types';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/Card';
import { Button } from './ui/Button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/Table';
import { TrashIcon } from './Icons';
import NewApiKeyModal from './NewApiKeyModal';

interface ApiSettingsProps {
  team: Team | undefined;
  onGenerateApiKey: (name: string) => ApiKey;
  onRevokeApiKey: (keyId: string) => void;
}

const ApiSettings: React.FC<ApiSettingsProps> = ({ team, onGenerateApiKey, onRevokeApiKey }) => {
  const [keyName, setKeyName] = useState('');
  const [newApiKey, setNewApiKey] = useState<ApiKey | null>(null);
  const [keyToRevoke, setKeyToRevoke] = useState<ApiKey | null>(null);

  const handleGenerate = (e: React.FormEvent) => {
    e.preventDefault();
    if (!keyName.trim()) {return;}
    const newKey = onGenerateApiKey(keyName);
    setNewApiKey(newKey);
    setKeyName('');
  };
  
  const handleRevokeConfirm = () => {
      if(keyToRevoke) {
          onRevokeApiKey(keyToRevoke.id);
          setKeyToRevoke(null);
      }
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>API & Integrations</CardTitle>
          <CardDescription>Manage API keys for custom integrations with LexiGen.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="p-4 border rounded-lg bg-zinc-50 dark:bg-zinc-800/50 dark:border-zinc-800">
            <h4 className="font-medium">Generate New API Key</h4>
            <form onSubmit={handleGenerate} className="flex items-center gap-2 mt-2">
              <input
                type="text"
                value={keyName}
                onChange={(e) => setKeyName(e.target.value)}
                placeholder="e.g., Marketing Site Integration"
                className="flex-1 block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 p-2 shadow-sm"
              />
              <Button type="submit" disabled={!keyName.trim()}>Generate Key</Button>
            </form>
          </div>

          <div>
            <h4 className="font-medium mb-2">Existing Keys</h4>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Key</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {team?.apiKeys?.map(key => (
                  <TableRow key={key.id}>
                    <TableCell className="font-medium">{key.name}</TableCell>
                    <TableCell className="font-mono text-sm">{key.key}</TableCell>
                    <TableCell>{new Date(key.createdAt).toLocaleDateString()}</TableCell>
                    <TableCell className="text-right">
                        <Button variant="ghost" size="icon" className="h-8 w-8 text-zinc-500 hover:text-red-500" onClick={() => setKeyToRevoke(key)}>
                            <TrashIcon className="w-4 h-4" />
                        </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
             {(!team?.apiKeys || team.apiKeys.length === 0) && (
                <p className="text-center py-8 text-sm text-zinc-500">No API keys have been generated yet.</p>
             )}
          </div>
        </CardContent>
      </Card>
      {newApiKey && <NewApiKeyModal apiKey={newApiKey} onClose={() => setNewApiKey(null)} />}
      
      {keyToRevoke && (
         <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-md p-6">
                <h3 className="text-lg font-medium text-zinc-900 dark:text-zinc-50">Revoke API Key</h3>
                <p className="mt-2 text-sm text-zinc-500 dark:text-zinc-400">
                    Are you sure you want to revoke the key "{keyToRevoke.name}"? This action is irreversible and will break any integration using this key.
                </p>
                <div className="mt-4 flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setKeyToRevoke(null)}>Cancel</Button>
                    <Button variant="destructive" onClick={handleRevokeConfirm}>Revoke</Button>
                </div>
            </div>
        </div>
    )}
    </>
  );
};

export default ApiSettings;
