

import React, { useCallback, useEffect, useState } from 'react';
import { Document as DocType, User, Collaborator, Permission } from '../types';
import { Button } from './ui/Button';
import Input from './ui/Input';
// FIX: Added missing LinkIcon to import.
import { CloseIcon, LinkIcon, TrashIcon, ChevronDownIcon, DocumentIcon, EditIcon } from './Icons';
import Dropdown from './ui/Dropdown';
import { apiFetch } from '../lib/api';

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  document: DocType;
  currentUser: User;
  allUsers: User[];
  onUpdateCollaborators: (updatedCollaborators: Collaborator[]) => void;
}

type CollaboratorWithId = Collaborator & { id?: string };

interface ApiCollaborator {
  id: string;
  email: string;
  permission: Permission;
  avatar_url?: string | null;
}

const ShareModal: React.FC<ShareModalProps> = ({ isOpen, onClose, document, currentUser, allUsers, onUpdateCollaborators }) => {
  const [inviteEmail, setInviteEmail] = useState('');
  const [invitePermission, setInvitePermission] = useState<Permission>('view');
  const [error, setError] = useState('');
  const [linkCopied, setLinkCopied] = useState(false);
  const [collaborators, setCollaborators] = useState<CollaboratorWithId[]>(() => document.collaborators.map(c => ({ ...c })));

  const parseErrorMessage = (err: unknown) => {
    if (err instanceof Error) {
      try {
        const parsed = JSON.parse(err.message) as { error?: string };
        if (parsed?.error) {return parsed.error;}
      } catch {
        /* noop */
      }
      return err.message;
    }
    return 'Something went wrong. Please try again.';
  };

  const refreshCollaborators = useCallback(async () => {
    try {
      const resp = await apiFetch<{ collaborators: ApiCollaborator[] }>(`/api/documents/${document.id}/collaborators`);
      const normalized = (resp.collaborators || []).map((c): CollaboratorWithId => ({
        id: c.id,
        email: c.email,
        permission: c.permission,
        avatarUrl: c.avatar_url ?? undefined,
      }));
      setCollaborators(normalized);
      onUpdateCollaborators(normalized.map(({ id: _id, ...rest }) => rest));
      setError('');
    } catch (err) {
      setError(parseErrorMessage(err));
    }
  }, [document.id, onUpdateCollaborators]);

  useEffect(() => {
    setCollaborators(document.collaborators.map(c => ({ ...c })));
  }, [document.id]);

  useEffect(() => {
    if (isOpen) {
      refreshCollaborators();
    }
  }, [isOpen, refreshCollaborators]);

  if (!isOpen) {return null;}

  const handleInvite = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    if (!inviteEmail) {return;}

    if (inviteEmail === currentUser.email) {
        setError("You can't invite yourself.");
        return;
    }
    
    if (collaborators.find(c => c.email === inviteEmail)) {
        setError('This user is already a collaborator.');
        return;
    }

    const userExists = allUsers.find(u => u.email === inviteEmail);
    if (!userExists) {
        setError("User not found in the system. Please ensure the email is correct.");
        return;
    }

    try {
      await apiFetch<{ collaborator: ApiCollaborator }>(`/api/documents/${document.id}/collaborators`, {
        method: 'POST',
        body: JSON.stringify({
          email: inviteEmail,
          permission: invitePermission,
          avatarUrl: userExists.avatarUrl,
        }),
      });
      await refreshCollaborators();
      setInviteEmail('');
    } catch (err) {
      setError(parseErrorMessage(err));
    }
  };

  const handlePermissionChange = async (email: string, permission: Permission) => {
    setError('');
    const collaborator = collaborators.find(c => c.email === email);
    if (!collaborator?.id) {
      setError('Unable to update collaborator. Please refresh and try again.');
      return;
    }

    try {
      await apiFetch<{ collaborator: ApiCollaborator }>(`/api/documents/collaborators/${collaborator.id}`, {
        method: 'PUT',
        body: JSON.stringify({ permission }),
      });
      await refreshCollaborators();
    } catch (err) {
      setError(parseErrorMessage(err));
    }
  };

  const handleRemoveCollaborator = async (email: string) => {
    setError('');
    const collaborator = collaborators.find(c => c.email === email);
    if (!collaborator?.id) {
      setError('Unable to remove collaborator. Please refresh and try again.');
      return;
    }

    try {
      await apiFetch<void>(`/api/documents/collaborators/${collaborator.id}`, {
        method: 'DELETE',
      });
      await refreshCollaborators();
    } catch (err) {
      setError(parseErrorMessage(err));
    }
  };
  
  const handleCopyLink = () => {
    const link = `${window.location.origin}/doc/${document.id}`;
    navigator.clipboard.writeText(link);
    setLinkCopied(true);
    setTimeout(() => setLinkCopied(false), 2000);
  };

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-zinc-950 rounded-2xl shadow-xl w-full max-w-lg">
        <header className="flex items-center justify-between p-4 border-b border-zinc-200 dark:border-zinc-800">
          <h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200">Share "{document.name}"</h2>
          <button onClick={onClose} className="p-2 text-zinc-500 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800 rounded-lg">
            <CloseIcon className="w-6 h-6" />
          </button>
        </header>
        <main className="p-6 space-y-6">
            <form onSubmit={handleInvite} className="flex flex-col sm:flex-row items-center gap-2">
                <Input
                  type="email"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  placeholder="Enter email to invite..."
                  className="bg-white dark:bg-zinc-800 border-zinc-300 dark:border-zinc-700"
                />
                 <Dropdown>
                    <Dropdown.Trigger>
                        <Button type="button" variant="outline" className="w-full sm:w-auto">
                            {invitePermission === 'view' ? 'Can View' : 'Can Edit'}
                            <ChevronDownIcon className="w-4 h-4 ml-2" />
                        </Button>
                    </Dropdown.Trigger>
                    <Dropdown.Content align="right">
                        <Dropdown.Item onClick={() => setInvitePermission('view')} icon={<DocumentIcon className="w-4 h-4"/>}>Can View</Dropdown.Item>
                        <Dropdown.Item onClick={() => setInvitePermission('edit')} icon={<EditIcon className="w-4 h-4"/>}>Can Edit</Dropdown.Item>
                    </Dropdown.Content>
                 </Dropdown>
                <Button type="submit" className="w-full sm:w-auto">Invite</Button>
            </form>
            {error && <p className="text-sm text-red-500">{error}</p>}
            
            <div>
                <h3 className="text-base font-medium text-zinc-900 dark:text-zinc-200">People with access</h3>
                <div className="mt-2 space-y-2 max-h-40 overflow-y-auto">
                    {/* Owner */}
                    <div className="flex items-center justify-between p-2">
                        <div className="flex items-center gap-3">
                            <img src={currentUser.avatarUrl} alt={currentUser.email} className="w-8 h-8 rounded-full" />
                            <div>
                                <p className="text-sm font-medium text-zinc-800 dark:text-zinc-200">{currentUser.name || currentUser.email}</p>
                                <p className="text-xs text-zinc-500 dark:text-zinc-400">{currentUser.email}</p>
                            </div>
                        </div>
                        <span className="text-sm text-zinc-500 dark:text-zinc-400">Owner</span>
                    </div>
                    {/* Collaborators */}
                    {collaborators.map(c => (
                        <div key={c.email} className="flex items-center justify-between p-2 rounded-lg hover:bg-zinc-50 dark:hover:bg-zinc-800">
                             <div className="flex items-center gap-3">
                                <img src={c.avatarUrl} alt={c.email} className="w-8 h-8 rounded-full" />
                                <div>
                                    <p className="text-sm font-medium text-zinc-800 dark:text-zinc-200">{allUsers.find(u => u.email === c.email)?.name || c.email}</p>
                                    <p className="text-xs text-zinc-500 dark:text-zinc-400">{c.email}</p>
                                </div>
                            </div>
                             <div className="flex items-center gap-2">
                                <Dropdown>
                                    <Dropdown.Trigger>
                                        <Button type="button" variant="outline" size="sm" className="h-8">
                                            {c.permission === 'view' ? 'Can View' : 'Can Edit'}
                                            <ChevronDownIcon className="w-4 h-4 ml-2" />
                                        </Button>
                                    </Dropdown.Trigger>
                                    <Dropdown.Content align="right">
                                        <Dropdown.Item onClick={() => handlePermissionChange(c.email, 'view')} icon={<DocumentIcon className="w-4 h-4"/>}>Can View</Dropdown.Item>
                                        <Dropdown.Item onClick={() => handlePermissionChange(c.email, 'edit')} icon={<EditIcon className="w-4 h-4"/>}>Can Edit</Dropdown.Item>
                                    </Dropdown.Content>
                                </Dropdown>
                                <Button variant="ghost" size="icon" className="h-8 w-8 text-zinc-500 dark:text-zinc-400 hover:text-red-600 dark:hover:text-red-500" onClick={() => handleRemoveCollaborator(c.email)}>
                                    <TrashIcon className="w-4 h-4"/>
                                </Button>
                             </div>
                        </div>
                    ))}
                </div>
            </div>
        </main>
        <footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t border-zinc-200 dark:border-zinc-800">
          <div className="flex-1 flex items-center gap-2">
            <LinkIcon className="w-5 h-5 text-zinc-500 dark:text-zinc-400"/>
            <input type="text" readOnly value={`${window.location.origin}/doc/${document.id}`} className="w-full bg-zinc-200 dark:bg-zinc-700 text-zinc-600 dark:text-zinc-300 text-sm rounded-md p-1.5 border border-zinc-300 dark:border-zinc-600"/>
            <Button variant="outline" size="sm" onClick={handleCopyLink}>{linkCopied ? 'Copied!' : 'Copy Link'}</Button>
          </div>
          <Button onClick={onClose}>Done</Button>
        </footer>
      </div>
    </div>
  );
};

export default ShareModal;
