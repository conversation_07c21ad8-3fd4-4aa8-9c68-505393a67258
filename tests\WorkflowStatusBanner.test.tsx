
// @vitest-environment jsdom
import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { describe, it, expect } from 'vitest';
import WorkflowStatusBanner from '../components/WorkflowStatusBanner';

describe('WorkflowStatusBanner', () => {
  const instance = { 
    id: 'instance-1',
    currentNodeId: 'node-1',
    templateId: 'template-1',
    documentId: 'doc-1',
    status: 'running' as const,
    createdAt: '2023-01-01',
    updatedAt: '2023-01-01'
  };
  const template = {
    id: 'template-1',
    name: 'Test Workflow',
    nodes: [
      { id: 'node-1', type: 'trigger' as const, data: { label: 'Start Node', triggerType: 'document-created' as const }, position: { x: 0, y: 0 } },
      { id: 'node-2', type: 'approval' as const, data: { label: 'End Node' }, position: { x: 100, y: 100 } },
    ],
    edges: [],
    status: 'active' as const
  };

  it('renders the workflow status banner with correct info', () => {
    render(
      <WorkflowStatusBanner instance={instance} template={template} />
    );
    expect(screen.getByText('Workflow Active:')).toBeInTheDocument();
    expect(screen.getByText('Test Workflow -')).toBeInTheDocument();
    expect(screen.getByText('Start Node')).toBeInTheDocument();
  });

  it('returns null if current node is not found', () => {
    const badInstance = { 
      id: 'instance-2',
      currentNodeId: 'missing-node',
      templateId: 'template-1',
      documentId: 'doc-1',
      status: 'running' as const,
      createdAt: '2023-01-01',
      updatedAt: '2023-01-01'
    };
    const { container } = render(
      <WorkflowStatusBanner instance={badInstance} template={template} />
    );
    expect(container.firstChild).toBeNull();
  });
});
