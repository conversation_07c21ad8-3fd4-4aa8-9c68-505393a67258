import React, { useState, useEffect } from 'react';
import { Button } from './ui/Button';

const CookieConsent: React.FC = () => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    try {
      const consent = localStorage.getItem('cookie_consent');
      if (!consent) {
        setVisible(true);
      }
    } catch {
      // Could not access localStorage - error handled by UI state
    }
  }, []);

  const handleAccept = () => {
    try {
      localStorage.setItem('cookie_consent', 'true');
      setVisible(false);
    } catch {
      // Could not set item in localStorage - error handled by UI state
      setVisible(false);
    }
  };

  if (!visible) {return null;}

  return (
    <div className="fixed bottom-4 left-4 bg-white dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200 p-5 rounded-lg shadow-2xl max-w-sm z-50 animate-fade-in-up">
      <h4 className="font-semibold">Cookie Preferences</h4>
      <p className="text-sm mt-2 mb-4 text-zinc-600 dark:text-zinc-300">
        We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.
        <a href="#privacy" className="underline ml-1 hover:text-brand-500">Learn more</a>
      </p>
      <Button onClick={handleAccept} size="sm" className="w-full">Accept</Button>
    </div>
  );
};

export default CookieConsent;