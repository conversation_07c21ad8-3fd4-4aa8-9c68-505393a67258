import { Router } from 'express';
import { z } from 'zod';
import { requireAuth, AuthRequest, getAccessToken } from '../middleware/auth';
import { getUserClient, supabaseAdmin } from '../supabaseClient';
import { requireAdmin } from '../middleware/admin';
import { getFromCache, setCache, deleteFromCache, cacheKeys, CACHE_TTL } from '../lib/redis';
import { getSupabaseErrorMessage } from '../lib/httpErrors';

const router = Router();

// Public pricing plans (no auth). Uses RLS policy allowing public select.
router.get('/public', async (_req, res) => {
  const cacheKey = cacheKeys.pricingPlans();
  
  // Try to get from cache first
  const cachedPlans = await getFromCache(cacheKey);
  if (cachedPlans) {
    return res.json({ pricingPlans: cachedPlans });
  }

  const { data, error } = await supabaseAdmin
    .from('pricing_plans')
    .select('id, name, price, price_detail, features, cta, is_featured, sort_order')
    .order('sort_order', { ascending: true })
    .order('name');
  if (error) {return res.status(500).json({ error: getSupabaseErrorMessage(error) });}
  
  // Cache the result
  await setCache(cacheKey, data, CACHE_TTL.PRICING_PLANS);
  
  res.json({ pricingPlans: data });
});

router.get('/', requireAuth, async (req: AuthRequest, res) => {
  const cacheKey = `${cacheKeys.pricingPlans()}:detailed`;
  
  // Try to get from cache first
  const cachedPlans = await getFromCache(cacheKey);
  if (cachedPlans) {
    return res.json({ pricingPlans: cachedPlans });
  }

  const supa = getUserClient(getAccessToken(req));
  const { data, error } = await supa
    .from('pricing_plans')
    .select('id, name, price, price_detail, features, cta, is_featured, sort_order')
    .order('sort_order', { ascending: true })
    .order('name');
  if (error) {return res.status(500).json({ error: getSupabaseErrorMessage(error) });}
  
  // Cache the result
  await setCache(cacheKey, data, CACHE_TTL.PRICING_PLANS);
  
  res.json({ pricingPlans: data });
});

export default router;

// Admin-only pricing management
const planSchema = z.object({
  name: z.string(),
  price: z.string(),
  priceDetail: z.string().optional(),
  features: z.array(z.string()).default([]),
  cta: z.string(),
  isFeatured: z.boolean().default(false),
  sortOrder: z.number().int().nonnegative().optional(),
});
router.post('/', requireAuth, requireAdmin, async (req: AuthRequest, res) => {
  const parsed = planSchema.safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const { data, error } = await supabaseAdmin.from('pricing_plans').insert({
    name: parsed.data.name,
    price: parsed.data.price,
    price_detail: parsed.data.priceDetail,
    features: parsed.data.features,
    cta: parsed.data.cta,
    is_featured: parsed.data.isFeatured,
    sort_order: parsed.data.sortOrder,
  }).select('id, name, price, price_detail, features, cta, is_featured, sort_order').single();
  if (error) {return res.status(500).json({ error: getSupabaseErrorMessage(error) });}
  
  // Invalidate pricing plans cache
  await deleteFromCache(cacheKeys.pricingPlans());
  await deleteFromCache(`${cacheKeys.pricingPlans()}:detailed`);
  
  res.status(201).json({ plan: data });
});

router.put('/:id', requireAuth, requireAdmin, async (req: AuthRequest<{ id: string }>, res) => {
  const parsed = planSchema.partial().safeParse(req.body);
  if (!parsed.success) {return res.status(400).json({ error: parsed.error.message });}
  const patch: Partial<{
    name: string;
    price: string;
    price_detail: string;
    features: string[];
    cta: string;
    is_featured: boolean;
    sort_order: number;
  }> = {
    name: parsed.data.name,
    price: parsed.data.price,
    price_detail: parsed.data.priceDetail,
    features: parsed.data.features,
    cta: parsed.data.cta,
    is_featured: parsed.data.isFeatured,
    sort_order: parsed.data.sortOrder,
  };
  Object.keys(patch).forEach(k => patch[k] === undefined && delete patch[k]);
  const { data, error } = await supabaseAdmin.from('pricing_plans').update(patch).eq('id', req.params.id).select('id, name, price, price_detail, features, cta, is_featured, sort_order').single();
  if (error) {return res.status(500).json({ error: getSupabaseErrorMessage(error) });}
  
  // Invalidate pricing plans cache
  await deleteFromCache(cacheKeys.pricingPlans());
  await deleteFromCache(`${cacheKeys.pricingPlans()}:detailed`);
  
  res.json({ plan: data });
});

router.delete('/:id', requireAuth, requireAdmin, async (req: AuthRequest<{ id: string }>, res) => {
  const { error } = await supabaseAdmin.from('pricing_plans').delete().eq('id', req.params.id);
  if (error) {return res.status(500).json({ error: getSupabaseErrorMessage(error) });}
  
  // Invalidate pricing plans cache
  await deleteFromCache(cacheKeys.pricingPlans());
  await deleteFromCache(`${cacheKeys.pricingPlans()}:detailed`);
  
  res.status(204).end();
});
