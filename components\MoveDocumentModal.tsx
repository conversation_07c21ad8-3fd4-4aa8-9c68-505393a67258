

import React from 'react';
import { <PERSON><PERSON> } from './ui/Button';
import { CloseIcon, FolderIcon } from './Icons';
import { Folder, Document as DocType } from '../types';
import { cn } from '../lib/utils';

interface MoveDocumentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onMove: (folderId: string | null) => void;
  document: DocType;
  folders: Folder[];
}

const MoveDocumentModal: React.FC<MoveDocumentModalProps> = ({ isOpen, onClose, onMove, document, folders }) => {
  if (!isOpen) {return null;}

  return (
    <div className="fixed inset-0 bg-black/60 z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-zinc-900 rounded-2xl shadow-xl w-full max-w-md">
        <header className="flex items-center justify-between p-4 border-b dark:border-zinc-800">
          <h2 className="text-lg font-semibold text-zinc-800 dark:text-zinc-200">Move Document</h2>
          <button onClick={onClose} className="p-2 text-zinc-500 dark:text-zinc-400 hover:bg-zinc-100 dark:hover:bg-zinc-800 rounded-lg">
            <CloseIcon className="w-6 h-6" />
          </button>
        </header>
        <main className="p-6">
          <p className="text-sm text-zinc-600 dark:text-zinc-400 mb-4">
            Select a destination for <span className="font-semibold text-zinc-800 dark:text-zinc-200">"{document.name}"</span>:
          </p>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            <button
              onClick={() => onMove(null)}
              className={cn(
                'w-full text-left flex items-center p-3 rounded-md text-sm hover:bg-zinc-100 dark:hover:bg-zinc-800',
                !document.folderId ? 'bg-brand-100 text-brand-800 font-semibold dark:bg-brand-900/50 dark:text-brand-300' : 'text-zinc-700 dark:text-zinc-300'
              )}
            >
              <FolderIcon className="w-5 h-5 mr-3" /> Uncategorized (Root)
            </button>
            {folders.map(folder => (
              <button
                key={folder.id}
                onClick={() => onMove(folder.id)}
                className={cn(
                  'w-full text-left flex items-center p-3 rounded-md text-sm hover:bg-zinc-100 dark:hover:bg-zinc-800',
                  document.folderId === folder.id ? 'bg-brand-100 text-brand-800 font-semibold dark:bg-brand-900/50 dark:text-brand-300' : 'text-zinc-700 dark:text-zinc-300'
                )}
                disabled={document.folderId === folder.id}
              >
                <FolderIcon className="w-5 h-5 mr-3" /> {folder.name}
              </button>
            ))}
          </div>
        </main>
        <footer className="p-4 bg-zinc-50 dark:bg-zinc-950 flex justify-end gap-3 rounded-b-2xl border-t dark:border-zinc-800">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </footer>
      </div>
    </div>
  );
};

export default MoveDocumentModal;