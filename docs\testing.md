# Testing Guide

This project uses Vitest + Supertest for unit and integration tests. The server exports an Express app (`server/app.ts`) for testability. Supabase and other services are mocked in tests via `vi.mock`.

## Running tests

- npm install
- npm test

## Patterns

- Mock Supabase: `vi.mock('../server/supabaseClient', () => ({ getUserClient: jestImpl, supabaseAdmin: jestImpl }))`.
- Bypass auth in tests: set `process.env.TEST_BYPASS_AUTH = '1'` in test `beforeAll`.
- Use Supertest against the exported `app` for route tests.

## Adding tests for new features

1. Create a test with the scaffold:
   - npm run test:scaffold -- my-feature
   - Edit `tests/my-feature.test.ts` with route calls and mocks.
2. If you add a new route under `server/routes/` or a component under `components/`, the CI workflow enforces that you also add or update a test (`.github/workflows/enforce-tests.yml`).
3. Keep mocks minimal and focused on the API surface you exercise.

## Existing coverage

- Sanitization (unit)
- Health routes (integration)
- Profiles: self vs admin updates (integration, mocked Supabase)
- Clients: create/update/delete (integration, mocked storage)
- Client assets: upload-url sanitation, signed-url path
- Documents: list and CRUD
- Comments/Threads: create flows
- Pricing public: list
- Quota: plan and counters
- AI routes: fail path without configured key

## Tips

- If a test requires DB behavior, prefer mocking the specific Supabase calls used by the route.
- For new routes, copy a similar test and adjust the mocks and assertions.

