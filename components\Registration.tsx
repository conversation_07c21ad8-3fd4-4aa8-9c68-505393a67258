

import React, { useState } from 'react';
import { UserIcon, LockIcon, MailIcon } from './Icons';

interface RegistrationProps {
  onSwitchToLogin: () => void;
  handleRegister: (email: string, password: string) => boolean;
  authError: string | null;
}

const Registration: React.FC<RegistrationProps> = ({ onSwitchToLogin, handleRegister, authError }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [agreedToTerms, setAgreedToTerms] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!agreedToTerms) {
        setError("You must agree to the Terms and Conditions to create an account.");
        return;
    }
    if (password !== confirmPassword) {
      setError("Passwords do not match.");
      return;
    }
    if (password.length < 8) {
        setError("Password must be at least 8 characters long.");
        return;
    }

    const success = handleRegister(email, password);
    if(success) {
        setIsSubmitted(true);
    }
  };

  if (isSubmitted) {
    return (
       <div className="text-center">
            <MailIcon className="mx-auto h-12 w-12 text-green-500" />
            <h2 className="mt-4 text-2xl font-bold text-zinc-900 dark:text-white">Confirm your email</h2>
            <p className="mt-2 text-zinc-600 dark:text-zinc-400">
                We've sent a verification link to <span className="font-semibold text-zinc-800 dark:text-zinc-200">{email}</span>. Please check your inbox (and spam folder) to complete your registration.
            </p>
            <p className="mt-6 text-center text-sm text-zinc-600 dark:text-zinc-400">
                Already verified?{' '}
                <button onClick={onSwitchToLogin} className="font-medium text-brand-600 hover:text-brand-700 focus:outline-none">
                    Log in
                </button>
            </p>
        </div>
    );
  }


  return (
    <div className="w-full max-w-md">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-zinc-900 dark:text-white">Create an Account</h2>
        <p className="text-zinc-500 dark:text-zinc-400 mt-2">Get 10 free generations per month.</p>
      </div>
      <form onSubmit={handleSubmit} className="space-y-5">
        <div>
          <label htmlFor="email-register" className="block text-sm font-medium text-zinc-700 dark:text-zinc-300">Email Address</label>
           <div className="mt-1 relative rounded-md shadow-sm">
             <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <UserIcon className="h-5 w-5 text-zinc-400" />
            </div>
            <input 
                type="email" 
                id="email-register" 
                name="email" 
                autoComplete="email"
                required 
                className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 focus:border-brand-500 focus:ring-brand-500 sm:text-sm p-3" 
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
            />
          </div>
        </div>
        <div>
          <label htmlFor="password-register" className="block text-sm font-medium text-zinc-700 dark:text-zinc-300">Password</label>
           <div className="mt-1 relative rounded-md shadow-sm">
             <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <LockIcon className="h-5 w-5 text-zinc-400" />
            </div>
            <input 
                type="password" 
                id="password-register" 
                name="password" 
                autoComplete="new-password"
                required 
                className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 focus:border-brand-500 focus:ring-brand-500 sm:text-sm p-3" 
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
            />
          </div>
        </div>
         <div>
          <label htmlFor="password-confirm" className="block text-sm font-medium text-zinc-700 dark:text-zinc-300">Confirm Password</label>
           <div className="mt-1 relative rounded-md shadow-sm">
             <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <LockIcon className="h-5 w-5 text-zinc-400" />
            </div>
            <input 
                type="password" 
                id="password-confirm" 
                name="password-confirm" 
                autoComplete="new-password"
                required 
                className="block w-full rounded-md border-zinc-300 dark:border-zinc-700 bg-white dark:bg-zinc-800 pl-10 focus:border-brand-500 focus:ring-brand-500 sm:text-sm p-3" 
                placeholder="••••••••"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
            />
          </div>
        </div>

        <div className="flex items-start">
            <div className="flex items-center h-5">
                <input
                    id="terms"
                    name="terms"
                    type="checkbox"
                    checked={agreedToTerms}
                    onChange={(e) => setAgreedToTerms(e.target.checked)}
                    className="focus:ring-brand-500 h-4 w-4 text-brand-600 border-zinc-300 rounded"
                />
            </div>
            <div className="ml-3 text-sm">
                <label htmlFor="terms" className="text-zinc-600 dark:text-zinc-400">
                    I agree to the{' '}
                    <a href="#terms" target="_blank" rel="noopener noreferrer" className="font-medium text-brand-600 hover:text-brand-700">
                        Terms and Conditions
                    </a>
                </label>
            </div>
        </div>

        {(error || authError) && <p className="text-sm text-red-600 text-center">{error || authError}</p>}
        
        <div className="pt-2">
          <button type="submit" className="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-brand-600 hover:bg-brand-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-brand-500 disabled:bg-brand-400 disabled:cursor-not-allowed" disabled={!agreedToTerms}>
            Create Account
          </button>
        </div>
      </form>
      <p className="mt-6 text-center text-sm text-zinc-600 dark:text-zinc-400">
        Already have an account?{' '}
        <button onClick={onSwitchToLogin} className="font-medium text-brand-600 hover:text-brand-700 focus:outline-none">
          Log in
        </button>
      </p>
    </div>
  );
};
export default Registration;